%----------------------------------------
% GMAT Mission Script
%
% Final Version: Multi-Body Contact Analysis with Global Ground Stations
%
% Author: AI Assistant
% Date: 2024-07-30
%
% Scenario:
%   - Two spacecraft (SV1, SV2) in polar lunar orbit.
%   - A lunar ground station (HLS) at the Moon's South Pole.
%   - Three Earth ground stations (LEGS) in Ohio, Sydney, and Ireland.
%
% Analysis:
%   - Line-of-sight contact analysis for a 24-hour period.
%   - 10-second step size for high-resolution event finding.
%   - Earth and Moon are modeled as occulting bodies to ensure realistic
%     line-of-sight calculations.
%
%----------------------------------------

%----------------------------------------
%---------- Spacecraft Configuration
%----------------------------------------

% Create two spacecraft in polar lunar orbits. SV2 is phased 180 degrees
% from SV1 to provide more continuous coverage opportunities.
Create Spacecraft SV1, SV2;

% SV1 Configuration
GMAT SV1.DateFormat            = UTCGregorian;
GMAT SV1.Epoch                 = '01 Jan 2000 00:00:00.000';
GMAT SV1.CoordinateSystem      = LunarMJ2000Eq;
GMAT SV1.DisplayStateType      = Keplerian;
GMAT SV1.SMA                   = 2037.4;  % km
GMAT SV1.ECC                   = 0.0;
GMAT SV1.INC                   = 90.0;    % deg
GMAT SV1.RAAN                  = 0.0;     % deg
GMAT SV1.AOP                   = 0.0;     % deg
GMAT SV1.TA                    = 0.0;     % deg
GMAT SV1.OrbitColor = 'Blue';

% SV2 Configuration
GMAT SV2.DateFormat            = UTCGregorian;
GMAT SV2.Epoch                 = '01 Jan 2000 00:00:00.000';
GMAT SV2.CoordinateSystem      = LunarMJ2000Eq;
GMAT SV2.DisplayStateType      = Keplerian;
GMAT SV2.SMA                   = 2037.4;  % km
GMAT SV2.ECC                   = 0.0;
GMAT SV2.INC                   = 90.0;    % deg
GMAT SV2.RAAN                  = 180.0;   % deg
GMAT SV2.AOP                   = 0.0;     % deg
GMAT SV2.TA                    = 180.0;   % deg
GMAT SV2.OrbitColor = 'Magenta';

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

% Define a Moon-centered MJ2000 Equatorial coordinate system.
Create CoordinateSystem LunarMJ2000Eq;
GMAT LunarMJ2000Eq.Origin      = Luna;
GMAT LunarMJ2000Eq.Axes        = MJ2000Eq;

%----------------------------------------
%---------- Ground Stations
%----------------------------------------

% Define the three Earth ground stations (formerly LEGS).
Create GroundStation LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland;

% LEGS1 - Ohio, USA
GMAT LEGS1_Ohio.CentralBody        = Earth;
GMAT LEGS1_Ohio.StateType          = Spherical;
GMAT LEGS1_Ohio.HorizonReference   = Ellipsoid;
GMAT LEGS1_Ohio.Location1          = 40.4173;   % Latitude (deg)
GMAT LEGS1_Ohio.Location2          = 277.0147;  % Longitude (deg)
GMAT LEGS1_Ohio.Location3          = 0.260;     % Altitude (km)
GMAT LEGS1_Ohio.MinimumElevationAngle = 5.0;       % deg

% LEGS2 - Sydney, Australia
GMAT LEGS2_Sydney.CentralBody      = Earth;
GMAT LEGS2_Sydney.StateType        = Spherical;
GMAT LEGS2_Sydney.HorizonReference = Ellipsoid;
GMAT LEGS2_Sydney.Location1        = -33.8688;  % Latitude (deg)
GMAT LEGS2_Sydney.Location2        = 151.2093;  % Longitude (deg)
GMAT LEGS2_Sydney.Location3        = 0.058;     % Altitude (km)
GMAT LEGS2_Sydney.MinimumElevationAngle = 5.0;       % deg

% LEGS3 - Dublin, Ireland
GMAT LEGS3_Ireland.CentralBody     = Earth;
GMAT LEGS3_Ireland.StateType       = Spherical;
GMAT LEGS3_Ireland.HorizonReference = Ellipsoid;
GMAT LEGS3_Ireland.Location1       = 53.3498;   % Latitude (deg)
GMAT LEGS3_Ireland.Location2       = 353.7603;  % Longitude (deg)
GMAT LEGS3_Ireland.Location3       = 0.047;     % Altitude (km)
GMAT LEGS3_Ireland.MinimumElevationAngle = 5.0;       % deg

% Define the lunar ground station (HLS).
Create GroundStation HLS_Station;
GMAT HLS_Station.CentralBody       = Luna;
GMAT HLS_Station.StateType         = Spherical;
GMAT HLS_Station.HorizonReference  = Ellipsoid;
GMAT HLS_Station.Location1         = -89.0;     % Latitude (deg)
GMAT HLS_Station.Location2         = 0.0;       % Longitude (deg)
GMAT HLS_Station.Location3         = 0.0;       % Altitude (km)
GMAT HLS_Station.MinimumElevationAngle = 0.0;       % deg

%----------------------------------------
%---------- Force Models and Propagators
%----------------------------------------

% Define a simplified force model for lunar propagation.
Create ForceModel LunarProp_fm;
GMAT LunarProp_fm.CentralBody           = Luna;
GMAT LunarProp_fm.PrimaryBodies         = {Luna};
GMAT LunarProp_fm.PointMasses           = {Earth, Sun};
GMAT LunarProp_fm.Drag                  = None;
GMAT LunarProp_fm.SRP                   = Off;
GMAT LunarProp_fm.RelativisticCorrection = Off;
GMAT LunarProp_fm.ErrorControl        = RSSStep;

% Define the propagator for the lunar orbiters.
Create Propagator LunarProp;
GMAT LunarProp.FM                      = LunarProp_fm;
GMAT LunarProp.Type                    = RungeKutta89;
GMAT LunarProp.InitialStepSize         = 60;
GMAT LunarProp.Accuracy                = 9.999999999999999e-12;
GMAT LunarProp.MinStep                 = 0.001;
GMAT LunarProp.MaxStep                 = 2700;

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

% Define ContactLocators for each link in the communication chain.
Create ContactLocator SV1_to_LEGS, SV2_to_LEGS, HLS_to_SV1, HLS_to_SV2;

% Configure SV1 to Earth Ground Stations contacts
GMAT SV1_to_LEGS.Target              = SV1;
GMAT SV1_to_LEGS.Observers           = {LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland};
GMAT SV1_to_LEGS.OccultingBodies     = {Luna}; % Earth is implicit
GMAT SV1_to_LEGS.Filename            = '../output/SV1_to_Earth_Contacts.txt';
GMAT SV1_to_LEGS.StepSize            = 1.0;
GMAT SV1_to_LEGS.UseLightTimeDelay   = false;
GMAT SV1_to_LEGS.UseStellarAberration = false;
GMAT SV1_to_LEGS.WriteReport         = true;
GMAT SV1_to_LEGS.RunMode             = Automatic;

% Configure SV2 to Earth Ground Stations contacts
GMAT SV2_to_LEGS.Target              = SV2;
GMAT SV2_to_LEGS.Observers           = {LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland};
GMAT SV2_to_LEGS.OccultingBodies     = {Luna}; % Earth is implicit
GMAT SV2_to_LEGS.Filename            = '../output/SV2_to_Earth_Contacts.txt';
GMAT SV2_to_LEGS.StepSize            = 1.0;
GMAT SV2_to_LEGS.UseLightTimeDelay   = false;
GMAT SV2_to_LEGS.UseStellarAberration = false;
GMAT SV2_to_LEGS.WriteReport         = true;
GMAT SV2_to_LEGS.RunMode             = Automatic;

% Configure HLS to Spacecraft contacts
GMAT HLS_to_SV1.Target                = SV1;
GMAT HLS_to_SV1.Observers             = {HLS_Station};
% GMAT HLS_to_SV1.OccultingBodies is not needed as Luna is the central body.
GMAT HLS_to_SV1.Filename              = '../output/HLS_to_SV1_Contacts.txt';
GMAT HLS_to_SV1.StepSize              = 1.0;
GMAT HLS_to_SV1.UseLightTimeDelay     = false;
GMAT HLS_to_SV1.UseStellarAberration   = false;
GMAT HLS_to_SV1.WriteReport           = true;
GMAT HLS_to_SV1.RunMode               = Automatic;

GMAT HLS_to_SV2.Target                = SV2;
GMAT HLS_to_SV2.Observers             = {HLS_Station};
% GMAT HLS_to_SV2.OccultingBodies is not needed as Luna is the central body.
GMAT HLS_to_SV2.Filename              = '../output/HLS_to_SV2_Contacts.txt';
GMAT HLS_to_SV2.StepSize              = 1.0;
GMAT HLS_to_SV2.UseLightTimeDelay     = false;
GMAT HLS_to_SV2.UseStellarAberration   = false;
GMAT HLS_to_SV2.WriteReport           = true;
GMAT HLS_to_SV2.RunMode               = Automatic;


%----------------------------------------
%---------- Visualizers
%----------------------------------------

% Create a single OrbitView window for visualization.
Create OrbitView MissionView;

% Configure the Earth-centered view to show all mission elements.
GMAT MissionView.SolverIterations      = Current;
GMAT MissionView.UpperLeft             = [0.0, 0.0];
GMAT MissionView.Size                  = [1.0, 1.0];
GMAT MissionView.Add                   = {SV1, SV2, HLS_Station, LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland, Earth, Luna};
GMAT MissionView.CoordinateSystem      = EarthMJ2000Eq;
GMAT MissionView.DrawObject            = [true, true, true, true, true, true, true, true];
GMAT MissionView.ViewPointReference    = Earth;
GMAT MissionView.ViewPointVector       = [0, -600000, 300000]; % Zoomed out to see Moon
GMAT MissionView.ViewDirection         = Earth;
GMAT MissionView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT MissionView.ViewUpAxis            = Z;
GMAT MissionView.ShowPlot              = true;
GMAT MissionView.ShowLabels            = true;
GMAT MissionView.EnableStars           = Off;
GMAT MissionView.EnableConstellations  = Off;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

% Propagate the lunar orbiters for one day. The OrbitView will update
% automatically when running in GUI mode.
Propagate Synchronized LunarProp(SV1, SV2) {SV1.ElapsedDays = 1.0}; 