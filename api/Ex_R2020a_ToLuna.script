%General Mission Analysis Tool(GMAT) Script
%Created: 2019-04-26 09:25:18


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = UTCGregorian;
GMAT Sat.Epoch = '26 Apr 2019 12:00:00.000';
GMAT Sat.CoordinateSystem = EarthFixed;
GMAT Sat.DisplayStateType = Cartesian;
GMAT Sat.X = 639.2234690795908;
GMAT Sat.Y = -3854.671035535996;
GMAT Sat.Z = 5348.791562420619;
GMAT Sat.VX = -0.1652938147345514;
GMAT Sat.VY = 6.129069442591823;
GMAT Sat.VZ = 4.669570395189814;
GMAT Sat.DryMass = 850;
GMAT Sat.Cd = 2.2;
GMAT Sat.Cr = 1.8;
GMAT Sat.DragArea = 15;
GMAT Sat.SRPArea = 1;
GMAT Sat.SPADDragScaleFactor = 1;
GMAT Sat.SPADSRPScaleFactor = 1;
GMAT Sat.NAIFId = -10001001;
GMAT Sat.NAIFIdReferenceFrame = -9001001;
GMAT Sat.OrbitColor = Red;
GMAT Sat.TargetColor = Teal;
GMAT Sat.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
GMAT Sat.CdSigma = 1e+70;
GMAT Sat.CrSigma = 1e+70;
GMAT Sat.Id = 'SatId';
GMAT Sat.Attitude = CoordinateSystemFixed;
GMAT Sat.SPADSRPInterpolationMethod = Bilinear;
GMAT Sat.SPADSRPScaleFactorSigma = 1e+70;
GMAT Sat.SPADDragInterpolationMethod = Bilinear;
GMAT Sat.SPADDragScaleFactorSigma = 1e+70;
GMAT Sat.ModelFile = 'aura.3ds';
GMAT Sat.ModelOffsetX = 0;
GMAT Sat.ModelOffsetY = 0;
GMAT Sat.ModelOffsetZ = 0;
GMAT Sat.ModelRotationX = 0;
GMAT Sat.ModelRotationY = 0;
GMAT Sat.ModelRotationZ = 0;
GMAT Sat.ModelScale = 1;
GMAT Sat.AttitudeDisplayStateType = 'Quaternion';
GMAT Sat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT Sat.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT Sat.EulerAngleSequence = '321';



%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PrimaryBodies = {Earth};
GMAT DefaultProp_ForceModel.PointMasses = {Sun, Luna};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = On;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;
GMAT DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.Order = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.StmLimit = 100;
GMAT DefaultProp_ForceModel.GravityField.Earth.PotentialFile = '../data/gravity/earth/JGM2.cof';
GMAT DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT DefaultProp_ForceModel.SRP.Flux = 1367;
GMAT DefaultProp_ForceModel.SRP.SRPModel = Spherical;
GMAT DefaultProp_ForceModel.SRP.Nominal_Sun = 149597870.691;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = PrinceDormand78;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-12;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 3.1;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.81;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.06818822203437588 0.01508844953173777 ];
GMAT DefaultOrbitView.Size = [ 0.998872921949845 0.9838709677419355 ];
GMAT DefaultOrbitView.RelativeZOrder = 36;
GMAT DefaultOrbitView.Maximized = true;
GMAT DefaultOrbitView.Add = {Sat, Earth, Luna};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.MaxPlotPoints = 20000;
GMAT DefaultOrbitView.ShowLabels = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 0 0 1500000 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Y;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable LeoTime MoonDistance StartEpoch;
StartEpoch = -0.5;

LeoTime = 5400;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

StartEpoch = StartEpoch + Sat.A1ModJulian;
Sat.A1ModJulian = StartEpoch;
Sat.EarthFixed.X = 639.2234690795908;
Sat.EarthFixed.Y = -3854.671035535996;
Sat.EarthFixed.Z = 5348.791562420619;
Sat.EarthFixed.VX = -0.1652938147345514;
Sat.EarthFixed.VY = 6.129069442591823;
Sat.EarthFixed.VZ = 4.669570395189814;


Propagate DefaultProp(Sat) {Sat.ElapsedSecs = LeoTime};
Maneuver TOI(Sat);
Propagate DefaultProp(Sat) {Sat.Earth.Apoapsis, Sat.Luna.Periapsis, Sat.ElapsedDays = 8};
GMAT MoonDistance = Sat.Luna.RMAG;
