%----------------------------------------
% GMAT Mission Script
%
% Scenario: Lunar Relay Satellite Constellation
% Author: AI Assistant
% Date: 2024-07-30
%
% This script simulates a two-satellite constellation in polar orbit
% around the Moon to provide communication coverage for a ground station
% at the Shackleton Crater (lunar south pole).
%
% It performs a line-of-sight contact analysis between:
% 1. The lunar satellites and three ground stations on Earth.
% 2. The lunar ground station and the two lunar satellites.
%
% The visualization is split into two views for clarity:
% - A Moon-centered view for observing the satellite orbits.
% - An Earth-Moon barycenter view for observing the entire system.
%----------------------------------------

%----------------------------------------
%---------- Celestial Bodies & Calculated Points
%----------------------------------------

Create Barycenter EarthMoonBarycenter;
EarthMoonBarycenter.OrbitColor = Yellow;
EarthMoonBarycenter.TargetColor = DarkGray;
EarthMoonBarycenter.BodyNames = {Earth, Luna};

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

% Two spacecraft in polar lunar orbits, phased 180 degrees apart.
Create Spacecraft Sat1;

% Sat1 Configuration (Stable 2000km altitude circular polar orbit)
Sat1.DateFormat = UTCGregorian;
Sat1.Epoch = '01 Jan 2000 12:00:00.000';
Sat1.CoordinateSystem = MoonMJ2000Eq;
Sat1.DisplayStateType = Keplerian;
Sat1.SMA = 3737.400000000046; % 1737.4km (Moon radius) + 2000km (altitude)
Sat1.ECC = 6.093517721456613e-15;
Sat1.INC = 90;
Sat1.RAAN = 0;
Sat1.AOP = 0;
Sat1.TA = 0;
Sat1.DryMass = 850;
Sat1.Cd = 2.2;
Sat1.Cr = 1.8;
Sat1.DragArea = 15;
Sat1.SRPArea = 1;
Sat1.SPADDragScaleFactor = 1;
Sat1.SPADSRPScaleFactor = 1;
Sat1.AtmosDensityScaleFactor = 1;
Sat1.ExtendedMassPropertiesModel = 'None';
Sat1.NAIFId = -10000001;
Sat1.NAIFIdReferenceFrame = -9000001;
Sat1.OrbitColor = Red;
Sat1.TargetColor = Teal;
Sat1.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
Sat1.CdSigma = 1e+70;
Sat1.CrSigma = 1e+70;
Sat1.Id = 'SatId';
Sat1.Attitude = CoordinateSystemFixed;
Sat1.SPADSRPInterpolationMethod = Bilinear;
Sat1.SPADSRPScaleFactorSigma = 1e+70;
Sat1.SPADDragInterpolationMethod = Bilinear;
Sat1.SPADDragScaleFactorSigma = 1e+70;
Sat1.AtmosDensityScaleFactorSigma = 1e+70;
Sat1.ModelFile = 'aura.3ds';
Sat1.ModelOffsetX = 0;
Sat1.ModelOffsetY = 0;
Sat1.ModelOffsetZ = 0;
Sat1.ModelRotationX = 0;
Sat1.ModelRotationY = 0;
Sat1.ModelRotationZ = 0;
Sat1.ModelScale = 1;
Sat1.AttitudeDisplayStateType = 'Quaternion';
Sat1.AttitudeRateDisplayStateType = 'AngularVelocity';
Sat1.AttitudeCoordinateSystem = EarthMJ2000Eq;
Sat1.EulerAngleSequence = '321';

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

% Two spacecraft in polar lunar orbits, phased 180 degrees apart.
Create Spacecraft Sat2;

% Sat2 Configuration (Phased 180 deg from Sat1)
Sat2.DateFormat = UTCGregorian;
Sat2.Epoch = '01 Jan 2000 12:00:00.000';
Sat2.CoordinateSystem = MoonMJ2000Eq;
Sat2.DisplayStateType = Keplerian;
Sat2.SMA = 3737.400000000046;
Sat2.ECC = 6.094288660899315e-15;
Sat2.INC = 90;
Sat2.RAAN = 0;
Sat2.AOP = 0;
Sat2.TA = 180;
Sat2.DryMass = 850;
Sat2.Cd = 2.2;
Sat2.Cr = 1.8;
Sat2.DragArea = 15;
Sat2.SRPArea = 1;
Sat2.SPADDragScaleFactor = 1;
Sat2.SPADSRPScaleFactor = 1;
Sat2.AtmosDensityScaleFactor = 1;
Sat2.ExtendedMassPropertiesModel = 'None';
Sat2.NAIFId = -10001001;
Sat2.NAIFIdReferenceFrame = -9001001;
Sat2.OrbitColor = Green;
Sat2.TargetColor = LightGray;
Sat2.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
Sat2.CdSigma = 1e+70;
Sat2.CrSigma = 1e+70;
Sat2.Id = 'SatId';
Sat2.Attitude = CoordinateSystemFixed;
Sat2.SPADSRPInterpolationMethod = Bilinear;
Sat2.SPADSRPScaleFactorSigma = 1e+70;
Sat2.SPADDragInterpolationMethod = Bilinear;
Sat2.SPADDragScaleFactorSigma = 1e+70;
Sat2.AtmosDensityScaleFactorSigma = 1e+70;
Sat2.ModelFile = 'aura.3ds';
Sat2.ModelOffsetX = 0;
Sat2.ModelOffsetY = 0;
Sat2.ModelOffsetZ = 0;
Sat2.ModelRotationX = 0;
Sat2.ModelRotationY = 0;
Sat2.ModelRotationZ = 0;
Sat2.ModelScale = 1;
Sat2.AttitudeDisplayStateType = 'Quaternion';
Sat2.AttitudeRateDisplayStateType = 'AngularVelocity';
Sat2.AttitudeCoordinateSystem = EarthMJ2000Eq;
Sat2.EulerAngleSequence = '321';

%----------------------------------------
%---------- GroundStations
%----------------------------------------

% Earth Ground Stations
Create GroundStation Houston;
Houston.OrbitColor = Thistle;
Houston.TargetColor = DarkGray;

Houston.CentralBody = Earth;
Houston.StateType = Spherical;
Houston.HorizonReference = Sphere;
Houston.Location1 = 29.76;
Houston.Location2 = 264.64;
Houston.Location3 = 0;
Houston.Id = 'StationId';
Houston.IonosphereModel = 'None';
Houston.TroposphereModel = 'None';
Houston.DataSource = 'Constant';
Houston.Temperature = 295.1;
Houston.Pressure = 1013.5;
Houston.Humidity = 55;
Houston.MinimumElevationAngle = 7;

%----------------------------------------
%---------- GroundStations
%----------------------------------------

% Earth Ground Stations
Create GroundStation Sydney;
Sydney.OrbitColor = Thistle;
Sydney.TargetColor = DarkGray;

Sydney.CentralBody = Earth;
Sydney.StateType = Spherical;
Sydney.HorizonReference = Sphere;
Sydney.Location1 = -33.87;
Sydney.Location2 = 151.21;
Sydney.Location3 = 0;
Sydney.Id = 'StationId';
Sydney.IonosphereModel = 'None';
Sydney.TroposphereModel = 'None';
Sydney.DataSource = 'Constant';
Sydney.Temperature = 295.1;
Sydney.Pressure = 1013.5;
Sydney.Humidity = 55;
Sydney.MinimumElevationAngle = 7;

%----------------------------------------
%---------- GroundStations
%----------------------------------------

% Earth Ground Stations
Create GroundStation Ireland;
Ireland.OrbitColor = Thistle;
Ireland.TargetColor = DarkGray;

Ireland.CentralBody = Earth;
Ireland.StateType = Spherical;
Ireland.HorizonReference = Sphere;
Ireland.Location1 = 53;
Ireland.Location2 = 352;
Ireland.Location3 = 0;
Ireland.Id = 'StationId';
Ireland.IonosphereModel = 'None';
Ireland.TroposphereModel = 'None';
Ireland.DataSource = 'Constant';
Ireland.Temperature = 295.1;
Ireland.Pressure = 1013.5;
Ireland.Humidity = 55;
Ireland.MinimumElevationAngle = 7;

% Lunar South Pole Ground Station
Create GroundStation ShackletonCrater;
ShackletonCrater.OrbitColor = Orange;
ShackletonCrater.TargetColor = DarkGray;
ShackletonCrater.CentralBody = Luna;
ShackletonCrater.StateType = Spherical;
ShackletonCrater.HorizonReference = Sphere;
ShackletonCrater.Location1 = -90; % South Pole
ShackletonCrater.Location2 = 0;
ShackletonCrater.Location3 = 0;
ShackletonCrater.Id = 'StationId';
ShackletonCrater.IonosphereModel = 'None';
ShackletonCrater.TroposphereModel = 'None';
ShackletonCrater.DataSource = 'Constant';
ShackletonCrater.Temperature = 295.1;
ShackletonCrater.Pressure = 1013.5;
ShackletonCrater.Humidity = 55;
ShackletonCrater.MinimumElevationAngle = 7;


%----------------------------------------
%---------- ForceModels & Propagators
%----------------------------------------

Create ForceModel LunarProp_ForceModel;
LunarProp_ForceModel.CentralBody = Luna;
LunarProp_ForceModel.PrimaryBodies = {Luna};
LunarProp_ForceModel.PointMasses = {Earth, Sun};
LunarProp_ForceModel.Drag = None;
LunarProp_ForceModel.SRP = Off;
LunarProp_ForceModel.RelativisticCorrection = Off;
LunarProp_ForceModel.ErrorControl = RSSStep;
LunarProp_ForceModel.GravityField.Luna.Degree = 8;
LunarProp_ForceModel.GravityField.Luna.Order = 8;
LunarProp_ForceModel.GravityField.Luna.StmLimit = 100;
LunarProp_ForceModel.GravityField.Luna.PotentialFile = 'LP165P.cof';
LunarProp_ForceModel.GravityField.Luna.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LunarProp;
LunarProp.FM = LunarProp_ForceModel;
LunarProp.Type = RungeKutta89;
LunarProp.InitialStepSize = 60;
LunarProp.Accuracy = 9.999999999999999e-12;
LunarProp.MinStep = 0.001;
LunarProp.MaxStep = 2700;
LunarProp.MaxStepAttempts = 50;
LunarProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
MoonMJ2000Eq.Origin = Luna;
MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem BarycenterMJ2000Eq;
BarycenterMJ2000Eq.Origin = EarthMoonBarycenter;
BarycenterMJ2000Eq.Axes = MJ2000Eq;

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

Create ContactLocator Sat1_To_Earth;

% Contacts from Sat1 to Earth Stations
Sat1_To_Earth.Target = Sat1;
Sat1_To_Earth.Filename = '../output/Sat1_To_Earth_Contacts.txt';
Sat1_To_Earth.OccultingBodies = {Luna};
Sat1_To_Earth.InputEpochFormat = 'TAIModJulian';
Sat1_To_Earth.InitialEpoch = '21545';
Sat1_To_Earth.StepSize = 10;
Sat1_To_Earth.FinalEpoch = '21545.138';
Sat1_To_Earth.UseLightTimeDelay = true;
Sat1_To_Earth.UseStellarAberration = true;
Sat1_To_Earth.WriteReport = true;
Sat1_To_Earth.RunMode = Automatic;
Sat1_To_Earth.UseEntireInterval = true;
Sat1_To_Earth.Observers = {Houston, Sydney, Ireland};
Sat1_To_Earth.LightTimeDirection = Transmit;
Sat1_To_Earth.LeftJustified = false;
Sat1_To_Earth.ReportPrecision = 6;
Sat1_To_Earth.ReportFormat = 'Legacy';
Sat1_To_Earth.IntervalStepSize = 0;
Sat1_To_Earth.ReportTimeFormat = 'UTCGregorian';

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

Create ContactLocator Sat2_To_Earth;

% Contacts from Sat2 to Earth Stations
Sat2_To_Earth.Target = Sat2;
Sat2_To_Earth.Filename = '../output/Sat2_To_Earth_Contacts.txt';
Sat2_To_Earth.OccultingBodies = {Luna};
Sat2_To_Earth.InputEpochFormat = 'TAIModJulian';
Sat2_To_Earth.InitialEpoch = '21545';
Sat2_To_Earth.StepSize = 10;
Sat2_To_Earth.FinalEpoch = '21545.138';
Sat2_To_Earth.UseLightTimeDelay = true;
Sat2_To_Earth.UseStellarAberration = true;
Sat2_To_Earth.WriteReport = true;
Sat2_To_Earth.RunMode = Automatic;
Sat2_To_Earth.UseEntireInterval = true;
Sat2_To_Earth.Observers = {Houston, Sydney, Ireland};
Sat2_To_Earth.LightTimeDirection = Transmit;
Sat2_To_Earth.LeftJustified = false;
Sat2_To_Earth.ReportPrecision = 6;
Sat2_To_Earth.ReportFormat = 'Legacy';
Sat2_To_Earth.IntervalStepSize = 0;
Sat2_To_Earth.ReportTimeFormat = 'UTCGregorian';

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

Create ContactLocator Shackleton_To_Sat1;

% Contacts from Shackleton Crater to Sat1
Shackleton_To_Sat1.Target = Sat1;
Shackleton_To_Sat1.Filename = '../output/Shackleton_To_Sat1_Contacts.txt';
Shackleton_To_Sat1.InputEpochFormat = 'TAIModJulian';
Shackleton_To_Sat1.InitialEpoch = '21545';
Shackleton_To_Sat1.StepSize = 10;
Shackleton_To_Sat1.FinalEpoch = '21545.138';
Shackleton_To_Sat1.UseLightTimeDelay = true;
Shackleton_To_Sat1.UseStellarAberration = true;
Shackleton_To_Sat1.WriteReport = true;
Shackleton_To_Sat1.RunMode = Automatic;
Shackleton_To_Sat1.UseEntireInterval = true;
Shackleton_To_Sat1.Observers = {ShackletonCrater};
Shackleton_To_Sat1.LightTimeDirection = Transmit;
Shackleton_To_Sat1.LeftJustified = false;
Shackleton_To_Sat1.ReportPrecision = 6;
Shackleton_To_Sat1.ReportFormat = 'Legacy';
Shackleton_To_Sat1.IntervalStepSize = 0;
Shackleton_To_Sat1.ReportTimeFormat = 'UTCGregorian';

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

Create ContactLocator Shackleton_To_Sat2;

% Contacts from Shackleton Crater to Sat2
Shackleton_To_Sat2.Target = Sat2;
Shackleton_To_Sat2.Filename = '../output/Shackleton_To_Sat2_Contacts.txt';
Shackleton_To_Sat2.InputEpochFormat = 'TAIModJulian';
Shackleton_To_Sat2.InitialEpoch = '21545';
Shackleton_To_Sat2.StepSize = 10;
Shackleton_To_Sat2.FinalEpoch = '21545.138';
Shackleton_To_Sat2.UseLightTimeDelay = true;
Shackleton_To_Sat2.UseStellarAberration = true;
Shackleton_To_Sat2.WriteReport = true;
Shackleton_To_Sat2.RunMode = Automatic;
Shackleton_To_Sat2.UseEntireInterval = true;
Shackleton_To_Sat2.Observers = {ShackletonCrater};
Shackleton_To_Sat2.LightTimeDirection = Transmit;
Shackleton_To_Sat2.LeftJustified = false;
Shackleton_To_Sat2.ReportPrecision = 6;
Shackleton_To_Sat2.ReportFormat = 'Legacy';
Shackleton_To_Sat2.IntervalStepSize = 0;
Shackleton_To_Sat2.ReportTimeFormat = 'UTCGregorian';

%----------------------------------------
%---------- Subscribers (Visualizers)
%----------------------------------------

% A Moon-centered view to see the satellite orbits clearly
Create OrbitView MoonView;
MoonView.SolverIterations = Current;
MoonView.UpperLeft = [ 0 0 ];
MoonView.Size = [ 0.5 1 ];
MoonView.RelativeZOrder = 0;
MoonView.Maximized = false;
MoonView.Add = {Sat1, Sat2, ShackletonCrater, Luna, Earth};
MoonView.CoordinateSystem = MoonMJ2000Eq;
MoonView.DrawObject = [ true true true true true ];
MoonView.DataCollectFrequency = 1;
MoonView.UpdatePlotFrequency = 50;
MoonView.NumPointsToRedraw = 0;
MoonView.ShowPlot = true;
MoonView.MaxPlotPoints = 20000;
MoonView.ShowLabels = true;
MoonView.ViewPointReference = Luna;
MoonView.ViewPointVector = [ 0 40000 0 ]; % View from the side
MoonView.ViewDirection = Luna;
MoonView.ViewScaleFactor = 1;
MoonView.ViewUpCoordinateSystem = EarthMJ2000Eq;
MoonView.ViewUpAxis = Z;
MoonView.EclipticPlane = Off;
MoonView.XYPlane = On;
MoonView.WireFrame = Off;
MoonView.Axes = On;
MoonView.Grid = Off;
MoonView.SunLine = Off;
MoonView.UseInitialView = On;
MoonView.StarCount = 7000;
MoonView.EnableStars = On;
MoonView.EnableConstellations = On;

% A view centered on the Earth-Moon barycenter to see the whole system
Create OrbitView BarycenterView;
BarycenterView.SolverIterations = Current;
BarycenterView.UpperLeft = [ 0.5 0 ];
BarycenterView.Size = [ 0.5 1 ];
BarycenterView.RelativeZOrder = 0;
BarycenterView.Maximized = false;
BarycenterView.Add = {Sat1, Sat2, Earth, Luna, EarthMoonBarycenter};
BarycenterView.CoordinateSystem = BarycenterMJ2000Eq;
BarycenterView.DrawObject = [ true true true true true ];
BarycenterView.DataCollectFrequency = 1;
BarycenterView.UpdatePlotFrequency = 50;
BarycenterView.NumPointsToRedraw = 0;
BarycenterView.ShowPlot = true;
BarycenterView.MaxPlotPoints = 20000;
BarycenterView.ShowLabels = true;
BarycenterView.ViewPointReference = EarthMoonBarycenter;
BarycenterView.ViewPointVector = [ 0 0 600000 ]; % Top-down view
BarycenterView.ViewDirection = EarthMoonBarycenter;
BarycenterView.ViewScaleFactor = 1;
BarycenterView.ViewUpCoordinateSystem = EarthMJ2000Eq;
BarycenterView.ViewUpAxis = Y;
BarycenterView.EclipticPlane = Off;
BarycenterView.XYPlane = On;
BarycenterView.WireFrame = Off;
BarycenterView.Axes = On;
BarycenterView.Grid = Off;
BarycenterView.SunLine = Off;
BarycenterView.UseInitialView = On;
BarycenterView.StarCount = 7000;
BarycenterView.EnableStars = On;
BarycenterView.EnableConstellations = On;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate Synchronized LunarProp(Sat1, Sat2) {Sat1.ElapsedDays = 1.0};
