#!/usr/bin/env python3
"""
Test if we can import all required packages
"""

import sys
from pathlib import Path

print("Testing imports...")

# Test basic Python packages
try:
    import matplotlib.pyplot as plt
    import matplotlib.animation as animation
    import numpy as np
    print("✓ matplotlib and numpy imported successfully")
except ImportError as e:
    print(f"✗ Error importing matplotlib/numpy: {e}")

try:
    import networkx as nx
    print("✓ networkx imported successfully")
except ImportError as e:
    print(f"✗ Error importing networkx: {e}")

# Test GMAT API
GmatInstall = "/home/<USER>/Downloads/gmat-ubuntu-x64-R2025a/GMAT/R2025a"
GmatBinPath = GmatInstall + "/bin"
apistartup = "api_startup_file.txt"
Startup = GmatBinPath + "/" + apistartup

if Path(Startup).exists():
    print(f"✓ GMAT startup file found: {Startup}")
    
    try:
        sys.path.insert(1, GmatBinPath)
        import gmatpy as gmat
        print("✓ gmatpy imported successfully")
        
        # Try to setup GMAT
        gmat.Setup(Startup)
        print("✓ GMAT setup completed successfully")
        
    except Exception as e:
        print(f"✗ Error with GMAT: {e}")
else:
    print(f"✗ GMAT startup file not found: {Startup}")

# Test our contact parser
try:
    sys.path.append('.')
    from process_contact import ContactReport
    print("✓ process_contact module imported successfully")
except ImportError as e:
    print(f"✗ Error importing process_contact: {e}")

# Test if contact files exist
output_dir = Path('./output')
eva1_file = output_dir / 'EVA1Contacts.txt'
eva2_file = output_dir / 'EVA2Contacts.txt'

if eva1_file.exists():
    print(f"✓ EVA1 contact file found: {eva1_file}")
else:
    print(f"✗ EVA1 contact file not found: {eva1_file}")

if eva2_file.exists():
    print(f"✓ EVA2 contact file found: {eva2_file}")
else:
    print(f"✗ EVA2 contact file not found: {eva2_file}")

print("\nImport test completed!")
