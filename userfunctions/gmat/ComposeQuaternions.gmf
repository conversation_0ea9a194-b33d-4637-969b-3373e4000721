
%----------------------------------------
%               ComposeQuaternions.gmf             |
%----------------------------------------

function [q] = ComposeQuaternions (qA, qB)
%
% Author: <PERSON>, GSFC Code 583
%
% function does quaternion multiplication to compose rotations
% equivalent to q = qA * qB in mathematical notation

Create Array q[4] qA[4] qB[4]

BeginMissionSequence

% no need to create matrix from qA, just write out the equations
% for each element of the result

q(1) =  qA(4)*qB(1) + qA(3)*qB(2) - qA(2)*qB(3) + qA(1)*qB(4)
q(2) = -qA(3)*qB(1) + qA(4)*qB(2) + qA(1)*qB(3) + qA(2)*qB(4)
q(3) =  qA(2)*qB(1) - qA(1)*qB(2) + qA(4)*qB(3) + qA(3)*qB(4)
q(4) = -qA(1)*qB(1) - qA(2)*qB(2) - qA(3)*qB(3) + qA(4)*qB(4)
