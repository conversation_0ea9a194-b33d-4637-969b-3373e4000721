
%----------------------------------------
%           ToggleQuaternion.gmf        |
%----------------------------------------

function [qNew] = ToggleQuaternion(q)
%
% Author: <PERSON>, GSFC Code 583
%
% negates each element of a quaternion; the computed value represents the
% same rotation as the original. One of the uses of this is to assure that
% q4, the scalar part of the quaternion, is always a positive value.

Create Array q[4] qNew[4]

BeginMissionSequence

qNew(1) = -q(1)
qNew(2) = -q(2)
qNew(3) = -q(3)
qNew(4) = -q(4)
