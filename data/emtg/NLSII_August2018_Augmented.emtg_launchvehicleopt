#EMTG launch vehicle library (space delimited)
#tokens are:
#name ModelType DLA_lowerbound(degrees) DLA_upperbound(degrees) C3_lowerbound C3_upperbound coefficient[0] coefficient[1] ...
#ModelType = 0 is the standard polynomial curve, with the ith coefficient corresponding to the ith power of C3
#Additional ModelTypes may be implemented later
#
Atlas_V_401                      0 -28.5 28.5 0 60    0.0 3034.669922054189 -64.48021028098837 0.3574690697626631 -0.001436080419863095 2.5304020641252383e-05 -1.549419386562148e-07
TestConfig1               0  28.5 28.5 0.5 0.5 0.0 3034.66992205    -64.480210281      0.357469069763   -0.00143608041986   2.53040206412e-05 -1.54941938656e-07
TestConfig2               0  28.5 -28.5 0.5 20.5 0.0 3034.66992205    -64.480210281      0.357469069763   -0.00143608041986   2.53040206412e-05 -1.54941938656e-07
#EndHardwareBlock
