[MAIN]
; Sort properties = true to sort by property name (not property label)
;Sort Properties=True

;[Tank Properties]
;Type=Vertical
;Label=Tank Properties

[Fuel Properties]
Type=Vertical
Label=Properties
;Position Before=Tank Properties

[FuelMass]
Hint=The total mass of fuel available in the fuel tank.\nCaution: By default, GMAT will not allow the fuel mass to be negative. However, occasionally \nin iterative processes such as targeting, a solver will try values of a maneuver parameter \nthat result in total fuel depletion. Using the default tank settings this will throw an \nexcpeption stopping the run unless you set the AllowNegativeFuelMass flag to true.
Label=F&uel Mass
Parent=Fuel Properties

[AllowNegativeFuelMass]
Parent=Fuel Properties
Label=Allow &Negative Fuel Mass
; Position Before=FuelDensity
Hint=This field allows the fuel tank to have negagive fuel mass which can be useful in \noptimization and targeting sequences before convergences has occurred.

