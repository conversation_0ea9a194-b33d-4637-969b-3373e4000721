[MAIN]
; Sort properties = true to sort by property name (not property label)
;Sort Properties=True

[Tank Properties]
Type=Vertical
Label=Tank Properties

[Fuel Properties]
Type=Vertical
Label=Fuel Properties
Position Before=Tank Properties

[FuelMass]
Hint=The total mass of fuel available in the fuel tank.\nCaution: By default, GMAT will not allow the fuel mass to be negative. However, occasionally \nin iterative processes such as targeting, a solver will try values of a maneuver parameter \nthat result in total fuel depletion. Using the default tank settings this will throw an \nexcpeption stopping the run unless you set the AllowNegativeFuelMass flag to true.
Label=F&uel Mass
Parent=Fuel Properties

[FuelDensity]
Label=Fuel Density
Parent=Fuel Properties
Position Before=Temperature
Hint=The density of the fuel.

[Temperature]
Parent=Fuel Properties
Hint=The temperature of the fuel and ullage in the tank. \nGMAT currently assumes ullage and fuel are always at the same temperature.

[RefTemperature]
Label=Reference Temperature
Parent=Fuel Properties
Hint=The temperature of the tank when fuel was loaded.

[AllowNegativeFuelMass]
Parent=Fuel Properties
Label=Allow &Negative Fuel Mass
Position Before=FuelDensity
Hint=This field allows the fuel tank to have negagive fuel mass which can be useful in \noptimization and targeting sequences before convergences has occurred.

[Pressure]
Parent=Fuel Properties
Position Before=
Hint=The pressure in the tank

[Volume]
Parent=Tank Properties
Hint=The volume of the tank. GMAT checks to ensure that the volume of the tank is larger \nthan the volume of fuel loaded in the tank and throws an exception in the case that \nthe fuel volume is larger than the tank volume.

[PressureModel]
Parent=Tank Properties
Hint=The pressure model describes how pressure in the tank changes as fuel is depleted.


