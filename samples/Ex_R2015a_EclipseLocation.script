
%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft GEOSat;
GMAT GEOSat.NAIFId = -899;
GMAT GEOSat.NAIFIdReferenceFrame = -8990;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create <PERSON><PERSON><PERSON><PERSON> DefaultProp_ForceModel;
Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;

%----------------------------------------
%---------- EventLocators
%----------------------------------------

Create EclipseLocator EclipseLocator1;
GMAT EclipseLocator1.Spacecraft = GEOSat;
GMAT EclipseLocator1.Filename = 'EclipseLocator1.txt';
GMAT EclipseLocator1.OccultingBodies = {Earth, Luna, Mercury, Venus};
GMAT EclipseLocator1.InputEpochFormat = 'TAIModJulian';
GMAT EclipseLocator1.InitialEpoch = '21545';
GMAT EclipseLocator1.StepSize = 120;
GMAT EclipseLocator1.FinalEpoch = '21545.138';
GMAT EclipseLocator1.UseLightTimeDelay = true;
GMAT EclipseLocator1.UseStellarAberration = true;
GMAT EclipseLocator1.WriteReport = true;
GMAT EclipseLocator1.RunMode = Automatic;
GMAT EclipseLocator1.UseEntireInterval = true;
GMAT EclipseLocator1.EclipseTypes = {'Umbra', 'Penumbra', 'Antumbra'};

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = All;
GMAT DefaultOrbitView.UpperLeft = [ 0 0.1343283582089552 ];
GMAT DefaultOrbitView.Size = [ 0.4988235294117647 0.605543710021322 ];
GMAT DefaultOrbitView.RelativeZOrder = 106;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {GEOSat, Earth, Sun};
GMAT DefaultOrbitView.CoordinateSystem = EarthFixed;
GMAT DefaultOrbitView.DrawObject = [ true true true ];
GMAT DefaultOrbitView.ViewPointReference = GEOSat;
GMAT DefaultOrbitView.ViewPointVector = [ 0 -30000 0 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 2;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthFixed;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EnableConstellations = Off;

Create GroundTrackPlot DefaultGroundTrackPlot;
GMAT DefaultGroundTrackPlot.SolverIterations = Current;
GMAT DefaultGroundTrackPlot.UpperLeft = [ 0.4982352941176471 0.1332622601279318 ];
GMAT DefaultGroundTrackPlot.Size = [ 0.4988235294117647 0.6066098081023454 ];
GMAT DefaultGroundTrackPlot.RelativeZOrder = 90;
GMAT DefaultGroundTrackPlot.Maximized = false;
GMAT DefaultGroundTrackPlot.Add = {GEOSat};
GMAT DefaultGroundTrackPlot.DataCollectFrequency = 1;
GMAT DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
GMAT DefaultGroundTrackPlot.NumPointsToRedraw = 0;
GMAT DefaultGroundTrackPlot.ShowPlot = true;
GMAT DefaultGroundTrackPlot.CentralBody = Earth;
GMAT DefaultGroundTrackPlot.TextureMap = '../data/graphics/texture/ModifiedBlueMarble.jpg';

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

%  Set up constants, spacecraft config, etc.
BeginScript 'Config and Init'
   GMAT GEOSat.DateFormat = UTCGregorian;
   GMAT GEOSat.Epoch = '22 Aug 2015 00:00:00.000';
   GMAT GEOSat.SMA = 42165.52841001855;
   GMAT GEOSat.ECC = 0.0001016372703183695;
   GMAT GEOSat.INC = 28.22038171126684;
   GMAT GEOSat.RAAN = 152.4975046294811;
   GMAT GEOSat.AOP = 172.7618673633059;
   GMAT GEOSat.TA = 242.5561749993354;
EndScript;

Propagate DefaultProp(GEOSat) {GEOSat.ElapsedDays = 10};












