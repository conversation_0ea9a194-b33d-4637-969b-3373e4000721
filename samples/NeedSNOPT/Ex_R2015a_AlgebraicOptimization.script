%-----------------------------------------------------------------
%-----------------Create and Setup the Optimizer------------------
%-----------------------------------------------------------------
Create SNOPT NLP;
GMAT NLP.ShowProgress = true;
GMAT NLP.ReportStyle = Normal;
GMAT NLP.ReportFile = output.report;
GMAT NLP.MajorOptimalityTolerance = 0.001234;
GMAT NLP.MajorFeasibilityTolerance = 0.0004567;
GMAT NLP.MajorIterationsLimit = 456;
GMAT NLP.TotalIterationsLimit = 789012;
GMAT NLP.OutputFileName = 'SNOPTName123.out';
GMAT NLP.SpecsFileName = 'SNOPT.spec';
GMAT NLP.OverrideSpecsFileValues = true;

%-----------------------------------------------------------------
%------------------------------OutPut-----------------------------
%-----------------------------------------------------------------
Create ReportFile Data
Data.WriteHeaders = false;
Data.Filename = Ex_AlgebraicOptimization.report

%-----------------------------------------------------------------
%-------------------Arrays, Variables, Strings--------------------
%-----------------------------------------------------------------
Create Variable X1 X2 J G;

%-----------------------------------------------------------------
%---------------------------Mission Sequence----------------------
%-----------------------------------------------------------------

BeginMissionSequence;

Optimize NLP {SolveMode = Solve, ExitMode = DiscardAndContinue};
   
   %  Vary the independent variables
   Vary 'Vary X1' NLP(X1 = 0, {Perturbation = 0.0000001, Upper = 10, Lower = -10, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary X2' NLP(X2 = 0, {Perturbation = 0.0000001, Upper = 10, Lower = -10, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   %  The cost function and Minimize command
   GMAT 'Compute Cost (J)' J = ( X1 - 2 )^2 + ( X2 - 2 )^2;
   Minimize 'Minimize Cost (J)' NLP(J);
   
   %  Calculate constraint and use NonLinearConstraint command
   %   ( yes, the equation below is actually a linear constraint, were 
   %     testing functionality with this test)
   %  The constraint is to require the solution to lie above the line defined by X2 = -X1 + 6
   %  or X2 >= -X1 + 6;
   GMAT 'Compute Constraint (G)' G = X2 + X1;
   NonlinearConstraint 'G = 8' NLP(G<=8);

EndOptimize;  % For optimizer NLP

Report 'Report Solution' Data X1 X2 J G;



