%  Script Mission - Lunar Transfer Example
%
%  This script demonstrates how to set up a lunar transfer mission



%----------------------------------------
%---------- Spacecraft
%----------------------------------------

%**************************************************************************
%************ Create Objects for Use in Mission Sequence ******************
%**************************************************************************


%--------------------------------------------------------------------------
%----------------- SpaceCraft, Formations, Constellations -----------------
%--------------------------------------------------------------------------


Create Spacecraft MMSRef;
GMAT MMSRef.DateFormat = UTCGregorian;
GMAT MMSRef.Epoch = '22 Jul 2014 11:29:10.811';
GMAT MMSRef.CoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.DisplayStateType = Cartesian;
GMAT MMSRef.X = -137380.1984338506;
GMAT MMSRef.Y = 75679.87867537055;
GMAT MMSRef.Z = 21487.63875187856;
GMAT MMSRef.VX = -0.2324532014235503;
GMAT MMSRef.VY = -0.4462753967758019;
GMAT MMSRef.VZ = 0.08561205662877103;
GMAT MMSRef.DryMass = 1000;
GMAT MMSRef.Cd = 2.2;
GMAT MMSRef.Cr = 1.7;
GMAT MMSRef.DragArea = 15;
GMAT MMSRef.SRPArea = 1;
GMAT MMSRef.NAIFId = -123456789;
GMAT MMSRef.NAIFIdReferenceFrame = -123456789;
GMAT MMSRef.Id = 'SatId';
GMAT MMSRef.Attitude = CoordinateSystemFixed;
GMAT MMSRef.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT MMSRef.ModelOffsetX = 1;
GMAT MMSRef.ModelOffsetY = 0;
GMAT MMSRef.ModelOffsetZ = 0;
GMAT MMSRef.ModelRotationX = 0;
GMAT MMSRef.ModelRotationY = 0;
GMAT MMSRef.ModelRotationZ = 0;
GMAT MMSRef.ModelScale = 0.5;
GMAT MMSRef.AttitudeDisplayStateType = 'Quaternion';
GMAT MMSRef.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT MMSRef.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

%----------------------------------------
%---------- ForceModels
%----------------------------------------

%--------------------------------------------------------------------------
%------------------------------ Propagators -------------------------------
%--------------------------------------------------------------------------

Create ForceModel LunarSB_ForceModel;
GMAT LunarSB_ForceModel.CentralBody = Earth;
GMAT LunarSB_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT LunarSB_ForceModel.Drag = None;
GMAT LunarSB_ForceModel.SRP = Off;
GMAT LunarSB_ForceModel.RelativisticCorrection = Off;
GMAT LunarSB_ForceModel.ErrorControl = RSSStep;

Create ForceModel MoonCentered_ForceModel;
GMAT MoonCentered_ForceModel.CentralBody = Luna;
GMAT MoonCentered_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT MoonCentered_ForceModel.Drag = None;
GMAT MoonCentered_ForceModel.SRP = Off;
GMAT MoonCentered_ForceModel.RelativisticCorrection = Off;
GMAT MoonCentered_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LunarSB;
GMAT LunarSB.FM = LunarSB_ForceModel;
GMAT LunarSB.Type = RungeKutta89;
GMAT LunarSB.InitialStepSize = 60;
GMAT LunarSB.Accuracy = 9.999999999999999e-012;
GMAT LunarSB.MinStep = 0.001;
GMAT LunarSB.MaxStep = 45000;
GMAT LunarSB.MaxStepAttempts = 50;
GMAT LunarSB.StopIfAccuracyIsViolated = true;

Create Propagator MoonCentered;
GMAT MoonCentered.FM = MoonCentered_ForceModel;
GMAT MoonCentered.Type = RungeKutta89;
GMAT MoonCentered.InitialStepSize = 60;
GMAT MoonCentered.Accuracy = 9.999999999999999e-012;
GMAT MoonCentered.MinStep = 0.001;
GMAT MoonCentered.MaxStep = 15000;
GMAT MoonCentered.MaxStepAttempts = 50;
GMAT MoonCentered.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%--------------------------------------------------------------------------
%--------------------------------- Burns ----------------------------------
%--------------------------------------------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.14676929889;
GMAT TOI.Element2 = 0.046042675892;
GMAT TOI.Element3 = 0.090223244097;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LOI;
GMAT LOI.CoordinateSystem = Local;
GMAT LOI.Origin = Luna;
GMAT LOI.Axes = VNB;
GMAT LOI.Element1 = -0.652;
GMAT LOI.Element2 = 0;
GMAT LOI.Element3 = 0;
GMAT LOI.DecrementMass = false;
GMAT LOI.Isp = 300;
GMAT LOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable Cost;



%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

%--------------------------------------------------------------------------
%-------------------------- Coordinate Systems ----------------------------
%--------------------------------------------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

%----------------------------------------
%---------- Solvers
%----------------------------------------

%--------------------------------------------------------------------------
%-------------------------------- Solvers ---------------------------------
%--------------------------------------------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 25;
GMAT DC1.DerivativeMethod = CentralDifference;

% Create VF13ad SQP1;
% GMAT SQP1.ShowProgress = true;
% GMAT SQP1.ReportStyle = Normal;
% GMAT SQP1.ReportFile = 'VF13adVF13ad1.data';
% GMAT SQP1.MaximumIterations = 200;
% GMAT SQP1.Tolerance = 1e-005;
% GMAT SQP1.UseCentralDifferences = false;
% GMAT SQP1.FeasibilityTolerance = 1;

Create SNOPT SQP1;
GMAT SQP1.ShowProgress = true;
GMAT SQP1.ReportStyle = Normal;
GMAT SQP1.ReportFile = 'SNOPTSNOPT1.data';
GMAT SQP1.MajorOptimalityTolerance = 1e-5;
GMAT SQP1.MajorFeasibilityTolerance = 1e-005;
GMAT SQP1.MajorIterationsLimit = 100;
GMAT SQP1.TotalIterationsLimit = 100000;
GMAT SQP1.OutputFileName = 'SNOPT.out';
GMAT SQP1.OverrideSpecsFileValues = true;



%----------------------------------------
%---------- Subscribers
%----------------------------------------


%--------------------------------------------------------------------------
%-------------------------- Plots and Reports -----------------------------
%--------------------------------------------------------------------------

Create OrbitView OGL_EarthMJ2K;
GMAT OGL_EarthMJ2K.SolverIterations = None;
GMAT OGL_EarthMJ2K.UpperLeft = [ 0.2803468208092486 0.03641456582633054 ];
GMAT OGL_EarthMJ2K.Size = [ 0.3554913294797688 0.453781512605042 ];
GMAT OGL_EarthMJ2K.RelativeZOrder = 1790;
GMAT OGL_EarthMJ2K.Add = {MMSRef, Earth, Luna};
GMAT OGL_EarthMJ2K.CoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.DrawObject = [ true true true ];
GMAT OGL_EarthMJ2K.DataCollectFrequency = 1;
GMAT OGL_EarthMJ2K.UpdatePlotFrequency = 50;
GMAT OGL_EarthMJ2K.NumPointsToRedraw = 0;
GMAT OGL_EarthMJ2K.ShowPlot = true;
GMAT OGL_EarthMJ2K.ViewPointReference = Earth;
GMAT OGL_EarthMJ2K.ViewPointVector = [ 500000 500000 500000 ];
GMAT OGL_EarthMJ2K.ViewDirection = Earth;
GMAT OGL_EarthMJ2K.ViewScaleFactor = 1;
GMAT OGL_EarthMJ2K.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.ViewUpAxis = Z;
GMAT OGL_EarthMJ2K.EclipticPlane = Off;
GMAT OGL_EarthMJ2K.XYPlane = Off;
GMAT OGL_EarthMJ2K.WireFrame = Off;
GMAT OGL_EarthMJ2K.Axes = On;
GMAT OGL_EarthMJ2K.Grid = Off;
GMAT OGL_EarthMJ2K.SunLine = Off;
GMAT OGL_EarthMJ2K.UseInitialView = On;
GMAT OGL_EarthMJ2K.StarCount = 3000;
GMAT OGL_EarthMJ2K.EnableStars = On;
GMAT OGL_EarthMJ2K.EnableConstellations = On;

Create OrbitView OGL_MoonMJ2K;
GMAT OGL_MoonMJ2K.SolverIterations = Current;
GMAT OGL_MoonMJ2K.UpperLeft = [ 0.2813102119460501 0.5028011204481793 ];
GMAT OGL_MoonMJ2K.Size = [ 0.3554913294797688 0.453781512605042 ];
GMAT OGL_MoonMJ2K.RelativeZOrder = 1801;
GMAT OGL_MoonMJ2K.Add = {MMSRef, Earth, Luna};
GMAT OGL_MoonMJ2K.CoordinateSystem = MoonMJ2000Eq;
GMAT OGL_MoonMJ2K.DrawObject = [ true true true ];
GMAT OGL_MoonMJ2K.DataCollectFrequency = 1;
GMAT OGL_MoonMJ2K.UpdatePlotFrequency = 50;
GMAT OGL_MoonMJ2K.NumPointsToRedraw = 0;
GMAT OGL_MoonMJ2K.ShowPlot = true;
GMAT OGL_MoonMJ2K.ViewPointReference = Luna;
GMAT OGL_MoonMJ2K.ViewPointVector = [ 0 0 30000 ];
GMAT OGL_MoonMJ2K.ViewDirection = Luna;
GMAT OGL_MoonMJ2K.ViewScaleFactor = 1;
GMAT OGL_MoonMJ2K.ViewUpCoordinateSystem = MoonMJ2000Eq;
GMAT OGL_MoonMJ2K.ViewUpAxis = X;
GMAT OGL_MoonMJ2K.EclipticPlane = Off;
GMAT OGL_MoonMJ2K.XYPlane = Off;
GMAT OGL_MoonMJ2K.WireFrame = Off;
GMAT OGL_MoonMJ2K.Axes = On;
GMAT OGL_MoonMJ2K.Grid = Off;
GMAT OGL_MoonMJ2K.SunLine = Off;
GMAT OGL_MoonMJ2K.UseInitialView = On;
GMAT OGL_MoonMJ2K.StarCount = 3000;
GMAT OGL_MoonMJ2K.EnableStars = On;
GMAT OGL_MoonMJ2K.EnableConstellations = On;

Create OrbitView OGL_EarthMoonRot;
GMAT OGL_EarthMoonRot.SolverIterations = Current;
GMAT OGL_EarthMoonRot.UpperLeft = [ 0.640655105973025 0.5014005602240896 ];
GMAT OGL_EarthMoonRot.Size = [ 0.3554913294797688 0.453781512605042 ];
GMAT OGL_EarthMoonRot.RelativeZOrder = 1795;
GMAT OGL_EarthMoonRot.Add = {MMSRef, Earth, Luna, Sun};
GMAT OGL_EarthMoonRot.CoordinateSystem = EarthMoonRot;
GMAT OGL_EarthMoonRot.DrawObject = [ true true true true ];
GMAT OGL_EarthMoonRot.DataCollectFrequency = 1;
GMAT OGL_EarthMoonRot.UpdatePlotFrequency = 50;
GMAT OGL_EarthMoonRot.NumPointsToRedraw = 0;
GMAT OGL_EarthMoonRot.ShowPlot = true;
GMAT OGL_EarthMoonRot.ViewPointReference = Luna;
GMAT OGL_EarthMoonRot.ViewPointVector = [ 30000 30000 30000 ];
GMAT OGL_EarthMoonRot.ViewDirection = Luna;
GMAT OGL_EarthMoonRot.ViewScaleFactor = 1;
GMAT OGL_EarthMoonRot.ViewUpCoordinateSystem = EarthMoonRot;
GMAT OGL_EarthMoonRot.ViewUpAxis = X;
GMAT OGL_EarthMoonRot.EclipticPlane = Off;
GMAT OGL_EarthMoonRot.XYPlane = Off;
GMAT OGL_EarthMoonRot.WireFrame = Off;
GMAT OGL_EarthMoonRot.Axes = On;
GMAT OGL_EarthMoonRot.Grid = Off;
GMAT OGL_EarthMoonRot.SunLine = On;
GMAT OGL_EarthMoonRot.UseInitialView = On;
GMAT OGL_EarthMoonRot.StarCount = 3000;
GMAT OGL_EarthMoonRot.EnableStars = On;
GMAT OGL_EarthMoonRot.EnableConstellations = On;

Create XYPlot RadApoPlot;
GMAT RadApoPlot.SolverIterations = None;
GMAT RadApoPlot.UpperLeft = [ 0.6377649325626205 0.04061624649859944 ];
GMAT RadApoPlot.Size = [ 0.3554913294797688 0.453781512605042 ];
GMAT RadApoPlot.RelativeZOrder = 1785;
GMAT RadApoPlot.XVariable = MMSRef.A1ModJulian;
GMAT RadApoPlot.YVariables = {MMSRef.RMAG};
GMAT RadApoPlot.ShowGrid = true;
GMAT RadApoPlot.ShowPlot = true;

Create ReportFile Residuals
Create ReportFile solData
solData.Filename = Ex_MinFuelLunarTransfer.report
solData.WriteHeaders = false;

Create Variable smaCon eccCon incCon count 

%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************
BeginMissionSequence;
Propagate 'Prop to Periapsis' LunarSB(MMSRef) {MMSRef.Periapsis};

Optimize 'Optimal Transfer' SQP1 {SolveMode = Solve, ExitMode = DiscardAndContinue};

Vary 'Vary TOI.V' SQP1(TOI.Element1 = 0.1562, {Perturbation = 1e-005, Lower = 0, Upper = 1, AdditiveScaleFactor = 1.0, MultiplicativeScaleFactor = 10.0});
   Vary 'Vary TOI.N' SQP1(TOI.Element2 = 0.056042, {Perturbation = 1e-005, Lower = 0, Upper = 1, AdditiveScaleFactor = 1.0, MultiplicativeScaleFactor = 10.0});
   Vary 'Vary TOI.B' SQP1(TOI.Element3 = 0.1386, {Perturbation = 1e-005, Lower = 0, Upper = 1, AdditiveScaleFactor = 1.0, MultiplicativeScaleFactor = 10.0});
   
	count = count + 1;
   Maneuver 'Apply TOI' TOI(MMSRef);
   
   Propagate 'Prop 1.5 Days' LunarSB(MMSRef) {MMSRef.ElapsedDays = 1.5};
   Propagate 'Prop to Periselene' MoonCentered(MMSRef) {MMSRef.Luna.Periapsis};
   
   Vary 'Vary LOI.V' SQP1(LOI.Element1 = -0.6, {Perturbation = 1e-003, Lower = -2, Upper = 2, AdditiveScaleFactor = 1.0, MultiplicativeScaleFactor = 10.0});
   
   Maneuver 'Apply LOI' LOI(MMSRef);
   GMAT 'Compute Total delta V' Cost = sqrt(TOI.Element1^2 +TOI.Element2^2 + TOI.Element3^2) + TOI.Element1;
   
	smaCon = (MMSRef.Luna.SMA - 2300)/1000;
	incCon = (MMSRef.MoonMJ2000Eq.INC - 65)/100;
	eccCon = (MMSRef.Luna.ECC-0.01)
	
   NonlinearConstraint 'SMA = 2300' SQP1(smaCon = 0);
   NonlinearConstraint 'Inc = 65'   SQP1(incCon = 0);
   NonlinearConstraint 'ECC = 0.01' SQP1(eccCon = 0);
   Minimize 'Minimize delta V' SQP1(Cost);
	
	Report Residuals count smaCon incCon eccCon 

EndOptimize;  % For targeter DC1

Report solData smaCon incCon eccCon Cost

Propagate 'Prop to Periselene' MoonCentered(MMSRef) {MMSRef.Luna.Periapsis};
Propagate 'Prop 1 Day' MoonCentered(MMSRef) {MMSRef.ElapsedDays = 1};
