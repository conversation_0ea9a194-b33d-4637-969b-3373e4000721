%General Mission Analysis Tool(GMAT) Script
%Created: 2014-08-20 15:24:59


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = TAIModJulian;
GMAT DefaultSC.Epoch = '21545';
GMAT DefaultSC.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.DisplayStateType = Cartesian;
GMAT DefaultSC.X = 6900;
GMAT DefaultSC.Y = 0;
GMAT DefaultSC.Z = 0;
GMAT DefaultSC.VX = 0;
GMAT DefaultSC.VY = 7.60053813407552;
GMAT DefaultSC.VZ = 0;
GMAT DefaultSC.DryMass = 850;
GMAT DefaultSC.Cd = 2.2;
GMAT DefaultSC.Cr = 1.8;
GMAT DefaultSC.DragArea = 15;
GMAT DefaultSC.SRPArea = 1;
GMAT DefaultSC.Tanks = {FuelTank1};
GMAT DefaultSC.Thrusters = {Thruster1};
GMAT DefaultSC.NAIFId = -123456789;
GMAT DefaultSC.NAIFIdReferenceFrame = -123456789;
GMAT DefaultSC.OrbitColor = Red;
GMAT DefaultSC.TargetColor = Teal;
GMAT DefaultSC.Id = 'SatId';
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.SPADSRPScaleFactor = 1;
GMAT DefaultSC.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT DefaultSC.ModelOffsetX = 0;
GMAT DefaultSC.ModelOffsetY = 0;
GMAT DefaultSC.ModelOffsetZ = 0;
GMAT DefaultSC.ModelRotationX = 0;
GMAT DefaultSC.ModelRotationY = 0;
GMAT DefaultSC.ModelRotationZ = 0;
GMAT DefaultSC.ModelScale = 3;
GMAT DefaultSC.AttitudeDisplayStateType = 'Quaternion';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.EulerAngleSequence = '321';

%----------------------------------------
%---------- Hardware Components
%----------------------------------------

Create ChemicalTank FuelTank1;
GMAT FuelTank1.AllowNegativeFuelMass = false;
GMAT FuelTank1.FuelMass = 400;
GMAT FuelTank1.Pressure = 1500;
GMAT FuelTank1.Temperature = 20;
GMAT FuelTank1.RefTemperature = 20;
GMAT FuelTank1.Volume = 0.75;
GMAT FuelTank1.FuelDensity = 1260;
GMAT FuelTank1.PressureModel = PressureRegulated;

Create ChemicalThruster Thruster1;
GMAT Thruster1.CoordinateSystem = Local;
GMAT Thruster1.Origin = Earth;
GMAT Thruster1.Axes = VNB;
GMAT Thruster1.ThrustDirection1 = 0.577350269189626;
GMAT Thruster1.ThrustDirection2 = 0.577350269189626;
GMAT Thruster1.ThrustDirection3 = 0.577350269189626;
GMAT Thruster1.DutyCycle = 1;
GMAT Thruster1.ThrustScaleFactor = 1;
GMAT Thruster1.DecrementMass = true;
GMAT Thruster1.Tank = {FuelTank1};
GMAT Thruster1.GravitationalAccel = 9.810000000000001;
GMAT Thruster1.C1 = 1000;
GMAT Thruster1.C2 = 0;
GMAT Thruster1.C3 = 0;
GMAT Thruster1.C4 = 0;
GMAT Thruster1.C5 = 0;
GMAT Thruster1.C6 = 0;
GMAT Thruster1.C7 = 0;
GMAT Thruster1.C8 = 0;
GMAT Thruster1.C9 = 0;
GMAT Thruster1.C10 = 0;
GMAT Thruster1.C11 = 0;
GMAT Thruster1.C12 = 0;
GMAT Thruster1.C13 = 0;
GMAT Thruster1.C14 = 0;
GMAT Thruster1.C15 = 0;
GMAT Thruster1.C16 = 0;
GMAT Thruster1.K1 = 300;
GMAT Thruster1.K2 = 0;
GMAT Thruster1.K3 = 0;
GMAT Thruster1.K4 = 0;
GMAT Thruster1.K5 = 0;
GMAT Thruster1.K6 = 0;
GMAT Thruster1.K7 = 0;
GMAT Thruster1.K8 = 0;
GMAT Thruster1.K9 = 0;
GMAT Thruster1.K10 = 0;
GMAT Thruster1.K11 = 0;
GMAT Thruster1.K12 = 0;
GMAT Thruster1.K13 = 0;
GMAT Thruster1.K14 = 0;
GMAT Thruster1.K15 = 0;
GMAT Thruster1.K16 = 0;
GMAT Thruster1.MixRatio = [ 1 ];






%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PointMasses = {Earth};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = RungeKutta89;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn DefaultIB;
GMAT DefaultIB.CoordinateSystem = Local;
GMAT DefaultIB.Origin = Earth;
GMAT DefaultIB.Axes = VNB;
GMAT DefaultIB.Element1 = 0;
GMAT DefaultIB.Element2 = 0;
GMAT DefaultIB.Element3 = 0;
GMAT DefaultIB.DecrementMass = false;
GMAT DefaultIB.Isp = 300;
GMAT DefaultIB.GravitationalAccel = 9.810000000000001;

Create FiniteBurn FiniteBurn1;
GMAT FiniteBurn1.Thrusters = {Thruster1};

%----------------------------------------
%---------- Solvers
%----------------------------------------

% Create VF13ad NLP;
% GMAT NLP.ShowProgress = true;
% GMAT NLP.ReportStyle = Normal;
% GMAT NLP.ReportFile = 'VF13adNLP.data';
% GMAT NLP.MaximumIterations = 200;
% GMAT NLP.Tolerance = 0.001;
% GMAT NLP.UseCentralDifferences = false;
% GMAT NLP.FeasibilityTolerance = 0.001;

Create SNOPT NLP;
GMAT NLP.ShowProgress = true;
GMAT NLP.ReportStyle = Normal;
GMAT NLP.ReportFile = 'SNOPTSNOPT1.data';



%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.2776470588235294 0.02206619859578736 ];
GMAT DefaultOrbitView.Size = [ 0.4382352941176471 0.5747241725175527 ];
GMAT DefaultOrbitView.RelativeZOrder = 81;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {DefaultSC, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ShowLabels = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 30000 0 0 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

Create GroundTrackPlot DefaultGroundTrackPlot;
GMAT DefaultGroundTrackPlot.SolverIterations = Current;
GMAT DefaultGroundTrackPlot.UpperLeft = [ 0.4188235294117647 0.2547642928786359 ];
GMAT DefaultGroundTrackPlot.Size = [ 0.4894117647058824 0.6599799398194584 ];
GMAT DefaultGroundTrackPlot.RelativeZOrder = 83;
GMAT DefaultGroundTrackPlot.Maximized = false;
GMAT DefaultGroundTrackPlot.Add = {DefaultSC};
GMAT DefaultGroundTrackPlot.DataCollectFrequency = 1;
GMAT DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
GMAT DefaultGroundTrackPlot.NumPointsToRedraw = 0;
GMAT DefaultGroundTrackPlot.ShowPlot = true;
GMAT DefaultGroundTrackPlot.CentralBody = Earth;
GMAT DefaultGroundTrackPlot.TextureMap = '../data/graphics/texture/ModifiedBlueMarble.jpg';

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable burn1Duration burn2Duration propDuration costFunction smaError;
GMAT burn1Duration = 300;
GMAT burn2Duration = 300;

Create ReportFile solData
solData.WriteHeaders = false
solData.Filename = Ex_OptFiniteBurn.report

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

%Optimize
% Vary NLP(burn1Duration = 300 ,{Perturbation = .01,MaxStep = 50})

Optimize NLP {SolveMode = Solve, ExitMode = DiscardAndContinue};

   %  Define the variables
   Vary NLP(burn1Duration = 230, {Perturbation = .01, Lower = 1, Upper = 10000, MultiplicativeScaleFactor = .1});
   Vary NLP(burn2Duration = 221, {Perturbation = .01,  Lower = 1, Upper = 10000, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = .1});
   Vary NLP(propDuration = 2706, {Perturbation = .01,  Lower = 100, Upper = 10000, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = .01});
   
	%  Perform the first maneuver
   BeginFiniteBurn FiniteBurn1(DefaultSC);
   GMAT DefaultSC.OrbitColor = 'Red';
   If burn1Duration > 0
      Propagate DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = burn1Duration};
   Else
      Propagate BackProp DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = burn1Duration};
   EndIf;
   EndFiniteBurn FiniteBurn1(DefaultSC);
	
   %  Propagate through the coast phase
   GMAT DefaultSC.OrbitColor = 'Green';
   If propDuration > 0
      Propagate DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = propDuration};
   Else
      Propagate BackProp DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = propDuration};
   EndIf;
   
	%  Perform the second maneuver
   BeginFiniteBurn FiniteBurn1(DefaultSC);
   GMAT DefaultSC.OrbitColor = 'Red';
   If burn2Duration > 0
      Propagate DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = burn2Duration};
   Else
      Propagate BackProp DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = burn2Duration};
   EndIf;
   EndFiniteBurn FiniteBurn1(DefaultSC);
   
	%  Apply the final
	smaError = (DefaultSC.SMA - 7100)/10000
   NonlinearConstraint NLP(smaError = 0);
   NonlinearConstraint NLP(DefaultSC.ECC=0.0);
   GMAT costFunction = -DefaultSC.TotalMass/1000;
   Minimize NLP(costFunction);

EndOptimize;  % For optimizer NLP
%EndOptimize



Report solData smaError DefaultSC.ECC costFunction
