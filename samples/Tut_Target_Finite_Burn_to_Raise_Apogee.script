%General Mission Analysis Tool(GMAT) Script
%Created: 2012-09-09 04:02:32


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = TAIModJulian;
GMAT DefaultSC.Epoch = '21545';
GMAT DefaultSC.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.DisplayStateType = Cartesian;
GMAT DefaultSC.X = 7100;
GMAT DefaultSC.Y = 0;
GMAT DefaultSC.Z = 1300;
GMAT DefaultSC.VX = 0;
GMAT DefaultSC.VY = 7.35;
GMAT DefaultSC.VZ = 1;
GMAT DefaultSC.DryMass = 850;
GMAT DefaultSC.Cd = 2.2;
GMAT DefaultSC.Cr = 1.8;
GMAT DefaultSC.DragArea = 15;
GMAT DefaultSC.SRPArea = 1;
GMAT DefaultSC.Tanks = {ChemicalTank1};
GMAT DefaultSC.Thrusters = {ChemicalThruster1};
GMAT DefaultSC.NAIFId = -123456789;
GMAT DefaultSC.NAIFIdReferenceFrame = -123456789;
GMAT DefaultSC.OrbitColor = Red;
GMAT DefaultSC.TargetColor = Teal;
GMAT DefaultSC.Id = 'SatId';
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.SPADSRPScaleFactor = 1;
GMAT DefaultSC.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT DefaultSC.ModelOffsetX = 0;
GMAT DefaultSC.ModelOffsetY = 0;
GMAT DefaultSC.ModelOffsetZ = 0;
GMAT DefaultSC.ModelRotationX = 0;
GMAT DefaultSC.ModelRotationY = 0;
GMAT DefaultSC.ModelRotationZ = 0;
GMAT DefaultSC.ModelScale = 1;
GMAT DefaultSC.AttitudeDisplayStateType = 'Quaternion';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.EulerAngleSequence = '321';

%----------------------------------------
%---------- Hardware Components
%----------------------------------------

Create ChemicalTank ChemicalTank1;
GMAT ChemicalTank1.AllowNegativeFuelMass = false;
GMAT ChemicalTank1.FuelMass = 756;
GMAT ChemicalTank1.Pressure = 1500;
GMAT ChemicalTank1.Temperature = 20;
GMAT ChemicalTank1.RefTemperature = 20;
GMAT ChemicalTank1.Volume = 0.75;
GMAT ChemicalTank1.FuelDensity = 1260;
GMAT ChemicalTank1.PressureModel = PressureRegulated;

Create ChemicalThruster ChemicalThruster1;
GMAT ChemicalThruster1.CoordinateSystem = Local;
GMAT ChemicalThruster1.Origin = Earth;
GMAT ChemicalThruster1.Axes = VNB;
GMAT ChemicalThruster1.ThrustDirection1 = 1;
GMAT ChemicalThruster1.ThrustDirection2 = 0;
GMAT ChemicalThruster1.ThrustDirection3 = 0;
GMAT ChemicalThruster1.DutyCycle = 1;
GMAT ChemicalThruster1.ThrustScaleFactor = 1;
GMAT ChemicalThruster1.DecrementMass = true;
GMAT ChemicalThruster1.Tank = {ChemicalTank1};
GMAT ChemicalThruster1.MixRatio = [1];
GMAT ChemicalThruster1.GravitationalAccel = 9.810000000000001;
GMAT ChemicalThruster1.C1 = 1000;
GMAT ChemicalThruster1.C2 = 0;
GMAT ChemicalThruster1.C3 = 0;
GMAT ChemicalThruster1.C4 = 0;
GMAT ChemicalThruster1.C5 = 0;
GMAT ChemicalThruster1.C6 = 0;
GMAT ChemicalThruster1.C7 = 0;
GMAT ChemicalThruster1.C8 = 0;
GMAT ChemicalThruster1.C9 = 0;
GMAT ChemicalThruster1.C10 = 0;
GMAT ChemicalThruster1.C11 = 0;
GMAT ChemicalThruster1.C12 = 0;
GMAT ChemicalThruster1.C13 = 0;
GMAT ChemicalThruster1.C14 = 0;
GMAT ChemicalThruster1.C15 = 0;
GMAT ChemicalThruster1.C16 = 0;
GMAT ChemicalThruster1.K1 = 300;
GMAT ChemicalThruster1.K2 = 0;
GMAT ChemicalThruster1.K3 = 0;
GMAT ChemicalThruster1.K4 = 0;
GMAT ChemicalThruster1.K5 = 0;
GMAT ChemicalThruster1.K6 = 0;
GMAT ChemicalThruster1.K7 = 0;
GMAT ChemicalThruster1.K8 = 0;
GMAT ChemicalThruster1.K9 = 0;
GMAT ChemicalThruster1.K10 = 0;
GMAT ChemicalThruster1.K11 = 0;
GMAT ChemicalThruster1.K12 = 0;
GMAT ChemicalThruster1.K13 = 0;
GMAT ChemicalThruster1.K14 = 0;
GMAT ChemicalThruster1.K15 = 0;
GMAT ChemicalThruster1.K16 = 0;





%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PrimaryBodies = {Earth};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;
GMAT DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.Order = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = RungeKutta89;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn DefaultIB;
GMAT DefaultIB.CoordinateSystem = Local;
GMAT DefaultIB.Origin = Earth;
GMAT DefaultIB.Axes = VNB;
GMAT DefaultIB.Element1 = 0;
GMAT DefaultIB.Element2 = 0;
GMAT DefaultIB.Element3 = 0;
GMAT DefaultIB.DecrementMass = false;
GMAT DefaultIB.Isp = 300;
GMAT DefaultIB.GravitationalAccel = 9.810000000000001;

Create FiniteBurn FiniteBurn1;
GMAT FiniteBurn1.Thrusters = {ChemicalThruster1};
GMAT FiniteBurn1.ThrottleLogicAlgorithm = 'MaxNumberOfThrusters';


%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 25;
GMAT DC1.DerivativeMethod = ForwardDifference;
GMAT DC1.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.002941176470588235 0.2149220489977728 ];
GMAT DefaultOrbitView.Size = [ 0.4664705882352941 0.5055679287305123 ];
GMAT DefaultOrbitView.RelativeZOrder = 170;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {DefaultSC, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ShowLabels = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 30000 0 0 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

Create GroundTrackPlot DefaultGroundTrackPlot;
GMAT DefaultGroundTrackPlot.SolverIterations = Current;
GMAT DefaultGroundTrackPlot.UpperLeft = [ 0.4688235294117647 0.2160356347438753 ];
GMAT DefaultGroundTrackPlot.Size = [ 0.47 0.5089086859688196 ];
GMAT DefaultGroundTrackPlot.RelativeZOrder = 172;
GMAT DefaultGroundTrackPlot.Maximized = false;
GMAT DefaultGroundTrackPlot.Add = {DefaultSC};
GMAT DefaultGroundTrackPlot.DataCollectFrequency = 1;
GMAT DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
GMAT DefaultGroundTrackPlot.NumPointsToRedraw = 0;
GMAT DefaultGroundTrackPlot.ShowPlot = true;
GMAT DefaultGroundTrackPlot.CentralBody = Earth;
GMAT DefaultGroundTrackPlot.TextureMap = '../data/graphics/texture/ModifiedBlueMarble.jpg';

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable BurnDuration;
GMAT BurnDuration = 0;






%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate 'Prop To Perigee' DefaultProp(DefaultSC) {DefaultSC.Earth.Periapsis};
Target 'Raise Apogee' DC1 {SolveMode = Solve, ExitMode = SaveAndContinue, ShowProgressWindow = true};
   Vary 'Vary Burn Duration' DC1(BurnDuration = 200, {Perturbation = 0.0001, Lower = 0.0, Upper = 10000, MaxStep = 100, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   BeginFiniteBurn 'Turn Thruster On' FiniteBurn1(DefaultSC);
   Propagate 'Prop BurnDuration' DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = BurnDuration};
   EndFiniteBurn 'Turn Thruster Off' FiniteBurn1(DefaultSC);
   Propagate 'Prop To Apogee' DefaultProp(DefaultSC) {DefaultSC.Earth.Apoapsis};
   Achieve 'Achieve Apogee Radius = 12000' DC1(DefaultSC.Earth.RMAG = 12000, {Tolerance = 0.1});
EndTarget;  % For targeter DC1
