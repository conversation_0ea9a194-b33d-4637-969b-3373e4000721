%General Mission Analysis Tool(GMAT) Script
%Created: 2011-06-13 02:33:42


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft LEOsat;
GMAT LEOsat.DateFormat = UTCGregorian;
GMAT LEOsat.Epoch = '01 Jan 2000 11:59:28.000';
GMAT LEOsat.CoordinateSystem = EarthMJ2000Eq;
GMAT LEOsat.DisplayStateType = Keplerian;
GMAT LEOsat.SMA = 6733.989999999996;
GMAT LEOsat.ECC = 0.0004329999999984123;
GMAT LEOsat.INC = 34.98399999999998;
GMAT LEOsat.RAAN = 274.742;
GMAT LEOsat.AOP = 287.8049999999732;
GMAT LEOsat.TA = 294.0690000000269;
GMAT LEOsat.DryMass = 850;
GMAT LEOsat.Cd = 2.2;
GMAT LEOsat.Cr = 1.8;
GMAT LEOsat.DragArea = 15;
GMAT LEOsat.SRPArea = 1;
GMAT LEOsat.NAIFId = -123456789;
GMAT LEOsat.NAIFIdReferenceFrame = -123456789;
GMAT LEOsat.OrbitColor = Red;
GMAT LEOsat.TargetColor = Teal;
GMAT LEOsat.Id = 'SatId';
GMAT LEOsat.Attitude = CoordinateSystemFixed;
GMAT LEOsat.SPADSRPScaleFactor = 1;
GMAT LEOsat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT LEOsat.ModelOffsetX = 0;
GMAT LEOsat.ModelOffsetY = 0;
GMAT LEOsat.ModelOffsetZ = 0;
GMAT LEOsat.ModelRotationX = 0;
GMAT LEOsat.ModelRotationY = 0;
GMAT LEOsat.ModelRotationZ = 0;
GMAT LEOsat.ModelScale = 3;
GMAT LEOsat.AttitudeDisplayStateType = 'Quaternion';
GMAT LEOsat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LEOsat.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT LEOsat.EulerAngleSequence = '321';


%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel LEOprop_ForceModel;
GMAT LEOprop_ForceModel.CentralBody = Earth;
GMAT LEOprop_ForceModel.PrimaryBodies = {Earth};
GMAT LEOprop_ForceModel.PointMasses = {Luna, Sun};
GMAT LEOprop_ForceModel.SRP = On;
GMAT LEOprop_ForceModel.RelativisticCorrection = Off;
GMAT LEOprop_ForceModel.ErrorControl = RSSStep;
GMAT LEOprop_ForceModel.GravityField.Earth.Degree = 4;
GMAT LEOprop_ForceModel.GravityField.Earth.Order = 4;
GMAT LEOprop_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT LEOprop_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT LEOprop_ForceModel.SRP.Flux = 1367;
GMAT LEOprop_ForceModel.SRP.SRPModel = Spherical;
GMAT LEOprop_ForceModel.SRP.Nominal_Sun = 149597870.691;
GMAT LEOprop_ForceModel.Drag.AtmosphereModel = JacchiaRoberts;
GMAT LEOprop_ForceModel.Drag.HistoricWeatherSource = 'ConstantFluxAndGeoMag';
GMAT LEOprop_ForceModel.Drag.PredictedWeatherSource = 'ConstantFluxAndGeoMag';
GMAT LEOprop_ForceModel.Drag.CSSISpaceWeatherFile = '../samples/SupportFiles/CSSI_2004To2026.txt';
GMAT LEOprop_ForceModel.Drag.SchattenFile = 'SchattenPredict.txt';
GMAT LEOprop_ForceModel.Drag.F107 = 150;
GMAT LEOprop_ForceModel.Drag.F107A = 150;
GMAT LEOprop_ForceModel.Drag.MagneticIndex = 3;
GMAT LEOprop_ForceModel.Drag.SchattenErrorModel = 'Nominal';
GMAT LEOprop_ForceModel.Drag.SchattenTimingModel = 'NominalCycle';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LEOprop;
GMAT LEOprop.FM = LEOprop_ForceModel;
GMAT LEOprop.Type = RungeKutta89;
GMAT LEOprop.InitialStepSize = 60;
GMAT LEOprop.Accuracy = 9.999999999999999e-012;
GMAT LEOprop.MinStep = 0.001;
GMAT LEOprop.MaxStep = 2700;
GMAT LEOprop.MaxStepAttempts = 50;
GMAT LEOprop.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TCM1;
GMAT TCM1.CoordinateSystem = Local;
GMAT TCM1.Origin = Earth;
GMAT TCM1.Axes = VNB;
GMAT TCM1.Element1 = 0;
GMAT TCM1.Element2 = 0;
GMAT TCM1.Element3 = 0;
GMAT TCM1.DecrementMass = false;
GMAT TCM1.Isp = 300;
GMAT TCM1.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn TCM2;
GMAT TCM2.CoordinateSystem = Local;
GMAT TCM2.Origin = Earth;
GMAT TCM2.Axes = VNB;
GMAT TCM2.Element1 = 0;
GMAT TCM2.Element2 = 0;
GMAT TCM2.Element3 = 0;
GMAT TCM2.DecrementMass = false;
GMAT TCM2.Isp = 300;
GMAT TCM2.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn ImpulsiveBurn1;
GMAT ImpulsiveBurn1.CoordinateSystem = Local;
GMAT ImpulsiveBurn1.Origin = Earth;
GMAT ImpulsiveBurn1.Axes = VNB;
GMAT ImpulsiveBurn1.Element1 = 0;
GMAT ImpulsiveBurn1.Element2 = 0;
GMAT ImpulsiveBurn1.Element3 = 0;
GMAT ImpulsiveBurn1.DecrementMass = false;
GMAT ImpulsiveBurn1.Isp = 300;
GMAT ImpulsiveBurn1.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC.MaximumIterations = 25;
GMAT DC.DerivativeMethod = ForwardDifference;
GMAT DC.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.3729411764705882 0.004264392324093817 ];
GMAT DefaultOrbitView.Size = [ 0.5958823529411764 0.4328358208955224 ];
GMAT DefaultOrbitView.RelativeZOrder = 30;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {LEOsat, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ShowLabels = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 30000 0 0 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

Create XYPlot XYPlot1;
GMAT XYPlot1.SolverIterations = Current;
GMAT XYPlot1.UpperLeft = [ 0.3758823529411765 0.4424307036247335 ];
GMAT XYPlot1.Size = [ 0.5958823529411764 0.4360341151385928 ];
GMAT XYPlot1.RelativeZOrder = 25;
GMAT XYPlot1.Maximized = false;
GMAT XYPlot1.XVariable = LEOsat.A1ModJulian;
GMAT XYPlot1.YVariables = {LEOsat.Earth.Altitude};
GMAT XYPlot1.ShowGrid = true;
GMAT XYPlot1.ShowPlot = true;

%----------------------------------------
%---------- Functions
%----------------------------------------
Create GmatFunction TargetLEOStationKeeping;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

While 'While ElapsedDays < 10' LEOsat.ElapsedDays < 10.0
   Propagate 'Prop One Step' LEOprop(LEOsat);
   If 'If Alt < Threshold' LEOsat.Earth.Altitude < 342
      Propagate 'Prop To Periapsis' LEOprop(LEOsat) {LEOsat.Periapsis};
      Target 'Raise Orbit' DC {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
         Vary 'Vary TCM1.V' DC(TCM1.Element1 = 0.002, {Perturbation = 0.0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.05, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
         Maneuver 'Apply TCM1' TCM1(LEOsat);
         Propagate 'Prop to Apoapsis' LEOprop(LEOsat) {LEOsat.Earth.Apoapsis};
         Achieve 'Achieve RMAG' DC(LEOsat.Earth.RMAG = 6737, {Tolerance = 0.1});
         Vary 'Vary TCM2.V' DC(TCM2.Element1 = 1e-005, {Perturbation = 0.00005, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.05, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
         Maneuver 'Apply TCM2' TCM2(LEOsat);
         Achieve 'Achieve ECC' DC(LEOsat.Earth.ECC = 0.00005, {Tolerance = 0.0001});
      EndTarget;  % For targeter DC
   EndIf;
EndWhile;
