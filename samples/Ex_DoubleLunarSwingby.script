%  Script Mission - Double Lunar Swingby Example
%
%  This script demonstrates how to set up a double Lunar swingby with
%  multiple targeting sequences.
%

%--------------------------------------------------------------------------
%----------------- SpaceCraft, Formations, Constellations -----------------
%--------------------------------------------------------------------------

Create Spacecraft MMSRef;
GMAT MMSRef.DateFormat = UTCGregorian;
GMAT MMSRef.Epoch = '24 May 2014 15:17:21.927';
GMAT MMSRef.CoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.DisplayStateType = Keplerian;
GMAT MMSRef.SMA = 83230.75058999992;
GMAT MMSRef.ECC = 0.89643283;
GMAT MMSRef.INC = 15.50000000000001;
GMAT MMSRef.RAAN = 15.96879432999999;
GMAT MMSRef.AOP = 289.7205240999998;
GMAT MMSRef.TA = 179.9999987925817;
GMAT MMSRef.DryMass = 400;
GMAT MMSRef.Cd = 2.2;
GMAT MMSRef.Cr = 1.7;
GMAT MMSRef.DragArea = 5;
GMAT MMSRef.SRPArea = 5;

%--------------------------------------------------------------------------
%------------------------------ Propagators -------------------------------
%--------------------------------------------------------------------------

Create ForceModel LunarSB_ForceModel;
GMAT LunarSB_ForceModel.CentralBody = Earth;
GMAT LunarSB_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT LunarSB_ForceModel.Drag = None;
GMAT LunarSB_ForceModel.SRP = Off;
GMAT LunarSB_ForceModel.RelativisticCorrection = Off;
GMAT LunarSB_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LunarSB;
GMAT LunarSB.FM = LunarSB_ForceModel;
GMAT LunarSB.Type = RungeKutta89;
GMAT LunarSB.InitialStepSize = 60;
GMAT LunarSB.Accuracy = 1e-011;
GMAT LunarSB.MinStep = 0.001;
GMAT LunarSB.MaxStep = 30000;
GMAT LunarSB.MaxStepAttempts = 50;
GMAT LunarSB.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%--------------------------------------------------------------------------
%--------------------------------- Burns ----------------------------------
%--------------------------------------------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.01;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LST;
GMAT LST.CoordinateSystem = Local;
GMAT LST.Origin = Earth;
GMAT LST.Axes = VNB;
GMAT LST.Element1 = 0;
GMAT LST.Element2 = 0;
GMAT LST.Element3 = 0;
GMAT LST.DecrementMass = false;
GMAT LST.Isp = 300;
GMAT LST.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn ALM;
GMAT ALM.CoordinateSystem = Local;
GMAT ALM.Origin = Earth;
GMAT ALM.Axes = VNB;
GMAT ALM.Element1 = -0.354;
GMAT ALM.Element2 = 0;
GMAT ALM.Element3 = 0;
GMAT ALM.DecrementMass = false;
GMAT ALM.Isp = 300;
GMAT ALM.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LunarPhasedV;
GMAT LunarPhasedV.CoordinateSystem = Local;
GMAT LunarPhasedV.Origin = Earth;
GMAT LunarPhasedV.Axes = VNB;
GMAT LunarPhasedV.Element1 = 0.027;
GMAT LunarPhasedV.Element2 = 0;
GMAT LunarPhasedV.Element3 = 0;
GMAT LunarPhasedV.DecrementMass = false;
GMAT LunarPhasedV.Isp = 300;
GMAT LunarPhasedV.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create CoordinateSystem GSE;
GMAT GSE.Origin = Earth;
GMAT GSE.Axes = ObjectReferenced;
GMAT GSE.XAxis = R;
GMAT GSE.ZAxis = N;
GMAT GSE.Primary = Sun;
GMAT GSE.Secondary = Earth;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 10;
GMAT DC1.DerivativeMethod = ForwardDifference;
GMAT DC1.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create XYPlot EarthDistance;
GMAT EarthDistance.SolverIterations = None;
GMAT EarthDistance.UpperLeft = [ 0.3035294117647059 0.02917771883289125 ];
GMAT EarthDistance.Size = [ 0.34 0.4509283819628647 ];
GMAT EarthDistance.RelativeZOrder = 190;
GMAT EarthDistance.Maximized = false;
GMAT EarthDistance.XVariable = MMSRef.A1ModJulian;
GMAT EarthDistance.YVariables = {MMSRef.Earth.RMAG};
GMAT EarthDistance.ShowGrid = true;
GMAT EarthDistance.ShowPlot = true;

Create OrbitView OGL_EarthMJ2K;
GMAT OGL_EarthMJ2K.SolverIterations = All;
GMAT OGL_EarthMJ2K.UpperLeft = [ 0.3011764705882353 0.4854111405835544 ];
GMAT OGL_EarthMJ2K.Size = [ 0.34 0.4509283819628647 ];
GMAT OGL_EarthMJ2K.RelativeZOrder = 148;
GMAT OGL_EarthMJ2K.Maximized = false;
GMAT OGL_EarthMJ2K.Add = {MMSRef, Earth, Luna};
GMAT OGL_EarthMJ2K.CoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.DrawObject = [ true true true ];
GMAT OGL_EarthMJ2K.DataCollectFrequency = 1;
GMAT OGL_EarthMJ2K.UpdatePlotFrequency = 50;
GMAT OGL_EarthMJ2K.NumPointsToRedraw = 0;
GMAT OGL_EarthMJ2K.ShowPlot = true;
GMAT OGL_EarthMJ2K.ShowLabels = true;
GMAT OGL_EarthMJ2K.ViewPointReference = Earth;
GMAT OGL_EarthMJ2K.ViewPointVector = [ 0 0 30000 ];
GMAT OGL_EarthMJ2K.ViewDirection = Earth;
GMAT OGL_EarthMJ2K.ViewScaleFactor = 38;
GMAT OGL_EarthMJ2K.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.ViewUpAxis = X;
GMAT OGL_EarthMJ2K.EclipticPlane = Off;
GMAT OGL_EarthMJ2K.XYPlane = On;
GMAT OGL_EarthMJ2K.WireFrame = Off;
GMAT OGL_EarthMJ2K.Axes = On;
GMAT OGL_EarthMJ2K.Grid = Off;
GMAT OGL_EarthMJ2K.SunLine = Off;
GMAT OGL_EarthMJ2K.UseInitialView = On;
GMAT OGL_EarthMJ2K.StarCount = 7000;
GMAT OGL_EarthMJ2K.EnableStars = On;
GMAT OGL_EarthMJ2K.EnableConstellations = On;

Create OrbitView OGL_EarthMoonRot;
GMAT OGL_EarthMoonRot.SolverIterations = All;
GMAT OGL_EarthMoonRot.UpperLeft = [ 0.6447058823529411 0.4840848806366048 ];
GMAT OGL_EarthMoonRot.Size = [ 0.3405882352941176 0.4522546419098144 ];
GMAT OGL_EarthMoonRot.RelativeZOrder = 164;
GMAT OGL_EarthMoonRot.Maximized = false;
GMAT OGL_EarthMoonRot.Add = {MMSRef, Earth, Luna};
GMAT OGL_EarthMoonRot.CoordinateSystem = EarthMoonRot;
GMAT OGL_EarthMoonRot.DrawObject = [ true true true ];
GMAT OGL_EarthMoonRot.DataCollectFrequency = 1;
GMAT OGL_EarthMoonRot.UpdatePlotFrequency = 50;
GMAT OGL_EarthMoonRot.NumPointsToRedraw = 0;
GMAT OGL_EarthMoonRot.ShowPlot = true;
GMAT OGL_EarthMoonRot.ShowLabels = true;
GMAT OGL_EarthMoonRot.ViewPointReference = Luna;
GMAT OGL_EarthMoonRot.ViewPointVector = [ 0 0 30000 ];
GMAT OGL_EarthMoonRot.ViewDirection = Luna;
GMAT OGL_EarthMoonRot.ViewScaleFactor = 10;
GMAT OGL_EarthMoonRot.ViewUpCoordinateSystem = EarthMoonRot;
GMAT OGL_EarthMoonRot.ViewUpAxis = X;
GMAT OGL_EarthMoonRot.EclipticPlane = Off;
GMAT OGL_EarthMoonRot.XYPlane = On;
GMAT OGL_EarthMoonRot.WireFrame = Off;
GMAT OGL_EarthMoonRot.Axes = On;
GMAT OGL_EarthMoonRot.Grid = Off;
GMAT OGL_EarthMoonRot.SunLine = Off;
GMAT OGL_EarthMoonRot.UseInitialView = On;
GMAT OGL_EarthMoonRot.StarCount = 7000;
GMAT OGL_EarthMoonRot.EnableStars = On;
GMAT OGL_EarthMoonRot.EnableConstellations = On;

Create OrbitView OGL_EarthGSE;
GMAT OGL_EarthGSE.SolverIterations = None;
GMAT OGL_EarthGSE.UpperLeft = [ 0.6435294117647059 0.02917771883289125 ];
GMAT OGL_EarthGSE.Size = [ 0.34 0.4482758620689655 ];
GMAT OGL_EarthGSE.RelativeZOrder = 188;
GMAT OGL_EarthGSE.Maximized = false;
GMAT OGL_EarthGSE.Add = {MMSRef, Earth, Luna};
GMAT OGL_EarthGSE.CoordinateSystem = GSE;
GMAT OGL_EarthGSE.DrawObject = [ true true true ];
GMAT OGL_EarthGSE.DataCollectFrequency = 1;
GMAT OGL_EarthGSE.UpdatePlotFrequency = 50;
GMAT OGL_EarthGSE.NumPointsToRedraw = 0;
GMAT OGL_EarthGSE.ShowPlot = true;
GMAT OGL_EarthGSE.ShowLabels = true;
GMAT OGL_EarthGSE.ViewPointReference = Earth;
GMAT OGL_EarthGSE.ViewPointVector = [ 0 0 30000 ];
GMAT OGL_EarthGSE.ViewDirection = Earth;
GMAT OGL_EarthGSE.ViewScaleFactor = 65;
GMAT OGL_EarthGSE.ViewUpCoordinateSystem = GSE;
GMAT OGL_EarthGSE.ViewUpAxis = X;
GMAT OGL_EarthGSE.EclipticPlane = Off;
GMAT OGL_EarthGSE.XYPlane = On;
GMAT OGL_EarthGSE.WireFrame = Off;
GMAT OGL_EarthGSE.Axes = On;
GMAT OGL_EarthGSE.Grid = Off;
GMAT OGL_EarthGSE.SunLine = Off;
GMAT OGL_EarthGSE.UseInitialView = On;
GMAT OGL_EarthGSE.StarCount = 7000;
GMAT OGL_EarthGSE.EnableStars = On;
GMAT OGL_EarthGSE.EnableConstellations = On;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable I;


%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************
BeginMissionSequence;
BeginScript
   Toggle OGL_EarthMoonRot Off;
   Toggle OGL_EarthMJ2K Off;
   Toggle OGL_EarthGSE Off;
   Toggle OGL_EarthMoonRot On;
   Toggle OGL_EarthMJ2K On;
   Toggle OGL_EarthGSE On;
EndScript;

%------------------------------
%  Waiting Loops
%------------------------------
For 'For I = 1:14' I = 1:1:14;
   Propagate 'Prop to Periapsis' LunarSB(MMSRef) {MMSRef.Periapsis};
EndFor;

%------------------------------
%  Phasing Loops
%------------------------------
For 'For I = 1:5' I = 1:1:5;
   Propagate 'Prop to Periapsis' LunarSB(MMSRef) {MMSRef.Periapsis};
   Maneuver 'Apply LunarPhasedV' LunarPhasedV(MMSRef);
EndFor;

%%------------------------------
%%  Prop to periapsis for LSI
%%------------------------------
Maneuver 'Apply TOI' TOI(MMSRef);
Propagate 'Prop to Apogee' LunarSB(MMSRef) {MMSRef.Apoapsis};

%------------------------------
%  Target Lunar B-plane
%------------------------------
Target 'Target Flyby' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary LST.N' DC1(LST.Element2 = 0.03413111106956, {Perturbation = 1e-005, Lower = -.5, Upper = .5, MaxStep = .02, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary LST.B' DC1(LST.Element3 = -0.09078959728280001, {Perturbation = 1e-004, Lower = -.5, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply LST' LST(MMSRef);
   Propagate 'Prop to Periselene' LunarSB(MMSRef) {MMSRef.Luna.Periapsis};
   Achieve 'Achieve BdotT' DC1(MMSRef.MoonMJ2000Eq.BdotT = 150000, {Tolerance = .001});
   Achieve 'Acheive BdotR' DC1(MMSRef.MoonMJ2000Eq.BdotR = -10000, {Tolerance = .001});
EndTarget;  % For targeter DC1


%%------------------------------
%% Propagate to Earth Periapsis
%%------------------------------
Propagate 'Prop to Perigee' LunarSB(MMSRef) {MMSRef.Periapsis};

%------------------------------
% Target to lower Apogee
%------------------------------
Target 'Lower Apogee' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary ALM.V' DC1(ALM.Element1 = -0.3198120104, {Perturbation = 1e-006, Lower = -.5, Upper = .5, MaxStep = .08, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply ALM' ALM(MMSRef);
   Achieve 'Achieve RadApo' DC1(MMSRef.Earth.RadApo = 191344.0, {Tolerance = .00001});
EndTarget;  % For targeter DC1

Propagate LunarSB(MMSRef) {MMSRef.ElapsedDays = 10};
