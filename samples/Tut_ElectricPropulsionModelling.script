%General Mission Analysis Tool(GMAT) Script
%Created: 2015-10-11 15:57:14


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = TAIModJulian;
GMAT DefaultSC.Epoch = '21545';
GMAT DefaultSC.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.DisplayStateType = Cartesian;
GMAT DefaultSC.X = 7100;
GMAT DefaultSC.Y = 0;
GMAT DefaultSC.Z = 1300;
GMAT DefaultSC.VX = 0;
GMAT DefaultSC.VY = 7.35;
GMAT DefaultSC.VZ = 1;
GMAT DefaultSC.DryMass = 850;
GMAT DefaultSC.Cd = 2.2;
GMAT DefaultSC.Cr = 1.8;
GMAT DefaultSC.DragArea = 15;
GMAT DefaultSC.SRPArea = 1;
GMAT DefaultSC.Tanks = {ElectricTank1};
GMAT DefaultSC.Thrusters = {ElectricThruster1};
GMAT DefaultSC.PowerSystem = SolarPowerSystem1;
GMAT DefaultSC.NAIFId = -10006001;
GMAT DefaultSC.NAIFIdReferenceFrame = -9006001;
GMAT DefaultSC.OrbitColor = Red;
GMAT DefaultSC.TargetColor = Teal;
GMAT DefaultSC.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT DefaultSC.CdSigma = 1e+070;
GMAT DefaultSC.CrSigma = 1e+070;
GMAT DefaultSC.Id = 'SatId';
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.SPADSRPScaleFactor = 1;
GMAT DefaultSC.ModelFile = 'aura.3ds';
GMAT DefaultSC.ModelOffsetX = 0;
GMAT DefaultSC.ModelOffsetY = 0;
GMAT DefaultSC.ModelOffsetZ = 0;
GMAT DefaultSC.ModelRotationX = 0;
GMAT DefaultSC.ModelRotationY = 0;
GMAT DefaultSC.ModelRotationZ = 0;
GMAT DefaultSC.ModelScale = 1;
GMAT DefaultSC.AttitudeDisplayStateType = 'Quaternion';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.EulerAngleSequence = '321';

%----------------------------------------
%---------- Hardware Components
%----------------------------------------

Create ElectricTank ElectricTank1;
GMAT ElectricTank1.AllowNegativeFuelMass = false;
GMAT ElectricTank1.FuelMass = 756;

Create ElectricThruster ElectricThruster1;
GMAT ElectricThruster1.CoordinateSystem = Local;
GMAT ElectricThruster1.Origin = Earth;
GMAT ElectricThruster1.Axes = VNB;
GMAT ElectricThruster1.ThrustDirection1 = 1;
GMAT ElectricThruster1.ThrustDirection2 = 0;
GMAT ElectricThruster1.ThrustDirection3 = 0;
GMAT ElectricThruster1.DutyCycle = 1;
GMAT ElectricThruster1.ThrustScaleFactor = 1;
GMAT ElectricThruster1.DecrementMass = true;
GMAT ElectricThruster1.Tank = {ElectricTank1};
GMAT ElectricThruster1.MixRatio = [ 1 ];
GMAT ElectricThruster1.GravitationalAccel = 9.810000000000001;
GMAT ElectricThruster1.ThrustModel = ThrustMassPolynomial;
GMAT ElectricThruster1.MaximumUsablePower = 7.266;
GMAT ElectricThruster1.MinimumUsablePower = 0.638;
GMAT ElectricThruster1.ThrustCoeff1 = -5.19082;
GMAT ElectricThruster1.ThrustCoeff2 = 2.96519;
GMAT ElectricThruster1.ThrustCoeff3 = -14.4789;
GMAT ElectricThruster1.ThrustCoeff4 = 54.05382;
GMAT ElectricThruster1.ThrustCoeff5 = -0.00100092;
GMAT ElectricThruster1.MassFlowCoeff1 = -0.004776;
GMAT ElectricThruster1.MassFlowCoeff2 = 0.05717;
GMAT ElectricThruster1.MassFlowCoeff3 = -0.09956;
GMAT ElectricThruster1.MassFlowCoeff4 = 0.03211;
GMAT ElectricThruster1.MassFlowCoeff5 = 2.13781;
GMAT ElectricThruster1.FixedEfficiency = 0.7;
GMAT ElectricThruster1.Isp = 4200;
GMAT ElectricThruster1.ConstantThrust = 5;

Create SolarPowerSystem SolarPowerSystem1;
GMAT SolarPowerSystem1.EpochFormat = 'UTCGregorian';
GMAT SolarPowerSystem1.InitialEpoch = ''01 Jan 2000 11:59:28.000'';
GMAT SolarPowerSystem1.InitialMaxPower = 1.2;
GMAT SolarPowerSystem1.AnnualDecayRate = 5;
GMAT SolarPowerSystem1.Margin = 5;
GMAT SolarPowerSystem1.BusCoeff1 = 0.3;
GMAT SolarPowerSystem1.BusCoeff2 = 0;
GMAT SolarPowerSystem1.BusCoeff3 = 0;
GMAT SolarPowerSystem1.ShadowModel = 'DualCone';
GMAT SolarPowerSystem1.ShadowBodies = {'Earth'};
GMAT SolarPowerSystem1.SolarCoeff1 = 1.32077;
GMAT SolarPowerSystem1.SolarCoeff2 = -0.10848;
GMAT SolarPowerSystem1.SolarCoeff3 = -0.11665;
GMAT SolarPowerSystem1.SolarCoeff4 = 0.10843;
GMAT SolarPowerSystem1.SolarCoeff5 = -0.01279;

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PrimaryBodies = {Earth};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;
GMAT DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.Order = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = RungeKutta89;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn DefaultIB;
GMAT DefaultIB.CoordinateSystem = Local;
GMAT DefaultIB.Origin = Earth;
GMAT DefaultIB.Axes = VNB;
GMAT DefaultIB.Element1 = 0;
GMAT DefaultIB.Element2 = 0;
GMAT DefaultIB.Element3 = 0;
GMAT DefaultIB.DecrementMass = false;
GMAT DefaultIB.Isp = 300;
GMAT DefaultIB.GravitationalAccel = 9.810000000000001;

Create FiniteBurn FiniteBurn1;
GMAT FiniteBurn1.Thrusters = {ElectricThruster1};
GMAT FiniteBurn1.ThrottleLogicAlgorithm = 'MaxNumberOfThrusters';

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.4063324538258575 0 ];
GMAT DefaultOrbitView.Size = [ 0.4986807387862797 0.5322391559202814 ];
GMAT DefaultOrbitView.RelativeZOrder = 63;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {DefaultSC, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ShowLabels = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 30000 0 0 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

Create GroundTrackPlot DefaultGroundTrackPlot;
GMAT DefaultGroundTrackPlot.SolverIterations = Current;
GMAT DefaultGroundTrackPlot.UpperLeft = [ 0 0.5334114888628371 ];
GMAT DefaultGroundTrackPlot.Size = [ 0.4080914687774846 0.5826494724501758 ];
GMAT DefaultGroundTrackPlot.RelativeZOrder = 67;
GMAT DefaultGroundTrackPlot.Maximized = false;
GMAT DefaultGroundTrackPlot.Add = {DefaultSC};
GMAT DefaultGroundTrackPlot.DataCollectFrequency = 1;
GMAT DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
GMAT DefaultGroundTrackPlot.NumPointsToRedraw = 0;
GMAT DefaultGroundTrackPlot.ShowPlot = true;
GMAT DefaultGroundTrackPlot.CentralBody = Earth;
GMAT DefaultGroundTrackPlot.TextureMap = 'ModifiedBlueMarble.jpg';

Create XYPlot PowerVsTime;
GMAT PowerVsTime.SolverIterations = Current;
GMAT PowerVsTime.UpperLeft = [ 0.4063324538258575 0.5369284876905041 ];
GMAT PowerVsTime.Size = [ 0.4854881266490765 0.4548651817116061 ];
GMAT PowerVsTime.RelativeZOrder = 59;
GMAT PowerVsTime.Maximized = false;
GMAT PowerVsTime.XVariable = DefaultSC.A1ModJulian;
GMAT PowerVsTime.YVariables = {DefaultSC.SolarPowerSystem1.TotalPowerAvailable};
GMAT PowerVsTime.ShowGrid = true;
GMAT PowerVsTime.ShowPlot = true;

Create XYPlot MassFlowRate;
GMAT MassFlowRate.SolverIterations = Current;
GMAT MassFlowRate.UpperLeft = [ 0 0 ];
GMAT MassFlowRate.Size = [ 0.4089709762532982 0.5357561547479485 ];
GMAT MassFlowRate.RelativeZOrder = 55;
GMAT MassFlowRate.Maximized = false;
GMAT MassFlowRate.XVariable = DefaultSC.A1ModJulian;
GMAT MassFlowRate.YVariables = {DefaultSC.ElectricThruster1.MassFlowRate};
GMAT MassFlowRate.ShowGrid = true;
GMAT MassFlowRate.ShowPlot = true;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
BeginFiniteBurn 'Start the Maneuver' FiniteBurn1(DefaultSC);
Propagate 'Propagate Two Days' DefaultProp(DefaultSC) {DefaultSC.ElapsedDays = 2};
EndFiniteBurn 'End the Maneuver' FiniteBurn1(DefaultSC);
