%General Mission Analysis Tool(GMAT) Script
%Created: 2012-05-09 04:36:49


%----------------------------------------
%---------- Spacecraft
%----------------------------------------
%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LRO;
GMAT LRO.DateFormat = UTCGregorian;
GMAT LRO.Epoch = '01 Jan 2000 11:59:28.000';
GMAT LRO.CoordinateSystem = EarthMJ2000Eq;
GMAT LRO.DisplayStateType = Cartesian;
GMAT LRO.X = 7100;
GMAT LRO.Y = 0;
GMAT LRO.Z = 1300;
GMAT LRO.VX = 0;
GMAT LRO.VY = 7.35;
GMAT LRO.VZ = 1;
GMAT LRO.DryMass = 850;
GMAT LRO.Cd = 2.2;
GMAT LRO.Cr = 1.8;
GMAT LRO.DragArea = 15;
GMAT LRO.SRPArea = 1;
GMAT LRO.NAIFId = -123456789;
GMAT LRO.NAIFIdReferenceFrame = -123456789;
GMAT LRO.Id = 'SatId';
GMAT LRO.Attitude = CoordinateSystemFixed;
GMAT LRO.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT LRO.ModelOffsetX = 0;
GMAT LRO.ModelOffsetY = 0;
GMAT LRO.ModelOffsetZ = 0;
GMAT LRO.ModelRotationX = 0;
GMAT LRO.ModelRotationY = 0;
GMAT LRO.ModelRotationZ = 0;
GMAT LRO.ModelScale = 3;
GMAT LRO.AttitudeDisplayStateType = 'Quaternion';
GMAT LRO.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LRO.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

Create Spacecraft ImpactSat;
GMAT ImpactSat.DateFormat = UTCGregorian;
GMAT ImpactSat.Epoch = '01 Jan 2000 11:59:28.000';
GMAT ImpactSat.CoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSat.DisplayStateType = Cartesian;
GMAT ImpactSat.X = 7100;
GMAT ImpactSat.Y = 0;
GMAT ImpactSat.Z = 1300;
GMAT ImpactSat.VX = 0;
GMAT ImpactSat.VY = 7.35;
GMAT ImpactSat.VZ = 1;
GMAT ImpactSat.DryMass = 850;
GMAT ImpactSat.Cd = 2.2;
GMAT ImpactSat.Cr = 1.8;
GMAT ImpactSat.DragArea = 15;
GMAT ImpactSat.SRPArea = 1;
GMAT ImpactSat.NAIFId = -123456789;
GMAT ImpactSat.NAIFIdReferenceFrame = -123456789;
GMAT ImpactSat.Id = 'SatId';
GMAT ImpactSat.Attitude = CoordinateSystemFixed;
GMAT ImpactSat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT ImpactSat.ModelOffsetX = 0;
GMAT ImpactSat.ModelOffsetY = 0;
GMAT ImpactSat.ModelOffsetZ = 0;
GMAT ImpactSat.ModelRotationX = 0;
GMAT ImpactSat.ModelRotationY = 0;
GMAT ImpactSat.ModelRotationZ = 0;
GMAT ImpactSat.ModelScale = 3;
GMAT ImpactSat.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSat.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

Create Spacecraft ImpactSatInit;
GMAT ImpactSatInit.DateFormat = TAIModJulian;
GMAT ImpactSatInit.Epoch = '21545';
GMAT ImpactSatInit.CoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSatInit.DisplayStateType = Cartesian;
GMAT ImpactSatInit.X = 7100;
GMAT ImpactSatInit.Y = 0;
GMAT ImpactSatInit.Z = 1300;
GMAT ImpactSatInit.VX = 0;
GMAT ImpactSatInit.VY = 7.35;
GMAT ImpactSatInit.VZ = 1;
GMAT ImpactSatInit.DryMass = 850;
GMAT ImpactSatInit.Cd = 2.2;
GMAT ImpactSatInit.Cr = 1.8;
GMAT ImpactSatInit.DragArea = 15;
GMAT ImpactSatInit.SRPArea = 1;
GMAT ImpactSatInit.NAIFId = -123456789;
GMAT ImpactSatInit.NAIFIdReferenceFrame = -123456789;
GMAT ImpactSatInit.Id = 'SatId';
GMAT ImpactSatInit.Attitude = CoordinateSystemFixed;
GMAT ImpactSatInit.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT ImpactSatInit.ModelOffsetX = 0;
GMAT ImpactSatInit.ModelOffsetY = 0;
GMAT ImpactSatInit.ModelOffsetZ = 0;
GMAT ImpactSatInit.ModelRotationX = 0;
GMAT ImpactSatInit.ModelRotationY = 0;
GMAT ImpactSatInit.ModelRotationZ = 0;
GMAT ImpactSatInit.ModelScale = 3;
GMAT ImpactSatInit.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSatInit.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSatInit.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

Create Spacecraft CanonImpactSat;
GMAT CanonImpactSat.DateFormat = TAIModJulian;
GMAT CanonImpactSat.Epoch = '21545';
GMAT CanonImpactSat.CoordinateSystem = EarthMJ2000Eq;
GMAT CanonImpactSat.DisplayStateType = Cartesian;
GMAT CanonImpactSat.X = 7100;
GMAT CanonImpactSat.Y = 0;
GMAT CanonImpactSat.Z = 1300;
GMAT CanonImpactSat.VX = 0;
GMAT CanonImpactSat.VY = 7.35;
GMAT CanonImpactSat.VZ = 1;
GMAT CanonImpactSat.DryMass = 850;
GMAT CanonImpactSat.Cd = 2.2;
GMAT CanonImpactSat.Cr = 1.8;
GMAT CanonImpactSat.DragArea = 15;
GMAT CanonImpactSat.SRPArea = 1;
GMAT CanonImpactSat.NAIFId = -123456789;
GMAT CanonImpactSat.NAIFIdReferenceFrame = -123456789;
GMAT CanonImpactSat.Id = 'SatId';
GMAT CanonImpactSat.Attitude = CoordinateSystemFixed;
GMAT CanonImpactSat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT CanonImpactSat.ModelOffsetX = 0;
GMAT CanonImpactSat.ModelOffsetY = 0;
GMAT CanonImpactSat.ModelOffsetZ = 0;
GMAT CanonImpactSat.ModelRotationX = 0;
GMAT CanonImpactSat.ModelRotationY = 0;
GMAT CanonImpactSat.ModelRotationZ = 0;
GMAT CanonImpactSat.ModelScale = 3;
GMAT CanonImpactSat.AttitudeDisplayStateType = 'Quaternion';
GMAT CanonImpactSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT CanonImpactSat.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

Create Spacecraft ImpactSat_MoonFixed;
GMAT ImpactSat_MoonFixed.DateFormat = UTCGregorian;
GMAT ImpactSat_MoonFixed.Epoch = '01 Jan 2000 11:59:28.000';
GMAT ImpactSat_MoonFixed.CoordinateSystem = MoonFixed;
GMAT ImpactSat_MoonFixed.DisplayStateType = Cartesian;
GMAT ImpactSat_MoonFixed.X = 404097.4004557607;
GMAT ImpactSat_MoonFixed.Y = 30873.01777335557;
GMAT ImpactSat_MoonFixed.Z = -45917.57222582575;
GMAT ImpactSat_MoonFixed.VX = 4.40593017683402;
GMAT ImpactSat_MoonFixed.VY = 5.5017164658627;
GMAT ImpactSat_MoonFixed.VZ = -2.099602413163657;
GMAT ImpactSat_MoonFixed.DryMass = 850;
GMAT ImpactSat_MoonFixed.Cd = 2.2;
GMAT ImpactSat_MoonFixed.Cr = 1.8;
GMAT ImpactSat_MoonFixed.DragArea = 15;
GMAT ImpactSat_MoonFixed.SRPArea = 1;
GMAT ImpactSat_MoonFixed.NAIFId = -123456789;
GMAT ImpactSat_MoonFixed.NAIFIdReferenceFrame = -123456789;
GMAT ImpactSat_MoonFixed.Id = 'SatId';
GMAT ImpactSat_MoonFixed.Attitude = CoordinateSystemFixed;
GMAT ImpactSat_MoonFixed.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT ImpactSat_MoonFixed.ModelOffsetX = 0;
GMAT ImpactSat_MoonFixed.ModelOffsetY = 0;
GMAT ImpactSat_MoonFixed.ModelOffsetZ = 0;
GMAT ImpactSat_MoonFixed.ModelRotationX = 0;
GMAT ImpactSat_MoonFixed.ModelRotationY = 0;
GMAT ImpactSat_MoonFixed.ModelRotationZ = 0;
GMAT ImpactSat_MoonFixed.ModelScale = 3;
GMAT ImpactSat_MoonFixed.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSat_MoonFixed.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSat_MoonFixed.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LCROSS;
GMAT LCROSS.DateFormat = TAIModJulian;
GMAT LCROSS.Epoch = '21545';
GMAT LCROSS.CoordinateSystem = EarthMJ2000Eq;
GMAT LCROSS.DisplayStateType = Cartesian;
GMAT LCROSS.X = 7100;
GMAT LCROSS.Y = 0;
GMAT LCROSS.Z = 1300;
GMAT LCROSS.VX = 0;
GMAT LCROSS.VY = 7.35;
GMAT LCROSS.VZ = 1;
GMAT LCROSS.DryMass = 850;
GMAT LCROSS.Cd = 2.2;
GMAT LCROSS.Cr = 1.8;
GMAT LCROSS.DragArea = 15;
GMAT LCROSS.SRPArea = 1;
GMAT LCROSS.NAIFId = -123456789;
GMAT LCROSS.NAIFIdReferenceFrame = -123456789;
GMAT LCROSS.Id = 'SatId';
GMAT LCROSS.Attitude = CoordinateSystemFixed;
GMAT LCROSS.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT LCROSS.ModelOffsetX = 0;
GMAT LCROSS.ModelOffsetY = 0;
GMAT LCROSS.ModelOffsetZ = 0;
GMAT LCROSS.ModelRotationX = 0;
GMAT LCROSS.ModelRotationY = 0;
GMAT LCROSS.ModelRotationZ = 0;
GMAT LCROSS.ModelScale = 3;
GMAT LCROSS.AttitudeDisplayStateType = 'Quaternion';
GMAT LCROSS.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LCROSS.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LCROSSInit;
GMAT LCROSSInit.DateFormat = TAIModJulian;
GMAT LCROSSInit.Epoch = '21545';
GMAT LCROSSInit.CoordinateSystem = EarthMJ2000Eq;
GMAT LCROSSInit.DisplayStateType = Cartesian;
GMAT LCROSSInit.X = 7100;
GMAT LCROSSInit.Y = 0;
GMAT LCROSSInit.Z = 1300;
GMAT LCROSSInit.VX = 0;
GMAT LCROSSInit.VY = 7.35;
GMAT LCROSSInit.VZ = 1;
GMAT LCROSSInit.DryMass = 850;
GMAT LCROSSInit.Cd = 2.2;
GMAT LCROSSInit.Cr = 1.8;
GMAT LCROSSInit.DragArea = 15;
GMAT LCROSSInit.SRPArea = 1;
GMAT LCROSSInit.NAIFId = -123456789;
GMAT LCROSSInit.NAIFIdReferenceFrame = -123456789;
GMAT LCROSSInit.Id = 'SatId';
GMAT LCROSSInit.Attitude = CoordinateSystemFixed;
GMAT LCROSSInit.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT LCROSSInit.ModelOffsetX = 0;
GMAT LCROSSInit.ModelOffsetY = 0;
GMAT LCROSSInit.ModelOffsetZ = 0;
GMAT LCROSSInit.ModelRotationX = 0;
GMAT LCROSSInit.ModelRotationY = 0;
GMAT LCROSSInit.ModelRotationZ = 0;
GMAT LCROSSInit.ModelScale = 3;
GMAT LCROSSInit.AttitudeDisplayStateType = 'Quaternion';
GMAT LCROSSInit.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LCROSSInit.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

%----------------------------------------
%---------- ForceModels
%----------------------------------------


%%------------------------------------------------------------
%%------------------- Define Propagators --------------------
%%------------------------------------------------------------

%% Create a force model for near Lunar propagation
Create ForceModel NearMoonProp_ForceModel;
GMAT NearMoonProp_ForceModel.CentralBody = Luna;
%GMAT NearMoonProp_ForceModel.PrimaryBodies =
GMAT NearMoonProp_ForceModel.PointMasses = {Sun, Earth, Jupiter, Luna};
GMAT NearMoonProp_ForceModel.Drag = None;
GMAT NearMoonProp_ForceModel.SRP = On;
GMAT NearMoonProp_ForceModel.RelativisticCorrection = Off;
GMAT NearMoonProp_ForceModel.ErrorControl = RSSStep;
GMAT NearMoonProp_ForceModel.SRP.Flux = 1367;
GMAT NearMoonProp_ForceModel.SRP.Nominal_Sun = 149597870.691;



%% Create a force model for near Lunar propagation
%Create ForceModel NearMoonProp_ForceModel;
%%GMAT NearMoonProp_ForceModel.CentralBody = Luna;
%GMAT NearMoonProp_ForceModel.PrimaryBodies =
%GMAT NearMoonProp_ForceModel.PointMasses = {Sun, Earth, Luna};
%GMAT NearMoonProp_ForceModel.Drag = None;
%GMAT NearMoonProp_ForceModel.SRP = On;
%GMAT NearMoonProp_ForceModel.ErrorControl = RSSStep;
%GMAT NearMoonProp_ForceModel.Luna.Flux = 1367;
%GMAT NearMoonProp_ForceModel.Luna.Nominal_Sun = 149597870.691;

%% Create a force model for deep space propagation
Create ForceModel EarthFull_ForceModel;
GMAT EarthFull_ForceModel.CentralBody = Earth;
GMAT EarthFull_ForceModel.PrimaryBodies = {Earth};
GMAT EarthFull_ForceModel.PointMasses = {Sun, Luna, Jupiter};
GMAT EarthFull_ForceModel.Drag = None;
GMAT EarthFull_ForceModel.SRP = On;
GMAT EarthFull_ForceModel.RelativisticCorrection = Off;
GMAT EarthFull_ForceModel.ErrorControl = RSSStep;
GMAT EarthFull_ForceModel.GravityField.Earth.Degree = 4;
GMAT EarthFull_ForceModel.GravityField.Earth.Order = 0;
GMAT EarthFull_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT EarthFull_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT EarthFull_ForceModel.SRP.Flux = 1367;
GMAT EarthFull_ForceModel.SRP.Nominal_Sun = 149597870.691;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator NearMoonProp;
GMAT NearMoonProp.FM = NearMoonProp_ForceModel;
GMAT NearMoonProp.Type = PrinceDormand78;  %PrinceDormand45;   %PrinceDormand78;
GMAT NearMoonProp.InitialStepSize = 60;
GMAT NearMoonProp.Accuracy = 1e-009;
GMAT NearMoonProp.MinStep = 0.001;
GMAT NearMoonProp.MaxStep = 20000;
GMAT NearMoonProp.MaxStepAttempts = 50;
GMAT NearMoonProp.StopIfAccuracyIsViolated = true;

Create Propagator EarthFull;
GMAT EarthFull.FM = EarthFull_ForceModel;
GMAT EarthFull.Type = RungeKutta56;  %PrinceDormand45;  %PrinceDormand78;
GMAT EarthFull.InitialStepSize = 60;
GMAT EarthFull.Accuracy = 1e-009;
GMAT EarthFull.MinStep = 0.001;
GMAT EarthFull.MaxStep = 86400;
GMAT EarthFull.MaxStepAttempts = 50;
GMAT EarthFull.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%%------------------------------------------------------------
%%------------------- Create Maneuvers-- --------------------
%%------------------------------------------------------------
Create ImpulsiveBurn EDUSdv;
GMAT EDUSdv.CoordinateSystem = Local;
GMAT EDUSdv.Origin = Earth;
GMAT EDUSdv.Axes = VNB;
GMAT EDUSdv.Element1 = 0;
GMAT EDUSdv.Element2 = 0;
GMAT EDUSdv.Element3 = 0;
GMAT EDUSdv.DecrementMass = false;
GMAT EDUSdv.Isp = 300;
GMAT EDUSdv.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn EDUSdv_J2000;
GMAT EDUSdv_J2000.CoordinateSystem = Local;
GMAT EDUSdv_J2000.Origin = Earth;
GMAT EDUSdv_J2000.Axes = MJ2000Eq;
GMAT EDUSdv_J2000.Element1 = 0;
GMAT EDUSdv_J2000.Element2 = 0;
GMAT EDUSdv_J2000.Element3 = 0;
GMAT EDUSdv_J2000.DecrementMass = false;
GMAT EDUSdv_J2000.Isp = 300;
GMAT EDUSdv_J2000.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LCM1;
GMAT LCM1.CoordinateSystem = Local;
GMAT LCM1.Origin = Earth;
GMAT LCM1.Axes = VNB;
GMAT LCM1.Element1 = 0;
GMAT LCM1.Element2 = 0;
GMAT LCM1.Element3 = 0;
GMAT LCM1.DecrementMass = false;
GMAT LCM1.Isp = 300;
GMAT LCM1.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LCM2;
GMAT LCM2.CoordinateSystem = Local;
GMAT LCM2.Origin = Earth;
GMAT LCM2.Axes = VNB;
GMAT LCM2.Element1 = 0;
GMAT LCM2.Element2 = 0;
GMAT LCM2.Element3 = 0;
GMAT LCM2.DecrementMass = false;
GMAT LCM2.Isp = 300;
GMAT LCM2.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn dV2;
GMAT dV2.CoordinateSystem = Local;
GMAT dV2.Origin = Earth;
GMAT dV2.Axes = MJ2000Eq;
GMAT dV2.Element1 = 0;
GMAT dV2.Element2 = 0;
GMAT dV2.Element3 = 0;
GMAT dV2.DecrementMass = false;
GMAT dV2.Isp = 300;
GMAT dV2.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn dV3;
GMAT dV3.CoordinateSystem = Local;
GMAT dV3.Origin = Earth;
GMAT dV3.Axes = MJ2000Eq;
GMAT dV3.Element1 = 0;
GMAT dV3.Element2 = 0;
GMAT dV3.Element3 = 0;
GMAT dV3.DecrementMass = false;
GMAT dV3.Isp = 300;
GMAT dV3.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn dVatMoon;
GMAT dVatMoon.CoordinateSystem = Local;
GMAT dVatMoon.Origin = Luna;
GMAT dVatMoon.Axes = MJ2000Eq;
GMAT dVatMoon.Element1 = 0;
GMAT dVatMoon.Element2 = 0;
GMAT dVatMoon.Element3 = 0;
GMAT dVatMoon.DecrementMass = false;
GMAT dVatMoon.Isp = 300;
GMAT dVatMoon.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------

Create Array X[15,1];

%----------------------------------------
%---------- Variables, Arrays, Strings
%----------------------------------------
%----- These are variables that are just useful -------
Create Variable LoopCounter PositionError VelocityError Cost LCM1Mag LCM2Mag EDUSdvMag EDMdvEpoch LCM1Epoch LCM2Epoch;
Create Variable A1ImpactEpoch LoopCounter2 ConstraintError dx dy dz dVx dVy dVz SF_EDUSx;
Create Variable dVatMoonMag Min_Cost UseGMAToutput_Flag RefEpoch PropEpoch ET_EDUSdV ET_LCM1 ET_LCM2 ET_Coll A1CollEpoch;
Create Variable VXdiff VYdiff VZdiff I DeltaVx DeltaVy DeltaVz PosError VelError EDUS_J2000_DVx;
Create Variable EDUS_J2000_DVy EDUS_J2000_DVz DV1_J2000_DVx DV1_J2000_DVy DV1_J2000_DVz DV2_J2000_DVx DV2_J2000_DVy DV2_J2000_DVz SteveReferenceEpoch Res;
Create Variable LunarSMA DesiredSMA ConError BPlaneAngle PeriselentAlt Error1 Error2 Error3 Loop2 NumSatRevs;
Create Variable NumMoonRevs NumCrossings DotProduct pi DesiredAOP Loop1 TargetUTCModJulian dVX dVY dVZ;
Create Variable UTCGregorian LCROSSEpoch ET_dv3 AOP_J2000 LCM2Mag_mps;


%----- Strings ---------
Create String EmptyLine NewIterate PostTLI PreEDUS PostEDUS PreLCM1 PostLCM1 PreLCM2 PostLCM2 Periselene;
Create String EDUSData BPlaneData LCM1Sol LCM2Sol PostLCM2Data ImpactSatData ImpactData;
GMAT EmptyLine = ' ';
GMAT NewIterate = '------ Begin New Iterate Data -------';
GMAT EDUSData = '------ EDUS Maneuver Data -------';
GMAT BPlaneData = '------  B-Plane  Data -------';
GMAT LCM1Sol = '------ LCM1 Maneuver Data -------';
GMAT LCM2Sol = '------ LCM2 Maneuver Data -------';
GMAT PostLCM2Data = '------ Post LCM2 State -------';
GMAT ImpactSatData = '---------- Impact Sat Data --------------';
GMAT ImpactData = '------ Impact Data -------';



%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

%%------------------------------------------------------------
%%------------------- Create Coordinate Systems -------------
%%------------------------------------------------------------

Create CoordinateSystem MoonFixed;
GMAT MoonFixed.Origin = Luna;
GMAT MoonFixed.Axes = BodyFixed;

Create CoordinateSystem MoonFK5;
GMAT MoonFK5.Origin = Luna;
GMAT MoonFK5.Axes = MJ2000Eq;

Create CoordinateSystem MoonEarthRot;
GMAT MoonEarthRot.Origin = Luna;
GMAT MoonEarthRot.Axes = ObjectReferenced;
GMAT MoonEarthRot.XAxis = R;
GMAT MoonEarthRot.ZAxis = N;
GMAT MoonEarthRot.Primary = Earth;
GMAT MoonEarthRot.Secondary = Luna;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Earth;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create CoordinateSystem LunaFixed;
GMAT LunaFixed.Origin = Luna;
GMAT LunaFixed.Axes = BodyFixed;

Create CoordinateSystem MoonInertial;
GMAT MoonInertial.Origin = Luna;
GMAT MoonInertial.Axes = BodyInertial;

%----------------------------------------
%---------- Solvers
%----------------------------------------

%%-----------------------------------------------------------------
%%-----------------Create and Setup the Solvers -------------------
%%-----------------------------------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC.data';
GMAT DC.MaximumIterations = 2500;
GMAT DC.DerivativeMethod = ForwardDifference;

Create DifferentialCorrector DC_central;
GMAT DC_central.ShowProgress = true;
GMAT DC_central.ReportStyle = Normal;
GMAT DC_central.ReportFile = 'DifferentialCorrectorDC_central.data';
GMAT DC_central.MaximumIterations = 2500;
GMAT DC_central.DerivativeMethod = ForwardDifference;

Create VF13ad VF13ad1;
GMAT VF13ad1.ShowProgress = true;
GMAT VF13ad1.ReportStyle = Normal;
GMAT VF13ad1.ReportFile = 'VF13adVF13ad1.data';
GMAT VF13ad1.MaximumIterations = 200;
GMAT VF13ad1.Tolerance = 0.0001;
GMAT VF13ad1.UseCentralDifferences = false;
GMAT VF13ad1.FeasibilityTolerance = 1;

%----------------------------------------
%---------- Subscribers
%----------------------------------------


%%------------------------------------------------------------
%%------------------- Create Plots and Reports --------------
%%------------------------------------------------------------
Create OrbitView EarthView;
GMAT EarthView.SolverIterations = All; %Current or None are other options
GMAT EarthView.UpperLeft = [ 0 0 ];
GMAT EarthView.Size = [ 0 0 ];
GMAT EarthView.RelativeZOrder = 0;
GMAT EarthView.Add = {LCROSS, Luna, Earth, ImpactSat};
GMAT EarthView.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthView.DrawObject = [ true true true true ];
GMAT EarthView.DataCollectFrequency = 3;
GMAT EarthView.UpdatePlotFrequency = 50;
GMAT EarthView.NumPointsToRedraw = 0;
GMAT EarthView.ShowPlot = false;
GMAT EarthView.ViewPointReference = Earth;
GMAT EarthView.ViewPointVector = [ 30000 30000 30000 ];
GMAT EarthView.ViewDirection = Earth;
GMAT EarthView.ViewScaleFactor = 35;
GMAT EarthView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT EarthView.ViewUpAxis = Z;
GMAT EarthView.EclipticPlane = Off;
GMAT EarthView.XYPlane = Off;
GMAT EarthView.WireFrame = Off;
GMAT EarthView.Axes = Off;
GMAT EarthView.Grid = Off;
GMAT EarthView.SunLine = Off;
GMAT EarthView.UseInitialView = Off;
GMAT EarthView.StarCount = 7000;
GMAT EarthView.EnableStars = On;
GMAT EarthView.EnableConstellations = On;

Create OrbitView MoonView;
GMAT MoonView.SolverIterations = Current;
GMAT MoonView.UpperLeft = [ 0.2744090441932169 0 ];
GMAT MoonView.Size = [ 0.3545734840698869 0.4518072289156627 ];
GMAT MoonView.RelativeZOrder = 1912;
GMAT MoonView.Add = {LCROSS, Luna, Earth};
GMAT MoonView.CoordinateSystem = MoonEarthRot;
GMAT MoonView.DrawObject = [ true true true ];
GMAT MoonView.DataCollectFrequency = 3;
GMAT MoonView.UpdatePlotFrequency = 50;
GMAT MoonView.NumPointsToRedraw = 0;
GMAT MoonView.ShowPlot = true;
GMAT MoonView.ViewPointReference = Luna;
GMAT MoonView.ViewPointVector = [ 30000 30000 30000 ];
GMAT MoonView.ViewDirection = Luna;
GMAT MoonView.ViewScaleFactor = 1;
GMAT MoonView.ViewUpCoordinateSystem = MoonEarthRot;
GMAT MoonView.ViewUpAxis = Z;
GMAT MoonView.EclipticPlane = Off;
GMAT MoonView.XYPlane = Off;
GMAT MoonView.WireFrame = Off;
GMAT MoonView.Axes = On;
GMAT MoonView.Grid = Off;
GMAT MoonView.SunLine = Off;
GMAT MoonView.UseInitialView = On;
GMAT MoonView.StarCount = 7000;
GMAT MoonView.EnableStars = On;
GMAT MoonView.EnableConstellations = On;

Create XYPlot PositionErrorPlot;
GMAT PositionErrorPlot.SolverIterations = All;
GMAT PositionErrorPlot.UpperLeft = [ 0 0 ];
GMAT PositionErrorPlot.Size = [ 0 0 ];
GMAT PositionErrorPlot.RelativeZOrder = 0;
GMAT PositionErrorPlot.XVariable = LoopCounter;
GMAT PositionErrorPlot.YVariables = {PositionError};
GMAT PositionErrorPlot.ShowGrid = true;
GMAT PositionErrorPlot.ShowPlot = false;

Create XYPlot VelocityErrorPlot;
GMAT VelocityErrorPlot.SolverIterations = All;
GMAT VelocityErrorPlot.UpperLeft = [ 0 0 ];
GMAT VelocityErrorPlot.Size = [ 0 0 ];
GMAT VelocityErrorPlot.RelativeZOrder = 0;
GMAT VelocityErrorPlot.XVariable = LoopCounter;
GMAT VelocityErrorPlot.YVariables = {VelocityError};
GMAT VelocityErrorPlot.ShowGrid = true;
GMAT VelocityErrorPlot.ShowPlot = false;

Create XYPlot CostPlot;
GMAT CostPlot.SolverIterations = All;
GMAT CostPlot.UpperLeft = [ 0 0 ];
GMAT CostPlot.Size = [ 0 0 ];
GMAT CostPlot.RelativeZOrder = 0;
GMAT CostPlot.XVariable = LoopCounter;
GMAT CostPlot.YVariables = {Cost, LCM1Mag, LCM2Mag};
GMAT CostPlot.ShowGrid = true;
GMAT CostPlot.ShowPlot = false;

Create XYPlot PositionErrorPlot2;
GMAT PositionErrorPlot2.SolverIterations = All;
GMAT PositionErrorPlot2.UpperLeft = [ 0.2702980472764646 0.4593373493975904 ];
GMAT PositionErrorPlot2.Size = [ 0.3545734840698869 0.4518072289156627 ];
GMAT PositionErrorPlot2.RelativeZOrder = 1905;
GMAT PositionErrorPlot2.XVariable = LoopCounter2;
GMAT PositionErrorPlot2.YVariables = {PositionError};
GMAT PositionErrorPlot2.ShowGrid = true;
GMAT PositionErrorPlot2.ShowPlot = true;

Create XYPlot VelocityErrorPlot2;
GMAT VelocityErrorPlot2.SolverIterations = All;
GMAT VelocityErrorPlot2.UpperLeft = [ 0.6423432682425488 0.4593373493975904 ];
GMAT VelocityErrorPlot2.Size = [ 0.3545734840698869 0.4518072289156627 ];
GMAT VelocityErrorPlot2.RelativeZOrder = 1900;
GMAT VelocityErrorPlot2.XVariable = LoopCounter2;
GMAT VelocityErrorPlot2.YVariables = {VelocityError};
GMAT VelocityErrorPlot2.ShowGrid = true;
GMAT VelocityErrorPlot2.ShowPlot = true;

Create XYPlot CostPlot2;
GMAT CostPlot2.SolverIterations = All;
GMAT CostPlot2.UpperLeft = [ 0.6392600205549845 0.003012048192771085 ];
GMAT CostPlot2.Size = [ 0.3545734840698869 0.4518072289156627 ];
GMAT CostPlot2.RelativeZOrder = 1910;
GMAT CostPlot2.XVariable = LoopCounter2;
GMAT CostPlot2.YVariables = {Cost, LCM1Mag, LCM2Mag};
GMAT CostPlot2.ShowGrid = true;
GMAT CostPlot2.ShowPlot = true;

Create XYPlot Distances;
GMAT Distances.SolverIterations = All;
GMAT Distances.UpperLeft = [ 0 0 ];
GMAT Distances.Size = [ 0 0 ];
GMAT Distances.RelativeZOrder = 0;
GMAT Distances.XVariable = LCROSS.UTCModJulian;
GMAT Distances.YVariables = {LCROSS.RMAG, LCROSS.Luna.RMAG};
GMAT Distances.ShowGrid = true;
GMAT Distances.ShowPlot = false;

Create ReportFile IterateData;
GMAT IterateData.SolverIterations = All;
GMAT IterateData.UpperLeft = [ 0 0 ];
GMAT IterateData.Size = [ 0 0 ];
GMAT IterateData.RelativeZOrder = 0;
GMAT IterateData.Filename = 'IterateData.txt';
GMAT IterateData.Precision = 16;
GMAT IterateData.WriteHeaders = On;
GMAT IterateData.LeftJustify = On;
GMAT IterateData.ZeroFill = Off;
GMAT IterateData.ColumnWidth = 20;
GMAT IterateData.WriteReport = true;

Create ReportFile Check;
GMAT Check.SolverIterations = Current;
GMAT Check.UpperLeft = [ 0 0 ];
GMAT Check.Size = [ 0 0 ];
GMAT Check.RelativeZOrder = 0;
GMAT Check.Precision = 16;
GMAT Check.WriteHeaders = On;
GMAT Check.LeftJustify = On;
GMAT Check.ZeroFill = Off;
GMAT Check.ColumnWidth = 20;
GMAT Check.WriteReport = true;

Create ReportFile Data;
GMAT Data.SolverIterations = None;
GMAT Data.UpperLeft = [ 0 0 ];
GMAT Data.Size = [ 0 0 ];
GMAT Data.RelativeZOrder = 0;
GMAT Data.Filename = 'Data.txt';
GMAT Data.Precision = 16;
GMAT Data.WriteHeaders = On;
GMAT Data.LeftJustify = On;
GMAT Data.ZeroFill = On;
GMAT Data.ColumnWidth = 30;
GMAT Data.WriteReport = true;

Create ReportFile Solution;
GMAT Solution.SolverIterations = Current;
GMAT Solution.UpperLeft = [ 0 0 ];
GMAT Solution.Size = [ 0 0 ];
GMAT Solution.RelativeZOrder = 0;
GMAT Solution.Precision = 16;
GMAT Solution.WriteHeaders = On;
GMAT Solution.LeftJustify = On;
GMAT Solution.ZeroFill = Off;
GMAT Solution.ColumnWidth = 20;
GMAT Solution.WriteReport = true;

Create ReportFile SolutionData;
GMAT SolutionData.SolverIterations = Current;
GMAT SolutionData.UpperLeft = [ 0 0 ];
GMAT SolutionData.Size = [ 0 0 ];
GMAT SolutionData.RelativeZOrder = 0;
GMAT SolutionData.Filename = 'SolutionData.txt';
GMAT SolutionData.Precision = 16;
GMAT SolutionData.WriteHeaders = On;
GMAT SolutionData.LeftJustify = On;
GMAT SolutionData.ZeroFill = Off;
GMAT SolutionData.ColumnWidth = 20;
GMAT SolutionData.WriteReport = true;

Create ReportFile CollocationData;
GMAT CollocationData.SolverIterations = Current;
GMAT CollocationData.UpperLeft = [ 0 0 ];
GMAT CollocationData.Size = [ 0 0 ];
GMAT CollocationData.RelativeZOrder = 0;
GMAT CollocationData.Filename = 'CollocationData.txt';
GMAT CollocationData.Precision = 16;
GMAT CollocationData.WriteHeaders = On;
GMAT CollocationData.LeftJustify = On;
GMAT CollocationData.ZeroFill = Off;
GMAT CollocationData.ColumnWidth = 20;
GMAT CollocationData.WriteReport = true;

Create ReportFile SensitivityData;
GMAT SensitivityData.SolverIterations = Current;
GMAT SensitivityData.UpperLeft = [ 0 0 ];
GMAT SensitivityData.Size = [ 0 0 ];
GMAT SensitivityData.RelativeZOrder = 0;
GMAT SensitivityData.Filename = 'ReportFile1.txt';
GMAT SensitivityData.Precision = 16;
GMAT SensitivityData.Add = {LRO.A1ModJulian, LRO.EarthMJ2000Eq.X};
GMAT SensitivityData.WriteHeaders = On;
GMAT SensitivityData.LeftJustify = On;
GMAT SensitivityData.ZeroFill = Off;
GMAT SensitivityData.ColumnWidth = 20;
GMAT SensitivityData.WriteReport = true;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%------------------------------------------------------------------------
%------------------------------------------------------------------------
%-------------------------- Mission Sequence ----------------------------
%------------------------------------------------------------------------
%------------------------------------------------------------------------
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
BeginMissionSequence;
%---- Initializations
GMAT LCROSS = LRO;
GMAT LunarSMA = 384410.3;
GMAT pi = 3.14159265358979;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%  Input state information and initial guesses for optimization variables
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

BeginScript
   
   %---- Define LCROSS physical properties
   GMAT LRO.DryMass = 3256.8;
   GMAT LRO.Cr = 1.4;
   GMAT LRO.SRPArea = 42.354;
   
   %---- Define the initial conditions for LRO
   GMAT LRO.Epoch = '19 Jun 2009 01:31:05.920 ';
   GMAT LRO.X = 20747.68418000;
   GMAT LRO.Y = 50758.29345000;
   GMAT LRO.Z = 8229.21836200;
   GMAT LRO.VX = 0.11610208;
   GMAT LRO.VY = 3.32267120;
   GMAT LRO.VZ = 1.05827656;
   GMAT LCROSS = LRO;
   GMAT RefEpoch = LRO.A1ModJulian;
   GMAT ImpactSat = LRO;
   
   %---- Define the MoonFixed impact location and Epoch
   GMAT ImpactSat_MoonFixed.Epoch = '9 Oct 2009 11:30:00.000';
   GMAT ImpactSat_MoonFixed.X = 125.734;
   GMAT ImpactSat_MoonFixed.Y = -89.636;
   GMAT ImpactSat_MoonFixed.Z = -1730.522;
   
   %---- Define the initial conditions for ImpactSat
   GMAT ImpactSat.Epoch.TAIModJulian = ImpactSat_MoonFixed.Epoch.TAIModJulian;
   GMAT ImpactSat.X = ImpactSat_MoonFixed.EarthMJ2000Eq.X;
   GMAT ImpactSat.Y = ImpactSat_MoonFixed.EarthMJ2000Eq.Y;
   GMAT ImpactSat.Z = ImpactSat_MoonFixed.EarthMJ2000Eq.Z;
   
   %---- Define the desired Resonance condition
   GMAT NumSatRevs = 3;
   GMAT NumMoonRevs = 4;
   GMAT Res = NumSatRevs/NumMoonRevs;
   GMAT DesiredSMA = 473722.2056;%((398600.4415)^(1/3)*(NumMoonRevs/NumSatRevs/2/pi*2360551.68)^(2/3))*1.00;  
   GMAT DesiredAOP = 35.4553172712919;
   GMAT AOP_J2000 = 35.4553172712919;
   GMAT BPlaneAngle = 79.5592;
   GMAT PeriselentAlt = 3318.092;
   
   %---- Define intial guess for EDUSdv  
   GMAT ET_EDUSdV = (0/86400);
   GMAT EDUSdv.Element1 = 0.00001;
   GMAT EDUSdv.Element2 = 0.00001;
   GMAT EDUSdv.Element3 = 0.00001;
   
   %---- Define intial guess for LCM1 
   GMAT ET_LCM1 = 0.905018439982086 + ET_EDUSdV;
   GMAT LCM1.Element1 = 0.001704940;
   GMAT LCM1.Element2 = 0.005791633;
   GMAT LCM1.Element3 = -0.000661562;
   
   %---- Define intial guess for LCM2 
   GMAT ET_LCM2 = 33.770;
   GMAT LCM2.Element1 = 0.013391482;
   GMAT LCM2.Element2 = -0.007895569;
   GMAT LCM2.Element3 = 0.013918756;
   
   %---- Define initial guess for Collocation Elapsed Time
   GMAT ET_Coll = 100;
   GMAT ImpactSat.X = 54580.02594841291;
   GMAT ImpactSat.Y = 331618.2005005924;
   GMAT ImpactSat.Z = 162362.268059963;
   GMAT ImpactSat.VX = -0.6416435343925693;
   GMAT ImpactSat.VY = -0.4568696547646986;
   GMAT ImpactSat.VZ = 2.402724393937031;
   
EndScript;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%  Report Initial Guess Data to file
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

BeginScript
   Report Check LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report Check ET_EDUSdV EDUSdv.Element1 EDUSdv.Element2 EDUSdv.Element3;
   Report Check ET_LCM1 LCM1.Element1 LCM1.Element2 LCM1.Element3;
   Report Check ET_LCM2 LCM2.Element1 LCM2.Element2 LCM2.Element3;
   Report Check dVatMoon.Element1 dVatMoon.Element2 dVatMoon.Element3;
   Report Check ET_Coll;
EndScript;

%+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%===============================================================================================================
%===============================================================================================================
%+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%----                      This is the beginning of the mission sequence
%+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%===============================================================================================================
%===============================================================================================================
%+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

Toggle Distances Off;
Toggle MoonView On;
Toggle EarthView Off;
Toggle PositionErrorPlot Off;
Toggle VelocityErrorPlot Off;
Toggle PositionErrorPlot2 Off;
Toggle VelocityErrorPlot2 Off;
Toggle CostPlot2 Off;

%================================================================================================================
%------------------------------------------------------------------------
%----  Part 1: Find the canonical solution
%------------------------------------------------------------------------
%================================================================================================================

%------------------------------------------------------------------------
%----  Target sequence 1:  Try using EDUS dv to achieve BPlane Coordinates
%------------------------------------------------------------------------

%---- Store LCROSS spacecraft state for later use
GMAT LCM1.Element1 = 0.00380663994953;
GMAT LCM1.Element2 = -0.00139743801086;
GMAT LCM1.Element3 = 0.00460537200732;

GMAT LCROSSInit = LCROSS;
Target DC {SolveMode = Solve, ExitMode = DiscardAndContinue};  %RunInitialGuess or Solve
   
   %---- Initializations
   GMAT LoopCounter = LoopCounter + 1;
   Report IterateData NewIterate;
   Report IterateData PostTLI LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to where EDUS performs maneuver
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
   GMAT PropEpoch = RefEpoch + ET_LCM1;
   Propagate EarthFull(LCROSS) {LCROSS.ElapsedDays = 1.2};
   
   Report IterateData PreEDUS LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   %---- Vary and perform the maneuver
   Vary DC(LCM1.Element1 = LCM1.Element1, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .005});
   Vary DC(LCM1.Element2 = LCM1.Element2, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .005});
   Vary DC(LCM1.Element3 = LCM1.Element3, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .005});
   Maneuver LCM1(LCROSS);
   
   %--- Apply constraint on EDUSdV
   GMAT EDUSdvMag = sqrt(EDUSdv.Element1^2 + EDUSdv.Element2^2 + EDUSdv.Element3^2 );
   
   %---- Report some data
   Report IterateData PostEDUS LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report SolutionData LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to periselene
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %----- Propagate LCROSS to Epoch of LCM1
   %GMAT PropEpoch = RefEpoch + ET_LCM1;
   
   %Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   Report IterateData PreLCM1 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %----- Propagate LCROSS to Periselene
   Propagate EarthFull(LCROSS) {LCROSS.Luna.Periapsis};
   Report IterateData Periselene LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report IterateData Periselene LCROSS.MoonEarthRot.BVectorAngle LCROSS.Luna.Altitude;
   
   
   %----- Enforce the Bplane constraints
   Achieve DC(LCROSS.MoonEarthRot.BVectorAngle = BPlaneAngle, {Tolerance = 1});
   Achieve DC(LCROSS.Luna.Altitude = PeriselentAlt, {Tolerance = 0.1});
   
   
EndTarget;  % For targeter DC

%------------------------------------------------------------------------
%----  Target sequence 2:  Shape the orbit by targeting on desired SMA 
%----  and AOP.
%------------------------------------------------------------------------
GMAT LCROSS = LCROSSInit;
GMAT LCM1.Element1 = 0.00622892217071;
GMAT LCM1.Element2 = -0.00132933207755;
GMAT LCM1.Element3 = 0.0048251117781;
Target DC_central {SolveMode = Solve, ExitMode = DiscardAndContinue}; %RunInitialGuess or Solve
   
   %---- Initializations
   GMAT LoopCounter = LoopCounter + 1;
   Report IterateData NewIterate;
   Report IterateData PostTLI LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to where EDUS performs maneuver
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
   GMAT PropEpoch = RefEpoch + ET_EDUSdV;
   %Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   
   %---- Perform the maneuver
   %Vary DC_central(EDUSdv.Element1 = EDUSdv.Element1, {Perturbation = .000001, MaxStep = .001, Upper = .09});
   %Vary DC_central(EDUSdv.Element2 = EDUSdv.Element2, {Perturbation = .000001, MaxStep = .001, Upper = .09});
   %Vary DC_central(EDUSdv.Element3 = EDUSdv.Element3, {Perturbation = .000001, MaxStep = .001, Upper = .09});
   Maneuver EDUSdv(LCROSS);
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to LCM1 and perform manevuer
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   
   
   %----- Propagate LCROSS to Epoch of LCM1
   GMAT PropEpoch = RefEpoch + ET_LCM1;
   Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   
   %---- Apply maneuver LCM1
   Vary DC_central(LCM1.Element1 = LCM1.Element1, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .001});
   Vary DC_central(LCM1.Element2 = LCM1.Element2, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .001});
   Vary DC_central(LCM1.Element3 = LCM1.Element3, {Perturbation = .000001, Lower = -9.999999e300, Upper = .05, MaxStep = .001});
   Maneuver LCM1(LCROSS);
   
   %---- Assemble and report some data 
   GMAT LCM1Mag = sqrt(LCM1.Element1^2 + LCM1.Element2^2 + LCM1.Element3^2 );
   Report IterateData PreLCM1 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report SolutionData LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to periselene
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %----- Propagate LCROSS to Periselene
   Propagate EarthFull(LCROSS) {LCROSS.Luna.Periapsis};
   Report IterateData Periselene LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report IterateData Periselene LCROSS.MoonEarthRot.BVectorAngle LCROSS.Luna.Altitude;
   
   %----- Propagate to outside of the Lunar Sphere of Influence 
   Propagate EarthFull(LCROSS) {LCROSS.ElapsedDays = 8};
   
   %----- Enforce the Bplane constraints 
   %Error2 = (LCROSS.Earth.SMA - DesiredSMA)^2/LunarSMA^2;
   Achieve DC_central(LCROSS.Earth.SMA = DesiredSMA, {Tolerance = 100});
   %Achieve DC_central(LCROSS.EarthMoonRot.AOP = DesiredAOP, {Tolerance = 1}); 
   
   Achieve DC_central(LCROSS.AOP = AOP_J2000, {Tolerance = 1});
   
   Report Data DesiredSMA LCROSS.Earth.SMA LCROSS.EarthMoonRot.AOP;
   Report IterateData DesiredSMA LCROSS.Earth.SMA LCROSS.EarthMoonRot.AOP;
   Report IterateData DesiredSMA LCROSS.Earth.SMA LCROSS.AOP;
   
EndTarget;  % For targeter DC_central

%%------------------------------------------------------------------------
%%----  Target sequence 3:  Target on RA and DEC in EarthMoonRotSystem
%%------------------------------------------------------------------------


%%  Reinitialize LCROSS spacecraft
GMAT LCROSS = LCROSSInit;
GMAT MoonView.SolverIterations = All;
GMAT LCM1.Element1 = 0.00436370095429;
GMAT LCM1.Element2 = -0.00111121612382;
GMAT LCM1.Element3 = 0.00465663351025;

Target DC_central {SolveMode = Solve, ExitMode = DiscardAndContinue};   %RunInitialGuess or Solve
   
   %---- Initializations
   GMAT LoopCounter = LoopCounter + 1;
   Report IterateData NewIterate;
   Report IterateData PostTLI LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to where EDUS performs maneuver
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
   
   %----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
   GMAT PropEpoch = RefEpoch + ET_EDUSdV;
   % Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   
   %---- Perform the maneuver 
   %Vary DC_central(EDUSdv.Element1 = EDUSdv.Element1, {Perturbation = 1e-8, MaxStep = 1e-3, Upper = .05});
   %Vary DC_central(EDUSdv.Element2 = EDUSdv.Element2, {Perturbation = 1e-8, MaxStep = 1e-3, Upper = .05});
   %Vary DC_central(EDUSdv.Element3 = EDUSdv.Element3, {Perturbation = 1e-8, MaxStep = 1e-3, Upper = .05});
   
   %---- Perform the maneuver
   GMAT dVx = LCROSS.VX;
   GMAT dVy = LCROSS.VY;
   GMAT dVz = LCROSS.VZ;
   Maneuver EDUSdv(LCROSS);
   GMAT dVx = LCROSS.VX - dVx;
   GMAT dVy = LCROSS.VY - dVy;
   GMAT dVz = LCROSS.VZ - dVz;
   
   %Report Solution PostEDUS  LCROSS.UTCGregorian dVx dVy dVz LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
   %---- Prop to LCM1 and perform manevuer
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++   
   
   %----- Propagate LCROSS to Epoch of LCM1
   GMAT PropEpoch = RefEpoch + ET_LCM1;
   Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   Vary DC_central(LCM1.Element1 = LCM1.Element1, {Perturbation = 1e-6, Lower = -9.999999e300, Upper = .05, MaxStep = 1e-4});
   Vary DC_central(LCM1.Element2 = LCM1.Element2, {Perturbation = 1e-6, Lower = -9.999999e300, Upper = .05, MaxStep = 1e-4});
   Vary DC_central(LCM1.Element3 = LCM1.Element3, {Perturbation = 1e-6, Lower = -9.999999e300, Upper = .05, MaxStep = 1e-4});
   Maneuver LCM1(LCROSS);
   
   %---- Assemble and report some data 
   GMAT LCM1Mag = sqrt(LCM1.Element1^2 + LCM1.Element2^2 + LCM1.Element3^2 );
   Report IterateData PreLCM1 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   Report SolutionData LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
   %---- Prop to periselene
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %----- Propagate LCROSS to Periselene
   Propagate EarthFull(LCROSS) {LCROSS.Luna.Periapsis};
   
   %----- Propagate to outside of the Lunar Sphere of Influence
   Propagate EarthFull(LCROSS) {LCROSS.ElapsedDays = 8};
   Report Data LCROSS.SMA LCROSS.EarthMoonRot.AOP;
   
   Report Data LCROSS.SMA LCROSS.AOP;
   %----- Propagate for desired number of Revs
   GMAT NumCrossings = 2*NumSatRevs;
   For I = 1:1:6;
      Propagate EarthFull(LCROSS) {LCROSS.EarthMoonRot.Z = -1738, StopTolerance = 0.01};
   EndFor;
   
   Report Check LCROSS.A1ModJulian;
   Report Check LCROSS.UTCGregorian LCROSS.MoonEarthRot.X LCROSS.MoonEarthRot.Y LCROSS.MoonEarthRot.Z;
   
   %----- Calculate constraint values 
   Achieve DC_central(LCROSS.MoonEarthRot.Y = 0, {Tolerance = 5});
   Achieve DC_central(LCROSS.MoonEarthRot.X = 0, {Tolerance = 5});
   Report Data LCROSS.UTCGregorian;
   
EndTarget;  % For targeter DC_central

%Report Check LCROSS.MoonFixed.X LCROSS.MoonFixed.Y LCROSS.MoonFixed.Z
%Report Check LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ
%Report Check LCROSS.UTCGregorian LCROSS.UTCModJulian 

%%================================================================================================================
%%------------------------------------------------------------------------
%%----  Part 2: Find a position continuous solution
%%------------------------------------------------------------------------
%%================================================================================================================

Toggle MoonView Off;
Toggle EarthView Off;

Toggle PositionErrorPlot On;
Toggle VelocityErrorPlot On;

%------------------------------------------------------------------------
%----  Target sequence:  
%------------------------------------------------------------------------

GMAT ImpactSat.VX = LCROSS.VX;
GMAT ImpactSat.VY = LCROSS.VY;
GMAT ImpactSat.VZ = LCROSS.VZ;

%  Reinitialize LCROSS spacecraft
GMAT LCROSS = LCROSSInit;
GMAT ImpactSatInit = ImpactSat;
GMAT LoopCounter = 0;

Report IterateData ImpactSatData ImpactSat.UTCGregorian ImpactSat.X ImpactSat.Y ImpactSat.Z ImpactSat.VX ImpactSat.VY ImpactSat.VZ;
Report IterateData ET_Coll;
Report IterateData ET_LCM2 LCM2.Element1 LCM2.Element2 LCM2.Element3;
GMAT dVatMoon.Element1 = -0.0134580783916;
GMAT dVatMoon.Element2 = -0.0977802732702;
GMAT dVatMoon.Element3 = -0.135186852167;

%Target DC_central {SolveMode =  Solve};   %RunInitialGuess
%   
%   %---- Initializations
%   GMAT LoopCounter = LoopCounter + 1;
%   Report IterateData NewIterate;
%   Report IterateData PostTLI LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
%   
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Prop to where EDUS performs maneuver
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   
%   %----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
%   GMAT PropEpoch = RefEpoch + ET_EDUSdV;
%   %Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
%   
%   Maneuver EDUSdv(LCROSS);
%   EDUSdvMag = sqrt(EDUSdv.Element1^2 + EDUSdv.Element2^2 + EDUSdv.Element3^2);
%   Report IterateData PostEDUS LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z  LCROSS.VX LCROSS.VY LCROSS.VZ
%   
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Prop to LCM1 and perform manevuer
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   
%   %----- Propagate LCROSS to Epoch of LCM1
%   GMAT PropEpoch = RefEpoch + ET_LCM1;
%   Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
%   Maneuver LCM1(LCROSS);
%     
%   %---- Assemble and report some data
%   GMAT LCM1Mag = sqrt(LCM1.Element1^2 + LCM1.Element2^2 + LCM1.Element3^2 );
%   Report IterateData PostLCM1 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
%
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Prop to periselene
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   
%   %----- Propagate LCROSS to Periselene
%   Propagate NearMoonProp(LCROSS) {LCROSS.Luna.Periapsis}; 
%   
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Prop to LCM2 and perform manevuer
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%
%   %---- Vary the epoch of LCM2
%   %Vary VF13ad1(ET_LCM2 = ET_LCM2, {Perturbation = .0001, MaxStep = .1});
%   GMAT PropEpoch = RefEpoch + ET_LCM2;
%   
%   %----- Propagate to epoch of LCM2 
%   Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
%
%   %---- Assemble and report some data
%   GMAT LCM2Mag = sqrt(LCM2.Element1^2 + LCM2.Element2^2 + LCM2.Element3^2 );
%   Report IterateData PostLCM2 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
%
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Prop to LCROSS to Collocation Epoch 
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%
%   %--- Vary the collocation epoch
%   %Vary VF13ad1(ET_Coll = ET_Coll, {Perturbation = .0001, MaxStep = .1});
%   GMAT A1CollEpoch = RefEpoch + ET_Coll;
%   A1ImpactEpoch = ImpactSat.A1ModJulian
%   Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = A1CollEpoch};
%   %---- Report some data
%   Report IterateData  LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ 
%
%   
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- BackProp to ImpactSat to Collocation Epoch
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 
%   %----- Vary and apply the maneuver LCM2
%   Vary DC_central(dVatMoon.Element1 = dVatMoon.Element1, {Perturbation = .0001, MaxStep = .175});
%   Vary DC_central(dVatMoon.Element2 = dVatMoon.Element2, {Perturbation = .0001, MaxStep = .175});
%   Vary DC_central(dVatMoon.Element3 = dVatMoon.Element3, {Perturbation = .0001, MaxStep = .175});
%   Maneuver dVatMoon(ImpactSat); 
%   dVatMoonMag = sqrt(dVatMoon.Element1^2 + dVatMoon.Element2^2 + dVatMoon.Element3^2);
%
%   Propagate BackProp EarthFull(ImpactSat){ImpactSat.A1ModJulian = A1CollEpoch}
%   
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Calculate Collocation contraints and Cost
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   dx = (ImpactSat.X - LCROSS.X)/1000;
%   dy = (ImpactSat.Y - LCROSS.Y)/1000; 
%   dz = (ImpactSat.Z - LCROSS.Z)/1000; 
%   dVx = (ImpactSat.VX - LCROSS.VX)*1000;
%   dVy = (ImpactSat.VY - LCROSS.VY)*1000;
%   dVz = (ImpactSat.VZ - LCROSS.VZ)*1000; 
%   PositionError = sqrt(dx^2 + dy^2 + dz^2);
%   VelocityError = sqrt(dVx^2 + dVy^2 + dVz^2);  
%   Cost = (LCM2Mag + LCM2Mag)*1000
%   
%   Report IterateData dx dy dz
%
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   %---- Apply Collocation contraints
%   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%   Achieve DC_central(dx = 0, {Tolerance = .000001});
%   Achieve DC_central(dy = 0, {Tolerance = .000001});
%   Achieve DC_central(dz = 0, {Tolerance = .000001}); 
%
%EndTarget;  % For targeter DC_central
%
%%%================================================================================================================
%%%------------------------------------------------------------------------
%%%----  Part 3: Optimize the maneuver sequence
%%%------------------------------------------------------------------------
%%%================================================================================================================

GMAT ImpactSat = ImpactSatInit;
GMAT LCROSS = LCROSSInit;

Toggle PositionErrorPlot Off;
Toggle VelocityErrorPlot Off;
Toggle PositionErrorPlot2 On;
Toggle VelocityErrorPlot2 On;
Toggle CostPlot2 On;
Toggle MoonView Off;
Toggle EarthView Off;


GMAT LoopCounter2 = 0;
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%!!!!!!!!! Epoch Specific Data
%!!!!!!!!!

GMAT ET_LCM2 = 33.770070;
GMAT dV2.Element1 = 0.003178521397726965;
GMAT dV2.Element2 = -0.02244286793727441;
GMAT dV2.Element3 = 0.01515079731975863;


%dV2.Element1 =  0.0005
%dV2.Element2 = -0.0005
%dV2.Element3 = 0.000000001515079731975863

GMAT ET_Coll = 100.068369408;
GMAT dVatMoon.Element1 = .0005;
GMAT dVatMoon.Element2 = .0005;
GMAT dVatMoon.Element3 = .0005;

%  PreManever State
GMAT LCROSS.Epoch = '22 Jul 2009 20:00:00.000';
GMAT LCROSS.X = 345917.01123122376;
GMAT LCROSS.Y = -45348.2489355510620;
GMAT LCROSS.Z = -346196.56439542578;
GMAT LCROSS.VX = 0.0748987221602946;
GMAT LCROSS.VY = 0.8442212019763390;
GMAT LCROSS.VZ = 0.2205073401698255;

%%  Post ManeverState
%GMAT LCROSS.Epoch = '22 Jul 2009 20:00:00.000';
%GMAT LCROSS.X  = 345917.01123122376;
%GMAT LCROSS.Y  = -45348.2489355510620;
%GMAT LCROSS.Z  = -346196.56439542578;
%GMAT LCROSS.VX =   0.078077243558021 
%GMAT LCROSS.VY =   0.8217783340390646 
%GMAT LCROSS.VZ = 0.2356581374895841 

%  Impact State                                                                                                                                                                      
GMAT ImpactSat.Epoch = '9 Oct 2009 11:30:00.000';
GMAT ImpactSat.X = 54575.9528409897180000;
GMAT ImpactSat.Y = 331624.2404970731600;
GMAT ImpactSat.Z = 162338.185372761160;
GMAT ImpactSat.VX = -0.6675701182709503;
GMAT ImpactSat.VY = -0.8381718106638686;
GMAT ImpactSat.VZ = 2.2506650884041499;

GMAT ET_dv3 = 12;
GMAT dV3.Element1 = 0.0000001;
GMAT dV3.Element2 = 0.0000001;
GMAT dV3.Element3 = 0.0000001;

GMAT ET_dv3 = 9.1444532062;
GMAT dV3.Element1 = -0.00221105682463;
GMAT dV3.Element2 = -0.00309109319075;
GMAT dV3.Element3 = -0.00576739355515;
GMAT ET_LCM2 = 34.2067708398;
GMAT dV2.Element1 = 0.00253358529976;
GMAT dV2.Element2 = -0.00818031665189;
GMAT dV2.Element3 = 0.0144488751008;
GMAT ET_Coll = 100.108452165;
GMAT dVatMoon.Element1 = -0.00542614290389;
GMAT dVatMoon.Element2 = -0.04112202831;
GMAT dVatMoon.Element3 = -0.0484465124964;

Optimize VF13ad1 {SolveMode = Solve, ExitMode = DiscardAndContinue};
   
   %---- Initializations
   %PenUp VelocityErrorPlot2;
   %PenUp PositionErrorPlot2;
   %PenUp CostPlot2;
   
   GMAT LoopCounter2 = LoopCounter2 + 1;
   Report IterateData NewIterate;
   Report IterateData PostTLI LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to LCM1 and perform manevuer
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %---- Vary the epoch of dv1
   Vary VF13ad1(ET_dv3 = ET_dv3, {Perturbation = .0001, MaxStep = .5, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   NonlinearConstraint VF13ad1(ET_dv3>=8);
   
   GMAT PropEpoch = RefEpoch + ET_dv3;
   
   %----- Propagate to epoch of dv3
   GMAT LCROSSEpoch = LCROSS.A1ModJulian;
   If LCROSSEpoch < PropEpoch
      Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   EndIf;
   If LCROSSEpoch > PropEpoch
      Propagate BackProp NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   EndIf;
   
   Report IterateData LCROSS.SMA LCROSS.AOP;
   
   %----- Vary and apply the maneuver LCM2
   Vary VF13ad1(dV3.Element1 = dV3.Element1, {Perturbation = .000001, MaxStep = .001, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dV3.Element2 = dV3.Element2, {Perturbation = .000001, MaxStep = .001, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dV3.Element3 = dV3.Element3, {Perturbation = .000001, MaxStep = .001, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver dV3(LCROSS);
   
   %---- Assemble and report some data
   GMAT LCM1Mag = sqrt(dV3.Element1^2 + dV3.Element2^2 + dV3.Element3^2 );
   
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to LCM2 and perform manevuer
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %---- Vary the epoch of LCM2
   Vary VF13ad1(ET_LCM2 = ET_LCM2, {Perturbation = .0001, MaxStep = .5, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   GMAT PropEpoch = RefEpoch + ET_LCM2;
   
   %----- Propagate to epoch of LCM2 
   GMAT LCROSSEpoch = LCROSS.A1ModJulian;
   If LCROSSEpoch < PropEpoch
      Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   EndIf;
   If LCROSSEpoch > PropEpoch
      Propagate BackProp NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};
   EndIf;
   
   Report IterateData LCROSS.SMA LCROSS.AOP;
   
   %----- Vary and apply the maneuver LCM2
   Vary VF13ad1(dV2.Element1 = dV2.Element1, {Perturbation = .000001, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dV2.Element2 = dV2.Element2, {Perturbation = .000001, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dV2.Element3 = dV2.Element3, {Perturbation = .000001, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver dV2(LCROSS);
   
   %---- Assemble and report some data
   GMAT LCM2Mag = sqrt(dV2.Element1^2 + dV2.Element2^2 + dV2.Element3^2 );
   
   GMAT LCM2Mag_mps = LCM2Mag * 1000;
   
   Report IterateData PostLCM2 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Prop to LCROSS to Collocation Epoch 
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   
   %--- Vary the collocation epoch
   Vary VF13ad1(ET_Coll = ET_Coll, {Perturbation = .00001, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   GMAT A1CollEpoch = RefEpoch + ET_Coll;
   Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = A1CollEpoch};
   Report IterateData LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- BackProp to ImpactSat to Collocation Epoch
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   Report SolutionData ImpactSat.UTCGregorian ImpactSat.MoonFixed.X ImpactSat.MoonFixed.Y ImpactSat.MoonFixed.Z;
   Report SolutionData ImpactSat.UTCGregorian ImpactSat.X ImpactSat.Y ImpactSat.Z ImpactSat.VX ImpactSat.VY ImpactSat.VZ;
   
   
   %----- Vary and apply the maneuver LCM2
   Vary VF13ad1(dVatMoon.Element1 = dVatMoon.Element1, {Perturbation = .0000001, MaxStep = .1, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dVatMoon.Element2 = dVatMoon.Element2, {Perturbation = .0000001, MaxStep = .1, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary VF13ad1(dVatMoon.Element3 = dVatMoon.Element3, {Perturbation = .0000001, MaxStep = .1, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver dVatMoon(ImpactSat);
   GMAT dVatMoonMag = sqrt(dVatMoon.Element1^2 + dVatMoon.Element2^2 + dVatMoon.Element3^2);
   
   Propagate BackProp NearMoonProp(ImpactSat) {ImpactSat.A1ModJulian = A1CollEpoch};
   
   %---- Report some data
   Report Data LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
   
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Calculate Collocation contraints and Cost
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   GMAT dx = (ImpactSat.X - LCROSS.X)/1000;
   GMAT dy = (ImpactSat.Y - LCROSS.Y)/1000;
   GMAT dz = (ImpactSat.Z - LCROSS.Z)/1000;
   GMAT dVx = (ImpactSat.VX - LCROSS.VX)*100;
   GMAT dVy = (ImpactSat.VY - LCROSS.VY)*100;
   GMAT dVz = (ImpactSat.VZ - LCROSS.VZ)*100;
   Report CollocationData LCROSS.UTCGregorian ImpactSat.UTCGregorian dx dy dz dVx dVy dVz;
   GMAT PositionError = sqrt(dx^2 + dy^2 + dz^2);
   GMAT VelocityError = sqrt(dVx^2 + dVy^2 + dVz^2);
   
   GMAT Cost = (LCM2Mag*LCM2Mag) *  1000*1000;
   GMAT Cost = LCM1Mag + LCM2Mag;
   
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %---- Apply Collocation contraints
   %++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
   %Minimize VF13ad1(VelocityError);
   Minimize VF13ad1(Cost);
   NonlinearConstraint VF13ad1(dx=0);
   NonlinearConstraint VF13ad1(dy=0);
   NonlinearConstraint VF13ad1(dz=0);
   NonlinearConstraint VF13ad1(dVx=0);
   NonlinearConstraint VF13ad1(dVy=0);
   NonlinearConstraint VF13ad1(dVz=0);
   %NonlinearConstraint VF13ad1(LCM2Mag = 0); 
   %PenDown VelocityErrorPlot2;
   %PenDown PositionErrorPlot2;
   %PenDown CostPlot2;
   Propagate NearMoonProp(LCROSS);

EndOptimize;  % For targeter DC_central 

%================================================================================================================
%------------------------------------------------------------------------
%----  Part 4: Generate solution data
%------------------------------------------------------------------------
%================================================================================================================

GMAT ImpactSat = ImpactSatInit;
GMAT LCROSS = LCROSSInit;

Toggle Distances On;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to where EDUS performs maneuver
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
GMAT PropEpoch = RefEpoch + ET_EDUSdV;
Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};

%---- Perform the maneuver
GMAT dVx = LCROSS.VX;
GMAT dVy = LCROSS.VY;
GMAT dVz = LCROSS.VZ;
Maneuver EDUSdv(LCROSS);
GMAT dVx = LCROSS.VX - dVx;
GMAT dVy = LCROSS.VY - dVy;
GMAT dVz = LCROSS.VZ - dVz;

Report IterateData PostEDUS LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;

%Report Solution EDUSData
%Report Solution LCROSS.UTCGregorian 
%Report Solution dVx  
%Report Solution dVy  
%Report Solution dVz

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to LCM1 and perform manevuer
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to Epoch of LCM1
GMAT PropEpoch = RefEpoch + ET_LCM1;
Propagate EarthFull(LCROSS) {LCROSS.A1ModJulian = PropEpoch};

%---- Apply maneuver LCM1
GMAT dVx = LCROSS.VX;
GMAT dVy = LCROSS.VY;
GMAT dVz = LCROSS.VZ;
Maneuver LCM1(LCROSS);
GMAT dVx = LCROSS.VX - dVx;
GMAT dVy = LCROSS.VY - dVy;
GMAT dVz = LCROSS.VZ - dVz;

Report IterateData PostLCM1 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
Report Solution LCM1Sol;
Report Solution LCROSS.UTCGregorian dVx dVy dVz;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to periselene
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to Periselene
Propagate NearMoonProp(LCROSS) {LCROSS.Luna.Periapsis};
Report Solution BPlaneData;
Report Solution LCROSS.UTCGregorian LCROSS.Luna.Altitude LCROSS.MoonEarthRot.BVectorAngle;


%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to LCM2 and perform manevuer
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%---- Vary the epoch of LCM2
GMAT PropEpoch = RefEpoch + ET_LCM2;

%----- Propagate to epoch of LCM2 
Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = PropEpoch};

%----- Vary and apply the maneuver LCM2
GMAT dVx = LCROSS.VX;
GMAT dVy = LCROSS.VY;
GMAT dVz = LCROSS.VZ;
Maneuver LCM2(LCROSS);
GMAT dVx = LCROSS.VX - dVx;
GMAT dVy = LCROSS.VY - dVy;
GMAT dVz = LCROSS.VZ - dVz;

Report IterateData PostLCM2 LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;

Report Solution LCM2Sol;
Report Solution LCROSS.UTCGregorian dVx dVy dVz;
Report Solution PostLCM2Data;
Report Solution LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;
Report Solution LCROSS.SMA LCROSS.EarthMoonRot.AOP;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to LCROSS to Impact
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 

%--- Vary the collocation epoch
GMAT A1CollEpoch = RefEpoch + ET_Coll;
Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = A1CollEpoch};
Report IterateData LCROSS.UTCGregorian LCROSS.X LCROSS.Y LCROSS.Z LCROSS.VX LCROSS.VY LCROSS.VZ;

GMAT A1ImpactEpoch = ImpactSat.A1ModJulian;
Propagate NearMoonProp(LCROSS) {LCROSS.A1ModJulian = A1ImpactEpoch};
Report IterateData LCROSS.UTCGregorian LCROSS.MoonFixed.X LCROSS.MoonFixed.Y LCROSS.MoonFixed.Z;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Calculate Collocation contraints and Cost
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
GMAT dx = (ImpactSat.X - LCROSS.X);
GMAT dy = (ImpactSat.Y - LCROSS.Y);
GMAT dz = (ImpactSat.Z - LCROSS.Z);

GMAT PositionError = sqrt(dx^2 + dy^2 + dz^2);
GMAT VelocityError = sqrt(dVx^2 + dVy^2 + dVz^2);

Report Solution ImpactData;
Report Solution LCROSS.UTCGregorian LCROSS.MoonInertial.X LCROSS.MoonInertial.Y LCROSS.MoonInertial.Z;
Report Solution LCROSS.MoonFixed.FPA;
