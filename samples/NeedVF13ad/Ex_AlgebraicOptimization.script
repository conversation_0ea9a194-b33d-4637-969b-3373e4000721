%  Script Mission - Optimization Example
%
%  This script demonstrates how to use an Optimize sequence

%-----------------------------------------------------------------
%-----------------Create and Setup the Optimizer------------------
%-----------------------------------------------------------------

Create VF13ad SQPfmincon;
GMAT SQPfmincon.ShowProgress = true;
GMAT SQPfmincon.ReportStyle = Normal;
GMAT SQPfmincon.ReportFile = 'VF13adSQPfmincon.data';
GMAT SQPfmincon.MaximumIterations = 200;
GMAT SQPfmincon.Tolerance = 1e-005;
GMAT SQPfmincon.UseCentralDifferences = false;
GMAT SQPfmincon.FeasibilityTolerance = .0001;

%-----------------------------------------------------------------
%------------------------------OutPut-----------------------------
%-----------------------------------------------------------------

Create ReportFile Data;
GMAT Data.SolverIterations = Current;
GMAT Data.UpperLeft = [ 0 0 ];
GMAT Data.Size = [ 0 0 ];
GMAT Data.RelativeZOrder = 0;
GMAT Data.Maximized = false;
GMAT Data.Filename = 'Ex_AlgebraicOptimization.report';
GMAT Data.Precision = 16;
GMAT Data.WriteHeaders = false;
GMAT Data.LeftJustify = On;
GMAT Data.ZeroFill = Off;
GMAT Data.ColumnWidth = 20;
GMAT Data.WriteReport = true;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable X1 X2 J G;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Optimize SQPfmincon {SolveMode = Solve, ExitMode = DiscardAndContinue};
   
   %  Vary the independent variables
   Vary 'Vary X1' SQPfmincon(X1 = 0, {Perturbation = 0.0000001, MaxStep = 9.999999e300, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary X2' SQPfmincon(X2 = 0, {Perturbation = 0.0000001, MaxStep = 9.999999e300, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   %  The cost function and Minimize command
   GMAT 'Compute Cost (J)' J = ( X1 - 2 )^2 + ( X2 - 2 )^2;
   Minimize 'Minimize Cost (J)' SQPfmincon(J);
   
   %  Calculate constraint and use NonLinearConstraint command
   %   ( yes, the equation below is actually a linear constraint, were 
   %     testing functionality with this test)
   %  The constraint is to require the solution to lie above the line defined by X2 = -X1 + 6
   %  or X2 >= -X1 + 6;
   GMAT 'Compute Constraint (G)' G = X2 + X1;
   NonlinearConstraint 'G = 8' SQPfmincon(G<=8);

EndOptimize;  % For optimizer SQPfmincon

Report 'Report Solution' Data X1 X2 J G;



