%  Script Mission - Optimization Example
%
%  This script demonstrates how to use Yukon in an Optimize sequence


%-----------------------------------------------------------------
%-----------------Create and Setup the Optimizer------------------
%-----------------------------------------------------------------

Create Yukon Yukon1;
GMAT Yukon1.ShowProgress = true;
GMAT Yukon1.ReportStyle = Normal;
GMAT Yukon1.ReportFile = 'MinNLPadYukon1.data';
GMAT Yukon1.MaximumIterations = 500;
GMAT Yukon1.UseCentralDifferences = false;
GMAT Yukon1.FeasibilityTolerance = 0.001;
GMAT Yukon1.HessianUpdateMethod = SelfScaledBFGS;
GMAT Yukon1.MaximumFunctionEvals = 1000;
GMAT Yukon1.OptimalityTolerance = 0.0001;
GMAT Yukon1.FunctionTolerance = 0.0001;
GMAT Yukon1.MaximumElasticWeight = 10000;

%-----------------------------------------------------------------
%------------------------------OutPut-----------------------------
%-----------------------------------------------------------------

Create ReportFile Data;
GMAT Data.SolverIterations = Current;
GMAT Data.UpperLeft = [ 0 0 ];
GMAT Data.Size = [ 0 0 ];
GMAT Data.RelativeZOrder = 0;
GMAT Data.Maximized = false;
GMAT Data.Filename = 'Ex_AlgebraicOptimization.report';
GMAT Data.Precision = 16;
GMAT Data.WriteHeaders = false;
GMAT Data.LeftJustify = On;
GMAT Data.ZeroFill = Off;
GMAT Data.FixedWidth = true;
GMAT Data.Delimiter = ' ';
GMAT Data.ColumnWidth = 20;
GMAT Data.WriteReport = true;
GMAT Data.Add = {X1, X2};

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable X1 X2 J G;



%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Optimize Yukon1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   
   %  Vary the independent variables
   Vary 'Vary X1' Yukon1(X1 = 0, {Perturbation = 0.0000001, MaxStep = 9.999999e300, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary X2' Yukon1(X2 = 0, {Perturbation = 0.0000001, MaxStep = 9.999999e300, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   %  The cost function and Minimize command
   GMAT 'Compute Cost (J)' J = ( X1 - 2 )^2 + ( X2 - 2 )^2;
   Minimize 'Minimize Cost J' Yukon1(J);
   
   %  Calculate constraint and use NonLinearConstraint command
   %   ( yes, the equation below is actually a linear constraint, were 
   %     testing functionality with this test)
   %  The constraint is to require the solution to lie above the line defined by X2 = -X1 + 6
   %  or X2 >= -X1 + 6;
   GMAT 'Compute Constraint (G)' G = X2 + X1;
   NonlinearConstraint 'G = 8' Yukon1(G<=8);
	Report Data X1 X2;

EndOptimize;  % For optimizer SQPfmincon
