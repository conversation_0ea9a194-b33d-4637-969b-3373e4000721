%General Mission Analysis Tool(GMAT) Script
%Created: 2015-10-12 19:03:43


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft MAVEN;
GMAT MAVEN.DateFormat = UTCGregorian;
GMAT MAVEN.Epoch = '18 Nov 2013 20:26:24.315';
GMAT MAVEN.CoordinateSystem = EarthMJ2000Eq;
GMAT MAVEN.DisplayStateType = Keplerian;
GMAT MAVEN.SMA = -32593.21599272774;
GMAT MAVEN.ECC = 1.202872548116186;
GMAT MAVEN.INC = 28.80241266404144;
GMAT MAVEN.RAAN = 173.9693759331483;
GMAT MAVEN.AOP = 240.9696529532762;
GMAT MAVEN.TA = 359.946553377841;
GMAT MAVEN.DryMass = 850;
GMAT MAVEN.Cd = 2.2;
GMAT MAVEN.Cr = 1.8;
GMAT MAVEN.DragArea = 15;
GMAT MAVEN.SRPArea = 1;
GMAT MAVEN.Tanks = {MainTank};
GMAT MAVEN.NAIFId = -123456789;
GMAT MAVEN.NAIFIdReferenceFrame = -123456789;
GMAT MAVEN.OrbitColor = Red;
GMAT MAVEN.TargetColor = Teal;
GMAT MAVEN.Id = 'SatId';
GMAT MAVEN.Attitude = CoordinateSystemFixed;
GMAT MAVEN.SPADSRPScaleFactor = 1;
GMAT MAVEN.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT MAVEN.ModelOffsetX = 0;
GMAT MAVEN.ModelOffsetY = 0;
GMAT MAVEN.ModelOffsetZ = 0;
GMAT MAVEN.ModelRotationX = 0;
GMAT MAVEN.ModelRotationY = 0;
GMAT MAVEN.ModelRotationZ = 0;
GMAT MAVEN.ModelScale = 1;
GMAT MAVEN.AttitudeDisplayStateType = 'Quaternion';
GMAT MAVEN.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT MAVEN.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT MAVEN.EulerAngleSequence = '321';


%----------------------------------------
%---------- Hardware Components
%----------------------------------------
Create ChemicalTank MainTank;
GMAT MainTank.AllowNegativeFuelMass = false;
GMAT MainTank.FuelMass = 1718;
GMAT MainTank.Pressure = 5000;
GMAT MainTank.Temperature = 20;
GMAT MainTank.RefTemperature = 20;
GMAT MainTank.Volume = 2;
GMAT MainTank.FuelDensity = 1000;
GMAT MainTank.PressureModel = PressureRegulated;


%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel NearEarth_ForceModel;
GMAT NearEarth_ForceModel.CentralBody = Earth;
GMAT NearEarth_ForceModel.PrimaryBodies = {Earth};
GMAT NearEarth_ForceModel.PointMasses = {Luna, Sun};
GMAT NearEarth_ForceModel.Drag = None;
GMAT NearEarth_ForceModel.SRP = On;
GMAT NearEarth_ForceModel.RelativisticCorrection = Off;
GMAT NearEarth_ForceModel.ErrorControl = RSSStep;
GMAT NearEarth_ForceModel.GravityField.Earth.Degree = 8;
GMAT NearEarth_ForceModel.GravityField.Earth.Order = 8;
GMAT NearEarth_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT NearEarth_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT NearEarth_ForceModel.SRP.Flux = 1367;
GMAT NearEarth_ForceModel.SRP.SRPModel = Spherical;
GMAT NearEarth_ForceModel.SRP.Nominal_Sun = 149597870.691;

Create ForceModel DeepSpace_ForceModel;
GMAT DeepSpace_ForceModel.CentralBody = Sun;
GMAT DeepSpace_ForceModel.PointMasses = {Earth, Jupiter, Luna, Mars, Neptune, Saturn, Sun, Uranus, Venus};
GMAT DeepSpace_ForceModel.Drag = None;
GMAT DeepSpace_ForceModel.SRP = On;
GMAT DeepSpace_ForceModel.RelativisticCorrection = Off;
GMAT DeepSpace_ForceModel.ErrorControl = RSSStep;
GMAT DeepSpace_ForceModel.SRP.Flux = 1367;
GMAT DeepSpace_ForceModel.SRP.SRPModel = Spherical;
GMAT DeepSpace_ForceModel.SRP.Nominal_Sun = 149597870.691;

Create ForceModel NearMars_ForceModel;
GMAT NearMars_ForceModel.CentralBody = Mars;
GMAT NearMars_ForceModel.PrimaryBodies = {Mars};
GMAT NearMars_ForceModel.PointMasses = {Sun};
GMAT NearMars_ForceModel.Drag = None;
GMAT NearMars_ForceModel.SRP = On;
GMAT NearMars_ForceModel.RelativisticCorrection = Off;
GMAT NearMars_ForceModel.ErrorControl = RSSStep;
GMAT NearMars_ForceModel.GravityField.Mars.Degree = 8;
GMAT NearMars_ForceModel.GravityField.Mars.Order = 8;
GMAT NearMars_ForceModel.GravityField.Mars.PotentialFile = 'Mars50c.cof';
GMAT NearMars_ForceModel.SRP.Flux = 1367;
GMAT NearMars_ForceModel.SRP.SRPModel = Spherical;
GMAT NearMars_ForceModel.SRP.Nominal_Sun = 149597870.691;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator NearEarth;
GMAT NearEarth.FM = NearEarth_ForceModel;
GMAT NearEarth.Type = RungeKutta89;
GMAT NearEarth.InitialStepSize = 600;
GMAT NearEarth.Accuracy = 1e-013;
GMAT NearEarth.MinStep = 0;
GMAT NearEarth.MaxStep = 600;
GMAT NearEarth.MaxStepAttempts = 50;
GMAT NearEarth.StopIfAccuracyIsViolated = true;

Create Propagator DeepSpace;
GMAT DeepSpace.FM = DeepSpace_ForceModel;
GMAT DeepSpace.Type = PrinceDormand78;
GMAT DeepSpace.InitialStepSize = 600;
GMAT DeepSpace.Accuracy = 1e-012;
GMAT DeepSpace.MinStep = 0;
GMAT DeepSpace.MaxStep = 864000;
GMAT DeepSpace.MaxStepAttempts = 50;
GMAT DeepSpace.StopIfAccuracyIsViolated = true;

Create Propagator NearMars;
GMAT NearMars.FM = NearMars_ForceModel;
GMAT NearMars.Type = PrinceDormand78;
GMAT NearMars.InitialStepSize = 600;
GMAT NearMars.Accuracy = 1e-012;
GMAT NearMars.MinStep = 0;
GMAT NearMars.MaxStep = 86400;
GMAT NearMars.MaxStepAttempts = 50;
GMAT NearMars.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TCM;
GMAT TCM.CoordinateSystem = Local;
GMAT TCM.Origin = Earth;
GMAT TCM.Axes = VNB;
GMAT TCM.Element1 = 0;
GMAT TCM.Element2 = 0;
GMAT TCM.Element3 = 0;
GMAT TCM.DecrementMass = true;
GMAT TCM.Tank = {MainTank};
GMAT TCM.Isp = 300;
GMAT TCM.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn MOI;
GMAT MOI.CoordinateSystem = Local;
GMAT MOI.Origin = Mars;
GMAT MOI.Axes = VNB;
GMAT MOI.Element1 = 0;
GMAT MOI.Element2 = 0;
GMAT MOI.Element3 = 0;
GMAT MOI.DecrementMass = true;
GMAT MOI.Tank = {MainTank};
GMAT MOI.Isp = 300;
GMAT MOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem SunEcliptic;
GMAT SunEcliptic.Origin = Sun;
GMAT SunEcliptic.Axes = MJ2000Ec;

Create CoordinateSystem MarsInertial;
GMAT MarsInertial.Origin = Mars;
GMAT MarsInertial.Axes = BodyInertial;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DefaultDC;
GMAT DefaultDC.ShowProgress = true;
GMAT DefaultDC.ReportStyle = Normal;
GMAT DefaultDC.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DefaultDC.MaximumIterations = 25;
GMAT DefaultDC.DerivativeMethod = ForwardDifference;
GMAT DefaultDC.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView EarthView;
GMAT EarthView.SolverIterations = Current;
GMAT EarthView.UpperLeft = [ 0.002775850104094379 0.03159041394335512 ];
GMAT EarthView.Size = [ 0.3046495489243581 0.9084967320261438 ];
GMAT EarthView.RelativeZOrder = 162;
GMAT EarthView.Maximized = true;
GMAT EarthView.Add = {MAVEN, Earth};
GMAT EarthView.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthView.DrawObject = [ true true ];
GMAT EarthView.DataCollectFrequency = 1;
GMAT EarthView.UpdatePlotFrequency = 50;
GMAT EarthView.NumPointsToRedraw = 0;
GMAT EarthView.ShowPlot = true;
GMAT EarthView.ShowLabels = true;
GMAT EarthView.ViewPointReference = Earth;
GMAT EarthView.ViewPointVector = [ 0 0 30000 ];
GMAT EarthView.ViewDirection = Earth;
GMAT EarthView.ViewScaleFactor = 4;
GMAT EarthView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT EarthView.ViewUpAxis = Z;
GMAT EarthView.EclipticPlane = Off;
GMAT EarthView.XYPlane = On;
GMAT EarthView.WireFrame = Off;
GMAT EarthView.Axes = On;
GMAT EarthView.Grid = Off;
GMAT EarthView.SunLine = Off;
GMAT EarthView.UseInitialView = On;
GMAT EarthView.StarCount = 7000;
GMAT EarthView.EnableStars = On;
GMAT EarthView.EnableConstellations = On;

Create OrbitView SolarSystemView;
GMAT SolarSystemView.SolverIterations = Current;
GMAT SolarSystemView.UpperLeft = [ 0.3081193615544761 0.03267973856209151 ];
GMAT SolarSystemView.Size = [ 0.3136710617626648 0.9052287581699346 ];
GMAT SolarSystemView.RelativeZOrder = 170;
GMAT SolarSystemView.Maximized = true;
GMAT SolarSystemView.Add = {MAVEN, Earth, Mars, Sun};
GMAT SolarSystemView.CoordinateSystem = SunEcliptic;
GMAT SolarSystemView.DrawObject = [ true true true true ];
GMAT SolarSystemView.DataCollectFrequency = 1;
GMAT SolarSystemView.UpdatePlotFrequency = 50;
GMAT SolarSystemView.NumPointsToRedraw = 0;
GMAT SolarSystemView.ShowPlot = true;
GMAT SolarSystemView.ShowLabels = true;
GMAT SolarSystemView.ViewPointReference = Sun;
GMAT SolarSystemView.ViewPointVector = [ 0 0 500000000 ];
GMAT SolarSystemView.ViewDirection = Sun;
GMAT SolarSystemView.ViewScaleFactor = 2;
GMAT SolarSystemView.ViewUpCoordinateSystem = SunEcliptic;
GMAT SolarSystemView.ViewUpAxis = Z;
GMAT SolarSystemView.EclipticPlane = Off;
GMAT SolarSystemView.XYPlane = On;
GMAT SolarSystemView.WireFrame = Off;
GMAT SolarSystemView.Axes = On;
GMAT SolarSystemView.Grid = Off;
GMAT SolarSystemView.SunLine = Off;
GMAT SolarSystemView.UseInitialView = On;
GMAT SolarSystemView.StarCount = 7000;
GMAT SolarSystemView.EnableStars = On;
GMAT SolarSystemView.EnableConstellations = On;

Create OrbitView MarsView;
GMAT MarsView.SolverIterations = Current;
GMAT MarsView.UpperLeft = [ 0.6231783483691881 0.03159041394335512 ];
GMAT MarsView.Size = [ 0.3539208882720333 0.9008714596949891 ];
GMAT MarsView.RelativeZOrder = 164;
GMAT MarsView.Maximized = true;
GMAT MarsView.Add = {MAVEN, Mars, Earth};
GMAT MarsView.CoordinateSystem = MarsInertial;
GMAT MarsView.DrawObject = [ true true true ];
GMAT MarsView.DataCollectFrequency = 1;
GMAT MarsView.UpdatePlotFrequency = 50;
GMAT MarsView.NumPointsToRedraw = 0;
GMAT MarsView.ShowPlot = true;
GMAT MarsView.ShowLabels = true;
GMAT MarsView.ViewPointReference = Mars;
GMAT MarsView.ViewPointVector = [ 22000 22000 0 ];
GMAT MarsView.ViewDirection = Mars;
GMAT MarsView.ViewScaleFactor = 1.5;
GMAT MarsView.ViewUpCoordinateSystem = MarsInertial;
GMAT MarsView.ViewUpAxis = Z;
GMAT MarsView.EclipticPlane = Off;
GMAT MarsView.XYPlane = On;
GMAT MarsView.WireFrame = Off;
GMAT MarsView.Axes = On;
GMAT MarsView.Grid = Off;
GMAT MarsView.SunLine = Off;
GMAT MarsView.UseInitialView = On;
GMAT MarsView.StarCount = 7000;
GMAT MarsView.EnableStars = On;
GMAT MarsView.EnableConstellations = On;

Create ReportFile rf;
GMAT rf.SolverIterations = Current;
GMAT rf.UpperLeft = [ 0 0 ];
GMAT rf.Size = [ 0 0 ];
GMAT rf.RelativeZOrder = 0;
GMAT rf.Maximized = false;
GMAT rf.Filename = 'ReportFile1.txt';
GMAT rf.Precision = 16;
GMAT rf.WriteHeaders = true;
GMAT rf.LeftJustify = On;
GMAT rf.ZeroFill = Off;
GMAT rf.FixedWidth = true;
GMAT rf.Delimiter = ' ';
GMAT rf.ColumnWidth = 23;
GMAT rf.WriteReport = true;

%----------------------------------------
%---------- GMAT Functions
%----------------------------------------
Create GmatFunction TargeterInsideFunction;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Global 'Make Objects Global' MAVEN DeepSpace_ForceModel DefaultDC EarthView MainTank MarsView MOI NearEarth_ForceModel NearMars_ForceModel rf SolarSystemView TCM;

% Call Gmat Function and perform targeting inside function:
GMAT 'Target Desired B-Plane Coord. From Inside Function' TargeterInsideFunction;

% Report MAVEN parameters to global 'rf' :
Report 'Report Parameters' rf MAVEN.UTCGregorian TCM.Element1 TCM.Element2 TCM.Element3 MAVEN.MarsInertial.BdotT MAVEN.MarsInertial.BdotR MAVEN.MarsInertial.INC;

Target 'Mars Capture' DefaultDC {SolveMode = Solve, ExitMode = SaveAndContinue, ShowProgressWindow = true};
   Vary 'Vary MOI.V' DefaultDC(MOI.Element1 = -1, {Perturbation = 0.00001, Lower = -10e300, Upper = 10e300, MaxStep = 0.1, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply MOI' MOI(MAVEN);
   Propagate 'Prop to Mars Apoapsis' NearMars(MAVEN) {MAVEN.Mars.Apoapsis};
   Achieve 'Achieve RMAG' DefaultDC(MAVEN.Mars.RMAG = 12000, {Tolerance = 0.1});
EndTarget;  % For targeter DefaultDC
Propagate 'Prop for 1 day' NearMars(MAVEN) {MAVEN.ElapsedDays = 1};


