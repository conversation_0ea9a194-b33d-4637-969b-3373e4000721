
%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.SMA = 9999.999999999995;
GMAT Sat.ECC = 0.09999999999999988;
GMAT Sat.INC = 12.85008005658097;
GMAT Sat.RAAN = 306.6148021947984;
GMAT Sat.AOP = 314.1905515359921;
GMAT Sat.TA = 99.88774933204877;
GMAT Sat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT Sat.ModelOffsetX = 0;
GMAT Sat.ModelOffsetY = 0;
GMAT Sat.ModelOffsetZ = 0;
GMAT Sat.ModelRotationX = 0;
GMAT Sat.ModelRotationY = 0;
GMAT Sat.ModelRotationZ = 0;
GMAT Sat.ModelScale = 1.5;
GMAT Sat.Attitude = CoordinateSystemFixed;
GMAT Sat.AttitudeCoordinateSystem = EarthMJ2000Eq;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.MaxStep = 2;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView Enhanced3DView1;
GMAT Enhanced3DView1.Add = {Sat, Earth};
GMAT Enhanced3DView1.DrawObject = [ true true ];
GMAT Enhanced3DView1.ViewPointVector = [ 30000 0 0 ];
GMAT Enhanced3DView1.MaxPlotPoints = 200000;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;


Propagate 'Prop 40000 s' DefaultProp(Sat) {Sat.ElapsedSecs = 40000};
Propagate 'Prop 40000 s' DefaultProp(Sat) {Sat.ElapsedSecs = 40000};
