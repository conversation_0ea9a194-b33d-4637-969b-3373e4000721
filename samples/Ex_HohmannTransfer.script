%  Script Mission - Hohmann Transfer Example
%
%  This script demonstrates how to target a Hohmann Transfer


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create <PERSON><PERSON><PERSON><PERSON> DefaultProp_ForceModel;

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;

Create ImpulsiveBurn GOI;
GMAT GOI.CoordinateSystem = Local;
GMAT GOI.Origin = Earth;
GMAT GOI.Axes = VNB;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;

%----------------------------------------
%---------- Plots/Reports
%----------------------------------------

Create OrbitView OpenGLPlot1;
GMAT OpenGLPlot1.Add = {Earth, DefaultSC};
OpenGLPlot1.ViewScaleFactor = 5;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------
BeginMissionSequence;

Propagate 'Prop to Perigee' DefaultProp(DefaultSC) {DefaultSC.Periapsis};

% Burn in the velocity direction to reach an alternate Apoapsis point
Target 'Raise and Circularize' DC {SolveMode = Solve, ExitMode = DiscardAndContinue};
   Vary 'Vary TOI.V' DC(TOI.Element1 = 0.5, {Perturbation = 0.0001, Lower = 0, Upper = 3.14159, MaxStep = 0.2});
   Maneuver 'Apply TOI' TOI(DefaultSC);
   Propagate 'Prop to Apogee' DefaultProp(DefaultSC) {DefaultSC.Apoapsis};
   Achieve 'Achieve RMAG' DC(DefaultSC.Earth.RMAG = 42165, {Tolerance = 0.1});
   Vary 'Vary GOI.V' DC(GOI.Element1 = 0.5, {Perturbation = 0.0001, Lower = 0, Upper = 3.14159, MaxStep = 0.2});
   Maneuver 'Apply GOI' GOI(DefaultSC);
   Achieve 'Achieve ECC' DC(DefaultSC.ECC = 0, {Tolerance = 0.1});
EndTarget;  % For targeter DC

Propagate 'Prop 1 Day' DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = 86400};
