Create String myString s1 s2 s3 A1 A2 A3 formatSpec str
Create Variable testVar
Create ReportFile rf
rf.WriteHeaders = false

BeginMissionSequence

% sprintf example
s1 = 'The Delta V is : ' 
s3 = 'km/s' 
s2 = sprintf('%10s %10.7f %4s', s1, 1.2345678901,s3)
Report rf s2 
A1 = 'GMAT';
A2 = 'IS';
A3 = 'AWESOME';
formatSpec = '%3s %2s %1s';
str = sprintf(formatSpec,A1,A2,A3)
Report rf str

% strcat example
s2 = 'Put these strings' 
myString = ' together ';
s3 = strcat(s2,myString)
Report rf s3

% strcmp example
testVar = strcmp('the same','the same')
Report rf testVar
testVar = strcmp('the same','not the same')
Report rf testVar

% strfind example
s2 = 'GMAT is Awesome'
testVar = strfind(s2,'Awesome')
Report rf testVar