%
%   Ex_R2022a_TLE_Propagation.script
%
%   Propagate covariance with <PERSON><PERSON> orbiting the Moon
%


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft ExampleSat;
GMAT ExampleSat.DateFormat = UTCGregorian;
GMAT ExampleSat.Epoch = '01 Jan 2000 11:59:28.000';
GMAT ExampleSat.CoordinateSystem = EarthMJ2000Eq;
GMAT ExampleSat.DisplayStateType = Cartesian;
GMAT ExampleSat.X = 7100;
GMAT ExampleSat.Y = 0;
GMAT ExampleSat.Z = 1300;
GMAT ExampleSat.VX = 0;
GMAT ExampleSat.VY = 7.35;
GMAT ExampleSat.VZ = 1;
GMAT ExampleSat.DryMass = 850;
GMAT ExampleSat.Cd = 2.2;
GMAT ExampleSat.Cr = 1.8;
GMAT ExampleSat.DragArea = 15;
GMAT ExampleSat.SRPArea = 1;
GMAT ExampleSat.SPADDragScaleFactor = 1;
GMAT ExampleSat.SPADSRPScaleFactor = 1;
GMAT ExampleSat.AtmosDensityScaleFactor = 1;
GMAT ExampleSat.ExtendedMassPropertiesModel = 'None';
GMAT ExampleSat.EphemerisName = '../samples/SupportFiles/Ex_R2022a_TLE_Propagation_TLE.txt';
GMAT ExampleSat.NAIFId = -10003001;
GMAT ExampleSat.NAIFIdReferenceFrame = -9003001;
GMAT ExampleSat.OrbitColor = Red;
GMAT ExampleSat.TargetColor = Teal;
GMAT ExampleSat.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
GMAT ExampleSat.CdSigma = 1e+70;
GMAT ExampleSat.CrSigma = 1e+70;
GMAT ExampleSat.Id = 'ExampleSat';
GMAT ExampleSat.Attitude = CoordinateSystemFixed;
GMAT ExampleSat.SPADSRPInterpolationMethod = Bilinear;
GMAT ExampleSat.SPADSRPScaleFactorSigma = 1e+70;
GMAT ExampleSat.SPADDragInterpolationMethod = Bilinear;
GMAT ExampleSat.SPADDragScaleFactorSigma = 1e+70;
GMAT ExampleSat.AtmosDensityScaleFactorSigma = 1e+70;
GMAT ExampleSat.ModelFile = 'aura.3ds';
GMAT ExampleSat.ModelOffsetX = 0;
GMAT ExampleSat.ModelOffsetY = 0;
GMAT ExampleSat.ModelOffsetZ = 0;
GMAT ExampleSat.ModelRotationX = 0;
GMAT ExampleSat.ModelRotationY = 0;
GMAT ExampleSat.ModelRotationZ = 0;
GMAT ExampleSat.ModelScale = 1;
GMAT ExampleSat.AttitudeDisplayStateType = 'Quaternion';
GMAT ExampleSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ExampleSat.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT ExampleSat.EulerAngleSequence = '321';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator TLEProp;
GMAT TLEProp.Type = SPICESGP4;
GMAT TLEProp.InitialStepSize = 300;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create ReportFile rf;
GMAT rf.SolverIterations = Current;
GMAT rf.UpperLeft = [ 0 0 ];
GMAT rf.Size = [ 0 0 ];
GMAT rf.RelativeZOrder = 0;
GMAT rf.Maximized = false;
GMAT rf.Filename = 'Ex_R2022a_TLE_Propagation.txt';
GMAT rf.Precision = 16;
GMAT rf.Add = {ExampleSat.UTCGregorian, ExampleSat.X, ExampleSat.Y, ExampleSat.Z, ExampleSat.VX, ExampleSat.VY, ExampleSat.VZ};
GMAT rf.WriteHeaders = true;
GMAT rf.LeftJustify = On;
GMAT rf.ZeroFill = Off;
GMAT rf.FixedWidth = true;
GMAT rf.Delimiter = ' ';
GMAT rf.ColumnWidth = 23;
GMAT rf.WriteReport = true;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Propagate TLEProp(ExampleSat) {ExampleSat.ElapsedDays = 1.0};
