
%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = UTCGregorian;
GMAT Sat.Epoch = '22 Jul 2014 11:29:10.811';
GMAT Sat.CoordinateSystem = EarthMJ2000Eq;
GMAT Sat.DisplayStateType = Keplerian;
GMAT Sat.SMA = 83474.31799999999;
GMAT Sat.ECC = 0.89652;
GMAT Sat.INC = 12.4606;
GMAT Sat.RAAN = 112.8362;
GMAT Sat.AOP = 218.9805;
GMAT Sat.TA = 180;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel FM;

Create Propagator EarthPointMass;
GMAT EarthPointMass.FM = FM;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------

Create Variable I;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView EarthView;
GMAT EarthView.SolverIterations = Current;
GMAT EarthView.UpperLeft = [ 0.3685770750988142 0.01259445843828715 ];
GMAT EarthView.Size = [ 0.5573122529644269 0.8501259445843828 ];
GMAT EarthView.RelativeZOrder = 183;
GMAT EarthView.Add = {Sat, Earth};
GMAT EarthView.DrawObject = [ true true ];
GMAT EarthView.ViewPointVector = [ 0 0 30000 ];
GMAT EarthView.ViewScaleFactor = 2.5;
GMAT EarthView.ViewUpCoordinateSystem = EarthMJ2000Eq;

% -------------------------------------------------------------------------
% ---------------------------  Begin Mission Sequence ---------------------
% -------------------------------------------------------------------------
BeginMissionSequence;

% =========================================================================
%  Example:  Use For loop to Prop for 5 orbits and save ephem at every apogee
% =========================================================================
For 'For I = 1:1:5' I = 1:1:5;
   Propagate 'Prop to Apoapsis' EarthPointMass(Sat) {Sat.Apoapsis};
EndFor;

% =========================================================================
%  Example: Prop to next apogee while the ElapsedDays is less than ten days
% =========================================================================
While 'While Epoch < 26882' Sat.TAIModJulian < 26882
   Propagate 'Prop to Apoapsis' EarthPointMass(Sat) {Sat.Apoapsis};
EndWhile;

% =========================================================================
%  Example:  If the true anomaly of Sat's orbit is greater than 178 degrees, then
%  propagate to periapsis.
% =========================================================================
If 'If Sat.TA > 178' Sat.TA > 178
   Propagate 'Prop to Periapsis' EarthPointMass(Sat) {Sat.Periapsis};
EndIf;

% =========================================================================
%  Example:  If TA is < 90 prop to periapsis, otherwise, prop to apoapsis
% =========================================================================
If 'If Sat.TA > 90' Sat.TA > 90
   Propagate 'Prop to Periapsis' EarthPointMass(Sat) {Sat.Periapsis};
Else
   Propagate 'Prop to Apoapsis' EarthPointMass(Sat) {Sat.Apoapsis};
EndIf;
