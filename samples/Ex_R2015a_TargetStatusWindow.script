%General Mission Analysis Tool(GMAT) Script
%Created: 2010-09-04 09:04:32


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft geoSat;
GMAT geoSat.DateFormat = TAIModJulian;
GMAT geoSat.Epoch = '21545';
GMAT geoSat.CoordinateSystem = EarthMJ2000Eq;
GMAT geoSat.DisplayStateType = Keplerian;
GMAT geoSat.SMA = 6578.000000000003;
GMAT geoSat.ECC = 0.001000000000001135;
GMAT geoSat.INC = 28.49999999999999;
GMAT geoSat.RAAN = 66.99999999999999;
GMAT geoSat.AOP = 355.0000000000957;
GMAT geoSat.TA = 249.9999999999781;
GMAT geoSat.DryMass = 850;
GMAT geoSat.Cd = 2.2;
GMAT geoSat.Cr = 1.8;
GMAT geoSat.DragArea = 15;
GMAT geoSat.SRPArea = 1;
GMAT geoSat.NAIFId = -123456789;
GMAT geoSat.NAIFIdReferenceFrame = -123456789;
GMAT geoSat.Id = 'SatId';
GMAT geoSat.Attitude = CoordinateSystemFixed;
GMAT geoSat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT geoSat.ModelOffsetX = 0;
GMAT geoSat.ModelOffsetY = 0;
GMAT geoSat.ModelOffsetZ = 0;
GMAT geoSat.ModelRotationX = 0;
GMAT geoSat.ModelRotationY = 0;
GMAT geoSat.ModelRotationZ = 0;
GMAT geoSat.ModelScale = 3;
GMAT geoSat.AttitudeDisplayStateType = 'Quaternion';
GMAT geoSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT geoSat.AttitudeCoordinateSystem = 'EarthMJ2000Eq';


%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel AllForces;
GMAT AllForces.CentralBody = Earth;
GMAT AllForces.PrimaryBodies = {Earth};
GMAT AllForces.SRP = On;
GMAT AllForces.RelativisticCorrection = Off;
GMAT AllForces.ErrorControl = RSSStep;
GMAT AllForces.GravityField.Earth.Degree = 20;
GMAT AllForces.GravityField.Earth.Order = 20;
GMAT AllForces.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT AllForces.GravityField.Earth.TideModel = 'None';
GMAT AllForces.SRP.Flux = 1367;
GMAT AllForces.SRP.Nominal_Sun = 149597870.691;
GMAT AllForces.Drag.AtmosphereModel = MSISE90;
GMAT AllForces.Drag.F107 = 150;
GMAT AllForces.Drag.F107A = 150;
GMAT AllForces.Drag.MagneticIndex = 3;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = AllForces;
GMAT DefaultProp.Type = PrinceDormand78;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 86400;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;
%GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn MCC;
GMAT MCC.CoordinateSystem = Local;
GMAT MCC.Origin = Earth;
GMAT MCC.Axes = VNB;
GMAT MCC.Element1 = 0;
GMAT MCC.Element2 = 0;
GMAT MCC.Element3 = 0;
GMAT MCC.DecrementMass = false;
GMAT MCC.Isp = 300;
GMAT MCC.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn MOI;
GMAT MOI.CoordinateSystem = Local;
GMAT MOI.Origin = Earth;
GMAT MOI.Axes = VNB;
GMAT MOI.Element1 = 0;
GMAT MOI.Element2 = 0;
GMAT MOI.Element3 = 0;
GMAT MOI.DecrementMass = false;
GMAT MOI.Isp = 300;
GMAT MOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable I lowerBound upperBound;
GMAT lowerBound = -119;
GMAT upperBound = -117.5;






%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC.MaximumIterations = 25;
GMAT DC.DerivativeMethod = ForwardDifference;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView inertialView;
GMAT inertialView.SolverIterations = Current;
GMAT inertialView.UpperLeft = [ 0.3660377358490566 0.006493506493506494 ];
GMAT inertialView.Size = [ 0.5745283018867925 0.4636363636363636 ];
GMAT inertialView.RelativeZOrder = 942;
GMAT inertialView.Add = {geoSat, Earth};
GMAT inertialView.CoordinateSystem = EarthMJ2000Eq;
GMAT inertialView.DrawObject = [ true true ];
GMAT inertialView.DataCollectFrequency = 1;
GMAT inertialView.UpdatePlotFrequency = 50;
GMAT inertialView.NumPointsToRedraw = 200;
GMAT inertialView.ShowPlot = true;
GMAT inertialView.ViewPointReference = Earth;
GMAT inertialView.ViewPointVector = [ -90000 -90000 20000 ];
GMAT inertialView.ViewDirection = Earth;
GMAT inertialView.ViewScaleFactor = 1;
GMAT inertialView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT inertialView.ViewUpAxis = Z;
GMAT inertialView.EclipticPlane = Off;
GMAT inertialView.XYPlane = Off;
GMAT inertialView.WireFrame = Off;
GMAT inertialView.Axes = On;
GMAT inertialView.Grid = Off;
GMAT inertialView.SunLine = Off;
GMAT inertialView.UseInitialView = On;
GMAT inertialView.StarCount = 2000;
GMAT inertialView.EnableStars = On;
GMAT inertialView.EnableConstellations = On;

Create OrbitView fixedView;
GMAT fixedView.SolverIterations = Current;
GMAT fixedView.UpperLeft = [ 0.3650943396226415 0.4857142857142857 ];
GMAT fixedView.Size = [ 0.5754716981132075 0.474025974025974 ];
GMAT fixedView.RelativeZOrder = 940;
GMAT fixedView.Add = {geoSat, Earth};
GMAT fixedView.CoordinateSystem = EarthFixed;
GMAT fixedView.DrawObject = [ true true ];
GMAT fixedView.DataCollectFrequency = 1;
GMAT fixedView.UpdatePlotFrequency = 50;
GMAT fixedView.NumPointsToRedraw = 200;
GMAT fixedView.ShowPlot = true;
GMAT fixedView.ViewPointReference = Earth;
GMAT fixedView.ViewPointVector = [ -20000 -50000 0 ];
GMAT fixedView.ViewDirection = Earth;
GMAT fixedView.ViewScaleFactor = 1.2;
GMAT fixedView.ViewUpCoordinateSystem = EarthFixed;
GMAT fixedView.ViewUpAxis = Z;
GMAT fixedView.EclipticPlane = Off;
GMAT fixedView.XYPlane = Off;
GMAT fixedView.WireFrame = Off;
GMAT fixedView.Axes = On;
GMAT fixedView.Grid = Off;
GMAT fixedView.SunLine = Off;
GMAT fixedView.UseInitialView = On;
GMAT fixedView.StarCount = 2000;
GMAT fixedView.EnableStars = On;
GMAT fixedView.EnableConstellations = On;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate 'Prop to Z Crossing' DefaultProp(geoSat) {geoSat.Z = 0, StopTolerance = 1e-005};

Target 'Raise Apogee' DC {SolveMode = Solve, ExitMode = DiscardAndContinue};
   Vary 'Vary TOI.V' DC(TOI.Element1 = 1, {Perturbation = .0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = .5});
   Maneuver 'Apply TOI' TOI(geoSat);
   Propagate 'Prop To Apogee' DefaultProp(geoSat) {geoSat.Apoapsis};
   Achieve 'Achieve RMAG' DC(geoSat.RMAG = 85000, {Tolerance = .1});
EndTarget;  % For targeter DC

Propagate 'Prop to Perigee' DefaultProp(geoSat) {geoSat.Earth.Periapsis};
Propagate 'Prop to Plane Crossing' DefaultProp(geoSat) {geoSat.Z = 0, StopTolerance = 1e-5};

Target 'Change Plane/Perigee' DC {SolveMode = Solve, ExitMode = DiscardAndContinue};
   Vary 'Vary MCC.V' DC(MCC.Element1 = 0.1, {Perturbation = .0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.5});
   Vary 'Vary MCC.N' DC(MCC.Element2 = 0.1, {Perturbation = .0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.5});
   Maneuver 'Apply MCC' MCC(geoSat);
   Propagate 'Prop to Perigee' DefaultProp(geoSat) {geoSat.Periapsis};
   Achieve 'Apply INC' DC(geoSat.EarthMJ2000Eq.INC = 2, {Tolerance = .001});
   Achieve 'Achieve RMAG' DC(geoSat.RMAG = 42195, {Tolerance = .001});
EndTarget;  % For targeter DC

Target 'Lower Apogee' DC {SolveMode = Solve, ExitMode = DiscardAndContinue};
   Vary 'Vary MOI.V' DC(MOI.Element1 = -0.1, {Perturbation = .0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = .1});
   Maneuver 'Apply MOI' MOI(geoSat);
   Achieve 'Achieve SMA' DC(geoSat.Earth.SMA = 42166.90, {Tolerance = .001});
EndTarget;  % For targeter DC

Propagate 'Prop 10 days' DefaultProp(geoSat) {geoSat.ElapsedDays = 10};








