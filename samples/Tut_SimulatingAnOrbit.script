%General Mission Analysis Tool(GMAT) Script
%Created: 2011-11-23 10:08:38


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = UTCGregorian;
GMAT Sat.Epoch = '22 Jul 2014 11:29:10.811';
GMAT Sat.CoordinateSystem = EarthMJ2000Eq;
GMAT Sat.DisplayStateType = Keplerian;
GMAT Sat.SMA = 83474.31800000001;
GMAT Sat.ECC = 0.89652;
GMAT Sat.INC = 12.4606;
GMAT Sat.RAAN = 292.8362;
GMAT Sat.AOP = 218.9805;
GMAT Sat.TA = 180;
GMAT Sat.DryMass = 850;
GMAT Sat.Cd = 2.2;
GMAT Sat.Cr = 1.8;
GMAT Sat.DragArea = 15;
GMAT Sat.SRPArea = 1;
GMAT Sat.NAIFId = -123456789;
GMAT Sat.NAIFIdReferenceFrame = -123456789;
GMAT Sat.Id = 'SatId';
GMAT Sat.Attitude = CoordinateSystemFixed;
GMAT Sat.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT Sat.ModelOffsetX = 0;
GMAT Sat.ModelOffsetY = 0;
GMAT Sat.ModelOffsetZ = 0;
GMAT Sat.ModelRotationX = 0;
GMAT Sat.ModelRotationY = 0;
GMAT Sat.ModelRotationZ = 0;
GMAT Sat.ModelScale = 1;
GMAT Sat.AttitudeDisplayStateType = 'Quaternion';
GMAT Sat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT Sat.AttitudeCoordinateSystem = 'EarthMJ2000Eq';

Create ForceModel LowEarthProp_ForceModel;
GMAT LowEarthProp_ForceModel.CentralBody = Earth;
GMAT LowEarthProp_ForceModel.PrimaryBodies = {Earth};
GMAT LowEarthProp_ForceModel.PointMasses = {Luna, Sun};
GMAT LowEarthProp_ForceModel.SRP = On;
GMAT LowEarthProp_ForceModel.RelativisticCorrection = Off;
GMAT LowEarthProp_ForceModel.ErrorControl = RSSStep;
GMAT LowEarthProp_ForceModel.GravityField.Earth.Degree = 10;
GMAT LowEarthProp_ForceModel.GravityField.Earth.Order = 10;
GMAT LowEarthProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT LowEarthProp_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT LowEarthProp_ForceModel.Drag.AtmosphereModel = 'JacchiaRoberts';
GMAT LowEarthProp_ForceModel.Drag.F107 = 150;
GMAT LowEarthProp_ForceModel.Drag.F107A = 150;
GMAT LowEarthProp_ForceModel.Drag.MagneticIndex = 3;
GMAT LowEarthProp_ForceModel.Drag.F107 = 150;
GMAT LowEarthProp_ForceModel.Drag.F107A = 150;
GMAT LowEarthProp_ForceModel.Drag.MagneticIndex = 3;
GMAT LowEarthProp_ForceModel.SRP.Flux = 1367;
GMAT LowEarthProp_ForceModel.SRP.Nominal_Sun = 149597870.691;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LowEarthProp;
GMAT LowEarthProp.FM = LowEarthProp_ForceModel;
GMAT LowEarthProp.Type = RungeKutta89;
GMAT LowEarthProp.InitialStepSize = 60;
GMAT LowEarthProp.Accuracy = 9.999999999999999e-012;
GMAT LowEarthProp.MinStep = 0.001;
GMAT LowEarthProp.MaxStep = 2700;
GMAT LowEarthProp.MaxStepAttempts = 50;
GMAT LowEarthProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn DefaultIB;
GMAT DefaultIB.CoordinateSystem = Local;
GMAT DefaultIB.Origin = Earth;
GMAT DefaultIB.Axes = VNB;
GMAT DefaultIB.Element1 = 0;
GMAT DefaultIB.Element2 = 0;
GMAT DefaultIB.Element3 = 0;
GMAT DefaultIB.DecrementMass = false;
GMAT DefaultIB.Isp = 300;
GMAT DefaultIB.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0 0 ];
GMAT DefaultOrbitView.Size = [ 0 0 ];
GMAT DefaultOrbitView.RelativeZOrder = 0;
GMAT DefaultOrbitView.Add = {Sat, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ -60000 30000 20000 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = Z;
GMAT DefaultOrbitView.EclipticPlane = Off;
GMAT DefaultOrbitView.XYPlane = Off;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate LowEarthProp(Sat) {Sat.Earth.Periapsis};
