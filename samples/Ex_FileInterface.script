% This script is a simple example of reading spacecraft initial state date from a
% data file instead of setting it in the script.

% Create default resources
Create Spacecraft sc
Create Propagator prop

% Link to the data file
Create FileInterface tvhf
tvhf.Filename = '../samples/SupportFiles/Ex_FileInterface_TVHF.sv'
tvhf.Format = 'TVHF_ASCII'

% Set up the visualization
Create OrbitView EarthView
EarthView.Add = {sc, Earth}
EarthView.CoordinateSystem = EarthMJ2000Eq
EarthView.ViewPointReference = Earth
EarthView.ViewPointVector = [ 0 0 2e5 ]
EarthView.ViewDirection = Earth
EarthView.ViewScaleFactor = 1
EarthView.ViewUpCoordinateSystem = EarthMJ2000Eq
EarthView.ViewUpAxis = 'Z'

% The Mission Sequence
BeginMissionSequence

% Populate sc with data from file
Set 'Read initial state' sc tvhf

% Propagate
Propagate 'Propagate 2 days' prop(sc) {sc.ElapsedDays = 2}
