
% Demonstration script for the Python interface
%
% This script uses GMAT's Python interface to compute the orbital angular momentum

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create <PERSON><PERSON> sat;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------

Create Array state[1,3] vel[1,3] crossProd[1,3];
Create Variable wx wy;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------
BeginMissionSequence;

GMAT state(1) = sat.X;
GMAT state(2) = sat.Y;
GMAT state(3) = sat.Z;

GMAT vel(1) = sat.VX;
GMAT vel(2) = sat.VY;
GMAT vel(3) = sat.VZ;

GMAT [crossProd] = Python.ArrayFunctions.cross(state, vel);

