%General Mission Analysis Tool(GMAT) Script
%Created: 2010-09-05 10:28:31


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = UTCGregorian;
GMAT DefaultSC.Epoch = '01 Jul 2000 11:59:28.000';
GMAT DefaultSC.SMA = 8999.999999999995;
GMAT DefaultSC.ECC = 0.02454974900598272;
GMAT DefaultSC.INC = 12.85008005658097;
GMAT DefaultSC.RAAN = 306.6148021947984;
GMAT DefaultSC.AOP = 314.1905515359913;
GMAT DefaultSC.TA = 99.88774933204955;
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.AttitudeDisplayStateType = 'EulerAngles';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.EulerAngleSequence = '321';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.MaxStep = 1;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView EarthView;
GMAT EarthView.SolverIterations = Current;
GMAT EarthView.UpperLeft = [ 0.3935294117647059 0.0636604774535809 ];
GMAT EarthView.Size = [ 0.5347058823529411 0.9230769230769231 ];
GMAT EarthView.Maximized = false;
GMAT EarthView.Add = {DefaultSC, Earth};
GMAT EarthView.DrawObject = [ true true ];
GMAT EarthView.ViewPointVector = [ 15000 15000 0 ];

GMAT EarthView.ViewScaleFactor = 1.8;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Propagate 'Prop 10000 s' DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = 10000.0};
