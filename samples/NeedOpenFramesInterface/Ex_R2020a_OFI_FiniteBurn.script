%  Script Mission - Finite Burn Examples  
%
%  This script demonstrates how to set up tanks and thrusters, and use them
%  in a finite maneuver sequence.  
%


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

% -------------------------------------------------------------------------
% --------------------------- Create Objects ------------------------------
% -------------------------------------------------------------------------

%  Create a default spacecraft
Create Spacecraft Sc;
GMAT Sc.DateFormat = TAIModJulian;
GMAT Sc.Epoch = '21545';
GMAT Sc.CoordinateSystem = EarthMJ2000Eq;
GMAT Sc.DisplayStateType = Cartesian;
GMAT Sc.X = 7100;
GMAT Sc.Y = 0;
GMAT Sc.Z = 1300;
GMAT Sc.VX = 0;
GMAT Sc.VY = 7.35;
GMAT Sc.VZ = 1;
GMAT Sc.DryMass = 850;
GMAT Sc.Cd = 2.2;
GMAT Sc.Cr = 1.8;
GMAT Sc.DragArea = 15;
GMAT Sc.SRPArea = 1;
GMAT Sc.SPADDragScaleFactor = 1;
GMAT Sc.SPADSRPScaleFactor = 1;
GMAT Sc.Tanks = {tank1};
GMAT Sc.Thrusters = {engine1};
GMAT Sc.NAIFId = -123456789;
GMAT Sc.NAIFIdReferenceFrame = -123456789;
GMAT Sc.OrbitColor = Red;
GMAT Sc.TargetColor = Teal;
GMAT Sc.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT Sc.CdSigma = 1e+070;
GMAT Sc.CrSigma = 1e+070;
GMAT Sc.Id = 'SatId';
GMAT Sc.Attitude = CoordinateSystemFixed;
GMAT Sc.SPADSRPInterpolationMethod = Bilinear;
GMAT Sc.SPADSRPScaleFactorSigma = 1e+070;
GMAT Sc.SPADDragInterpolationMethod = Bilinear;
GMAT Sc.SPADDragScaleFactorSigma = 1e+070;
GMAT Sc.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT Sc.ModelOffsetX = 0;
GMAT Sc.ModelOffsetY = 0;
GMAT Sc.ModelOffsetZ = 0;
GMAT Sc.ModelRotationX = 0;
GMAT Sc.ModelRotationY = 0;
GMAT Sc.ModelRotationZ = 0;
GMAT Sc.ModelScale = 3;
GMAT Sc.AttitudeDisplayStateType = 'Quaternion';
GMAT Sc.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT Sc.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT Sc.EulerAngleSequence = '321';

%----------------------------------------
%---------- Hardware Components
%----------------------------------------

%  Create a fuel tank and name it tank1
%          Here we create a fuel tank and set up its physical properties
%          including Temperature, Fuel Mass, Fuel Density etc.
Create ChemicalTank tank1;
GMAT tank1.AllowNegativeFuelMass = false;
GMAT tank1.FuelMass = 725;
GMAT tank1.Pressure = 1200;
GMAT tank1.Temperature = 20;
GMAT tank1.RefTemperature = 12;
GMAT tank1.Volume = 0.8;
GMAT tank1.FuelDensity = 1029;
GMAT tank1.PressureModel = PressureRegulated;

%  Create a thruster
%         Here we create a thruster and tell the thruster which tank to
%         draw fuel from.  We also set up the direction of the thruster.
%         Currently, you specify the thruster orientation with respect to
%         the spacecraft VNB or EarthMJ2000Eq systems.  This will change
%         when attitude capabilities are added to GMAT.
Create ChemicalThruster engine1;
GMAT engine1.CoordinateSystem = Local;
GMAT engine1.Origin = Earth;
GMAT engine1.Axes = VNB;
GMAT engine1.ThrustDirection1 = 1;
GMAT engine1.ThrustDirection2 = 0;
GMAT engine1.ThrustDirection3 = 0;
GMAT engine1.DutyCycle = 1;
GMAT engine1.ThrustScaleFactor = 1;
GMAT engine1.DecrementMass = false;
GMAT engine1.Tank = {tank1};
GMAT engine1.MixRatio = [ 1 ];
GMAT engine1.GravitationalAccel = 9.810000000000001;
GMAT engine1.C1 = 70;
GMAT engine1.C2 = 0;
GMAT engine1.C3 = 0;
GMAT engine1.C4 = 0;
GMAT engine1.C5 = 0;
GMAT engine1.C6 = 0;
GMAT engine1.C7 = 0;
GMAT engine1.C8 = 0;
GMAT engine1.C9 = 0;
GMAT engine1.C10 = 0;
GMAT engine1.C11 = 0;
GMAT engine1.C12 = 0;
GMAT engine1.C13 = 0;
GMAT engine1.C14 = 0;
GMAT engine1.C15 = 0;
GMAT engine1.C16 = 0;
GMAT engine1.K1 = 1500;
GMAT engine1.K2 = 0;
GMAT engine1.K3 = 0;
GMAT engine1.K4 = 0;
GMAT engine1.K5 = 0;
GMAT engine1.K6 = 0;
GMAT engine1.K7 = 0;
GMAT engine1.K8 = 0;
GMAT engine1.K9 = 0;
GMAT engine1.K10 = 0;
GMAT engine1.K11 = 0;
GMAT engine1.K12 = 0;
GMAT engine1.K13 = 0;
GMAT engine1.K14 = 0;
GMAT engine1.K15 = 0;
GMAT engine1.K16 = 0;





%----------------------------------------
%---------- ForceModels
%----------------------------------------



%  Create a force model
Create ForceModel fm;
GMAT fm.CentralBody = Earth;
GMAT fm.PointMasses = {Earth};
GMAT fm.Drag = None;
GMAT fm.SRP = Off;
GMAT fm.RelativisticCorrection = Off;
GMAT fm.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

%  Create a propagator
Create Propagator prop;
GMAT prop.FM = fm;
GMAT prop.Type = RungeKutta89;
GMAT prop.InitialStepSize = 60;
GMAT prop.Accuracy = 9.999999999999999e-012;
GMAT prop.MinStep = 0.001;
GMAT prop.MaxStep = 2700;
GMAT prop.MaxStepAttempts = 50;
GMAT prop.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%  Create a thruster
%         Here we create a finite maneuver.  We create the maneuver and
%         give it a name first.  We need to tell the maneuver which
%         thrusters to use, and which tank to draw from.  Currently you can
%         have multiple thrusters, but only one tank, from which both
%         thrusters draw fuel.  We can also apply a burn scale factor.  The
%         thrust from each thruster is added together, and the sum is
%         multiplied by BurnScaleFactor.
Create FiniteBurn fb;
GMAT fb.Thrusters = {engine1};
GMAT fb.ThrottleLogicAlgorithm = 'MaxNumberOfThrusters';

%----------------------------------------
%---------- Subscribers
%----------------------------------------

%  Create an XYPlot  
Create XYPlot energy;
GMAT energy.SolverIterations = Current;
GMAT energy.UpperLeft = [ 0 0 ];
GMAT energy.Size = [ 0.49 0.49 ];
GMAT energy.RelativeZOrder = 12;
GMAT energy.Maximized = true;
GMAT energy.XVariable = Sc.Epoch.A1ModJulian;
GMAT energy.YVariables = {Sc.Energy};
GMAT energy.ShowGrid = true;
GMAT energy.ShowPlot = true;

Create XYPlot SMAvsTime;
GMAT SMAvsTime.SolverIterations = Current;
GMAT SMAvsTime.UpperLeft = [ 0 0.5 ];
GMAT SMAvsTime.Size = [ 0.49 0.49 ];
GMAT SMAvsTime.RelativeZOrder = 17;
GMAT SMAvsTime.Maximized = true;
GMAT SMAvsTime.XVariable = Sc.Epoch.A1ModJulian;
GMAT SMAvsTime.YVariables = {Sc.SMA};
GMAT SMAvsTime.ShowGrid = true;
GMAT SMAvsTime.ShowPlot = true;

Create OpenFramesInterface SatOpenFrames;
GMAT SatOpenFrames.SolverIterations = Current;
GMAT SatOpenFrames.UpperLeft = [ 0.5 0.5 ];
GMAT SatOpenFrames.Size = [ 0.49 0.49 ];
GMAT SatOpenFrames.RelativeZOrder = 34;
GMAT SatOpenFrames.Maximized = true;
GMAT SatOpenFrames.Add = {Sc, Earth, Sun};
GMAT SatOpenFrames.View = {CoordinateSystemView1, EarthView1, ScView1};
GMAT SatOpenFrames.CoordinateSystem = EarthMJ2000Eq;
GMAT SatOpenFrames.DrawObject = [ true true true ];
GMAT SatOpenFrames.DrawTrajectory = [ true true true ];
GMAT SatOpenFrames.DrawAxes = [ false false false ];
GMAT SatOpenFrames.DrawXYPlane = [ false false false ];
GMAT SatOpenFrames.DrawLabel = [ true true true ];
GMAT SatOpenFrames.DrawUsePropLabel = [ false false false ];
GMAT SatOpenFrames.DrawCenterPoint = [ true true true ];
GMAT SatOpenFrames.DrawEndPoints = [ true true true ];
GMAT SatOpenFrames.DrawVelocity = [ false false false ];
GMAT SatOpenFrames.DrawGrid = [ false false false ];
GMAT SatOpenFrames.DrawLineWidth = [ 2 2 2 ];
GMAT SatOpenFrames.DrawMarkerSize = [ 10 10 10 ];
GMAT SatOpenFrames.DrawFontSize = [ 14 14 14 ];
GMAT SatOpenFrames.Axes = On;
GMAT SatOpenFrames.AxesLength = 1;
GMAT SatOpenFrames.AxesLabels = On;
GMAT SatOpenFrames.FrameLabel = Off;
GMAT SatOpenFrames.XYPlane = On;
GMAT SatOpenFrames.EclipticPlane = Off;
GMAT SatOpenFrames.EnableStars = On;
GMAT SatOpenFrames.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT SatOpenFrames.StarCount = 40000;
GMAT SatOpenFrames.MinStarMag = -2;
GMAT SatOpenFrames.MaxStarMag = 6;
GMAT SatOpenFrames.MinStarPixels = 1;
GMAT SatOpenFrames.MaxStarPixels = 10;
GMAT SatOpenFrames.MinStarDimRatio = 0.5;
GMAT SatOpenFrames.ShowPlot = true;
GMAT SatOpenFrames.ShowToolbar = true;
GMAT SatOpenFrames.SolverIterLastN = 1;
GMAT SatOpenFrames.ShowVR = false;
GMAT SatOpenFrames.PlaybackTimeScale = 3600;
GMAT SatOpenFrames.MultisampleAntiAliasing = On;
GMAT SatOpenFrames.MSAASamples = 2;
GMAT SatOpenFrames.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right'};

%----------------------------------------
%---------- User Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ 50000 50000 50000 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.CurrentUp = [ 0 0 1 ];
GMAT CoordinateSystemView1.FOVy = 45;

Create OpenFramesView EarthView1;
GMAT EarthView1.ViewFrame = Earth;
GMAT EarthView1.ViewTrajectory = Off;
GMAT EarthView1.InertialFrame = Off;
GMAT EarthView1.SetDefaultLocation = Off;
GMAT EarthView1.SetCurrentLocation = Off;
GMAT EarthView1.FOVy = 45;

Create OpenFramesView ScView1;
GMAT ScView1.ViewFrame = Sc;
GMAT ScView1.ViewTrajectory = Off;
GMAT ScView1.InertialFrame = Off;
GMAT ScView1.SetDefaultLocation = Off;
GMAT ScView1.SetCurrentLocation = Off;
GMAT ScView1.FOVy = 45;

% -------------------------------------------------------------------------
% --------------------------- Mission Sequence ----------------------------
% -------------------------------------------------------------------------
BeginMissionSequence;
%  Propagate for 1/10 of a day, without thrusters on.
Propagate 'Prop 0.1 Days' prop(Sc) {Sc.ElapsedSecs = 8640};

%  Turn on thrusters....they will remain on through all events until the
%  "EndFiniteBurn fb(Sc)" command is executed.
BeginFiniteBurn 'Turn on Thruster' fb(Sc);

%  Propagate for 5 days, while thrusters are turned on.
Propagate 'Prop 1 day' prop(Sc) {Sc.ElapsedDays = 1};

%  Turn off thrusters   
EndFiniteBurn 'Turn off Thruster' fb(Sc);

%  Propagate for 5 days
Propagate 'Prop 1 Day' prop(Sc) {Sc.ElapsedDays = 1};






