%  Script Mission - Hohmann Transfer Example
%
%  This script demonstrates how to target a Hohmann Transfer


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = TAIModJulian;
GMAT DefaultSC.Epoch = '21545';
GMAT DefaultSC.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.DisplayStateType = Cartesian;
GMAT DefaultSC.X = 7100;
GMAT DefaultSC.Y = 0;
GMAT DefaultSC.Z = 1300;
GMAT DefaultSC.VX = 0;
GMAT DefaultSC.VY = 7.35;
GMAT DefaultSC.VZ = 1;
GMAT DefaultSC.DryMass = 850;
GMAT DefaultSC.Cd = 2.2;
GMAT DefaultSC.Cr = 1.8;
GMAT DefaultSC.DragArea = 15;
GMAT DefaultSC.SRPArea = 1;
GMAT DefaultSC.SPADDragScaleFactor = 1;
GMAT DefaultSC.SPADSRPScaleFactor = 1;
GMAT DefaultSC.NAIFId = -10003001;
GMAT DefaultSC.NAIFIdReferenceFrame = -9003001;
GMAT DefaultSC.OrbitColor = Red;
GMAT DefaultSC.TargetColor = Teal;
GMAT DefaultSC.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT DefaultSC.CdSigma = 1e+070;
GMAT DefaultSC.CrSigma = 1e+070;
GMAT DefaultSC.Id = 'SatId';
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.SPADSRPInterpolationMethod = Bilinear;
GMAT DefaultSC.SPADSRPScaleFactorSigma = 1e+070;
GMAT DefaultSC.SPADDragInterpolationMethod = Bilinear;
GMAT DefaultSC.SPADDragScaleFactorSigma = 1e+070;
GMAT DefaultSC.ModelFile = 'aura.3ds';
GMAT DefaultSC.ModelOffsetX = 0;
GMAT DefaultSC.ModelOffsetY = 0;
GMAT DefaultSC.ModelOffsetZ = 0;
GMAT DefaultSC.ModelRotationX = 0;
GMAT DefaultSC.ModelRotationY = 0;
GMAT DefaultSC.ModelRotationZ = 0;
GMAT DefaultSC.ModelScale = 1;
GMAT DefaultSC.AttitudeDisplayStateType = 'Quaternion';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.EulerAngleSequence = '321';





%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PrimaryBodies = {Earth};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;
GMAT DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.Order = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.StmLimit = 100;
GMAT DefaultProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = RungeKutta89;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn GOI;
GMAT GOI.CoordinateSystem = Local;
GMAT GOI.Origin = Earth;
GMAT GOI.Axes = VNB;
GMAT GOI.Element1 = 0;
GMAT GOI.Element2 = 0;
GMAT GOI.Element3 = 0;
GMAT GOI.DecrementMass = false;
GMAT GOI.Isp = 300;
GMAT GOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC.data';
GMAT DC.MaximumIterations = 25;
GMAT DC.DerivativeMethod = ForwardDifference;
GMAT DC.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OpenFramesInterface OpenFrames1;
GMAT OpenFrames1.SolverIterations = Current;
GMAT OpenFrames1.UpperLeft = [ 0 0 ];
GMAT OpenFrames1.Size = [ 0.4978567054500919 0.9939320388349514 ];
GMAT OpenFrames1.RelativeZOrder = 222;
GMAT OpenFrames1.Maximized = false;
GMAT OpenFrames1.Add = {DefaultSC, Earth, Sun};
GMAT OpenFrames1.View = {CoordinateSystemView1, EarthView1, DefaultSCView1};
GMAT OpenFrames1.CoordinateSystem = EarthMJ2000Eq;
GMAT OpenFrames1.DrawObject = [ true true false ];
GMAT OpenFrames1.DrawTrajectory = [ true true false ];
GMAT OpenFrames1.DrawAxes = [ false false false ];
GMAT OpenFrames1.DrawXYPlane = [ false false false ];
GMAT OpenFrames1.DrawLabel = [ true true false ];
GMAT OpenFrames1.DrawUsePropLabel = [ false false false ];
GMAT OpenFrames1.DrawCenterPoint = [ true true false ];
GMAT OpenFrames1.DrawEndPoints = [ true true false ];
GMAT OpenFrames1.DrawVelocity = [ false false false ];
GMAT OpenFrames1.DrawGrid = [ false false false ];
GMAT OpenFrames1.DrawLineWidth = [ 2 2 2 ];
GMAT OpenFrames1.DrawMarkerSize = [ 10 10 10 ];
GMAT OpenFrames1.DrawFontSize = [ 14 14 14 ];
GMAT OpenFrames1.Axes = On;
GMAT OpenFrames1.AxesLength = 1;
GMAT OpenFrames1.AxesLabels = On;
GMAT OpenFrames1.FrameLabel = Off;
GMAT OpenFrames1.XYPlane = On;
GMAT OpenFrames1.EclipticPlane = Off;
GMAT OpenFrames1.EnableStars = On;
GMAT OpenFrames1.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT OpenFrames1.StarCount = 40000;
GMAT OpenFrames1.MinStarMag = -2;
GMAT OpenFrames1.MaxStarMag = 6;
GMAT OpenFrames1.MinStarPixels = 1;
GMAT OpenFrames1.MaxStarPixels = 10;
GMAT OpenFrames1.MinStarDimRatio = 0.5;
GMAT OpenFrames1.ShowPlot = true;
GMAT OpenFrames1.ShowToolbar = true;
GMAT OpenFrames1.SolverIterLastN = 1;
GMAT OpenFrames1.ShowVR = false;
GMAT OpenFrames1.PlaybackTimeScale = 3600;
GMAT OpenFrames1.MultisampleAntiAliasing = On;
GMAT OpenFrames1.MSAASamples = 2;
GMAT OpenFrames1.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right'};

%----------------------------------------
%---------- User Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = On;
GMAT CoordinateSystemView1.DefaultEye = [ 335.78274069 -1543.63919451 118501.88910654 ];
GMAT CoordinateSystemView1.DefaultCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.DefaultUp = [ 0.99991256 -0.0128788 -0.00300108 ];
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ 335.7827406916811 -1543.63919451052 118501.8891065422 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 2.842170943040401e-13 -4.547473508864641e-13 1.455191522836685e-11 ];
GMAT CoordinateSystemView1.CurrentUp = [ 0.9999125612384814 -0.01287879649157642 -0.003001079543866816 ];
GMAT CoordinateSystemView1.FOVy = 45;

Create OpenFramesView EarthView1;
GMAT EarthView1.ViewFrame = Earth;
GMAT EarthView1.ViewTrajectory = Off;
GMAT EarthView1.InertialFrame = Off;
GMAT EarthView1.SetDefaultLocation = Off;
GMAT EarthView1.SetCurrentLocation = Off;
GMAT EarthView1.FOVy = 45;

Create OpenFramesView DefaultSCView1;
GMAT DefaultSCView1.ViewFrame = DefaultSC;
GMAT DefaultSCView1.ViewTrajectory = Off;
GMAT DefaultSCView1.InertialFrame = Off;
GMAT DefaultSCView1.SetDefaultLocation = Off;
GMAT DefaultSCView1.SetCurrentLocation = Off;
GMAT DefaultSCView1.FOVy = 45;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------
BeginMissionSequence;

GMAT DefaultSC.OrbitColor = 'Yellow';
Propagate 'Prop to Perigee' DefaultProp(DefaultSC) {DefaultSC.Periapsis};

% Burn in the velocity direction to reach an alternate Apoapsis point
Target 'Raise and Circularize' DC {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary TOI.V' DC(TOI.Element1 = 0.5, {Perturbation = 0.0001, Lower = 0, Upper = 3.14159, MaxStep = 0.2, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply TOI' TOI(DefaultSC);
   Propagate 'Prop to Apogee' DefaultProp(DefaultSC) {DefaultSC.Apoapsis, OrbitColor = [255 0 0]};
   Achieve 'Achieve RMAG' DC(DefaultSC.Earth.RMAG = 42165, {Tolerance = 0.1});
   Vary 'Vary GOI.V' DC(GOI.Element1 = 0.5, {Perturbation = 0.0001, Lower = 0, Upper = 3.14159, MaxStep = 0.2, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply GOI' GOI(DefaultSC);
   Achieve 'Achieve ECC' DC(DefaultSC.ECC = 0, {Tolerance = 0.1});
EndTarget;  % For targeter DC
GMAT DefaultSC.OrbitColor = 'Blue';
Propagate 'Prop 1 Day' DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = 86400};
