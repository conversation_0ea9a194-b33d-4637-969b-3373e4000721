%General Mission Analysis Tool(GMAT) Script
%Created: 2018-03-20 17:21:06
%----------------------------------------
%---------- Calculated Points
%----------------------------------------

Create Barycenter <PERSON>ultBC;
GMAT DefaultBC.BodyNames = {Earth, Luna};

Create LibrationPoint ESL1;
GMAT ESL1.Primary = Sun;
GMAT ESL1.Secondary = Earth;
GMAT ESL1.Point = L1;

%----------------------------------------
%---------- Space<PERSON>
%----------------------------------------

Create Spacecraft ACE;
GMAT ACE.DateFormat = UTCGregorian;
GMAT ACE.Epoch = '09 Dec 2005 13:00:00.000';
GMAT ACE.CoordinateSystem = EarthMJ2000Eq;
GMAT ACE.DisplayStateType = Cartesian;
GMAT ACE.X = -528665.5773210001;
GMAT ACE.Y = -1325445.164527;
GMAT ACE.Z = -525969.843578;
GMAT ACE.VX = 0.240435056;
GMAT ACE.VY = -0.068342458;
GMAT ACE.VZ = 0.0315715361;
GMAT ACE.DryMass = 850;
GMAT ACE.Cd = 2.2;
GMAT ACE.Cr = 1.8;
GMAT ACE.DragArea = 15;
GMAT ACE.SRPArea = 1;

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PointMasses = {Sun, Earth, Luna};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator EarthSunMoon;
GMAT EarthSunMoon.FM = DefaultProp_ForceModel;
GMAT EarthSunMoon.Type = RungeKutta89;
GMAT EarthSunMoon.InitialStepSize = 60;
GMAT EarthSunMoon.Accuracy = 9.999999999999999e-012;
GMAT EarthSunMoon.MinStep = 0;
GMAT EarthSunMoon.MaxStep = 40000;
GMAT EarthSunMoon.MaxStepAttempts = 40000;
GMAT EarthSunMoon.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn MCC;
GMAT MCC.CoordinateSystem = EarthSunRotCS;
GMAT MCC.Element1 = 0;
GMAT MCC.Element2 = 0;
GMAT MCC.Element3 = 0;
GMAT MCC.DecrementMass = false;
GMAT MCC.Isp = 300;
GMAT MCC.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunL1CS;
GMAT EarthSunL1CS.Origin = ESL1;
GMAT EarthSunL1CS.Axes = ObjectReferenced;
GMAT EarthSunL1CS.XAxis = R;
GMAT EarthSunL1CS.ZAxis = N;
GMAT EarthSunL1CS.Primary = Sun;
GMAT EarthSunL1CS.Secondary = Earth;

Create CoordinateSystem EarthSunRotCS;
GMAT EarthSunRotCS.Origin = Earth;
GMAT EarthSunRotCS.Axes = ObjectReferenced;
GMAT EarthSunRotCS.XAxis = R;
GMAT EarthSunRotCS.ZAxis = N;
GMAT EarthSunRotCS.Primary = Sun;
GMAT EarthSunRotCS.Secondary = Earth;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DefaultDC;

%----------------------------------------------------------------------
%----------------------  Create OpenGL Plots -------------------------
%----------------------------------------------------------------------

Create OpenFramesInterface L1View;
GMAT L1View.SolverIterations = Current;
GMAT L1View.UpperLeft = [ 0.35 0.01 ];
GMAT L1View.Size = [ 0.52 0.48 ];
GMAT L1View.RelativeZOrder = 29;
GMAT L1View.Add = {ACE, Earth, Sun};
GMAT L1View.View = {CoordinateSystemView1};
GMAT L1View.CoordinateSystem = EarthSunL1CS;
GMAT L1View.DrawObject = [ true true true ];
GMAT L1View.DrawTrajectory = [ true true true ];

Create OpenFramesInterface EarthSunRotView;
GMAT EarthSunRotView.SolverIterations = Current;
GMAT EarthSunRotView.UpperLeft = [ 0.35 0.51 ];
GMAT EarthSunRotView.Size = [ 0.52 0.48 ];
GMAT EarthSunRotView.RelativeZOrder = 28;
GMAT EarthSunRotView.Add = {ACE, Earth, Luna, Sun};
GMAT EarthSunRotView.View = {CoordinateSystemView2, ACEView2};
GMAT EarthSunRotView.CoordinateSystem = EarthSunRotCS;
GMAT EarthSunRotView.DrawObject = [ true true ];
GMAT EarthSunRotView.DrawTrajectory = [ true true true true ];

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable I;


%----------------------------------------
%---------- View Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ -28500 -1000 520000 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.CurrentUp = [ 1 0 0 ];

Create OpenFramesView CoordinateSystemView2;
GMAT CoordinateSystemView2.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView2.ViewTrajectory = Off;
GMAT CoordinateSystemView2.InertialFrame = Off;
GMAT CoordinateSystemView2.SetDefaultLocation = Off;
GMAT CoordinateSystemView2.SetCurrentLocation = On;
GMAT CoordinateSystemView2.CurrentEye = [ -3000000 -1500000 1000000 ];
GMAT CoordinateSystemView2.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView2.CurrentUp = [ 0 0 1 ];

Create OpenFramesView ACEView2;
GMAT ACEView2.ViewFrame = ACE;
GMAT ACEView2.ViewTrajectory = Off;
GMAT ACEView2.InertialFrame = Off;
GMAT ACEView2.SetDefaultLocation = Off;
GMAT ACEView2.SetCurrentLocation = Off;

%----------------------------------------------------------------------
%------------------------ The Mission Sequence ------------------------
%----------------------------------------------------------------------

BeginMissionSequence;

% Comment:  This is for simple example purposes and results in a Delta v
% that is much too high!  Delta V's should be on the order of cm/s.  The
% targeting scheme is beyond the scope of this simple demo. -SPH
For 'For I = 1:7' I = 1:1:7;
   Target 'Target Loop' DefaultDC {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
      Vary 'Vary MCC.V' DefaultDC(MCC.Element1 = MCC.Element1, {Perturbation = 1e-5, Lower = -0.1, Upper = 0.1, MaxStep = 0.001, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
      Maneuver 'Apply MCC' MCC(ACE);
      Propagate 'Prop to Y = 0' EarthSunMoon(ACE) {ACE.EarthSunL1CS.Y = 0, StopTolerance = 1e-05};
      Achieve 'Achieve VX = 0' DefaultDC(ACE.EarthSunL1CS.VX = 0, {Tolerance = 1e-005});
   EndTarget;  % For targeter DefaultDC
EndFor;




