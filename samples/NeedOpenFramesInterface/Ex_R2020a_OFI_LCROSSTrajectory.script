%General Mission Analysis Tool(GMAT) Script
%Created: 2018-03-23 09:48:06


%----------------------------------------
%---------- Spacecraft
%----------------------------------------
%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LRO;
GMAT LRO.DateFormat = UTCGregorian;
GMAT LRO.Epoch = '01 Jan 2000 11:59:28.000';

GMAT LRO.CoordinateSystem = EarthMJ2000Eq;
GMAT LRO.DisplayStateType = Cartesian;
GMAT LRO.X = 7100;
GMAT LRO.Y = 0;
GMAT LRO.Z = 1300;
GMAT LRO.VX = 0;
GMAT LRO.VY = 7.35;
GMAT LRO.VZ = 1;
GMAT LRO.DryMass = 850;
GMAT LRO.Cd = 2.2;
GMAT LRO.Cr = 1.8;
GMAT LRO.DragArea = 15;
GMAT LRO.SRPArea = 1;
GMAT LRO.SPADDragScaleFactor = 1;
GMAT LRO.SPADSRPScaleFactor = 1;
GMAT LRO.NAIFId = -10008001;
GMAT LRO.NAIFIdReferenceFrame = -9008001;
GMAT LRO.OrbitColor = Red;
GMAT LRO.TargetColor = Teal;
GMAT LRO.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT LRO.CdSigma = 1e+070;
GMAT LRO.CrSigma = 1e+070;
GMAT LRO.Id = 'SatId';
GMAT LRO.Attitude = CoordinateSystemFixed;
GMAT LRO.SPADSRPInterpolationMethod = Bilinear;
GMAT LRO.SPADSRPScaleFactorSigma = 1e+070;
GMAT LRO.SPADDragInterpolationMethod = Bilinear;
GMAT LRO.SPADDragScaleFactorSigma = 1e+070;
GMAT LRO.ModelFile = 'aura.3ds';
GMAT LRO.ModelOffsetX = 0;
GMAT LRO.ModelOffsetY = 0;
GMAT LRO.ModelOffsetZ = 0;
GMAT LRO.ModelRotationX = 0;
GMAT LRO.ModelRotationY = 0;
GMAT LRO.ModelRotationZ = 0;
GMAT LRO.ModelScale = 1;
GMAT LRO.AttitudeDisplayStateType = 'Quaternion';
GMAT LRO.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LRO.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT LRO.EulerAngleSequence = '321';

Create Spacecraft ImpactSat;
GMAT ImpactSat.DateFormat = UTCGregorian;
GMAT ImpactSat.Epoch = '01 Jan 2000 11:59:28.000';
GMAT ImpactSat.CoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSat.DisplayStateType = Cartesian;
GMAT ImpactSat.X = 7100;
GMAT ImpactSat.Y = 0;
GMAT ImpactSat.Z = 1300;
GMAT ImpactSat.VX = 0;
GMAT ImpactSat.VY = 7.35;
GMAT ImpactSat.VZ = 1;
GMAT ImpactSat.DryMass = 850;
GMAT ImpactSat.Cd = 2.2;
GMAT ImpactSat.Cr = 1.8;
GMAT ImpactSat.DragArea = 15;
GMAT ImpactSat.SRPArea = 1;
GMAT ImpactSat.SPADDragScaleFactor = 1;
GMAT ImpactSat.SPADSRPScaleFactor = 1;
GMAT ImpactSat.NAIFId = -10009001;
GMAT ImpactSat.NAIFIdReferenceFrame = -9009001;
GMAT ImpactSat.OrbitColor = Green;
GMAT ImpactSat.TargetColor = LightGray;
GMAT ImpactSat.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT ImpactSat.CdSigma = 1e+070;
GMAT ImpactSat.CrSigma = 1e+070;
GMAT ImpactSat.Id = 'SatId';
GMAT ImpactSat.Attitude = CoordinateSystemFixed;
GMAT ImpactSat.SPADSRPInterpolationMethod = Bilinear;
GMAT ImpactSat.SPADSRPScaleFactorSigma = 1e+070;
GMAT ImpactSat.SPADDragInterpolationMethod = Bilinear;
GMAT ImpactSat.SPADDragScaleFactorSigma = 1e+070;
GMAT ImpactSat.ModelFile = 'aura.3ds';
GMAT ImpactSat.ModelOffsetX = 0;
GMAT ImpactSat.ModelOffsetY = 0;
GMAT ImpactSat.ModelOffsetZ = 0;
GMAT ImpactSat.ModelRotationX = 0;
GMAT ImpactSat.ModelRotationY = 0;
GMAT ImpactSat.ModelRotationZ = 0;
GMAT ImpactSat.ModelScale = 1;
GMAT ImpactSat.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSat.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSat.EulerAngleSequence = '321';

Create Spacecraft ImpactSatInit;
GMAT ImpactSatInit.DateFormat = TAIModJulian;
GMAT ImpactSatInit.Epoch = '21545';
GMAT ImpactSatInit.CoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSatInit.DisplayStateType = Cartesian;
GMAT ImpactSatInit.X = 7100;
GMAT ImpactSatInit.Y = 0;
GMAT ImpactSatInit.Z = 1300;
GMAT ImpactSatInit.VX = 0;
GMAT ImpactSatInit.VY = 7.35;
GMAT ImpactSatInit.VZ = 1;
GMAT ImpactSatInit.DryMass = 850;
GMAT ImpactSatInit.Cd = 2.2;
GMAT ImpactSatInit.Cr = 1.8;
GMAT ImpactSatInit.DragArea = 15;
GMAT ImpactSatInit.SRPArea = 1;
GMAT ImpactSatInit.SPADDragScaleFactor = 1;
GMAT ImpactSatInit.SPADSRPScaleFactor = 1;
GMAT ImpactSatInit.NAIFId = -10010001;
GMAT ImpactSatInit.NAIFIdReferenceFrame = -9010001;
GMAT ImpactSatInit.OrbitColor = Yellow;
GMAT ImpactSatInit.TargetColor = DarkGray;
GMAT ImpactSatInit.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT ImpactSatInit.CdSigma = 1e+070;
GMAT ImpactSatInit.CrSigma = 1e+070;
GMAT ImpactSatInit.Id = 'SatId';
GMAT ImpactSatInit.Attitude = CoordinateSystemFixed;
GMAT ImpactSatInit.SPADSRPInterpolationMethod = Bilinear;
GMAT ImpactSatInit.SPADSRPScaleFactorSigma = 1e+070;
GMAT ImpactSatInit.SPADDragInterpolationMethod = Bilinear;
GMAT ImpactSatInit.SPADDragScaleFactorSigma = 1e+070;
GMAT ImpactSatInit.ModelFile = 'aura.3ds';
GMAT ImpactSatInit.ModelOffsetX = 0;
GMAT ImpactSatInit.ModelOffsetY = 0;
GMAT ImpactSatInit.ModelOffsetZ = 0;
GMAT ImpactSatInit.ModelRotationX = 0;
GMAT ImpactSatInit.ModelRotationY = 0;
GMAT ImpactSatInit.ModelRotationZ = 0;
GMAT ImpactSatInit.ModelScale = 1;
GMAT ImpactSatInit.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSatInit.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSatInit.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSatInit.EulerAngleSequence = '321';

Create Spacecraft CanonImpactSat;
GMAT CanonImpactSat.DateFormat = TAIModJulian;
GMAT CanonImpactSat.Epoch = '21545';
GMAT CanonImpactSat.CoordinateSystem = EarthMJ2000Eq;
GMAT CanonImpactSat.DisplayStateType = Cartesian;
GMAT CanonImpactSat.X = 7100;
GMAT CanonImpactSat.Y = 0;
GMAT CanonImpactSat.Z = 1300;
GMAT CanonImpactSat.VX = 0;
GMAT CanonImpactSat.VY = 7.35;
GMAT CanonImpactSat.VZ = 1;
GMAT CanonImpactSat.DryMass = 850;
GMAT CanonImpactSat.Cd = 2.2;
GMAT CanonImpactSat.Cr = 1.8;
GMAT CanonImpactSat.DragArea = 15;
GMAT CanonImpactSat.SRPArea = 1;
GMAT CanonImpactSat.SPADDragScaleFactor = 1;
GMAT CanonImpactSat.SPADSRPScaleFactor = 1;
GMAT CanonImpactSat.NAIFId = -10011001;
GMAT CanonImpactSat.NAIFIdReferenceFrame = -9011001;
GMAT CanonImpactSat.OrbitColor = Blue;
GMAT CanonImpactSat.TargetColor = DimGray;
GMAT CanonImpactSat.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT CanonImpactSat.CdSigma = 1e+070;
GMAT CanonImpactSat.CrSigma = 1e+070;
GMAT CanonImpactSat.Id = 'SatId';
GMAT CanonImpactSat.Attitude = CoordinateSystemFixed;
GMAT CanonImpactSat.SPADSRPInterpolationMethod = Bilinear;
GMAT CanonImpactSat.SPADSRPScaleFactorSigma = 1e+070;
GMAT CanonImpactSat.SPADDragInterpolationMethod = Bilinear;
GMAT CanonImpactSat.SPADDragScaleFactorSigma = 1e+070;
GMAT CanonImpactSat.ModelFile = 'aura.3ds';
GMAT CanonImpactSat.ModelOffsetX = 0;
GMAT CanonImpactSat.ModelOffsetY = 0;
GMAT CanonImpactSat.ModelOffsetZ = 0;
GMAT CanonImpactSat.ModelRotationX = 0;
GMAT CanonImpactSat.ModelRotationY = 0;
GMAT CanonImpactSat.ModelRotationZ = 0;
GMAT CanonImpactSat.ModelScale = 1;
GMAT CanonImpactSat.AttitudeDisplayStateType = 'Quaternion';
GMAT CanonImpactSat.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT CanonImpactSat.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT CanonImpactSat.EulerAngleSequence = '321';

Create Spacecraft ImpactSat_MoonFixed;
GMAT ImpactSat_MoonFixed.DateFormat = UTCGregorian;
GMAT ImpactSat_MoonFixed.Epoch = '01 Jan 2000 11:59:28.000';
GMAT ImpactSat_MoonFixed.CoordinateSystem = MoonFixed;
GMAT ImpactSat_MoonFixed.DisplayStateType = Cartesian;
GMAT ImpactSat_MoonFixed.X = 404097.400455761;
GMAT ImpactSat_MoonFixed.Y = 30873.0177733555;
GMAT ImpactSat_MoonFixed.Z = -45917.57222582575;
GMAT ImpactSat_MoonFixed.VX = 4.405930176834023;
GMAT ImpactSat_MoonFixed.VY = 5.501716465862698;
GMAT ImpactSat_MoonFixed.VZ = -2.099602413163658;
GMAT ImpactSat_MoonFixed.DryMass = 850;
GMAT ImpactSat_MoonFixed.Cd = 2.2;
GMAT ImpactSat_MoonFixed.Cr = 1.8;
GMAT ImpactSat_MoonFixed.DragArea = 15;
GMAT ImpactSat_MoonFixed.SRPArea = 1;
GMAT ImpactSat_MoonFixed.SPADDragScaleFactor = 1;
GMAT ImpactSat_MoonFixed.SPADSRPScaleFactor = 1;
GMAT ImpactSat_MoonFixed.NAIFId = -10012001;
GMAT ImpactSat_MoonFixed.NAIFIdReferenceFrame = -9012001;
GMAT ImpactSat_MoonFixed.OrbitColor = Pink;
GMAT ImpactSat_MoonFixed.TargetColor = DarkSlateGray;
GMAT ImpactSat_MoonFixed.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT ImpactSat_MoonFixed.CdSigma = 1e+070;
GMAT ImpactSat_MoonFixed.CrSigma = 1e+070;
GMAT ImpactSat_MoonFixed.Id = 'SatId';
GMAT ImpactSat_MoonFixed.Attitude = CoordinateSystemFixed;
GMAT ImpactSat_MoonFixed.SPADSRPInterpolationMethod = Bilinear;
GMAT ImpactSat_MoonFixed.SPADSRPScaleFactorSigma = 1e+070;
GMAT ImpactSat_MoonFixed.SPADDragInterpolationMethod = Bilinear;
GMAT ImpactSat_MoonFixed.SPADDragScaleFactorSigma = 1e+070;
GMAT ImpactSat_MoonFixed.ModelFile = 'aura.3ds';
GMAT ImpactSat_MoonFixed.ModelOffsetX = 0;
GMAT ImpactSat_MoonFixed.ModelOffsetY = 0;
GMAT ImpactSat_MoonFixed.ModelOffsetZ = 0;
GMAT ImpactSat_MoonFixed.ModelRotationX = 0;
GMAT ImpactSat_MoonFixed.ModelRotationY = 0;
GMAT ImpactSat_MoonFixed.ModelRotationZ = 0;
GMAT ImpactSat_MoonFixed.ModelScale = 1;
GMAT ImpactSat_MoonFixed.AttitudeDisplayStateType = 'Quaternion';
GMAT ImpactSat_MoonFixed.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT ImpactSat_MoonFixed.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT ImpactSat_MoonFixed.EulerAngleSequence = '321';

%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LCROSS;
GMAT LCROSS.DateFormat = TAIModJulian;
GMAT LCROSS.Epoch = '21545';
GMAT LCROSS.CoordinateSystem = EarthMJ2000Eq;
GMAT LCROSS.DisplayStateType = Cartesian;
GMAT LCROSS.X = 7100;
GMAT LCROSS.Y = 0;
GMAT LCROSS.Z = 1300;
GMAT LCROSS.VX = 0;
GMAT LCROSS.VY = 7.35;
GMAT LCROSS.VZ = 1;
GMAT LCROSS.DryMass = 850;
GMAT LCROSS.Cd = 2.2;
GMAT LCROSS.Cr = 1.8;
GMAT LCROSS.DragArea = 15;
GMAT LCROSS.SRPArea = 1;
GMAT LCROSS.SPADDragScaleFactor = 1;
GMAT LCROSS.SPADSRPScaleFactor = 1;
GMAT LCROSS.NAIFId = -10013001;
GMAT LCROSS.NAIFIdReferenceFrame = -9013001;
GMAT LCROSS.OrbitColor = Lime;
GMAT LCROSS.TargetColor = LightGray;
GMAT LCROSS.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT LCROSS.CdSigma = 1e+070;
GMAT LCROSS.CrSigma = 1e+070;
GMAT LCROSS.Id = 'SatId';
GMAT LCROSS.Attitude = CoordinateSystemFixed;
GMAT LCROSS.SPADSRPInterpolationMethod = Bilinear;
GMAT LCROSS.SPADSRPScaleFactorSigma = 1e+070;
GMAT LCROSS.SPADDragInterpolationMethod = Bilinear;
GMAT LCROSS.SPADDragScaleFactorSigma = 1e+070;
GMAT LCROSS.ModelFile = 'aura.3ds';
GMAT LCROSS.ModelOffsetX = 0;
GMAT LCROSS.ModelOffsetY = 0;
GMAT LCROSS.ModelOffsetZ = 0;
GMAT LCROSS.ModelRotationX = 0;
GMAT LCROSS.ModelRotationY = 0;
GMAT LCROSS.ModelRotationZ = 0;
GMAT LCROSS.ModelScale = 1;
GMAT LCROSS.AttitudeDisplayStateType = 'Quaternion';
GMAT LCROSS.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LCROSS.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT LCROSS.EulerAngleSequence = '321';

%%-----------------------------------------------------------
%%------------------- Define Spacecraft --------------------
%%-----------------------------------------------------------
Create Spacecraft LCROSSInit;
GMAT LCROSSInit.DateFormat = TAIModJulian;
GMAT LCROSSInit.Epoch = '21545';
GMAT LCROSSInit.CoordinateSystem = EarthMJ2000Eq;
GMAT LCROSSInit.DisplayStateType = Cartesian;
GMAT LCROSSInit.X = 7100;
GMAT LCROSSInit.Y = 0;
GMAT LCROSSInit.Z = 1300;
GMAT LCROSSInit.VX = 0;
GMAT LCROSSInit.VY = 7.35;
GMAT LCROSSInit.VZ = 1;
GMAT LCROSSInit.DryMass = 850;
GMAT LCROSSInit.Cd = 2.2;
GMAT LCROSSInit.Cr = 1.8;
GMAT LCROSSInit.DragArea = 15;
GMAT LCROSSInit.SRPArea = 1;
GMAT LCROSSInit.SPADDragScaleFactor = 1;
GMAT LCROSSInit.SPADSRPScaleFactor = 1;
GMAT LCROSSInit.NAIFId = -10014001;
GMAT LCROSSInit.NAIFIdReferenceFrame = -9014001;
GMAT LCROSSInit.OrbitColor = Gold;
GMAT LCROSSInit.TargetColor = DarkGray;
GMAT LCROSSInit.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT LCROSSInit.CdSigma = 1e+070;
GMAT LCROSSInit.CrSigma = 1e+070;
GMAT LCROSSInit.Id = 'SatId';
GMAT LCROSSInit.Attitude = CoordinateSystemFixed;
GMAT LCROSSInit.SPADSRPInterpolationMethod = Bilinear;
GMAT LCROSSInit.SPADSRPScaleFactorSigma = 1e+070;
GMAT LCROSSInit.SPADDragInterpolationMethod = Bilinear;
GMAT LCROSSInit.SPADDragScaleFactorSigma = 1e+070;
GMAT LCROSSInit.ModelFile = 'aura.3ds';
GMAT LCROSSInit.ModelOffsetX = 0;
GMAT LCROSSInit.ModelOffsetY = 0;
GMAT LCROSSInit.ModelOffsetZ = 0;
GMAT LCROSSInit.ModelRotationX = 0;
GMAT LCROSSInit.ModelRotationY = 0;
GMAT LCROSSInit.ModelRotationZ = 0;
GMAT LCROSSInit.ModelScale = 1;
GMAT LCROSSInit.AttitudeDisplayStateType = 'Quaternion';
GMAT LCROSSInit.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT LCROSSInit.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT LCROSSInit.EulerAngleSequence = '321';




%----------------------------------------
%---------- ForceModels
%----------------------------------------


%%------------------------------------------------------------
%%------------------- Define Propagators --------------------
%%------------------------------------------------------------

%% Create a force model for near Lunar propagation
Create ForceModel NearMoonProp_ForceModel;
GMAT NearMoonProp_ForceModel.CentralBody = Luna;
GMAT NearMoonProp_ForceModel.PointMasses = {Sun, Earth, Jupiter, Luna};
GMAT NearMoonProp_ForceModel.Drag = None;
GMAT NearMoonProp_ForceModel.SRP = On;
GMAT NearMoonProp_ForceModel.RelativisticCorrection = Off;
GMAT NearMoonProp_ForceModel.ErrorControl = RSSStep;
GMAT NearMoonProp_ForceModel.SRP.Flux = 1367;
GMAT NearMoonProp_ForceModel.SRP.SRPModel = Spherical;
GMAT NearMoonProp_ForceModel.SRP.Nominal_Sun = 149597870.691;





%% Create a force model for deep space propagation
Create ForceModel EarthFull_ForceModel;
GMAT EarthFull_ForceModel.CentralBody = Earth;
GMAT EarthFull_ForceModel.PrimaryBodies = {Earth};
GMAT EarthFull_ForceModel.PointMasses = {Sun, Luna, Jupiter};
GMAT EarthFull_ForceModel.Drag = None;
GMAT EarthFull_ForceModel.SRP = On;
GMAT EarthFull_ForceModel.RelativisticCorrection = Off;
GMAT EarthFull_ForceModel.ErrorControl = RSSStep;
GMAT EarthFull_ForceModel.GravityField.Earth.Degree = 4;
GMAT EarthFull_ForceModel.GravityField.Earth.Order = 0;
GMAT EarthFull_ForceModel.GravityField.Earth.StmLimit = 100;
GMAT EarthFull_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT EarthFull_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT EarthFull_ForceModel.SRP.Flux = 1367;
GMAT EarthFull_ForceModel.SRP.SRPModel = Spherical;
GMAT EarthFull_ForceModel.SRP.Nominal_Sun = 149597870.691;

Create ForceModel MoonTwoBody_ForceModel;
GMAT MoonTwoBody_ForceModel.CentralBody = Luna;
GMAT MoonTwoBody_ForceModel.PointMasses = {Luna};
GMAT MoonTwoBody_ForceModel.Drag = None;
GMAT MoonTwoBody_ForceModel.SRP = Off;
GMAT MoonTwoBody_ForceModel.RelativisticCorrection = Off;
GMAT MoonTwoBody_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator NearMoonProp;
GMAT NearMoonProp.FM = NearMoonProp_ForceModel;
GMAT NearMoonProp.Type = PrinceDormand78;
GMAT NearMoonProp.InitialStepSize = 60;
GMAT NearMoonProp.Accuracy = 1e-009;
GMAT NearMoonProp.MinStep = 0.001;
GMAT NearMoonProp.MaxStep = 20000;
GMAT NearMoonProp.MaxStepAttempts = 50;
GMAT NearMoonProp.StopIfAccuracyIsViolated = true;

Create Propagator EarthFull;
GMAT EarthFull.FM = EarthFull_ForceModel;
GMAT EarthFull.Type = RungeKutta56;
GMAT EarthFull.InitialStepSize = 60;
GMAT EarthFull.Accuracy = 1e-009;
GMAT EarthFull.MinStep = 0.001;
GMAT EarthFull.MaxStep = 20000;
GMAT EarthFull.MaxStepAttempts = 50;
GMAT EarthFull.StopIfAccuracyIsViolated = true;

Create Propagator MoonTwoBody;
GMAT MoonTwoBody.FM = MoonTwoBody_ForceModel;
GMAT MoonTwoBody.Type = RungeKutta89;
GMAT MoonTwoBody.InitialStepSize = 60;
GMAT MoonTwoBody.Accuracy = 9.999999999999999e-012;
GMAT MoonTwoBody.MinStep = 0.001;
GMAT MoonTwoBody.MaxStep = 20000;
GMAT MoonTwoBody.MaxStepAttempts = 50;
GMAT MoonTwoBody.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%%------------------------------------------------------------
%%------------------- Create Maneuvers-- --------------------
%%------------------------------------------------------------
Create ImpulsiveBurn EDUSdv;
GMAT EDUSdv.CoordinateSystem = Local;
GMAT EDUSdv.Origin = Earth;
GMAT EDUSdv.Axes = VNB;
GMAT EDUSdv.Element1 = 0;
GMAT EDUSdv.Element2 = 0;
GMAT EDUSdv.Element3 = 0;
GMAT EDUSdv.DecrementMass = false;
GMAT EDUSdv.Isp = 300;
GMAT EDUSdv.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LROLOI;
GMAT LROLOI.CoordinateSystem = Local;
GMAT LROLOI.Origin = Luna;
GMAT LROLOI.Axes = VNB;
GMAT LROLOI.Element1 = -0.294816448336;
GMAT LROLOI.Element2 = 0;
GMAT LROLOI.Element3 = 0;
GMAT LROLOI.DecrementMass = false;
GMAT LROLOI.Isp = 300;
GMAT LROLOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LROTCM;
GMAT LROTCM.CoordinateSystem = Local;
GMAT LROTCM.Origin = Luna;
GMAT LROTCM.Axes = VNB;
GMAT LROTCM.Element1 = 0.00494673009567;
GMAT LROTCM.Element2 = -0.0134520458643;
GMAT LROTCM.Element3 = 0;
GMAT LROTCM.DecrementMass = false;
GMAT LROTCM.Isp = 300;
GMAT LROTCM.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn EDUSdv_J2000;
GMAT EDUSdv_J2000.CoordinateSystem = Local;
GMAT EDUSdv_J2000.Origin = Earth;
GMAT EDUSdv_J2000.Axes = MJ2000Eq;
GMAT EDUSdv_J2000.Element1 = 0;
GMAT EDUSdv_J2000.Element2 = 0;
GMAT EDUSdv_J2000.Element3 = 0;
GMAT EDUSdv_J2000.DecrementMass = false;
GMAT EDUSdv_J2000.Isp = 300;
GMAT EDUSdv_J2000.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LCM1;
GMAT LCM1.CoordinateSystem = Local;
GMAT LCM1.Origin = Earth;
GMAT LCM1.Axes = VNB;
GMAT LCM1.Element1 = 0;
GMAT LCM1.Element2 = 0;
GMAT LCM1.Element3 = 0;
GMAT LCM1.DecrementMass = false;
GMAT LCM1.Isp = 300;
GMAT LCM1.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LCM2;
GMAT LCM2.CoordinateSystem = Local;
GMAT LCM2.Origin = Earth;
GMAT LCM2.Axes = VNB;
GMAT LCM2.Element1 = 0;
GMAT LCM2.Element2 = 0;
GMAT LCM2.Element3 = 0;
GMAT LCM2.DecrementMass = false;
GMAT LCM2.Isp = 300;
GMAT LCM2.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn dVatMoon;
GMAT dVatMoon.CoordinateSystem = Local;
GMAT dVatMoon.Origin = Luna;
GMAT dVatMoon.Axes = MJ2000Eq;
GMAT dVatMoon.Element1 = 0;
GMAT dVatMoon.Element2 = 0;
GMAT dVatMoon.Element3 = 0;
GMAT dVatMoon.DecrementMass = false;
GMAT dVatMoon.Isp = 300;
GMAT dVatMoon.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------


%%------------------------------------------------------------
%%------------------- Create Coordinate Systems -------------
%%------------------------------------------------------------

Create CoordinateSystem MoonFixed;
GMAT MoonFixed.Origin = Luna;
GMAT MoonFixed.Axes = BodyFixed;

Create CoordinateSystem MoonFK5;
GMAT MoonFK5.Origin = Luna;
GMAT MoonFK5.Axes = MJ2000Eq;

Create CoordinateSystem MoonEarthRot;
GMAT MoonEarthRot.Origin = Luna;
GMAT MoonEarthRot.Axes = ObjectReferenced;
GMAT MoonEarthRot.XAxis = R;
GMAT MoonEarthRot.ZAxis = N;
GMAT MoonEarthRot.Primary = Earth;
GMAT MoonEarthRot.Secondary = Luna;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Earth;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create CoordinateSystem LunaFixed;
GMAT LunaFixed.Origin = Luna;
GMAT LunaFixed.Axes = BodyFixed;

Create CoordinateSystem MoonInertial;
GMAT MoonInertial.Origin = Luna;
GMAT MoonInertial.Axes = BodyInertial;

%----------------------------------------
%---------- Solvers
%----------------------------------------

%%-----------------------------------------------------------------
%%-----------------Create and Setup the Solvers -------------------
%%-----------------------------------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC.data';
GMAT DC.MaximumIterations = 2500;
GMAT DC.DerivativeMethod = ForwardDifference;
GMAT DC.Algorithm = NewtonRaphson;

Create DifferentialCorrector DC_central;
GMAT DC_central.ShowProgress = true;
GMAT DC_central.ReportStyle = Normal;
GMAT DC_central.ReportFile = 'DifferentialCorrectorDC.data';
GMAT DC_central.MaximumIterations = 2500;
GMAT DC_central.DerivativeMethod = ForwardDifference;
GMAT DC_central.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OpenFramesInterface EarthView_OpenFrames;
GMAT EarthView_OpenFrames.SolverIterations = Current;
GMAT EarthView_OpenFrames.UpperLeft = [ 0.4082352941176471 0 ];
GMAT EarthView_OpenFrames.Size = [ 0.3976470588235294 0.4797619047619048 ];
GMAT EarthView_OpenFrames.RelativeZOrder = 216;
GMAT EarthView_OpenFrames.Maximized = false;
GMAT EarthView_OpenFrames.Add = {LRO, Earth, LCROSS, Luna, Sun};
GMAT EarthView_OpenFrames.View = {EarthMoonView, MoonInertialView, LCROSSView, LROView};
GMAT EarthView_OpenFrames.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthView_OpenFrames.DrawObject = [ true true true true true ];
GMAT EarthView_OpenFrames.DrawTrajectory = [ true true true true true ];
GMAT EarthView_OpenFrames.DrawAxes = [ false false false false false ];
GMAT EarthView_OpenFrames.DrawXYPlane = [ false false false false false ];
GMAT EarthView_OpenFrames.DrawLabel = [ true true true true true ];
GMAT EarthView_OpenFrames.DrawUsePropLabel = [ false false false false false ];
GMAT EarthView_OpenFrames.DrawCenterPoint = [ true true false false false ];
GMAT EarthView_OpenFrames.DrawEndPoints = [ true true false false false ];
GMAT EarthView_OpenFrames.DrawVelocity = [ false false false false false ];
GMAT EarthView_OpenFrames.DrawGrid = [ false false false false false ];
GMAT EarthView_OpenFrames.DrawLineWidth = [ 2 2 2 2 2 ];
GMAT EarthView_OpenFrames.DrawMarkerSize = [ 10 10 10 10 10 ];
GMAT EarthView_OpenFrames.DrawFontSize = [ 14 14 14 14 14 ];
GMAT EarthView_OpenFrames.Axes = Off;
GMAT EarthView_OpenFrames.AxesLength = 1;
GMAT EarthView_OpenFrames.AxesLabels = On;
GMAT EarthView_OpenFrames.FrameLabel = Off;
GMAT EarthView_OpenFrames.XYPlane = Off;
GMAT EarthView_OpenFrames.EclipticPlane = Off;
GMAT EarthView_OpenFrames.EnableStars = On;
GMAT EarthView_OpenFrames.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT EarthView_OpenFrames.StarCount = 40000;
GMAT EarthView_OpenFrames.MinStarMag = -2;
GMAT EarthView_OpenFrames.MaxStarMag = 6;
GMAT EarthView_OpenFrames.MinStarPixels = 1;
GMAT EarthView_OpenFrames.MaxStarPixels = 10;
GMAT EarthView_OpenFrames.MinStarDimRatio = 0.5;
GMAT EarthView_OpenFrames.ShowPlot = true;
GMAT EarthView_OpenFrames.ShowToolbar = true;
GMAT EarthView_OpenFrames.SolverIterLastN = 1;
GMAT EarthView_OpenFrames.ShowVR = false;
GMAT EarthView_OpenFrames.PlaybackTimeScale = 3600;
GMAT EarthView_OpenFrames.MultisampleAntiAliasing = On;
GMAT EarthView_OpenFrames.MSAASamples = 2;
GMAT EarthView_OpenFrames.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right'};

Create OpenFramesInterface MoonView_OpenFrames;
GMAT MoonView_OpenFrames.SolverIterations = Current;
GMAT MoonView_OpenFrames.UpperLeft = [ 0.4082352941176471 0.4785714285714286 ];
GMAT MoonView_OpenFrames.Size = [ 0.3976470588235294 0.4785714285714286 ];
GMAT MoonView_OpenFrames.RelativeZOrder = 217;
GMAT MoonView_OpenFrames.Maximized = false;
GMAT MoonView_OpenFrames.Add = {LRO, Earth, LCROSS, Luna, Sun};
GMAT MoonView_OpenFrames.View = {MoonInertialView};
GMAT MoonView_OpenFrames.CoordinateSystem = MoonInertial;
GMAT MoonView_OpenFrames.DrawObject = [ true true true true false ];
GMAT MoonView_OpenFrames.DrawTrajectory = [ true true true true false ];
GMAT MoonView_OpenFrames.DrawAxes = [ false false false false false ];
GMAT MoonView_OpenFrames.DrawXYPlane = [ false false false false false ];
GMAT MoonView_OpenFrames.DrawLabel = [ true true true true false ];
GMAT MoonView_OpenFrames.DrawUsePropLabel = [ false false false false false ];
GMAT MoonView_OpenFrames.DrawCenterPoint = [ true true false false false ];
GMAT MoonView_OpenFrames.DrawEndPoints = [ true true false false false ];
GMAT MoonView_OpenFrames.DrawVelocity = [ false false false false false ];
GMAT MoonView_OpenFrames.DrawGrid = [ false false false false false ];
GMAT MoonView_OpenFrames.DrawLineWidth = [ 2 2 2 2 2 ];
GMAT MoonView_OpenFrames.DrawMarkerSize = [ 10 10 10 10 10 ];
GMAT MoonView_OpenFrames.DrawFontSize = [ 14 14 14 14 14 ];
GMAT MoonView_OpenFrames.Axes = On;
GMAT MoonView_OpenFrames.AxesLength = 1;
GMAT MoonView_OpenFrames.AxesLabels = On;
GMAT MoonView_OpenFrames.FrameLabel = Off;
GMAT MoonView_OpenFrames.XYPlane = Off;
GMAT MoonView_OpenFrames.EclipticPlane = Off;
GMAT MoonView_OpenFrames.EnableStars = On;
GMAT MoonView_OpenFrames.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT MoonView_OpenFrames.StarCount = 40000;
GMAT MoonView_OpenFrames.MinStarMag = -2;
GMAT MoonView_OpenFrames.MaxStarMag = 6;
GMAT MoonView_OpenFrames.MinStarPixels = 1;
GMAT MoonView_OpenFrames.MaxStarPixels = 10;
GMAT MoonView_OpenFrames.MinStarDimRatio = 0.5;
GMAT MoonView_OpenFrames.ShowPlot = true;
GMAT MoonView_OpenFrames.ShowToolbar = true;
GMAT MoonView_OpenFrames.SolverIterLastN = 1;
GMAT MoonView_OpenFrames.ShowVR = false;
GMAT MoonView_OpenFrames.PlaybackTimeScale = 3600;
GMAT MoonView_OpenFrames.MultisampleAntiAliasing = On;
GMAT MoonView_OpenFrames.MSAASamples = 2;
GMAT MoonView_OpenFrames.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right'};

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------

Create Array X[15,1];

%----------------------------------------
%---------- Variables, Arrays, Strings
%----------------------------------------
Create Variable LoopCounter PositionError VelocityError Cost LCM1Mag LCM2Mag EDUSdvMag EDMdvEpoch LCM1Epoch LCM2Epoch;
Create Variable A1ImpactEpoch LoopCounter2 ConstraintError dx dy dz dVx dVy dVz SF_EDUSx;
Create Variable dVatMoonMag Min_Cost UseGMAToutput_Flag RefEpoch PropEpoch ET_EDUSdV ET_LCM1 ET_LCM2 ET_Coll A1CollEpoch;
Create Variable VXdiff VYdiff VZdiff I DeltaVx DeltaVy DeltaVz PosError VelError EDUS_J2000_DVx;
Create Variable EDUS_J2000_DVy EDUS_J2000_DVz DV1_J2000_DVx DV1_J2000_DVy DV1_J2000_DVz DV2_J2000_DVx DV2_J2000_DVy DV2_J2000_DVz SteveReferenceEpoch Res;
Create Variable LunarSMA DesiredSMA ConError BPlaneAngle PeriselentAlt Error1 Error2 Error3 Loop2 NumSatRevs;
Create Variable NumMoonRevs NumCrossings DotProduct pi DesiredAOP Loop1 TargetUTCModJulian dVX dVY dVZ;
Create Variable UTCGregorian AOP_J2000 LCM2Mag_mps;


%----- Strings ---------
Create String EmptyLine NewIterate PostTLI PreEDUS PostEDUS PreLCM1 PostLCM1 PreLCM2 PostLCM2 Periselene;
Create String EDUSData BPlaneData LCM1Sol LCM2Sol PostLCM2Data ImpactSatData ImpactData;
GMAT EmptyLine = ' ';
GMAT NewIterate = '------ Begin New Iterate Data -------';
GMAT EDUSData = '------ EDUS Maneuver Data -------';
GMAT BPlaneData = '------  B-Plane  Data -------';
GMAT LCM1Sol = '------ LCM1 Maneuver Data -------';
GMAT LCM2Sol = '------ LCM2 Maneuver Data -------';
GMAT PostLCM2Data = '------ Post LCM2 State -------';
GMAT ImpactSatData = '---------- Impact Sat Data --------------';
GMAT ImpactData = '------ Impact Data -------';





%----------------------------------------
%---------- User Objects
%----------------------------------------

Create OpenFramesView EarthMoonView;
GMAT EarthMoonView.ViewFrame = CoordinateSystem;
GMAT EarthMoonView.ViewTrajectory = Off;
GMAT EarthMoonView.InertialFrame = Off;
GMAT EarthMoonView.SetDefaultLocation = On;
GMAT EarthMoonView.DefaultEye = [ 625000 625000 625000 ];
GMAT EarthMoonView.DefaultCenter = [ 0 0 0 ];
GMAT EarthMoonView.DefaultUp = [ 0 0 1 ];
GMAT EarthMoonView.SetCurrentLocation = On;
GMAT EarthMoonView.CurrentEye = [ 625000 625000 624999.9999999999 ];
GMAT EarthMoonView.CurrentCenter = [ 1.164153218269348e-10 0 0 ];
GMAT EarthMoonView.CurrentUp = [ -0.408248290463863 -0.4082482904638629 0.8164965809277262 ];
GMAT EarthMoonView.FOVy = 45;

Create OpenFramesView MoonInertialView;
GMAT MoonInertialView.ViewFrame = Luna;
GMAT MoonInertialView.ViewTrajectory = Off;
GMAT MoonInertialView.InertialFrame = On;
GMAT MoonInertialView.SetDefaultLocation = On;
GMAT MoonInertialView.DefaultEye = [ 12000 12000 12000 ];
GMAT MoonInertialView.DefaultCenter = [ 0 0 0 ];
GMAT MoonInertialView.DefaultUp = [ 0 0 1 ];
GMAT MoonInertialView.SetCurrentLocation = On;
GMAT MoonInertialView.CurrentEye = [ 12000 12000 12000 ];
GMAT MoonInertialView.CurrentCenter = [ 1.818989403545856e-12 0 0 ];
GMAT MoonInertialView.CurrentUp = [ -0.408248290463863 -0.4082482904638629 0.8164965809277262 ];
GMAT MoonInertialView.FOVy = 45;

Create OpenFramesView LCROSSView;
GMAT LCROSSView.ViewFrame = LCROSS;
GMAT LCROSSView.ViewTrajectory = Off;
GMAT LCROSSView.InertialFrame = On;
GMAT LCROSSView.SetDefaultLocation = Off;
GMAT LCROSSView.SetCurrentLocation = On;
GMAT LCROSSView.CurrentEye = [ -195.042292406567 -890.6101998537824 120.4754552950096 ];
GMAT LCROSSView.CurrentCenter = [ 0 2.273736754432321e-13 -2.842170943040401e-14 ];
GMAT LCROSSView.CurrentUp = [ -0.1500891747039094 0.1647402190408728 0.9748507064505133 ];
GMAT LCROSSView.FOVy = 45;

Create OpenFramesView LROView;
GMAT LROView.ViewFrame = LRO;
GMAT LROView.ViewTrajectory = Off;
GMAT LROView.InertialFrame = On;
GMAT LROView.SetDefaultLocation = Off;
GMAT LROView.SetCurrentLocation = On;
GMAT LROView.CurrentEye = [ 0 -919.6425170898438 0 ];
GMAT LROView.CurrentCenter = [ 0 0 0 ];
GMAT LROView.CurrentUp = [ 0 0 1 ];
GMAT LROView.FOVy = 45;

%------------------------------------------------------------------------
%-------------------------- Mission Sequence ----------------------------
%------------------------------------------------------------------------

BeginMissionSequence;

%---- Initializations
GMAT LCROSS = LRO;
GMAT LunarSMA = 384410.3;
GMAT pi = 3.14159265358979;

BeginScript 'Initializations'
   
   %---- Define LCROSS physical properties
   GMAT LRO.DryMass = 3256.8;
   GMAT LRO.Cr = 1.4;
   GMAT LRO.SRPArea = 42.354;
   
   %---- Define the initial conditions for LRO
   GMAT LRO.Epoch = '19 Jun 2009 01:31:05.920 ';
   GMAT LRO.X = 20747.68418000;
   GMAT LRO.Y = 50758.29345000;
   GMAT LRO.Z = 8229.21836200;
   GMAT LRO.VX = 0.11610208;
   GMAT LRO.VY = 3.32267120;
   GMAT LRO.VZ = 1.05827656;
   GMAT LCROSS = LRO;
   GMAT RefEpoch = LRO.A1ModJulian;
   GMAT ImpactSat = LRO;
   
   GMAT LRO.Epoch = '18 Jun 2009 23:58:54.680 ';
   GMAT LRO.X = 18997.18364;
   GMAT LRO.Y = 30057.69679;
   GMAT LRO.Z = 2054.89630;
   GMAT LRO.VX = 0.61569;
   GMAT LRO.VY = 4.29276;
   GMAT LRO.VZ = 1.17090;
   
   %---- Define the MoonFixed impact location and Epoch
   GMAT ImpactSat_MoonFixed.Epoch = '9 Oct 2009 11:30:00.000';
   GMAT ImpactSat_MoonFixed.X = 125.734;
   GMAT ImpactSat_MoonFixed.Y = -89.636;
   GMAT ImpactSat_MoonFixed.Z = -1730.522;
   
   %---- Define the initial conditions for ImpactSat
   GMAT ImpactSat.Epoch = ImpactSat_MoonFixed.Epoch.UTCGregorian;
   GMAT ImpactSat.X = ImpactSat_MoonFixed.EarthMJ2000Eq.X;
   GMAT ImpactSat.Y = ImpactSat_MoonFixed.EarthMJ2000Eq.Y;
   GMAT ImpactSat.Z = ImpactSat_MoonFixed.EarthMJ2000Eq.Z;
   
   %---- Define the desired Resonance condition
   GMAT NumSatRevs = 3;
   GMAT NumMoonRevs = 4;
   GMAT Res = NumSatRevs/NumMoonRevs;
   GMAT DesiredSMA = 473722.2056;%((398600.4415)^(1/3)*(NumMoonRevs/NumSatRevs/2/pi*2360551.68)^(2/3))*1.00;  
   GMAT DesiredAOP = 35.4553172712919;
   GMAT AOP_J2000 = 35.4553172712919;
   GMAT BPlaneAngle = 79.5592;
   GMAT PeriselentAlt = 3318.092;
   
   %---- Define intial guess for EDUSdv  
   GMAT ET_EDUSdV = (0/86400);
   GMAT EDUSdv.Element1 = 0.00001;
   GMAT EDUSdv.Element2 = 0.00001;
   GMAT EDUSdv.Element3 = 0.00001;
   
   %---- Define intial guess for LCM1 
   GMAT ET_LCM1 = 0.905018439982086 + ET_EDUSdV;
   GMAT LCM1.Element1 = 0.001704940;
   GMAT LCM1.Element2 = 0.005791633;
   GMAT LCM1.Element3 = -0.000661562;
   
   %---- Define intial guess for LCM2 
   GMAT ET_LCM2 = 33.770;
   GMAT LCM2.Element1 = 0.013391482;
   GMAT LCM2.Element2 = -0.007895569;
   GMAT LCM2.Element3 = 0.013918756;
   
   %---- Define initial guess for Collocation Elapsed Time
   GMAT ET_Coll = 100;
   GMAT ImpactSat.X = 54580.02594841291;
   GMAT ImpactSat.Y = 331618.2005005924;
   GMAT ImpactSat.Z = 162362.268059963;
   GMAT ImpactSat.VX = -0.6416435343925693;
   GMAT ImpactSat.VY = -0.4568696547646986;
   GMAT ImpactSat.VZ = 2.402724393937031;
   
   GMAT LCM1.Element1 = 0.00306139288909;
   GMAT LCM1.Element2 = -0.00154568531603;
   GMAT LCM1.Element3 = 0.00308387318091;
   GMAT ET_LCM2 = 33.8898378781;
   GMAT LCM2.Element1 = -0.00150770654724;
   GMAT LCM2.Element2 = 0.0229441083419;
   GMAT LCM2.Element3 = 0.00277315503137;
   GMAT ET_Coll = 100.068369408;
   GMAT dVatMoon.Element1 = -0.014073821735;
   GMAT dVatMoon.Element2 = -0.3936966287;
   GMAT dVatMoon.Element3 = -0.231610121973;
   GMAT LCROSSInit = LCROSS;
   GMAT ImpactSat = ImpactSatInit;
   GMAT LCROSS = LCROSSInit;
   
EndScript;

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to where EDUS performs maneuver
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to EDUS Maneuver and Perform Maneuver
Propagate EarthFull(LRO) {LRO.A1ModJulian = LCROSS.A1ModJulian};
GMAT PropEpoch = RefEpoch + ET_EDUSdV;
GMAT LRO.Epoch = LCROSS.Epoch.UTCGregorian;
Propagate EarthFull(LCROSS, LRO) {LCROSS.A1ModJulian = PropEpoch};

%---- Perform the maneuver
Maneuver EDUSdv(LCROSS);

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to LCM1 and perform manevuer
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to Epoch of LCM1
GMAT PropEpoch = RefEpoch + ET_LCM1;
Propagate EarthFull(LCROSS, LRO) {LCROSS.A1ModJulian = PropEpoch};

%---- Apply maneuver LCM1
Maneuver LCM1(LCROSS);
Maneuver LROTCM(LRO);


%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to periselene
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

%----- Propagate LCROSS to Periselene
Propagate NearMoonProp(LCROSS, LRO) {LRO.Luna.Periapsis};
Maneuver LROLOI(LRO);

%----- Propagate LCROSS to Periselene
Propagate NearMoonProp(LCROSS, LRO) {LRO.Luna.Periapsis};
GMAT LROLOI.Element1 = -.500;
Maneuver LROLOI(LRO);

%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
%---- Prop to LCM2 and perform manevuer
%++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
GMAT PropEpoch = RefEpoch + ET_LCM2;
Propagate NearMoonProp(LCROSS, LRO) {LCROSS.A1ModJulian = PropEpoch};
Maneuver LCM2(LCROSS);
Propagate NearMoonProp(LCROSS, LRO) {LCROSS.Luna.RMAG = 1700, StopTolerance = 1e-005};


 




