%General Mission Analysis Tool(GMAT) Script
%Created: 2010-09-28 08:16:24

% Animation of this script shows a Mars orbiter that uses a
% SPICE kernel to control its attitude.  The attitude control
% is best viewed using the ExpressView view setting.

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft MarsExpress;
GMAT MarsExpress.DateFormat = UTCGregorian;
GMAT MarsExpress.Epoch = '01 Jun 2010 16:00:00.000';
GMAT MarsExpress.NAIFId = -41;
GMAT MarsExpress.NAIFIdReferenceFrame = -41001;
GMAT MarsExpress.OrbitSpiceKernelName = {'../data/vehicle/ephem/spk/MarsExpress_Short.bsp'};
GMAT MarsExpress.AttitudeSpiceKernelName = {'../data/vehicle/ephem/spk/MarsExpress_ATNM_PTR00012_100531_002.BC'};
GMAT MarsExpress.SCClockSpiceKernelName = {'../data/vehicle/ephem/spk/MarsExpress_MEX_100921_STEP.TSC'};
GMAT MarsExpress.FrameSpiceKernelName = {'../data/vehicle/ephem/spk/MarsExpress_MEX_V10.TF'};
GMAT MarsExpress.Attitude = 'SpiceAttitude';
GMAT MarsExpress.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT MarsExpress.AttitudeDisplayStateType = 'Quaternion';
GMAT MarsExpress.AttitudeRateDisplayStateType = 'AngularVelocity';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator spkProp;
GMAT spkProp.Type = SPK;
GMAT spkProp.StepSize = 5;
GMAT spkProp.CentralBody = Mars;
GMAT spkProp.EpochFormat = 'UTCGregorian';
GMAT spkProp.StartEpoch = '01 Jun 2010 16:00:00.000';

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem MarsMJ2000Eq;
GMAT MarsMJ2000Eq.Origin = Mars;
GMAT MarsMJ2000Eq.Axes = MJ2000Eq;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OpenFramesInterface OpenFrames1;
GMAT OpenFrames1.SolverIterations = Current;
GMAT OpenFrames1.UpperLeft = [ 0 0 ];
GMAT OpenFrames1.Size = [ 0 0 ];
GMAT OpenFrames1.RelativeZOrder = 0;
GMAT OpenFrames1.Maximized = false;
GMAT OpenFrames1.Add = {MarsExpress, Mars, Sun};
GMAT OpenFrames1.CoordinateSystem = MarsMJ2000Eq;
GMAT OpenFrames1.View = {CoordinateSystemView1, ExpressView};

%----------------------------------------
%---------- View Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT OpenFrames1.SolverIterations = Current;
GMAT OpenFrames1.UpperLeft = [ 0.01 0.01 ];
GMAT OpenFrames1.Size = [ 0.98 0.98 ];
GMAT OpenFrames1.RelativeZOrder = 0;
GMAT OpenFrames1.Maximized = false;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ 19500 -12000 -5500 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 -2500 ];
GMAT CoordinateSystemView1.CurrentUp = [ 0 0 1 ];


Create OpenFramesView ExpressView;
GMAT ExpressView.ViewFrame = MarsExpress;
GMAT ExpressView.ViewTrajectory = Off;
GMAT ExpressView.InertialFrame = On;
GMAT ExpressView.LookAtFrame = Mars;
GMAT ExpressView.ShortestAngle = Off;
GMAT ExpressView.SetDefaultLocation = Off;
GMAT ExpressView.SetCurrentLocation = On;
GMAT ExpressView.CurrentEye = [ 0 -3500 0 ];
GMAT ExpressView.CurrentCenter = [ 0 0 0 ];
GMAT ExpressView.CurrentUp = [ 0 0 1 ];


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

Propagate spkProp(MarsExpress) {MarsExpress.ElapsedDays = 1.0};
