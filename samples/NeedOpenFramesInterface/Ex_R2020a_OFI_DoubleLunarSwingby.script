%  Script Mission - Double Lunar Swingby Example
%
%  This script demonstrates how to set up a double Lunar swingby with
%  multiple targeting sequences.
%


%--------------------------------------------------------------------------
%----------------- SpaceCraft, Formations, Constellations -----------------
%--------------------------------------------------------------------------

Create Spacecraft MMSRef;
GMAT MMSRef.DateFormat = UTCGregorian;
GMAT MMSRef.Epoch = '24 May 2014 15:17:21.927';
GMAT MMSRef.CoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.DisplayStateType = Keplerian;
GMAT MMSRef.SMA = 83230.75058999992;
GMAT MMSRef.ECC = 0.89643283;
GMAT MMSRef.INC = 15.50000000000001;
GMAT MMSRef.RAAN = 15.96879432999999;
GMAT MMSRef.AOP = 289.7205240999998;
GMAT MMSRef.TA = 179.9999987925817;
GMAT MMSRef.DryMass = 400;
GMAT MMSRef.Cd = 2.2;
GMAT MMSRef.Cr = 1.7;
GMAT MMSRef.DragArea = 5;
GMAT MMSRef.SRPArea = 5;

%--------------------------------------------------------------------------
%------------------------------ Force Models ------------------------------
%--------------------------------------------------------------------------

Create ForceModel LunarSB_ForceModel;
GMAT LunarSB_ForceModel.CentralBody = Earth;
GMAT LunarSB_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT LunarSB_ForceModel.Drag = None;
GMAT LunarSB_ForceModel.SRP = Off;
GMAT LunarSB_ForceModel.RelativisticCorrection = Off;
GMAT LunarSB_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LunarSB;
GMAT LunarSB.FM = LunarSB_ForceModel;
GMAT LunarSB.Type = RungeKutta89;
GMAT LunarSB.InitialStepSize = 60;
GMAT LunarSB.Accuracy = 1e-011;
GMAT LunarSB.MinStep = 0.001;
GMAT LunarSB.MaxStep = 30000;
GMAT LunarSB.MaxStepAttempts = 50;
GMAT LunarSB.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.01;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LST;
GMAT LST.CoordinateSystem = Local;
GMAT LST.Origin = Earth;
GMAT LST.Axes = VNB;
GMAT LST.Element1 = 0;
GMAT LST.Element2 = 0;
GMAT LST.Element3 = 0;
GMAT LST.DecrementMass = false;
GMAT LST.Isp = 300;
GMAT LST.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn ALM;
GMAT ALM.CoordinateSystem = Local;
GMAT ALM.Origin = Earth;
GMAT ALM.Axes = VNB;
GMAT ALM.Element1 = -0.354;
GMAT ALM.Element2 = 0;
GMAT ALM.Element3 = 0;
GMAT ALM.DecrementMass = false;
GMAT ALM.Isp = 300;
GMAT ALM.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn LunarPhasedV;
GMAT LunarPhasedV.CoordinateSystem = Local;
GMAT LunarPhasedV.Origin = Earth;
GMAT LunarPhasedV.Axes = VNB;
GMAT LunarPhasedV.Element1 = 0.027;
GMAT LunarPhasedV.Element2 = 0;
GMAT LunarPhasedV.Element3 = 0;
GMAT LunarPhasedV.DecrementMass = false;
GMAT LunarPhasedV.Isp = 300;
GMAT LunarPhasedV.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create CoordinateSystem GSE;
GMAT GSE.Origin = Earth;
GMAT GSE.Axes = ObjectReferenced;
GMAT GSE.XAxis = R;
GMAT GSE.ZAxis = N;
GMAT GSE.Primary = Sun;
GMAT GSE.Secondary = Earth;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 10;
GMAT DC1.DerivativeMethod = ForwardDifference;
GMAT DC1.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create XYPlot EarthDistance;
GMAT EarthDistance.SolverIterations = None;
GMAT EarthDistance.UpperLeft = [ 0.3 0.03 ];
GMAT EarthDistance.Size = [ 0.34 0.45 ];
GMAT EarthDistance.RelativeZOrder = 135;
GMAT EarthDistance.Maximized = true;
GMAT EarthDistance.XVariable = MMSRef.A1ModJulian;
GMAT EarthDistance.YVariables = {MMSRef.Earth.RMAG};
GMAT EarthDistance.ShowGrid = true;
GMAT EarthDistance.ShowPlot = true;

Create OpenFramesInterface OF_EarthMJ2K;
GMAT OF_EarthMJ2K.SolverIterations = All;
GMAT OF_EarthMJ2K.UpperLeft = [ 0.3 0.48 ];
GMAT OF_EarthMJ2K.Size = [ 0.34 0.45 ];
GMAT OF_EarthMJ2K.RelativeZOrder = 148;
GMAT OF_EarthMJ2K.Maximized = false;
GMAT OF_EarthMJ2K.Add = {MMSRef, Earth, Luna, Sun};
GMAT OF_EarthMJ2K.View = {CoordinateSystemView1};
GMAT OF_EarthMJ2K.CoordinateSystem = EarthMJ2000Eq;
GMAT OF_EarthMJ2K.DrawObject = [ true true true true ];

Create OpenFramesInterface OF_EarthMoonRot;
GMAT OF_EarthMoonRot.SolverIterations = Current;
GMAT OF_EarthMoonRot.UpperLeft = [ 0.65 0.48 ];
GMAT OF_EarthMoonRot.Size = [ 0.34 0.45 ];
GMAT OF_EarthMoonRot.RelativeZOrder = 126;
GMAT OF_EarthMoonRot.Maximized = true;
GMAT OF_EarthMoonRot.Add = {MMSRef, Earth, Luna, Sun};
GMAT OF_EarthMoonRot.View = {CoordinateSystemView2};
GMAT OF_EarthMoonRot.CoordinateSystem = EarthMoonRot;
GMAT OF_EarthMoonRot.DrawObject = [ true true true true ];

Create OpenFramesInterface OF_EarthGSE;
GMAT OF_EarthGSE.SolverIterations = Current;
GMAT OF_EarthGSE.UpperLeft = [ 0.65 0.03 ];
GMAT OF_EarthGSE.Size = [ 0.34 0.45 ];
GMAT OF_EarthGSE.RelativeZOrder = 127;
GMAT OF_EarthGSE.Maximized = true;
GMAT OF_EarthGSE.Add = {MMSRef, Earth, Luna, Sun};
GMAT OF_EarthGSE.View = {CoordinateSystemView3};
GMAT OF_EarthGSE.CoordinateSystem = GSE;
GMAT OF_EarthGSE.DrawObject = [ true true true true ];

%----------------------------------------
%---------- View Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ -18000 -85000 1650000 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.CurrentUp = [ 0 1 0 ];

Create OpenFramesView CoordinateSystemView2;
GMAT CoordinateSystemView2.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView2.ViewTrajectory = Off;
GMAT CoordinateSystemView2.InertialFrame = Off;
GMAT CoordinateSystemView2.SetDefaultLocation = Off;
GMAT CoordinateSystemView2.SetCurrentLocation = On;
GMAT CoordinateSystemView2.CurrentEye = [ 50000 -240000 2250000 ];
GMAT CoordinateSystemView2.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView2.CurrentUp = [ 0 1 0 ];

Create OpenFramesView CoordinateSystemView3;
GMAT CoordinateSystemView3.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView3.ViewTrajectory = Off;
GMAT CoordinateSystemView3.InertialFrame = Off;
GMAT CoordinateSystemView3.SetDefaultLocation = Off;
GMAT CoordinateSystemView3.SetCurrentLocation = On;
GMAT CoordinateSystemView3.CurrentEye = [ -200000 -75000 1500000 ];
GMAT CoordinateSystemView3.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView3.CurrentUp = [ 0 1 0 ];

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable I;


%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************
BeginMissionSequence;
BeginScript
   Toggle OF_EarthMoonRot Off;
   Toggle OF_EarthMJ2K Off;
   Toggle OF_EarthGSE Off;
   Toggle OF_EarthMoonRot On;
   Toggle OF_EarthMJ2K On;
   Toggle OF_EarthGSE On;
EndScript;

%------------------------------
%  Waiting Loops
%------------------------------
For 'For I = 1:14' I = 1:1:14;
   Propagate 'Prop to Periapsis' LunarSB(MMSRef) {MMSRef.Periapsis};
EndFor;

%------------------------------
%  Phasing Loops
%------------------------------
For 'For I = 1:5' I = 1:1:5;
   Propagate 'Prop to Periapsis' LunarSB(MMSRef) {MMSRef.Periapsis};
   Maneuver 'Apply LunarPhasedV' LunarPhasedV(MMSRef);
EndFor;

%%------------------------------
%%  Prop to periapsis for LSI
%%------------------------------
Maneuver 'Apply TOI' TOI(MMSRef);
Propagate 'Prop to Apogee' LunarSB(MMSRef) {MMSRef.Apoapsis};

%------------------------------
%  Target Lunar B-plane
%------------------------------
Target 'Target Flyby' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary LST.N' DC1(LST.Element2 = 0.03413111106956, {Perturbation = 1e-005, Lower = -.5, Upper = .5, MaxStep = .02, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary LST.B' DC1(LST.Element3 = -0.09078959728280001, {Perturbation = 1e-004, Lower = -.5, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply LST' LST(MMSRef);
   Propagate 'Prop to Periselene' LunarSB(MMSRef) {MMSRef.Luna.Periapsis};
   Achieve 'Achieve BdotT' DC1(MMSRef.MoonMJ2000Eq.BdotT = 150000, {Tolerance = .001});
   Achieve 'Acheive BdotR' DC1(MMSRef.MoonMJ2000Eq.BdotR = -10000, {Tolerance = .001});
EndTarget;  % For targeter DC1


%%------------------------------
%% Propagate to Earth Periapsis
%%------------------------------
Propagate 'Prop to Perigee' LunarSB(MMSRef) {MMSRef.Periapsis};

%------------------------------
% Target to lower Apogee
%------------------------------
Target 'Lower Apogee' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary ALM.V' DC1(ALM.Element1 = -0.3198120104, {Perturbation = 1e-006, Lower = -.5, Upper = .5, MaxStep = .08, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply ALM' ALM(MMSRef);
   Achieve 'Achieve RadApo' DC1(MMSRef.Earth.RadApo = 191344.0, {Tolerance = .00001});
EndTarget;  % For targeter DC1

Propagate LunarSB(MMSRef) {MMSRef.ElapsedDays = 10};
