%  Script Mission - Single Lunar Swingby Example
%
%  This script demonstrates how to set up a single Lunar swingby with
%  multiple targeting sequences.
%


%----------------------------------------
%---------- Spacecraft
%----------------------------------------


%**************************************************************************
%************ Create Objects for Use in Mission Sequence ******************
%**************************************************************************


%--------------------------------------------------------------------------
%----------------- SpaceCraft, Formations, Constellations -----------------
%--------------------------------------------------------------------------


Create Spacecraft MMSRef;
GMAT MMSRef.DateFormat = UTCGregorian;
GMAT MMSRef.Epoch = '22 Jul 2014 11:29:10.811';
GMAT MMSRef.CoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.DisplayStateType = Cartesian;
GMAT MMSRef.X = -137380.1984338506;
GMAT MMSRef.Y = 75679.87867537055;
GMAT MMSRef.Z = 21487.63875187856;
GMAT MMSRef.VX = -0.2324532014235503;
GMAT MMSRef.VY = -0.4462753967758019;
GMAT MMSRef.VZ = 0.08561205662877103;
GMAT MMSRef.DryMass = 1000;
GMAT MMSRef.Cd = 2.2;
GMAT MMSRef.Cr = 1.7;
GMAT MMSRef.DragArea = 15;
GMAT MMSRef.SRPArea = 1;
GMAT MMSRef.SPADDragScaleFactor = 1;
GMAT MMSRef.SPADSRPScaleFactor = 1;
GMAT MMSRef.NAIFId = -123456789;
GMAT MMSRef.NAIFIdReferenceFrame = -123456789;
GMAT MMSRef.OrbitColor = Red;
GMAT MMSRef.TargetColor = Teal;
GMAT MMSRef.OrbitErrorCovariance = [ 1e+070 0 0 0 0 0 ; 0 1e+070 0 0 0 0 ; 0 0 1e+070 0 0 0 ; 0 0 0 1e+070 0 0 ; 0 0 0 0 1e+070 0 ; 0 0 0 0 0 1e+070 ];
GMAT MMSRef.CdSigma = 1e+070;
GMAT MMSRef.CrSigma = 1e+070;
GMAT MMSRef.Id = 'SatId';
GMAT MMSRef.Attitude = CoordinateSystemFixed;
GMAT MMSRef.SPADSRPInterpolationMethod = Bilinear;
GMAT MMSRef.SPADSRPScaleFactorSigma = 1e+070;
GMAT MMSRef.SPADDragInterpolationMethod = Bilinear;
GMAT MMSRef.SPADDragScaleFactorSigma = 1e+070;
GMAT MMSRef.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT MMSRef.ModelOffsetX = 0;
GMAT MMSRef.ModelOffsetY = 0;
GMAT MMSRef.ModelOffsetZ = 0;
GMAT MMSRef.ModelRotationX = 0;
GMAT MMSRef.ModelRotationY = 0;
GMAT MMSRef.ModelRotationZ = 0;
GMAT MMSRef.ModelScale = 3;
GMAT MMSRef.AttitudeDisplayStateType = 'Quaternion';
GMAT MMSRef.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT MMSRef.AttitudeCoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.EulerAngleSequence = '321';

%----------------------------------------
%---------- ForceModels
%----------------------------------------


%--------------------------------------------------------------------------
%------------------------------ Propagators -------------------------------
%--------------------------------------------------------------------------

Create ForceModel EarthProp_ForceModel;
GMAT EarthProp_ForceModel.CentralBody = Earth;
GMAT EarthProp_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT EarthProp_ForceModel.Drag = None;
GMAT EarthProp_ForceModel.SRP = Off;
GMAT EarthProp_ForceModel.RelativisticCorrection = Off;
GMAT EarthProp_ForceModel.ErrorControl = RSSStep;

Create ForceModel MoonProp_ForceModel;
GMAT MoonProp_ForceModel.CentralBody = Luna;
GMAT MoonProp_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT MoonProp_ForceModel.Drag = None;
GMAT MoonProp_ForceModel.SRP = Off;
GMAT MoonProp_ForceModel.RelativisticCorrection = Off;
GMAT MoonProp_ForceModel.ErrorControl = RSSStep;



%----------------------------------------
%---------- ForceModels
%----------------------------------------


%--------------------------------------------------------------------------
%------------------------------ Propagators -------------------------------
%--------------------------------------------------------------------------

Create ForceModel LunarSB_ForceModel;
GMAT LunarSB_ForceModel.CentralBody = Earth;
GMAT LunarSB_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT LunarSB_ForceModel.Drag = None;
GMAT LunarSB_ForceModel.SRP = Off;
GMAT LunarSB_ForceModel.RelativisticCorrection = Off;
GMAT LunarSB_ForceModel.ErrorControl = RSSStep;

Create ForceModel MoonCentered_ForceModel;
GMAT MoonCentered_ForceModel.CentralBody = Luna;
GMAT MoonCentered_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT MoonCentered_ForceModel.Drag = None;
GMAT MoonCentered_ForceModel.SRP = Off;
GMAT MoonCentered_ForceModel.RelativisticCorrection = Off;
GMAT MoonCentered_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator EarthProp;
GMAT EarthProp.FM = LunarSB_ForceModel;
GMAT EarthProp.Type = RungeKutta89;
GMAT EarthProp.InitialStepSize = 60;
GMAT EarthProp.Accuracy = 9.999999999999999e-012;
GMAT EarthProp.MinStep = 0.001;
GMAT EarthProp.MaxStep = 5000;
GMAT EarthProp.MaxStepAttempts = 50;
GMAT EarthProp.StopIfAccuracyIsViolated = true;

Create Propagator MoonProp;
GMAT MoonProp.FM = MoonCentered_ForceModel;
GMAT MoonProp.Type = RungeKutta89;
GMAT MoonProp.InitialStepSize = 60;
GMAT MoonProp.Accuracy = 9.999999999999999e-012;
GMAT MoonProp.MinStep = 0.001;
GMAT MoonProp.MaxStep = 45000;
GMAT MoonProp.MaxStepAttempts = 50;
GMAT MoonProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

%--------------------------------------------------------------------------
%--------------------------------- Burns ----------------------------------
%--------------------------------------------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.14676929889;
GMAT TOI.Element2 = 0.046042675892;
GMAT TOI.Element3 = 0.090223244097;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn MOI;
GMAT MOI.CoordinateSystem = Local;
GMAT MOI.Origin = Earth;
GMAT MOI.Axes = VNB;
GMAT MOI.Element1 = -0.3198120104;
GMAT MOI.Element2 = 0;
GMAT MOI.Element3 = 0;
GMAT MOI.DecrementMass = false;
GMAT MOI.Isp = 300;
GMAT MOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

%--------------------------------------------------------------------------
%-------------------------- Coordinate Systems ----------------------------
%--------------------------------------------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

%----------------------------------------
%---------- Solvers
%----------------------------------------

%--------------------------------------------------------------------------
%-------------------------------- Solvers ---------------------------------
%--------------------------------------------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 25;
GMAT DC1.DerivativeMethod = ForwardDifference;
GMAT DC1.Algorithm = NewtonRaphson;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OpenFramesInterface EarthInertialView;
GMAT EarthInertialView.SolverIterations = All;
GMAT EarthInertialView.UpperLeft = [ 0.35 0 ];
GMAT EarthInertialView.Size = [ 0.43 0.46 ];
GMAT EarthInertialView.RelativeZOrder = 681;
GMAT EarthInertialView.Maximized = false;
GMAT EarthInertialView.Add = {MMSRef, Earth, Luna, Sun};
GMAT EarthInertialView.View = {CoordinateSystemView2, EarthView2, LunaView2, MMSRefView2};
GMAT EarthInertialView.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthInertialView.DrawObject = [ true true true true ];
GMAT EarthInertialView.DrawTrajectory = [ true true true true ];
GMAT EarthInertialView.DrawAxes = [ false false false false ];
GMAT EarthInertialView.DrawXYPlane = [ false false false false ];
GMAT EarthInertialView.DrawLabel = [ true true true true ];
GMAT EarthInertialView.DrawUsePropLabel = [ false false false false ];
GMAT EarthInertialView.DrawCenterPoint = [ true true true true ];
GMAT EarthInertialView.DrawEndPoints = [ true true true true ];
GMAT EarthInertialView.DrawVelocity = [ false false false false ];
GMAT EarthInertialView.DrawGrid = [ false false false false ];
GMAT EarthInertialView.DrawLineWidth = [ 2 2 2 2 ];
GMAT EarthInertialView.DrawMarkerSize = [ 10 10 10 10 ];
GMAT EarthInertialView.DrawFontSize = [ 14 14 14 14 ];
GMAT EarthInertialView.Axes = On;
GMAT EarthInertialView.AxesLength = 1;
GMAT EarthInertialView.AxesLabels = On;
GMAT EarthInertialView.FrameLabel = Off;
GMAT EarthInertialView.XYPlane = On;
GMAT EarthInertialView.EclipticPlane = Off;
GMAT EarthInertialView.EnableStars = On;
GMAT EarthInertialView.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT EarthInertialView.StarCount = 40000;
GMAT EarthInertialView.MinStarMag = -2;
GMAT EarthInertialView.MaxStarMag = 6;
GMAT EarthInertialView.MinStarPixels = 1;
GMAT EarthInertialView.MaxStarPixels = 10;
GMAT EarthInertialView.MinStarDimRatio = 0.5;
GMAT EarthInertialView.ShowPlot = true;
GMAT EarthInertialView.ShowToolbar = true;
GMAT EarthInertialView.SolverIterLastN = 0;
GMAT EarthInertialView.ShowVR = false;
GMAT EarthInertialView.PlaybackTimeScale = 3600;
GMAT EarthInertialView.MultisampleAntiAliasing = On;
GMAT EarthInertialView.MSAASamples = 2;
GMAT EarthInertialView.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right'};

Create OpenFramesInterface MoonInertialView;
GMAT MoonInertialView.SolverIterations = All;
GMAT MoonInertialView.UpperLeft = [ 0.35 0.47 ];
GMAT MoonInertialView.Size = [ 0.43 0.5 ];
GMAT MoonInertialView.RelativeZOrder = 679;
GMAT MoonInertialView.Maximized = false;
GMAT MoonInertialView.Add = {MMSRef, Earth, Luna, Sun};
GMAT MoonInertialView.View = {CoordinateSystemView1, EarthView1, LunaView1, MMSRefView1};
GMAT MoonInertialView.CoordinateSystem = MoonMJ2000Eq;
GMAT MoonInertialView.DrawObject = [ true true true true ];
GMAT MoonInertialView.DrawTrajectory = [ true true true true ];
GMAT MoonInertialView.DrawAxes = [ false false false false ];
GMAT MoonInertialView.DrawXYPlane = [ false false false false ];
GMAT MoonInertialView.DrawLabel = [ true true true true ];
GMAT MoonInertialView.DrawUsePropLabel = [ false false false false ];
GMAT MoonInertialView.DrawCenterPoint = [ true true true true ];
GMAT MoonInertialView.DrawEndPoints = [ true true true true ];
GMAT MoonInertialView.DrawVelocity = [ false false false false ];
GMAT MoonInertialView.DrawGrid = [ false false false false ];
GMAT MoonInertialView.DrawLineWidth = [ 2 2 2 2 ];
GMAT MoonInertialView.DrawMarkerSize = [ 10 10 10 10 ];
GMAT MoonInertialView.DrawFontSize = [ 14 14 14 14 ];
GMAT MoonInertialView.Axes = On;
GMAT MoonInertialView.AxesLength = 1;
GMAT MoonInertialView.AxesLabels = On;
GMAT MoonInertialView.FrameLabel = Off;
GMAT MoonInertialView.XYPlane = On;
GMAT MoonInertialView.EclipticPlane = Off;
GMAT MoonInertialView.EnableStars = On;
GMAT MoonInertialView.StarCatalog = 'inp_StarsHYGv3.txt';
GMAT MoonInertialView.StarCount = 40000;
GMAT MoonInertialView.MinStarMag = -2;
GMAT MoonInertialView.MaxStarMag = 6;
GMAT MoonInertialView.MinStarPixels = 1;
GMAT MoonInertialView.MaxStarPixels = 10;
GMAT MoonInertialView.MinStarDimRatio = 0.5;
GMAT MoonInertialView.ShowPlot = true;
GMAT MoonInertialView.ShowToolbar = true;
GMAT MoonInertialView.SolverIterLastN = 0;
GMAT MoonInertialView.ShowVR = false;
GMAT MoonInertialView.PlaybackTimeScale = 3600;
GMAT MoonInertialView.MultisampleAntiAliasing = On;
GMAT MoonInertialView.MSAASamples = 2;
GMAT MoonInertialView.DrawFontPosition = {'Top-Right', 'Top-Right', 'Top-Right', 'Top-Right'};

%----------------------------------------
%---------- User Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ 0 0 1050000 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.CurrentUp = [ 1 0 0 ];
GMAT CoordinateSystemView1.FOVy = 45;

Create OpenFramesView EarthView1;
GMAT EarthView1.ViewFrame = Earth;
GMAT EarthView1.ViewTrajectory = Off;
GMAT EarthView1.InertialFrame = Off;
GMAT EarthView1.SetDefaultLocation = Off;
GMAT EarthView1.SetCurrentLocation = Off;
GMAT EarthView1.FOVy = 45;

Create OpenFramesView LunaView1;
GMAT LunaView1.ViewFrame = Luna;
GMAT LunaView1.ViewTrajectory = Off;
GMAT LunaView1.InertialFrame = Off;
GMAT LunaView1.SetDefaultLocation = Off;
GMAT LunaView1.SetCurrentLocation = Off;
GMAT LunaView1.FOVy = 45;

Create OpenFramesView MMSRefView1;
GMAT MMSRefView1.ViewFrame = MMSRef;
GMAT MMSRefView1.ViewTrajectory = Off;
GMAT MMSRefView1.InertialFrame = Off;
GMAT MMSRefView1.SetDefaultLocation = Off;
GMAT MMSRefView1.SetCurrentLocation = Off;
GMAT MMSRefView1.FOVy = 45;

Create OpenFramesView CoordinateSystemView2;
GMAT CoordinateSystemView2.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView2.ViewTrajectory = Off;
GMAT CoordinateSystemView2.InertialFrame = Off;
GMAT CoordinateSystemView2.SetDefaultLocation = Off;
GMAT CoordinateSystemView2.SetCurrentLocation = On;
GMAT CoordinateSystemView2.CurrentEye = [ 0 0 1200000 ];
GMAT CoordinateSystemView2.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView2.CurrentUp = [ 1 0 0 ];
GMAT CoordinateSystemView2.FOVy = 45;

Create OpenFramesView EarthView2;
GMAT EarthView2.ViewFrame = Earth;
GMAT EarthView2.ViewTrajectory = Off;
GMAT EarthView2.InertialFrame = Off;
GMAT EarthView2.SetDefaultLocation = Off;
GMAT EarthView2.SetCurrentLocation = Off;
GMAT EarthView2.FOVy = 45;

Create OpenFramesView LunaView2;
GMAT LunaView2.ViewFrame = Luna;
GMAT LunaView2.ViewTrajectory = Off;
GMAT LunaView2.InertialFrame = Off;
GMAT LunaView2.SetDefaultLocation = Off;
GMAT LunaView2.SetCurrentLocation = Off;
GMAT LunaView2.FOVy = 45;

Create OpenFramesView MMSRefView2;
GMAT MMSRefView2.ViewFrame = MMSRef;
GMAT MMSRefView2.ViewTrajectory = Off;
GMAT MMSRefView2.InertialFrame = Off;
GMAT MMSRefView2.SetDefaultLocation = Off;
GMAT MMSRefView2.SetCurrentLocation = Off;
GMAT MMSRefView2.FOVy = 45;




%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************
BeginMissionSequence;

%------------------------------
%  Propagate to Earth periapsis
%------------------------------
Propagate 'Prop to Perigee' EarthProp(MMSRef) {MMSRef.Periapsis};

%------------------------------
%  Target Lunar B-plane
%------------------------------
Target 'Target B-Plane' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary TOI.V' DC1(TOI.Element1 = 0.1462, {Perturbation = 1e-005, Lower = .13, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary TOI.B' DC1(TOI.Element3 = 0.1086, {Perturbation = 1e-005, Lower = -.5, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply TOI' TOI(MMSRef);
   Propagate 'Prop 1.5 Days' EarthProp(MMSRef) {MMSRef.ElapsedDays = 1.5};
   Propagate 'Prop to Periselene' MoonProp(MMSRef) {MMSRef.Luna.Periapsis};
   Achieve 'Achieve BdotT' DC1(MMSRef.MoonMJ2000Eq.BdotT = 15000.4401777, {Tolerance = 3});
   Achieve 'Achieve BdotR' DC1(MMSRef.MoonMJ2000Eq.BdotR = 4000.59308992, {Tolerance = 3});
EndTarget;  % For targeter DC1

%------------------------------
% Propagate to Earth Periapsis
%------------------------------
Propagate 'Prop to Perigee' EarthProp(MMSRef) {MMSRef.Periapsis};

%------------------------------
% Target to lower Apogee
%------------------------------
Target 'Lower Apogee' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   Vary 'Vary MOI' DC1(MOI.Element1 = -0.3198120104, {Perturbation = 1e-006, Lower = -.5, Upper = .5, MaxStep = .08, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Maneuver 'Apply MOI' MOI(MMSRef);
   Achieve 'Achieve Apogee' DC1(MMSRef.Earth.RadApo = 191340, {Tolerance = 0.1});
EndTarget;  % For targeter DC1
Propagate 'Prop 12 Days' EarthProp(MMSRef) {MMSRef.ElapsedDays = 12};
