%  Script Mission - Lunar Transfer Example
%
%  This script demonstrates how to set up a lunar transfer mission
%

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = UTCGregorian;
GMAT Sat.Epoch = '22 Jul 2014 11:29:10.811';
GMAT Sat.CoordinateSystem = EarthMJ2000Eq;
GMAT Sat.DisplayStateType = Cartesian;
GMAT Sat.X = -137380.1984338506;
GMAT Sat.Y = 75679.87867537055;
GMAT Sat.Z = 21487.63875187856;
GMAT Sat.VX = -0.2324532014235503;
GMAT Sat.VY = -0.4462753967758019;
GMAT Sat.VZ = 0.08561205662877103;
GMAT Sat.DryMass = 1000;
GMAT Sat.Cd = 2.2;
GMAT Sat.Cr = 1.7;
GMAT Sat.DragArea = 15;
GMAT Sat.SRPArea = 1;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel AllForces;
GMAT AllForces.CentralBody = Earth;
GMAT AllForces.PrimaryBodies = {Earth};
GMAT AllForces.PointMasses = {Sun, Luna, Venus, Mars, Jupiter, Saturn, Uranus, Neptune};
GMAT AllForces.SRP = On;
GMAT AllForces.RelativisticCorrection = Off;
GMAT AllForces.ErrorControl = RSSStep;
GMAT AllForces.GravityField.Earth.Degree = 20;
GMAT AllForces.GravityField.Earth.Order = 20;
GMAT AllForces.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT AllForces.GravityField.Earth.TideModel = 'None';
GMAT AllForces.SRP.Flux = 1367;
GMAT AllForces.SRP.SRPModel = Spherical;
GMAT AllForces.SRP.Nominal_Sun = 149597870.691;
GMAT AllForces.Drag.AtmosphereModel = MSISE90;
GMAT AllForces.Drag.HistoricWeatherSource = 'ConstantFluxAndGeoMag';
GMAT AllForces.Drag.PredictedWeatherSource = 'ConstantFluxAndGeoMag';
GMAT AllForces.Drag.CSSISpaceWeatherFile = '../samples/SupportFiles/CSSI_2004To2026.txt';
GMAT AllForces.Drag.SchattenFile = 'SchattenPredict.txt';
GMAT AllForces.Drag.F107 = 150;
GMAT AllForces.Drag.F107A = 150;
GMAT AllForces.Drag.MagneticIndex = 3;
GMAT AllForces.Drag.SchattenErrorModel = 'Nominal';
GMAT AllForces.Drag.SchattenTimingModel = 'NominalCycle';

Create ForceModel MoonAllForces;
GMAT MoonAllForces.CentralBody = Luna;
GMAT MoonAllForces.PrimaryBodies = {Luna};
GMAT MoonAllForces.PointMasses = {Sun, Earth, Venus, Mars, Jupiter, Saturn, Uranus, Neptune};
GMAT MoonAllForces.Drag = None;
GMAT MoonAllForces.SRP = On;
GMAT MoonAllForces.RelativisticCorrection = Off;
GMAT MoonAllForces.ErrorControl = RSSStep;
GMAT MoonAllForces.GravityField.Luna.Degree = 20;
GMAT MoonAllForces.GravityField.Luna.Order = 20;
GMAT MoonAllForces.GravityField.Luna.PotentialFile = 'LP165P.cof';
GMAT MoonAllForces.SRP.Flux = 1367;
GMAT MoonAllForces.SRP.SRPModel = Spherical;
GMAT MoonAllForces.SRP.Nominal_Sun = 149597870.691;

Create Propagator EarthFull;
GMAT EarthFull.FM = AllForces;
GMAT EarthFull.Type = RungeKutta89;
GMAT EarthFull.InitialStepSize = 60;
GMAT EarthFull.Accuracy = 9.999999999999999e-012;
GMAT EarthFull.MinStep = 0.001;
GMAT EarthFull.MaxStep = 45000;
GMAT EarthFull.MaxStepAttempts = 50;
GMAT EarthFull.StopIfAccuracyIsViolated = true;

Create Propagator MoonFull;
GMAT MoonFull.FM = MoonAllForces;
GMAT MoonFull.Type = RungeKutta89;
GMAT MoonFull.InitialStepSize = 60;
GMAT MoonFull.Accuracy = 9.999999999999999e-012;
GMAT MoonFull.MinStep = 0.001;
GMAT MoonFull.MaxStep = 15000;
GMAT MoonFull.MaxStepAttempts = 50;
GMAT MoonFull.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.14676929889;
GMAT TOI.Element2 = 0.046042675892;
GMAT TOI.Element3 = 0.090223244097;

Create ImpulsiveBurn LOI;
GMAT LOI.CoordinateSystem = Local;
GMAT LOI.Origin = Luna;
GMAT LOI.Axes = VNB;
GMAT LOI.Element1 = -0.652;
GMAT LOI.Element2 = 0;
GMAT LOI.Element3 = 0;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OpenFramesInterface EarthInertialView;
GMAT EarthInertialView.UpperLeft = [ 0.25 0.0 ];
GMAT EarthInertialView.Size = [ 0.37 0.45];
GMAT EarthInertialView.RelativeZOrder = 716;
GMAT EarthInertialView.Maximized = false;
GMAT EarthInertialView.Add = {Sat, Earth, Luna, Sun};
GMAT EarthInertialView.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthInertialView.View = {CoordinateSystemView1, EarthView1, SatView1};

Create OpenFramesInterface MoonInertialView;
GMAT MoonInertialView.SolverIterations = Current;
GMAT MoonInertialView.UpperLeft = [ 0.25 0.46 ];
GMAT MoonInertialView.Size = [ 0.37 0.45 ];
GMAT MoonInertialView.RelativeZOrder = 710;
GMAT MoonInertialView.Maximized = false;
GMAT MoonInertialView.Add = {Sat, Earth, Luna, Sun};
GMAT MoonInertialView.CoordinateSystem = MoonMJ2000Eq;
GMAT MoonInertialView.View = {CoordinateSystemView2, LunaView2, SatView2};


Create XYPlot RadApoPlot;
GMAT RadApoPlot.SolverIterations = None;
GMAT RadApoPlot.UpperLeft = [ 0.63 0.15 ];
GMAT RadApoPlot.Size = [ 0.36 0.45 ];
GMAT RadApoPlot.RelativeZOrder = 705;
GMAT RadApoPlot.Maximized = false;
GMAT RadApoPlot.XVariable = Sat.A1ModJulian;
GMAT RadApoPlot.YVariables = {Sat.RMAG};
GMAT RadApoPlot.ShowGrid = true;
GMAT RadApoPlot.ShowPlot = true;


%----------------------------------------
%---------- View Objects
%----------------------------------------

Create OpenFramesView CoordinateSystemView1;
GMAT CoordinateSystemView1.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView1.ViewTrajectory = Off;
GMAT CoordinateSystemView1.InertialFrame = Off;
GMAT CoordinateSystemView1.SetDefaultLocation = Off;
GMAT CoordinateSystemView1.SetCurrentLocation = On;
GMAT CoordinateSystemView1.CurrentEye = [ 900000 -600000 300000 ];
GMAT CoordinateSystemView1.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView1.CurrentUp = [ 0 0 1 ];

Create OpenFramesView EarthView1;
GMAT EarthView1.ViewFrame = Earth;
GMAT EarthView1.ViewTrajectory = Off;
GMAT EarthView1.InertialFrame = Off;
GMAT EarthView1.SetDefaultLocation = Off;
GMAT EarthView1.SetCurrentLocation = Off;

Create OpenFramesView SatView1;
GMAT SatView1.ViewFrame = Sat;
GMAT SatView1.ViewTrajectory = Off;
GMAT SatView1.InertialFrame = Off;
GMAT SatView1.SetDefaultLocation = Off;
GMAT SatView1.SetCurrentLocation = Off;

Create OpenFramesView CoordinateSystemView2;
GMAT CoordinateSystemView2.ViewFrame = CoordinateSystem;
GMAT CoordinateSystemView2.ViewTrajectory = Off;
GMAT CoordinateSystemView2.InertialFrame = Off;
GMAT CoordinateSystemView2.SetDefaultLocation = Off;
GMAT CoordinateSystemView2.SetCurrentLocation = On;
GMAT CoordinateSystemView2.CurrentEye = [ 5000 -10000 22000 ];
GMAT CoordinateSystemView2.CurrentCenter = [ 0 0 0 ];
GMAT CoordinateSystemView2.CurrentUp = [ 1 0 0 ];

Create OpenFramesView LunaView2;
GMAT LunaView2.ViewFrame = Luna;
GMAT LunaView2.ViewTrajectory = Off;
GMAT LunaView2.InertialFrame = Off;
GMAT LunaView2.SetDefaultLocation = Off;
GMAT LunaView2.SetCurrentLocation = Off;

Create OpenFramesView SatView2;
GMAT SatView2.ViewFrame = Sat;
GMAT SatView2.ViewTrajectory = Off;
GMAT SatView2.InertialFrame = Off;
GMAT SatView2.SetDefaultLocation = Off;
GMAT SatView2.SetCurrentLocation = Off;


%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************

BeginMissionSequence;

%------------------------------
%  Propagate to Earth periapsis
%------------------------------

Propagate 'Prop to Perigee' EarthFull(Sat) {Sat.Periapsis};


%------------------------------
%  Target Lunar B-plane
%------------------------------
Target 'Target B-Plane' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   
   Vary 'Vary TOI.V' DC1(TOI.Element1 = 0.14, {Perturbation = 1e-005, Lower = .13, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary TOI.B' DC1(TOI.Element3 = 0.1, {Perturbation = 1e-005, Lower = -.5, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   Maneuver 'Apply TOI' TOI(Sat);
   
   Propagate 'Prop to Moon SOI' EarthFull(Sat) {Sat.Earth.RMAG = 325000, StopTolerance = 1e-005};
   Propagate 'Prop to Periselene' MoonFull(Sat) {Sat.Luna.Periapsis, StopTolerance = 1e-005};
   
   Achieve 'Achieve BdotR' DC1(Sat.MoonMJ2000Eq.BdotT = 15000.4401777, {Tolerance = 3});
   Achieve 'Achieve BdotT' DC1(Sat.MoonMJ2000Eq.BdotR = 4000.59308992, {Tolerance = 3});
   %Achieve DC1(Sat.RMAG = 390000 , {Tolerance = 200000}); 
   
EndTarget;  % For targeter DC1

%------------------------------
% Target to orbit the Moon
%------------------------------
Target 'Target Lunar Insertion' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   
   Vary 'Vary LOI.V' DC1(LOI.Element1 = -0.65198120104, {Perturbation = 1e-003, Lower = -3, Upper = 0, MaxStep = .3, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   Maneuver 'Apply LOI' LOI(Sat);
   
   Achieve 'Achieve ECC = 0' DC1(Sat.Luna.ECC = 0, {Tolerance = 0.0001});
   
EndTarget;  % For targeter DC1


%------------------------------
% Propagate for a few days
%------------------------------
Propagate 'Prop 15 days' MoonFull(Sat) {Sat.ElapsedDays = 15};
