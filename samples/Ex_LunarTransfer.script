%  Script Mission - Lunar Transfer Example
%
%  This script demonstrates how to set up a lunar transfer mission
%

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = UTCGregorian;
GMAT Sat.Epoch = '22 Jul 2014 11:29:10.811';
GMAT Sat.CoordinateSystem = EarthMJ2000Eq;
GMAT Sat.DisplayStateType = Cartesian;
GMAT Sat.X = -137380.1984338506;
GMAT Sat.Y = 75679.87867537055;
GMAT Sat.Z = 21487.63875187856;
GMAT Sat.VX = -0.2324532014235503;
GMAT Sat.VY = -0.4462753967758019;
GMAT Sat.VZ = 0.08561205662877103;
GMAT Sat.DryMass = 1000;
GMAT Sat.Cd = 2.2;
GMAT Sat.Cr = 1.7;
GMAT Sat.DragArea = 15;
GMAT Sat.SRPArea = 1;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create ForceModel AllForces;
GMAT AllForces.CentralBody = Earth;
GMAT AllForces.PrimaryBodies = {Earth};
GMAT AllForces.PointMasses = {Sun, Luna, Venus, Mars, Jupiter, Saturn, Uranus, Neptune};
GMAT AllForces.SRP = On;
GMAT AllForces.RelativisticCorrection = Off;
GMAT AllForces.ErrorControl = RSSStep;
GMAT AllForces.GravityField.Earth.Degree = 20;
GMAT AllForces.GravityField.Earth.Order = 20;
GMAT AllForces.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT AllForces.GravityField.Earth.TideModel = 'None';
GMAT AllForces.SRP.Flux = 1367;
GMAT AllForces.SRP.SRPModel = Spherical;
GMAT AllForces.SRP.Nominal_Sun = 149597870.691;
GMAT AllForces.Drag.AtmosphereModel = MSISE90;
GMAT AllForces.Drag.HistoricWeatherSource = 'ConstantFluxAndGeoMag';
GMAT AllForces.Drag.PredictedWeatherSource = 'ConstantFluxAndGeoMag';
GMAT AllForces.Drag.CSSISpaceWeatherFile = '../samples/SupportFiles/CSSI_2004To2026.txt';
GMAT AllForces.Drag.SchattenFile = 'SchattenPredict.txt';
GMAT AllForces.Drag.F107 = 150;
GMAT AllForces.Drag.F107A = 150;
GMAT AllForces.Drag.MagneticIndex = 3;
GMAT AllForces.Drag.SchattenErrorModel = 'Nominal';
GMAT AllForces.Drag.SchattenTimingModel = 'NominalCycle';

Create ForceModel MoonAllForces;
GMAT MoonAllForces.CentralBody = Luna;
GMAT MoonAllForces.PrimaryBodies = {Luna};
GMAT MoonAllForces.PointMasses = {Sun, Earth, Venus, Mars, Jupiter, Saturn, Uranus, Neptune};
GMAT MoonAllForces.Drag = None;
GMAT MoonAllForces.SRP = On;
GMAT MoonAllForces.RelativisticCorrection = Off;
GMAT MoonAllForces.ErrorControl = RSSStep;
GMAT MoonAllForces.GravityField.Luna.Degree = 20;
GMAT MoonAllForces.GravityField.Luna.Order = 20;
GMAT MoonAllForces.GravityField.Luna.PotentialFile = 'LP165P.cof';
GMAT MoonAllForces.SRP.Flux = 1367;
GMAT MoonAllForces.SRP.SRPModel = Spherical;
GMAT MoonAllForces.SRP.Nominal_Sun = 149597870.691;

Create Propagator EarthFull;
GMAT EarthFull.FM = AllForces;
GMAT EarthFull.Type = RungeKutta89;
GMAT EarthFull.InitialStepSize = 60;
GMAT EarthFull.Accuracy = 9.999999999999999e-012;
GMAT EarthFull.MinStep = 0.001;
GMAT EarthFull.MaxStep = 45000;
GMAT EarthFull.MaxStepAttempts = 50;
GMAT EarthFull.StopIfAccuracyIsViolated = true;

Create Propagator MoonFull;
GMAT MoonFull.FM = MoonAllForces;
GMAT MoonFull.Type = RungeKutta89;
GMAT MoonFull.InitialStepSize = 60;
GMAT MoonFull.Accuracy = 9.999999999999999e-012;
GMAT MoonFull.MinStep = 0.001;
GMAT MoonFull.MaxStep = 15000;
GMAT MoonFull.MaxStepAttempts = 50;
GMAT MoonFull.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0.14676929889;
GMAT TOI.Element2 = 0.046042675892;
GMAT TOI.Element3 = 0.090223244097;

Create ImpulsiveBurn LOI;
GMAT LOI.CoordinateSystem = Local;
GMAT LOI.Origin = Luna;
GMAT LOI.Axes = VNB;
GMAT LOI.Element1 = -0.652;
GMAT LOI.Element2 = 0;
GMAT LOI.Element3 = 0;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView EarthInertialView;
GMAT EarthInertialView.UpperLeft = [ 0.3088235294117647 0.003978779840848806 ];
GMAT EarthInertialView.Size = [ 0.3588235294117647 0.4535809018567639 ];
GMAT EarthInertialView.RelativeZOrder = 716;
GMAT EarthInertialView.Maximized = false;
GMAT EarthInertialView.Add = {Sat, Earth, Luna};
GMAT EarthInertialView.DrawObject = [ true true true ];
GMAT EarthInertialView.ViewPointVector = [ 30000 -20000 10000 ];
GMAT EarthInertialView.ViewScaleFactor = 30;

Create OrbitView MoonInertialView;
GMAT MoonInertialView.SolverIterations = Current;
GMAT MoonInertialView.UpperLeft = [ 0.3082352941176471 0.4641909814323608 ];
GMAT MoonInertialView.Size = [ 0.3588235294117647 0.4535809018567639 ];
GMAT MoonInertialView.RelativeZOrder = 710;
GMAT MoonInertialView.Maximized = false;
GMAT MoonInertialView.Add = {Sat, Earth, Luna};
GMAT MoonInertialView.CoordinateSystem = MoonMJ2000Eq;
GMAT MoonInertialView.DrawObject = [ true true true ];
GMAT MoonInertialView.DataCollectFrequency = 1;
GMAT MoonInertialView.UpdatePlotFrequency = 50;
GMAT MoonInertialView.NumPointsToRedraw = 0;
GMAT MoonInertialView.ShowPlot = true;
GMAT MoonInertialView.ShowLabels = true;
GMAT MoonInertialView.ViewPointReference = Luna;
GMAT MoonInertialView.ViewDirection = Luna;
GMAT MoonInertialView.ViewUpCoordinateSystem = MoonMJ2000Eq;

Create XYPlot RadApoPlot;
GMAT RadApoPlot.SolverIterations = None;
GMAT RadApoPlot.UpperLeft = [ 0.6305882352941177 0.149867374005305 ];
GMAT RadApoPlot.Size = [ 0.3588235294117647 0.4535809018567639 ];
GMAT RadApoPlot.RelativeZOrder = 705;
GMAT RadApoPlot.Maximized = false;
GMAT RadApoPlot.XVariable = Sat.A1ModJulian;
GMAT RadApoPlot.YVariables = {Sat.RMAG};
GMAT RadApoPlot.ShowGrid = true;
GMAT RadApoPlot.ShowPlot = true;

%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************

BeginMissionSequence;

%------------------------------
%  Propagate to Earth periapsis
%------------------------------

Propagate 'Prop to Perigee' EarthFull(Sat) {Sat.Periapsis};


%------------------------------
%  Target Lunar B-plane
%------------------------------
Target 'Target B-Plane' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   
   Vary 'Vary TOI.V' DC1(TOI.Element1 = 0.14, {Perturbation = 1e-005, Lower = .13, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   Vary 'Vary TOI.B' DC1(TOI.Element3 = 0.1, {Perturbation = 1e-005, Lower = -.5, Upper = .5, MaxStep = .01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   Maneuver 'Apply TOI' TOI(Sat);
   
   Propagate 'Prop to Moon SOI' EarthFull(Sat) {Sat.Earth.RMAG = 325000, StopTolerance = 1e-005};
   Propagate 'Prop to Periselene' MoonFull(Sat) {Sat.Luna.Periapsis, StopTolerance = 1e-005};
   
   Achieve 'Achieve BdotR' DC1(Sat.MoonMJ2000Eq.BdotT = 15000.4401777, {Tolerance = 3});
   Achieve 'Achieve BdotT' DC1(Sat.MoonMJ2000Eq.BdotR = 4000.59308992, {Tolerance = 3});
   %Achieve DC1(Sat.RMAG = 390000 , {Tolerance = 200000}); 
   
EndTarget;  % For targeter DC1

%------------------------------
% Target to orbit the Moon
%------------------------------
Target 'Target Lunar Insertion' DC1 {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
   
   Vary 'Vary LOI.V' DC1(LOI.Element1 = -0.65198120104, {Perturbation = 1e-003, Lower = -3, Upper = 0, MaxStep = .3, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
   
   Maneuver 'Apply LOI' LOI(Sat);
   
   Achieve 'Achieve ECC = 0' DC1(Sat.Luna.ECC = 0, {Tolerance = 0.0001});
   
EndTarget;  % For targeter DC1


%------------------------------
% Propagate for a few days
%------------------------------
Propagate 'Prop 15 days' MoonFull(Sat) {Sat.ElapsedDays = 15};
