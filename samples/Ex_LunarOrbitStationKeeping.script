
%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft LunaSat;
GMAT LunaSat.DateFormat = TAIModJulian;
GMAT LunaSat.Epoch = '21545';
GMAT LunaSat.CoordinateSystem = MoonInertial;
GMAT LunaSat.DisplayStateType = Keplerian;
GMAT LunaSat.SMA = 1851.000000000005;
GMAT LunaSat.ECC = 0.0009999999999749656;
GMAT LunaSat.INC = 15.33311977007591;
GMAT LunaSat.RAAN = 201.083353371649;
GMAT LunaSat.AOP = 167.2638606069433;
GMAT LunaSat.TA = 38.03104991952923;
GMAT LunaSat.DryMass = 850;
GMAT LunaSat.Cd = 2.2;
GMAT LunaSat.Cr = 1.8;
GMAT LunaSat.ModelScale = 0.08;

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel MoonProp_ForceModel;
GMAT MoonProp_ForceModel.CentralBody = Luna;
GMAT MoonProp_ForceModel.PrimaryBodies = {Luna};
GMAT MoonProp_ForceModel.PointMasses = {Earth, Jupiter, Sun};
GMAT MoonProp_ForceModel.Drag = None;
GMAT MoonProp_ForceModel.SRP = On;
GMAT MoonProp_ForceModel.RelativisticCorrection = Off;
GMAT MoonProp_ForceModel.ErrorControl = RSSStep;
GMAT MoonProp_ForceModel.GravityField.Luna.Degree = 80;
GMAT MoonProp_ForceModel.GravityField.Luna.Order = 80;
GMAT MoonProp_ForceModel.GravityField.Luna.PotentialFile = 'LP165P.cof';
GMAT MoonProp_ForceModel.SRP.Flux = 1367;
GMAT MoonProp_ForceModel.SRP.SRPModel = Spherical;
GMAT MoonProp_ForceModel.SRP.Nominal_Sun = 149597870.691;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator MoonProp;
GMAT MoonProp.FM = MoonProp_ForceModel;
GMAT MoonProp.Type = PrinceDormand78;
GMAT MoonProp.InitialStepSize = 60;
GMAT MoonProp.Accuracy = 9.999999999999999e-012;
GMAT MoonProp.MinStep = 0.001;
GMAT MoonProp.MaxStep = 2700;
GMAT MoonProp.MaxStepAttempts = 50;
GMAT MoonProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn dv1;
GMAT dv1.CoordinateSystem = Local;
GMAT dv1.Origin = Luna;
GMAT dv1.Axes = VNB;

Create ImpulsiveBurn dv2;
GMAT dv2.CoordinateSystem = Local;
GMAT dv2.Origin = Luna;
GMAT dv2.Axes = VNB;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem MoonInertial;
GMAT MoonInertial.Origin = Luna;
GMAT MoonInertial.Axes = BodyInertial;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DefaultDC;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.UpperLeft = [ 0.4064705882352941 0.01856763925729443 ];
GMAT DefaultOrbitView.Size = [ 0.451764705882353 0.4509283819628647 ];
GMAT DefaultOrbitView.RelativeZOrder = 661;
GMAT DefaultOrbitView.Add = {LunaSat, Luna};
GMAT DefaultOrbitView.CoordinateSystem = MoonInertial;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.ViewPointReference = Luna;
GMAT DefaultOrbitView.ViewPointVector = [ 3500 3500 3500 ];
GMAT DefaultOrbitView.ViewDirection = Luna;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = MoonInertial;
GMAT DefaultOrbitView.MaxPlotPoints = 200000;

Create XYPlot XYPlot1;
GMAT XYPlot1.UpperLeft = [ 0.3858823529411765 0.4986737400530504 ];
GMAT XYPlot1.Size = [ 0.5229411764705882 0.4509283819628647 ];
GMAT XYPlot1.RelativeZOrder = 656;
GMAT XYPlot1.XVariable = LunaSat.A1ModJulian;
GMAT XYPlot1.YVariables = {LunaSat.Luna.RadPer};

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------

Create Variable I;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
For 'For I = 1:3' I = 1:1:3;
   Propagate 'Prop to RMAG = 1785' MoonProp(LunaSat) {LunaSat.Luna.RMAG = 1825};
   Target 'Raise Periapsis' DefaultDC {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
      Propagate 'Prop to Periapsis' MoonProp(LunaSat) {LunaSat.Luna.Periapsis};
      Vary 'Vary dv1.V' DefaultDC(dv1.Element1 = 0.02, {Perturbation = 0.0001, Lower = -1, Upper = 1, MaxStep = 0.01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
      Vary 'Vary dv2.V' DefaultDC(dv2.Element1 = 0.02, {Perturbation = 0.0001, Lower = -1, Upper = 1, MaxStep = 0.01, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
      Maneuver 'Apply dv1' dv1(LunaSat);
      Propagate 'Prop to Apoapsis' MoonProp(LunaSat) {LunaSat.Luna.Apoapsis};
      Achieve 'Achieve RMAG = 1850' DefaultDC(LunaSat.Luna.RMAG = 1850, {Tolerance = 0.1});
      Maneuver 'Apply dv2' dv2(LunaSat);
      Achieve 'Achieve ECC = 0' DefaultDC(LunaSat.Luna.ECC = 0, {Tolerance = .0001});
   EndTarget;  % For targeter DefaultDC
EndFor;
Propagate 'Prop to RMAG = 1785' MoonProp(LunaSat) {LunaSat.Luna.RadPer = 1825};
