%-----------------------------------------------------------------------------------------
%% 	1.  Create and configure the spacecraft, spacecraft transponder, and related parameters 
%-----------------------------------------------------------------------------------------

%  Create the Sun-centered J2000 frame.
Create CoordinateSystem SunMJ2000Eq;
SunMJ2000Eq.Origin = Sun;
SunMJ2000Eq.Axes   = MJ2000Eq;  %Earth mean equator axes

%  Specify the satellite orbit and the satellite parameters
Create Spacecraft Sat;
Sat.DateFormat       = UTCGregorian;
Sat.CoordinateSystem = SunMJ2000Eq;
Sat.DisplayStateType = Cartesian;

Sat.Epoch            = 19 Aug 2015 00:00:00.000;
Sat.X                = -126544968
Sat.Y                =  61978514
Sat.Z                =  24133221
Sat.VX               = -13.789
Sat.VY               = -24.673
Sat.VZ               = -10.662

Sat.Id               = 11111;
Sat.AddHardware      = {HGA, SatTransponder};


%	Spacecraft electronics. 
Create Antenna HGA;
Create Transponder SatTransponder;

SatTransponder.PrimaryAntenna  = HGA;
SatTransponder.HardwareDelay   = 1e-06;		%in seconds
SatTransponder.TurnAroundRatio = '880/749';

%-----------------------------------------------------------------------------------------
%%  	2.  Create and configure the Ground Station and related parameters
%-----------------------------------------------------------------------------------------

%  Ground Station electronics. 
Create Antenna DSNAntenna;
Create Transmitter DSNTransmitter;
Create Receiver DSNReceiver;

DSNTransmitter.Frequency      = 7200;   %MHz.
DSNTransmitter.PrimaryAntenna = DSNAntenna;
DSNReceiver.PrimaryAntenna    = DSNAntenna;

%   Create Ground station and associated error models
Create GroundStation CAN;
CAN.CentralBody           = Earth;
CAN.StateType             = Cartesian;
CAN.HorizonReference      = Ellipsoid;
CAN.Location1             = -4461.083514
CAN.Location2             = 2682.281745
CAN.Location3             =  -3674.570392
CAN.Id                    = 22222;
CAN.AddHardware           = {DSNTransmitter, DSNAntenna, DSNReceiver};
CAN.MinimumElevationAngle = 7.0;
CAN.IonosphereModel       = 'IRI2007';
CAN.TroposphereModel      = 'HopfieldSaastamoinen';


Create ErrorModel DSNrange;
DSNrange.Type             = 'DSN_SeqRange';
DSNrange.NoiseSigma       = 10.63;
DSNrange.Bias             = 0.0;

Create ErrorModel DSNdoppler;
DSNdoppler.Type       		= 'DSN_TCP';
DSNdoppler.NoiseSigma		= 0.0282;
DSNdoppler.Bias  				= 0.0;

CAN.ErrorModels           = {DSNrange, DSNdoppler};

%-----------------------------------------------------------------------------------------
%% 	3.  Define the types of measurements to be simulated
%-----------------------------------------------------------------------------------------

Create TrackingFileSet DSNsimData;
DSNsimData.AddTrackingConfig 			= {{CAN, Sat, CAN}, 'DSN_SeqRange'};   
DSNsimData.AddTrackingConfig 			= {{CAN, Sat, CAN},'DSN_TCP'}; 
                   
DSNsimData.FileName 						= {'Sat_dsn_range_and_doppler_measurements.gmd'};
DSNsimData.UseLightTime 				= true;
DSNsimData.UseRelativityCorrection 	= true;
DSNsimData.UseETminusTAI 				= true;
DSNsimData.SimDopplerCountInterval 	= 10.0;
DSNsimData.SimRangeModuloConstant 	= 3.3554432e+07;

%-----------------------------------------------------------------------------------------
%%   4.  Create and configure Force model and propagator.
%-----------------------------------------------------------------------------------------

Create ForceModel Fm;
Create Propagator Prop;
Fm.CentralBody            = Sun;
Fm.PointMasses            = {Sun, Earth, Luna, Mars, Saturn, ...
                             Uranus, Mercury, Venus, Jupiter};
Fm.ErrorControl 		     = None;
Fm.SRP                    = On;
Fm.RelativisticCorrection = On;
Prop.FM                   = Fm;
Prop.MinStep 			  = 0;
%-----------------------------------------------------------------------------------------
%%		5.  Create and configure Simulator object
%-----------------------------------------------------------------------------------------

Create Simulator Sim;
Sim.AddData             = {DSNsimData};
Sim.EpochFormat    		= UTCGregorian;
Sim.InitialEpoch        = '19 Aug 2015 00:00:00.000';
Sim.FinalEpoch          = '19 Aug 2015 00:12:00.000';
Sim.MeasurementTimeStep = 600;
Sim.Propagator          = Prop;
Sim.AddNoise = Off

%-----------------------------------------------------------------------------------------
%%		6.  Run the mission 
%-----------------------------------------------------------------------------------------

BeginMissionSequence
 
RunSimulator Sim