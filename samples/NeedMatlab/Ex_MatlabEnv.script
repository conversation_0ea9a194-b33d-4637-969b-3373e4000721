% This script prints MATLAB interface environment information
% to the report file Ex_MatlabEnv.report, located in the GMAT
% output directory.

Create MatlabFunction matlabroot path pwd version

Create ReportFile r
r.Filename = 'Ex_MatlabEnv.report'
r.WriteHeaders = off

Create String s matlabroot_hdr path_hdr pwd_hdr version_hdr blank 
matlabroot_hdr = 'matlabroot()'
path_hdr = 'path()'
pwd_hdr = 'pwd()'
version_hdr = 'version()'
blank = ''

BeginMissionSequence

[s] = path
Report r path_hdr
Report r s
Report r blank

[s] = pwd
Report r pwd_hdr
Report r s
Report r blank

[s] = matlabroot
Report r matlabroot_hdr
Report r s
Report r blank

[s] = version
Report r version_hdr
Report r s
