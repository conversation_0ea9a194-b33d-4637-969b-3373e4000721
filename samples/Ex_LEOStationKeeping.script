%General Mission Analysis Tool(GMAT) Script
%Created: 2011-06-13 02:33:42


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft LEOsat;
GMAT LEOsat.DateFormat = UTCGregorian;
GMAT LEOsat.Epoch = '01 Jan 2000 11:59:28.000';
GMAT LEOsat.CoordinateSystem = EarthMJ2000Eq;
GMAT LEOsat.DisplayStateType = Cartesian;
GMAT LEOsat.X = -4083.9;
GMAT LEOsat.Y = 4691.8;
GMAT LEOsat.Z = -2576.7;
GMAT LEOsat.VX = -4.252;
GMAT LEOsat.VY = -5.509;
GMAT LEOsat.VZ = -3.284;
GMAT LEOsat.DryMass = 850;
GMAT LEOsat.Cd = 2.2;
GMAT LEOsat.Cr = 1.8;
GMAT LEOsat.DragArea = 15;
GMAT LEOsat.SRPArea = 1

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel LEOprop_ForceModel;
GMAT LEOprop_ForceModel.CentralBody = Earth;
GMAT LEOprop_ForceModel.PrimaryBodies = {Earth};
GMAT LEOprop_ForceModel.PointMasses = {Luna, Sun};
GMAT LEOprop_ForceModel.SRP = On;
GMAT LEOprop_ForceModel.RelativisticCorrection = Off;
GMAT LEOprop_ForceModel.ErrorControl = RSSStep;
GMAT LEOprop_ForceModel.GravityField.Earth.Degree = 4;
GMAT LEOprop_ForceModel.GravityField.Earth.Order = 4;
GMAT LEOprop_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT LEOprop_ForceModel.GravityField.Earth.TideModel = 'None';
GMAT LEOprop_ForceModel.SRP.Flux = 1367;
GMAT LEOprop_ForceModel.SRP.SRPModel = Spherical;
GMAT LEOprop_ForceModel.SRP.Nominal_Sun = 149597870.691;
GMAT LEOprop_ForceModel.Drag.AtmosphereModel = JacchiaRoberts;
GMAT LEOprop_ForceModel.Drag.HistoricWeatherSource = 'ConstantFluxAndGeoMag';
GMAT LEOprop_ForceModel.Drag.PredictedWeatherSource = 'ConstantFluxAndGeoMag';
GMAT LEOprop_ForceModel.Drag.CSSISpaceWeatherFile = '../samples/SupportFiles/CSSI_2004To2026.txt';
GMAT LEOprop_ForceModel.Drag.SchattenFile = 'SchattenPredict.txt';
GMAT LEOprop_ForceModel.Drag.F107 = 150;
GMAT LEOprop_ForceModel.Drag.F107A = 150;
GMAT LEOprop_ForceModel.Drag.MagneticIndex = 3;
GMAT LEOprop_ForceModel.Drag.SchattenErrorModel = 'Nominal';
GMAT LEOprop_ForceModel.Drag.SchattenTimingModel = 'NominalCycle';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LEOprop;
GMAT LEOprop.FM = LEOprop_ForceModel;
GMAT LEOprop.Type = RungeKutta89;
GMAT LEOprop.InitialStepSize = 60;
GMAT LEOprop.Accuracy = 1e-011;
GMAT LEOprop.MinStep = 0.001;
GMAT LEOprop.MaxStep = 2700;
GMAT LEOprop.MaxStepAttempts = 50;
GMAT LEOprop.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TCM1;
GMAT TCM1.CoordinateSystem = Local;
GMAT TCM1.Origin = Earth;
GMAT TCM1.Axes = VNB;

Create ImpulsiveBurn TCM2;
GMAT TCM2.CoordinateSystem = Local;
GMAT TCM2.Origin = Earth;
GMAT TCM2.Axes = VNB;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ 0.4041176470588235 0.4721485411140584 ];
GMAT DefaultOrbitView.Size = [ 0.5 0.4496021220159151 ];
GMAT DefaultOrbitView.RelativeZOrder = 571;
GMAT DefaultOrbitView.Maximized = false;
GMAT DefaultOrbitView.Add = {LEOsat, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];

Create XYPlot XYPlot1;
GMAT XYPlot1.SolverIterations = Current;
GMAT XYPlot1.UpperLeft = [ 0.4052941176470588 0.02122015915119363 ];
GMAT XYPlot1.Size = [ 0.5 0.4496021220159151 ];
GMAT XYPlot1.RelativeZOrder = 569;
GMAT XYPlot1.Maximized = false;
GMAT XYPlot1.XVariable = LEOsat.A1ModJulian;
GMAT XYPlot1.YVariables = {LEOsat.Earth.Altitude};
GMAT XYPlot1.ShowGrid = true;
GMAT XYPlot1.ShowPlot = true;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

While 'While ElapsedDays < 10' LEOsat.ElapsedDays < 10.0
   Propagate 'Prop One Step' LEOprop(LEOsat);
   If 'If Alt < Threshold' LEOsat.Earth.Altitude < 342
      Propagate 'Prop To Periapsis' LEOprop(LEOsat) {LEOsat.Periapsis};
      Target 'Raise Orbit' DC {SolveMode = Solve, ExitMode = DiscardAndContinue, ShowProgressWindow = true};
         Vary 'Vary TCM1.V' DC(TCM1.Element1 = 0.002, {Perturbation = 0.0001, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.05, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
         Maneuver 'Apply TCM1' TCM1(LEOsat);
         Propagate 'Prop to Apoapsis' LEOprop(LEOsat) {LEOsat.Earth.Apoapsis};
         Achieve 'Achieve RMAG' DC(LEOsat.Earth.RMAG = 6737, {Tolerance = 0.1});
         Vary 'Vary TCM2.V' DC(TCM2.Element1 = 1e-005, {Perturbation = 0.00005, Lower = -9.999999e300, Upper = 9.999999e300, MaxStep = 0.05, AdditiveScaleFactor = 0.0, MultiplicativeScaleFactor = 1.0});
         Maneuver 'Apply TCM2' TCM2(LEOsat);
         Achieve 'Achieve ECC' DC(LEOsat.Earth.ECC = 0.00005, {Tolerance = 0.0001});
      EndTarget;  % For targeter DC
   EndIf;
EndWhile;
