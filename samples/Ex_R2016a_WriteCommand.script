Create Spacecraft mySat

Create String scriptStatus
Create ReportFile theReport

BeginMissionSequence

%  Write the spacecraft object to the report text file, but not to Message Window or log file
Write mySat { Style = Script, LogFile = false, MessageWindow = false, ReportFile = theReport }

%  Write status to message window and log file
scriptStatus = 'RunStatus = Script Ran To Completion' 
Write scriptStatus { Style = Concise, LogFile = true, MessageWindow = true }
