%
%   Ex_R2020a_Estimate_ThrustScaleFactor
%
%   Apply a finite maneuver to simulated data and estimate an associated thrust
%   scale factor. We will simulate a 20% cold burn and estimate that error in 
%   the BLS OD.
%

%
%   Spacecraft
%

Create Spacecraft SimSat;

SimSat.DateFormat       = UTCGregorian;
SimSat.Epoch            = '01 Jan 2000 00:00:00.000';
SimSat.CoordinateSystem = EarthMJ2000Eq;
SimSat.DisplayStateType = Cartesian;
SimSat.X                = 8000.0;
SimSat.Y                = 0.0;
SimSat.Z                = 0.0;
SimSat.VX               = 0.0;
SimSat.VY               = 8.7;
SimSat.VZ               = 4.2;
SimSat.DryMass          = 850;
SimSat.Cd               = 2.2;
SimSat.Cr               = 1.4;
SimSat.DragArea         = 15;
SimSat.SRPArea          = 15;
SimSat.Id               = 'LEOSat';
SimSat.AddHardware      = {Transponder1, SpacecraftAntenna};
SimSat.Tanks            = {SimPropTank};

Create Spacecraft EstSat

EstSat.DateFormat       = UTCGregorian;
EstSat.Epoch            = '01 Jan 2000 00:00:00.000';
EstSat.CoordinateSystem = EarthMJ2000Eq;
EstSat.DisplayStateType = Cartesian;
EstSat.X                = 8000.0;
EstSat.Y                = 0.0;
EstSat.Z                = 0.0;
EstSat.VX               = 0.0;
EstSat.VY               = 8.7;
EstSat.VZ               = 4.2;
EstSat.DryMass          = 850;
EstSat.Cd               = 2.2;
EstSat.Cr               = 1.4;
EstSat.DragArea         = 15;
EstSat.SRPArea          = 15;
EstSat.Id               = 'LEOSat';
EstSat.AddHardware      = {Transponder1, SpacecraftAntenna};
EstSat.Tanks            = {EstPropTank};
EstSat.SolveFors        = {CartesianState};

%
%   Spacecraft hardware
%

Create Antenna SpacecraftAntenna;
Create Transponder Transponder1;

Transponder1.PrimaryAntenna  = SpacecraftAntenna;
Transponder1.HardwareDelay   = 0.0;
Transponder1.TurnAroundRatio = '240/221' 

Create ChemicalTank SimPropTank EstPropTank;

SimPropTank.FuelMass      = 200;
SimPropTank.PressureModel = PressureRegulated;

EstPropTank.FuelMass      = 200;
EstPropTank.PressureModel = PressureRegulated;

%
%   GroundStation hardware
%

Create Transmitter Transmitter1;
Create Antenna GroundAntenna;
Create Receiver Receiver1;

Transmitter1.PrimaryAntenna = GroundAntenna;
Transmitter1.Frequency      = 2067.5;
Receiver1.PrimaryAntenna    = GroundAntenna;

%
%   Error models
%

Create ErrorModel RangeModel;

RangeModel.Type         = 'DSN_SeqRange';
RangeModel.NoiseSigma   = 35.0;
RangeModel.Bias         = 0.0;
RangeModel.SolveFors    = {};

Create ErrorModel DopplerModel;

DopplerModel.Type       = 'DSN_TCP';
DopplerModel.NoiseSigma = 0.001;
DopplerModel.Bias       = 0.0;
DopplerModel.SolveFors  = {};

%
%   Ground stations
%

Create GroundStation GDS;

GDS.CentralBody           = Earth;
GDS.StateType             = Cartesian;
GDS.HorizonReference      = Ellipsoid;
GDS.Location1             = -2353.621251;
GDS.Location2             = -4641.341542;
GDS.Location3             =  3677.052370;
GDS.Id                    = 'GDS';
GDS.AddHardware           = {Transmitter1, Receiver1, GroundAntenna};
GDS.MinimumElevationAngle = 0;
GDS.IonosphereModel       = 'None';
GDS.TroposphereModel      = 'None';
GDS.ErrorModels           = {RangeModel, DopplerModel};

Create GroundStation CAN;

CAN.CentralBody           = Earth;
CAN.StateType             = Cartesian;
CAN.HorizonReference      = Ellipsoid;
CAN.Location1             = -4461.083514;
CAN.Location2             =  2682.281745;
CAN.Location3             = -3674.570392;
CAN.Id                    = 'CAN';
CAN.AddHardware           = {Transmitter1, Receiver1, GroundAntenna};
CAN.MinimumElevationAngle = 0;
CAN.IonosphereModel       = 'None';
CAN.TroposphereModel      = 'None';
CAN.ErrorModels           = {RangeModel, DopplerModel};


Create GroundStation MAD; 

MAD.CentralBody           = Earth;
MAD.StateType             = Cartesian;
MAD.HorizonReference      = Ellipsoid;
MAD.Location1             =  4849.519988;
MAD.Location2             = -0360.641653;
MAD.Location3             =  4114.504590;
MAD.Id                    = 'MAD';
MAD.AddHardware           = {Transmitter1, Receiver1, GroundAntenna};
MAD.MinimumElevationAngle = 0;
MAD.IonosphereModel       = 'None';
MAD.TroposphereModel      = 'None';
MAD.ErrorModels           = {RangeModel, DopplerModel};

%
%   Tracking file sets
%

Create TrackingFileSet simData;

simData.AddTrackingConfig       = {{GDS, SimSat, GDS}, 'DSN_SeqRange', 'DSN_TCP'};
simData.AddTrackingConfig       = {{CAN, SimSat, CAN}, 'DSN_SeqRange', 'DSN_TCP'};
simData.AddTrackingConfig       = {{MAD, SimSat, MAD}, 'DSN_SeqRange', 'DSN_TCP'};
simData.FileName                = {'Ex_R2020a_Estimate_ThrustScaleFactor.gmd'};
simData.RampTable               = {};
simData.UseLightTime            = True;
simData.UseRelativityCorrection = False;
simData.UseETminusTAI           = False;
simData.SimRangeModuloConstant  = 67108864;
simData.SimDopplerCountInterval = 10.;  
simData.DataFilters             = {};

Create TrackingFileSet estData;

estData.AddTrackingConfig       = {{GDS, EstSat, GDS}, 'DSN_SeqRange', 'DSN_TCP'};
estData.AddTrackingConfig       = {{CAN, EstSat, CAN}, 'DSN_SeqRange', 'DSN_TCP'};
estData.AddTrackingConfig       = {{MAD, EstSat, MAD}, 'DSN_SeqRange', 'DSN_TCP'};
estData.FileName                = {'Ex_R2020a_Estimate_ThrustScaleFactor.gmd'};
estData.RampTable               = {};
estData.UseLightTime            = True;
estData.UseRelativityCorrection = False;
estData.UseETminusTAI           = False;
estData.DataFilters             = {};

%
%   Propagators
%

Create ForceModel FM;
Create Propagator Prop;

FM.CentralBody       = Earth;
FM.PrimaryBodies     = {Earth};
FM.SRP               = Off;
FM.Drag              = None
FM.ErrorControl      = None;

Prop.FM              = FM;
Prop.Type            = RungeKutta89;
Prop.InitialStepSize = 60;
Prop.Accuracy        = 1e-13;
Prop.MinStep         = 0;
Prop.MaxStep         = 2700;
Prop.MaxStepAttempts = 50;

%
%   Simulator
%

Create Simulator sim;

sim.AddData             = {simData};
sim.EpochFormat         = 'UTCGregorian';
sim.InitialEpoch        = '01 Jan 2000 00:00:00.000';
sim.FinalEpoch          = '02 Jan 2000 00:00:00.000';
sim.MeasurementTimeStep = 60;
sim.Propagator          = Prop;
sim.AddNoise            = On;

%
%   Estimator
%

Create BatchEstimator bat

bat.ShowProgress                = True;
bat.Measurements                = {estData} 
bat.AbsoluteTol                 = 0.0001;
bat.RelativeTol                 = 0.0001;
bat.MaximumIterations           = 5;
bat.MaxConsecutiveDivergences   = 10;
bat.Propagator                  = Prop;
bat.ShowAllResiduals            = On;
bat.OLSEInitialRMSSigma         = 3.0e6;
bat.OLSEMultiplicativeConstant  = 3;
bat.OLSEAdditiveConstant        = 0;
bat.InversionAlgorithm          = 'Internal';
bat.EstimationEpoch             = 'FromParticipants'; 
bat.ReportStyle                 = 'Normal';
bat.ReportFile                  = 'Ex_R2020a_Estimate_ThrustScaleFactor.txt';

%
%   Thrust history file
%

%   Here we create the simulated finite burn and apply a thrust scale factor of
%   0.8, or 20% cold.

Create ThrustSegment ThrustSegmentCold;  

ThrustSegmentCold.ThrustScaleFactor             = 0.8
ThrustSegmentCold.ApplyThrustScaleToMassFlow    = False
ThrustSegmentCold.MassFlowScaleFactor           = 1.0
ThrustSegmentCold.SolveFors                     = {};
ThrustSegmentCold.MassSource                    = {SimPropTank}

Create ThrustHistoryFile simThrustHistoryFile

simThrustHistoryFile.AddThrustSegment = {ThrustSegmentCold}   
simThrustHistoryFile.FileName         = '../SupportFiles/Thrust_Sim.thf'

%   Here we create the burn model for estimation and assume the burn performance
%   was nominal, using a thrust scale factor of 1.0

Create ThrustSegment ThrustSegmentNominal;

ThrustSegmentNominal.ThrustScaleFactor          = 1.0
ThrustSegmentNominal.ApplyThrustScaleToMassFlow = False
ThrustSegmentNominal.MassFlowScaleFactor        = 1.0
ThrustSegmentNominal.SolveFors                  = {ThrustScaleFactor}  
ThrustSegmentNominal.MassSource                 = {EstPropTank}

Create ThrustHistoryFile estThrustHistoryFile

estThrustHistoryFile.AddThrustSegment = {ThrustSegmentNominal}  
estThrustHistoryFile.FileName         = '../SupportFiles/Thrust_Est.thf'

%
%   Run mission sequence
%

BeginMissionSequence

SetSeed(1);

%   Simulate data using the cold burn model

BeginFileThrust simThrustHistoryFile(SimSat);
RunSimulator sim;
EndFileThrust simThrustHistoryFile(SimSat);

%   Estimate the orbit and thrust scale factor, starting from assumption of 
%   nominal burn performance

BeginFileThrust estThrustHistoryFile(EstSat); 
RunEstimator bat
EndFileThrust estThrustHistoryFile(EstSat);
