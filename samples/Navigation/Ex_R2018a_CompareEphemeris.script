% GMAT Script used to compare two Code500 binary ephemeris files
% To use this script, update the fields in the USER SETTINGS block below

%
%   Create variables
%

Create String compareStart compareEnd Sat1Id Sat2Id Ephemeris1 Ephemeris2;
Create Variable CompareStep;
Create String ReportName

%
%   User settings
%

%   Enter your first and second ephemeris file names and locations here.
%   Enter the spacecraft names, to be displayed in the report.

Ephemeris1 = '../data/vehicle/ephem/code500/DemoLuna-1.ephem';
Sat1Id     = 'Sat1';

Ephemeris2 = '../data/vehicle/ephem/code500/DemoLuna-2.ephem';
Sat2Id     = 'Sat2';

%   Enter the UTC Gregorian start and end epochs for the comparison

compareStart = '01 Jan 2018 12:00:00.000';
compareEnd   = '02 Jan 2018 12:00:00.000';

%   Enter the step size for the comparisons, in seconds, here

CompareStep        = 60;

%   Set the name (and path, if not the default output path) of the output file here

ReportName = 'compare.data.out';

%   Build the ephemeris comparison frame

Create CoordinateSystem CompareFrame;

%   Change central body of ephemeris here

CompareFrame.Origin = Luna;
CompareFrame.Axes   = MJ2000Eq;

%
%   Run the ephemeris comparison.
%
#Include '../SupportFiles/Ex_EphemerisCompareGeneric.script';
