%  Script Mission - Libration Point Example


%----------------------------------------
%---------- Calculated Points
%----------------------------------------

Create LibrationPoint EarthMoonL2;
GMAT EarthMoonL2.Primary = Earth;
GMAT EarthMoonL2.Secondary = Luna;
GMAT EarthMoonL2.Point = L2;

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft Sat;
GMAT Sat.DateFormat = TAIModJulian;
GMAT Sat.Epoch = '25219.50041705303';
GMAT Sat.CoordinateSystem = EarthMJ2000Eq;
GMAT Sat.DisplayStateType = Cartesian;
GMAT Sat.X = 406326.226613191;
GMAT Sat.Y = 177458.3876159782;
GMAT Sat.Z = 145838.5807900441;
GMAT Sat.VX = -0.5172746738228751;
GMAT Sat.VY = 0.7746503665608931;
GMAT Sat.VZ = 0.3314166026539857;
GMAT Sat.DryMass = 850;
GMAT Sat.Cd = 2.2;
GMAT Sat.Cr = 1.8;
GMAT Sat.DragArea = 15;
GMAT Sat.SRPArea = 1;

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel EarthMoonL2Prop_ForceModel;
GMAT EarthMoonL2Prop_ForceModel.CentralBody = Earth;
GMAT EarthMoonL2Prop_ForceModel.PointMasses = {Earth, Luna, Sun};
GMAT EarthMoonL2Prop_ForceModel.Drag = None;
GMAT EarthMoonL2Prop_ForceModel.SRP = Off;
GMAT EarthMoonL2Prop_ForceModel.RelativisticCorrection = Off;
GMAT EarthMoonL2Prop_ForceModel.ErrorControl = RSSStep;

Create ForceModel EarthProp_ForceModel;
GMAT EarthProp_ForceModel.CentralBody = Earth;
GMAT EarthProp_ForceModel.PointMasses = {Sun, Earth, Luna};
GMAT EarthProp_ForceModel.Drag = None;
GMAT EarthProp_ForceModel.SRP = Off;
GMAT EarthProp_ForceModel.RelativisticCorrection = Off;
GMAT EarthProp_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator EarthMoonL2Prop;
GMAT EarthMoonL2Prop.FM = EarthMoonL2Prop_ForceModel;
GMAT EarthMoonL2Prop.Type = RungeKutta68;
GMAT EarthMoonL2Prop.InitialStepSize = 130;
GMAT EarthMoonL2Prop.Accuracy = 1e-010;
GMAT EarthMoonL2Prop.MinStep = 0.001;
GMAT EarthMoonL2Prop.MaxStep = 24000;
GMAT EarthMoonL2Prop.MaxStepAttempts = 50;
GMAT EarthMoonL2Prop.StopIfAccuracyIsViolated = true;

Create Propagator EarthProp;
GMAT EarthProp.FM = EarthProp_ForceModel;
GMAT EarthProp.Type = PrinceDormand78;
GMAT EarthProp.InitialStepSize = 60;
GMAT EarthProp.Accuracy = 9.999999999999999e-012;
GMAT EarthProp.MinStep = 0.001;
GMAT EarthProp.MaxStep = 2700;
GMAT EarthProp.MaxStepAttempts = 50;
GMAT EarthProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn dV;
GMAT dV.CoordinateSystem = Local;
GMAT dV.Origin = Earth;
GMAT dV.Axes = VNB;
GMAT dV.Element1 = -3e-005;
GMAT dV.Element2 = 0;
GMAT dV.Element3 = 0;
GMAT dV.DecrementMass = false;
GMAT dV.Isp = 300;
GMAT dV.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Arrays, Variables, Strings
%----------------------------------------
Create Variable I J;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem CSEarthMoonRot;
GMAT CSEarthMoonRot.Origin = Earth;
GMAT CSEarthMoonRot.Axes = ObjectReferenced;
GMAT CSEarthMoonRot.XAxis = R;
GMAT CSEarthMoonRot.ZAxis = N;
GMAT CSEarthMoonRot.Primary = Earth;
GMAT CSEarthMoonRot.Secondary = Luna;

Create CoordinateSystem L2Centered;
GMAT L2Centered.Origin = EarthMoonL2;
GMAT L2Centered.Axes = ObjectReferenced;
GMAT L2Centered.XAxis = R;
GMAT L2Centered.ZAxis = N;
GMAT L2Centered.Primary = Earth;
GMAT L2Centered.Secondary = Luna;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC;
GMAT DC.ShowProgress = true;
GMAT DC.ReportStyle = Normal;
GMAT DC.ReportFile = 'DifferentialCorrectorDC.data';
GMAT DC.MaximumIterations = 25;
GMAT DC.DerivativeMethod = ForwardDifference;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView ViewEarthMoonRot;
GMAT ViewEarthMoonRot.SolverIterations = Current;
GMAT ViewEarthMoonRot.UpperLeft = [ 0.3858490566037736 0.003778337531486146 ];
GMAT ViewEarthMoonRot.Size = [ 0.4518867924528302 0.4521410579345088 ];
GMAT ViewEarthMoonRot.RelativeZOrder = 196;
GMAT ViewEarthMoonRot.Add = {Earth, Luna, Sun, Sat, EarthMoonL2};
GMAT ViewEarthMoonRot.CoordinateSystem = L2Centered;
GMAT ViewEarthMoonRot.DrawObject = [ true true true true true ];
GMAT ViewEarthMoonRot.ViewPointReference = EarthMoonL2;
GMAT ViewEarthMoonRot.ViewDirection = EarthMoonL2;
GMAT ViewEarthMoonRot.ViewScaleFactor = 10;

Create OrbitView EarthMJ2K;
GMAT EarthMJ2K.SolverIterations = Current;
GMAT EarthMJ2K.UpperLeft = [ 0.3858490566037736 0.4672544080604534 ];
GMAT EarthMJ2K.Size = [ 0.4518867924528302 0.4596977329974811 ];
GMAT EarthMJ2K.RelativeZOrder = 200;
GMAT EarthMJ2K.Add = {Sat, Earth, Luna};
GMAT EarthMJ2K.CoordinateSystem = EarthMJ2000Eq;
GMAT EarthMJ2K.DrawObject = [ true true true ];
GMAT EarthMJ2K.ViewScaleFactor = 45;

% -------------------------------------------------------------------------
% --------------------------- Mission Sequence-----------------------------
% -------------------------------------------------------------------------

BeginMissionSequence;


Propagate 'Prop to Y Crossing' EarthMoonL2Prop(Sat) {Sat.CSEarthMoonRot.Y = 0,StopTolerance = 1e-005};

For 'J = 1:2' J = 1:1:4;
   
   Target 'Target Plane Crossing' DC {SolveMode = Solve, ExitMode = DiscardAndContinue};
      
      
      Vary 'Vary dV.V' DC(dV.Element1 = -5e-005, {Perturbation = 1e-6, Lower = -0.04, Upper = 0.0297, MaxStep = 0.0005});
      Maneuver 'Apply dV' dV(Sat);
      
      For 'I = 1:2' I = 1:1:1;
         Propagate 'Prop to Y Crossing' EarthMoonL2Prop(Sat) {Sat.L2Centered.Y = 0, StopTolerance = 1e-005};
      EndFor;
      
      Achieve 'Achieve Vx' DC(Sat.L2Centered.VX = 0.00125, {Tolerance = 0.00002});
      
   EndTarget;  % For targeter DC

EndFor;
