# GMAT Application Programmer's Interface Example
#
# Coded by D. Conway. Thinking Systems, Inc.
#
# This file is a template for files used run the GMAT API from a folder outside  
# of the GMAT application folder.

import sys
from os import path, remove
from pathlib import Path

from process_contact import ContactReport

apistartup = "api_startup_file.txt"
GmatInstall = "/home/<USER>/Downloads/gmat-ubuntu-x64-R2025a/GMAT/R2025a"
GMAT_SCRIPT_FILE = GmatInstall + "/lunar_contact_generator.script"
GmatBinPath = GmatInstall + "/bin"
Startup = GmatBinPath + "/" + apistartup

if path.exists(Startup):


   sys.path.insert(1, GmatBinPath)

   import gmatpy as gmat
   gmat.Setup(Startup)
else:
    print("Cannot find ", Startup)
    print()
    print("Please set up a GMAT startup file named ", apistartup, " in the ",
          GmatBinPath, " folder.")

print("Imported")
# gmat.Clear()
gmat.LoadScript(GMAT_SCRIPT_FILE)
gmat.Initialize()


output_dir = Path(GmatInstall) / 'output'
output_dir.mkdir(exist_ok=True)
# remove(output_dir / 'EVA1Contacts.txt')
# remove(output_dir / 'EVA2Contacts.txt')


# Run the GMAT script to generate ephemeris and contact reports
print("Running GMAT simulation to generate data...")
if gmat.RunScript():
  gmat.SaveScript(GMAT_SCRIPT_FILE + ".edited")
  print("GMAT run completed successfully.")
else:
  print("GMAT run failed. Please check the GMAT script for errors.")


eva1_contacts = ContactReport(output_dir / 'EVA1Contacts.txt')
eva1_contacts.scale_down(600) # 10 mins -> 1 sec

eva2_contacts = ContactReport(output_dir / 'EVA2Contacts.txt')
eva2_contacts.scale_down(600) # 10 mins -> 1 sec

eva1_contacts.print_summary()
eva2_contacts.print_summary()
