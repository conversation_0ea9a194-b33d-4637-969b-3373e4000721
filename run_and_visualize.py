# GMAT Application Programmer's Interface Example
#
# Coded by D. Conway. Thinking Systems, Inc.
#
# This file is a template for files used run the GMAT API from a folder outside  
# of the GMAT application folder.

import sys
from os import path, remove
from pathlib import Path

sys.path.append('.')  # Add current directory to path
from process_contact import ContactReport

apistartup = "api_startup_file.txt"
GmatInstall = "/home/<USER>/Downloads/gmat-ubuntu-x64-R2025a/GMAT/R2025a"
GMAT_SCRIPT_FILE = GmatInstall + "/lunar_contact_generator.script"
GmatBinPath = GmatInstall + "/bin"
Startup = GmatBinPath + "/" + apistartup

if path.exists(Startup):

    sys.path.insert(1, GmatBinPath)

    import gmatpy as gmat

    gmat.Setup(Startup)
else:
    print("Cannot find ", Startup)
    print()
    print("Please set up a GMAT startup file named ", apistartup, " in the ", GmatBinPath, " folder.")

output_dir = Path(GmatInstall) / 'output'
output_dir.mkdir(exist_ok=True)
try:
    remove(output_dir / 'EVA1Contacts.txt')
except FileNotFoundError:
    pass
try:
    remove(output_dir / 'EVA2Contacts.txt')
except FileNotFoundError:
    pass

print("Imported")
gmat.Clear()
gmat.LoadScript(GMAT_SCRIPT_FILE)

# Run the GMAT script to generate ephemeris and contact reports
print("Running GMAT simulation to generate data...")
if gmat.RunScript():
    gmat.SaveScript(GMAT_SCRIPT_FILE + ".edited")
    print("GMAT run completed successfully.")
else:
    print("GMAT run failed. Please check the GMAT script for errors.")

eva1_contacts = ContactReport(output_dir / 'EVA1Contacts.txt')
eva1_contacts.scale_down(600)  # 10 mins -> 1 sec

eva2_contacts = ContactReport(output_dir / 'EVA2Contacts.txt')
eva2_contacts.scale_down(600)  # 10 mins -> 1 sec


# Import visualization libraries
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import networkx as nx
import numpy as np
from collections import defaultdict

def create_animated_contact_graph(eva1_contacts, eva2_contacts):
    """Create an animated graph showing contact links appearing and disappearing"""

    # Create the graph
    G = nx.Graph()

    # Add nodes for spacecraft and ground stations
    spacecraft_nodes = set()
    ground_station_nodes = set()

    # Collect all nodes from both contact reports
    for contacts in [eva1_contacts, eva2_contacts]:
        spacecraft_nodes.add(contacts.target)
        for observer_name in contacts.get_observer_names():
            ground_station_nodes.add(observer_name)

    # Add nodes to graph
    G.add_nodes_from(spacecraft_nodes, node_type='spacecraft')
    G.add_nodes_from(ground_station_nodes, node_type='ground_station')

    # Create time-based contact events
    contact_events = []

    # Process EVA1 contacts
    for observer_name in eva1_contacts.get_observer_names():
        events = eva1_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva1_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA1'
            })

    # Process EVA2 contacts
    for observer_name in eva2_contacts.get_observer_names():
        events = eva2_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva2_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA2'
            })

    # Sort events by start time
    contact_events.sort(key=lambda x: x['start_time'])

    # Determine animation time range
    if contact_events:
        min_time = min(event['start_time'] for event in contact_events)
        max_time = max(event['stop_time'] for event in contact_events)
        time_range = max_time - min_time

        # Create time steps for animation (100 steps)
        time_steps = np.linspace(min_time, max_time, 200)
    else:
        time_steps = np.linspace(0, 100, 200)

    # Set up the plot
    fig, ax = plt.subplots(figsize=(14, 10))

    # Create layout for nodes - matching your diagram
    pos = {}

    # Position spacecraft in the center area
    spacecraft_list = list(spacecraft_nodes)
    if len(spacecraft_list) == 2:
        # EVA1 and EVA2 positioned vertically in center
        pos[spacecraft_list[0]] = (0, 0.5)   # EVA1 upper center
        pos[spacecraft_list[1]] = (0, -0.5)  # EVA2 lower center
    elif len(spacecraft_list) == 1:
        pos[spacecraft_list[0]] = (0, 0)
    else:
        # More than 2 spacecraft - arrange in small circle
        for i, sc in enumerate(spacecraft_list):
            angle = 2 * np.pi * i / len(spacecraft_list)
            pos[sc] = (0.3 * np.cos(angle), 0.3 * np.sin(angle))

    # Position ground stations in specific locations to match your diagram
    gs_list = list(ground_station_nodes)

    # Define specific positions for known ground stations
    gs_positions = {
        'Houston': (-2.5, 1.5),
        'Sydney': (-2.5, -1.5),
        'Ireland': (-2.5, -3),
        'HLS': (2.5, 0),
        # Fallback positions for any other ground stations
    }

    # Assign positions
    assigned_positions = set()
    for gs in gs_list:
        if gs in gs_positions:
            pos[gs] = gs_positions[gs]
            assigned_positions.add(gs_positions[gs])
        else:
            # For unknown ground stations, place them in remaining circle positions
            for i in range(len(gs_list)):
                angle = 2 * np.pi * i / max(len(gs_list), 4)
                candidate_pos = (3 * np.cos(angle), 3 * np.sin(angle))
                if candidate_pos not in assigned_positions:
                    pos[gs] = candidate_pos
                    assigned_positions.add(candidate_pos)
                    break

    def animate(frame):
        ax.clear()
        current_time = time_steps[frame]

        # Find active contacts at current time
        active_contacts = []
        for event in contact_events:
            if event['start_time'] <= current_time <= event['stop_time']:
                active_contacts.append(event)

        # Draw all nodes
        spacecraft_positions = [pos[node] for node in spacecraft_nodes]
        gs_positions = [pos[node] for node in ground_station_nodes]

        # Draw spacecraft nodes
        if spacecraft_positions:
            sc_x, sc_y = zip(*spacecraft_positions)
            ax.scatter(sc_x, sc_y, c='red', s=300, marker='s', label='Spacecraft', zorder=3)
            for node in spacecraft_nodes:
                ax.annotate(node, pos[node], xytext=(5, 5), textcoords='offset points',
                           fontsize=10, fontweight='bold')

        # Draw ground station nodes
        if gs_positions:
            gs_x, gs_y = zip(*gs_positions)
            ax.scatter(gs_x, gs_y, c='blue', s=200, marker='^', label='Ground Stations', zorder=3)
            for node in ground_station_nodes:
                ax.annotate(node, pos[node], xytext=(5, 5), textcoords='offset points',
                           fontsize=9)

        # Draw active contact links
        for contact in active_contacts:
            sc_pos = pos[contact['spacecraft']]
            gs_pos = pos[contact['ground_station']]

            # Color code by mission
            color = 'green' if contact['mission'] == 'EVA1' else 'orange'
            linewidth = 3

            ax.plot([sc_pos[0], gs_pos[0]], [sc_pos[1], gs_pos[1]],
                   color=color, linewidth=linewidth, alpha=0.7, zorder=2)

        # Set plot properties
        ax.set_xlim(-3, 3)
        ax.set_ylim(-3, 3)
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3)
        ax.set_title(f'Contact Network Animation\nTime: {current_time:.1f}s | Active Contacts: {len(active_contacts)}',
                    fontsize=14, fontweight='bold')

        # Add legend
        ax.legend(loc='upper right')

        # Add mission color legend
        ax.text(-2.8, 2.5, 'EVA1 Contacts', color='green', fontweight='bold', fontsize=10)
        ax.text(-2.8, 2.2, 'EVA2 Contacts', color='orange', fontweight='bold', fontsize=10)

    # Create animation
    anim = animation.FuncAnimation(fig, animate, frames=len(time_steps),
                                 interval=100, repeat=True, blit=False)

    plt.tight_layout()
    return fig, anim

# Create and show the animated visualization
print("\nCreating animated contact visualization...")
fig, anim = create_animated_contact_graph(eva1_contacts, eva2_contacts)

# Save animation as GIF (optional)
try:
    print("Saving animation as contact_animation.gif...")
    anim.save('contact_animation.gif', writer='pillow', fps=10)
    print("Animation saved successfully!")
except Exception as e:
    print(f"Could not save animation: {e}")

# Show the plot
plt.show()
