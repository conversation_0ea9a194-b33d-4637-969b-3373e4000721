# GMAT Application Programmer's Interface Example
#
# Coded by <PERSON><PERSON> Conway. Thinking Systems, Inc.
#
# This file is a template for files used run the GMAT API from a folder outside
# of the GMAT application folder.

import sys
from os import path, remove
from pathlib import Path

from process_contact import ContactReport

API_STARTUP = "api_startup_file.txt"
GMAT_INSTALL = "/home/<USER>/Downloads/gmat-ubuntu-x64-R2025a/GMAT/R2025a"
GMAT_SCRIPT_FILE = GMAT_INSTALL + "/lunar_contact_generator.script"
GMAT_BIN_PATH = GMAT_INSTALL + "/bin"
STARTUP = GMAT_BIN_PATH + "/" + API_STARTUP

# Simple animation setup: 30s @ 30fps = 900 frames
ANIMATION_DURATION_SECONDS = 30.0
ANIMATION_FPS = 30

if path.exists(STARTUP):

    sys.path.insert(1, GMAT_BIN_PATH)

    import gmatpy as gmat

    gmat.Setup(STARTUP)
else:
    print("Cannot find ", STARTUP)
    print()
    print("Please set up a GMAT startup file named ", API_STARTUP, " in the ", GMAT_BIN_PATH, " folder.")

output_dir = Path(GMAT_INSTALL) / 'output'
output_dir.mkdir(exist_ok=True)
try:
    remove(output_dir / 'EVA1Contacts.txt')
except FileNotFoundError:
    pass
try:
    remove(output_dir / 'EVA2Contacts.txt')
except FileNotFoundError:
    pass
try:
    remove(output_dir / 'GMATSpiceKernelError.txt')
except FileNotFoundError:
    pass

gmat.Clear()
gmat.LoadScript(GMAT_SCRIPT_FILE)

# Run the GMAT script to generate ephemeris and contact reports
print("Running GMAT simulation to generate data...")
if gmat.RunScript():
    gmat.SaveScript(GMAT_SCRIPT_FILE + ".edited")
    print("GMAT run completed successfully.")
else:
    print("GMAT run failed. Please check the GMAT script for errors.")

eva1_contacts = ContactReport(output_dir / 'EVA1Contacts.txt')
eva1_contacts.scale_down(150)  # 2.5 mins -> 1 sec

eva2_contacts = ContactReport(output_dir / 'EVA2Contacts.txt')
eva2_contacts.scale_down(150)  # 2.5 mins -> 1 sec


# Import visualization libraries
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import networkx as nx
import numpy as np
from collections import defaultdict

def create_animated_contact_graph(eva1_contacts, eva2_contacts):
    """Create an animated graph showing contact links appearing and disappearing"""

    # Collect all nodes from both contact reports
    spacecraft_nodes = set()
    ground_station_nodes = set()
    
    for contacts in [eva1_contacts, eva2_contacts]:
        spacecraft_nodes.add(contacts.target)
        for observer_name in contacts.get_observer_names():
            ground_station_nodes.add(observer_name)

    # Simple contact events list
    contact_events = []

    # Process EVA1 contacts
    for observer_name in eva1_contacts.get_observer_names():
        events = eva1_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva1_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA1',
                'contact_key': f"{eva1_contacts.target}_{observer_name}"
            })

    # Process EVA2 contacts
    for observer_name in eva2_contacts.get_observer_names():
        events = eva2_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva2_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA2',
                'contact_key': f"{eva2_contacts.target}_{observer_name}"
            })

    # Sort contact events by start time
    contact_events.sort(key=lambda x: x['start_time'])

    # Find actual simulation time range (no negative padding)
    if contact_events:
        sim_min_time = min(event['start_time'] for event in contact_events)
        sim_max_time = max(event['stop_time'] for event in contact_events)
        sim_total_duration = sim_max_time - sim_min_time
    else:
        sim_min_time = 0
        sim_max_time = 100
        sim_total_duration = 100
    

    total_frames = int(ANIMATION_DURATION_SECONDS * ANIMATION_FPS)  # 900 frames

    # Set up the plot
    fig, ax = plt.subplots(figsize=(16, 12))

    # Improved node positions for better visual layout
    pos = {
        # Spacecraft positioned vertically
        'EVA1': (0, 2),
        'EVA2': (0, -2),
        
        # Ground stations positioned in a more spread out pattern
        'Ohio': (-3, 1.5),      # Upper left
        'Dublin': (-3, -1.5),   # Lower left  
        'Sydney': (-3, 0),      # Middle left
        'HLS': (3, 0),          # Right side
    }

    def get_active_contacts_at_frame(frame_number):
        """Simple: get active contacts at this frame number (0 to 899)"""
        # Map frame to simulation time
        progress = frame_number / (total_frames - 1)  # 0.0 to 1.0
        current_sim_time = sim_min_time + (progress * sim_total_duration)
        
        # Find active contacts at this time
        active = []
        for event in contact_events:
            if event['start_time'] <= current_sim_time <= event['stop_time']:
                active.append(event)
        return active, current_sim_time

    def draw_node(node_pos, radius, color, edge_color, label, label_size=10):
        """Helper function to draw a node with consistent styling"""
        circle = plt.Circle(node_pos, radius, color=color, ec=edge_color, 
                           linewidth=2, zorder=3)
        ax.add_patch(circle)
        ax.text(node_pos[0], node_pos[1], label, ha='center', va='center',
               fontsize=label_size, fontweight='bold', zorder=4)

    def draw_contact_link(sc_pos, gs_pos, mission, is_active=True):
        """Draw contact link with proper styling based on mission and state"""
        # Mission-specific colors
        colors = {
            'EVA1': '#FF6B6B' if is_active else '#FFB3B3',  # Red tones
            'EVA2': '#4ECDC4' if is_active else '#B3E5E0'   # Teal tones
        }
        
        color = colors.get(mission, '#333333')
        linewidth = 3 if is_active else 1.5
        alpha = 1.0 if is_active else 0.3
        
        # Calculate edge-to-edge arrow positioning
        dx = gs_pos[0] - sc_pos[0]
        dy = gs_pos[1] - sc_pos[1] 
        length = np.sqrt(dx**2 + dy**2)
        
        if length > 0:
            dx_norm = dx / length
            dy_norm = dy / length
            
            # Adjust for node radius (0.5)
            radius = 0.5
            start_x = sc_pos[0] + radius * dx_norm
            start_y = sc_pos[1] + radius * dy_norm
            end_x = gs_pos[0] - radius * dx_norm
            end_y = gs_pos[1] - radius * dy_norm
            
            # Draw bidirectional arrow for active links
            arrow_style = '<->' if is_active else '->'
            
            ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                       arrowprops=dict(arrowstyle=arrow_style, color=color, 
                                     lw=linewidth, alpha=alpha),
                       zorder=2)

    def animate(frame):
        ax.clear()
        
        # Simple: get active contacts at this frame
        active_contacts, sim_time = get_active_contacts_at_frame(frame)
        active_contact_keys = {contact['contact_key'] for contact in active_contacts}
        
        # Draw all nodes
        # Spacecraft nodes
        for node in spacecraft_nodes:
            # Highlight spacecraft if they have active contacts
            has_active = any(contact['spacecraft'] == node for contact in active_contacts)
            color = '#87CEEB' if has_active else '#B0E0E6'  # Sky blue variations
            draw_node(pos[node], 0.5, color, 'black', node, 12)
        
        # Ground station nodes  
        for node in ground_station_nodes:
            # Highlight ground stations if they have active contacts
            has_active = any(contact['ground_station'] == node for contact in active_contacts)
            color = '#FFD700' if has_active else '#F0F0F0'  # Gold/gray variations
            draw_node(pos[node], 0.5, color, 'black', node, 10)
        
        # Draw all possible contact links (faded for inactive, bright for active)
        drawn_links = set()
        
        # First pass: draw all inactive links as faded
        for event in contact_events:
            link_key = f"{event['spacecraft']}_{event['ground_station']}"
            if link_key not in drawn_links:
                sc_pos = pos[event['spacecraft']]
                gs_pos = pos[event['ground_station']]
                is_active = event['contact_key'] in active_contact_keys
                
                if not is_active:
                    draw_contact_link(sc_pos, gs_pos, event['mission'], False)
                drawn_links.add(link_key)
        
        # Second pass: draw active links on top
        for contact in active_contacts:
            sc_pos = pos[contact['spacecraft']]
            gs_pos = pos[contact['ground_station']]
            draw_contact_link(sc_pos, gs_pos, contact['mission'], True)
        
        # Set plot properties
        ax.set_xlim(-5, 5)
        ax.set_ylim(-4, 4)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # Enhanced title and information display
        ax.text(0, 3.5, 'Lunar Surface Contact Network', ha='center', va='center',
               fontsize=18, fontweight='bold')
        
        # Simple time display
        animation_time = (frame / (total_frames - 1)) * ANIMATION_DURATION_SECONDS
        progress_pct = (frame / (total_frames - 1)) * 100
        sim_time_minutes = sim_time / 60
        
        ax.text(0, 3.1, f'Animation: {animation_time:.1f}s / {ANIMATION_DURATION_SECONDS:.0f}s ({progress_pct:.1f}%)', 
               ha='center', va='center', fontsize=12)
        ax.text(0, 2.9, f'Simulation Time: {sim_time:.1f}s ({sim_time_minutes:.1f} min)', 
               ha='center', va='center', fontsize=11, style='italic')
        
        ax.text(0, 2.7, f'Active Contacts: {len(active_contacts)}', 
               ha='center', va='center', fontsize=12)
        
        # Improved legend with color coding
        legend_y = 3.2
        ax.text(-4.5, legend_y, 'Missions:', fontsize=12, fontweight='bold')
        ax.text(-4.5, legend_y-0.3, f'● EVA1: {eva1_contacts.target}', 
               fontsize=10, color='#FF6B6B')
        ax.text(-4.5, legend_y-0.6, f'● EVA2: {eva2_contacts.target}', 
               fontsize=10, color='#4ECDC4')
        
        # Contact status for each mission
        eva1_active = len([c for c in active_contacts if c['mission'] == 'EVA1'])
        eva2_active = len([c for c in active_contacts if c['mission'] == 'EVA2'])
        
        ax.text(3.5, legend_y, 'Active Links:', fontsize=12, fontweight='bold')
        ax.text(3.5, legend_y-0.3, f'EVA1: {eva1_active}', fontsize=10, color='#FF6B6B')
        ax.text(3.5, legend_y-0.6, f'EVA2: {eva2_active}', fontsize=10, color='#4ECDC4')

    # Create animation: 30s @ 30fps = 900 frames  
    frame_interval_ms = int(1000 / ANIMATION_FPS)  # ~33ms per frame
    anim = animation.FuncAnimation(fig, animate, frames=total_frames,
                                 interval=frame_interval_ms, repeat=True, blit=False)

    plt.tight_layout()
    return fig, anim

# Create and show the animated visualization
print("\nCreating animated contact visualization...")
print("Animation duration: 30 seconds at 30 FPS (900 frames)")
fig, anim = create_animated_contact_graph(eva1_contacts, eva2_contacts)

# Save animation as GIF (optional)
try:
    print("Saving animation as contact_animation.gif (this may take a moment)...")
    # Use lower FPS for GIF to reduce file size while maintaining smoothness
    anim.save('contact_animation.gif', writer='pillow', fps=30)
    print("Animation saved successfully!")
except Exception as e:
    print(f"Could not save animation: {e}")

# Show the plot
plt.show()
