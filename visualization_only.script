%----------------------------------------
% GMAT Mission Script - Visualization Only
%
% Author: AI Assistant
% Date: 2024-07-30
%
% This script is designed for visualizing line-of-sight (LOS) links
% between spacecraft and ground stations. It does not perform contact
% analysis. To use this script, manually set the epoch to a time when
% a known contact occurs (taken from the contact analysis reports).
%
% Instructions:
% 1. Run the `final_contact_analysis.script` to generate contact reports.
% 2. Open a contact report (e.g., 'output/HLS_to_SV1_Contacts.txt').
% 3. Choose a time within a contact interval.
% 4. Set the `Epoch` of all objects in this script to that time.
% 5. Run this script to visualize the LOS link at that moment.
%----------------------------------------

%----------------------------------------
%---------- Spacecraft Configuration
%----------------------------------------

Create Spacecraft SV1, SV2;
GMAT SV1.Epoch = '01 Jan 2000 00:00:00.000';
GMAT SV2.Epoch = '01 Jan 2000 00:00:00.000';
% ... (rest of SV1 and SV2 configuration is the same as the main script)

% --- Dummy Spacecraft for LOS Visualization ---
% These spacecraft are not real. They are used to draw lines.
Create Spacecraft HLS_to_SV1_LOS, SV1_to_LEGS_LOS;

% LOS from HLS to SV1
GMAT HLS_to_SV1_LOS.Epoch = '01 Jan 2000 00:00:00.000';
GMAT HLS_to_SV1_LOS.CoordinateSystem = LunarMJ2000Eq;
GMAT HLS_to_SV1_LOS.DisplayStateType = Cartesian;
GMAT HLS_to_SV1_LOS.X = -1737.4; % HLS_Station approximate position
GMAT HLS_to_SV1_LOS.Y = 0;
GMAT HLS_to_SV1_LOS.Z = 0;
GMAT HLS_to_SV1_LOS.VX = 1000; % High velocity vector pointing towards SV1
GMAT HLS_to_SV1_LOS.VY = 0;
GMAT HLS_to_SV1_LOS.VZ = 0;
GMAT HLS_to_SV1_LOS.OrbitColor = 'Yellow';

% LOS from SV1 to LEGS (approximated)
GMAT SV1_to_LEGS_LOS.Epoch = '01 Jan 2000 00:00:00.000';
GMAT SV1_to_LEGS_LOS.CoordinateSystem = EarthMJ2000Eq;
GMAT SV1_to_LEGS_LOS.DisplayStateType = Cartesian;
GMAT SV1_to_LEGS_LOS.X = 2037.4; % SV1 approximate position
GMAT SV1_to_LEGS_LOS.Y = 0;
GMAT SV1_to_LEGS_LOS.Z = 0;
GMAT SV1_to_LEGS_LOS.VX = -1000; % High velocity vector pointing towards Earth
GMAT SV1_to_LEGS_LOS.VY = 0;
GMAT SV1_to_LEGS_LOS.VZ = 0;
GMAT SV1_to_LEGS_LOS.OrbitColor = 'Cyan';

%----------------------------------------
%---------- Visualizers
%----------------------------------------

Create OrbitView LOS_View;
GMAT LOS_View.Add = {SV1, SV2, HLS_Station, LEGS1_Ohio, Earth, Luna, HLS_to_SV1_LOS, SV1_to_LEGS_LOS};
GMAT LOS_View.CoordinateSystem = EarthMJ2000Eq;
GMAT LOS_View.DrawObject = [true, true, true, true, true, true, true, true];
GMAT LOS_View.ViewPointVector = [0, -600000, 300000];

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

% Propagate for a very short time to draw the LOS lines
Propagate Propagator(HLS_to_SV1_LOS, SV1_to_LEGS_LOS) {HLS_to_SV1_LOS.ElapsedSecs = 1}; 