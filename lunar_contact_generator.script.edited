%General Mission Analysis Tool(GMAT) Script
%Created: 2025-07-30 13:51:19


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

<PERSON>reate Spacecraft EVA1;
EVA1.DateFormat = UTCGregorian;
EVA1.Epoch = '01 Jan 2000 00:00:00.000';
EVA1.CoordinateSystem = MoonMJ2000Eq;
EVA1.DisplayStateType = Keplerian;
EVA1.SMA = 54999.99999999999;
EVA1.ECC = 1.556821079382799e-16;
EVA1.INC = 0;
EVA1.RAAN = 0;
EVA1.AOP = 0;
EVA1.TA = 0;
EVA1.DryMass = 850;
EVA1.Cd = 2.2;
EVA1.Cr = 1.8;
EVA1.DragArea = 15;
EVA1.SRPArea = 1;
EVA1.SPADDragScaleFactor = 1;
EVA1.SPADSRPScaleFactor = 1;
EVA1.AtmosDensityScaleFactor = 1;
EVA1.ExtendedMassPropertiesModel = 'None';
EVA1.NAIFId = -10000001;
EVA1.NAIFIdReferenceFrame = -9000001;
EVA1.OrbitColor = Red;
EVA1.TargetColor = Teal;
EVA1.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
EVA1.CdSigma = 1e+70;
EVA1.CrSigma = 1e+70;
EVA1.Id = 'SatId';
EVA1.Attitude = CoordinateSystemFixed;
EVA1.SPADSRPInterpolationMethod = Bilinear;
EVA1.SPADSRPScaleFactorSigma = 1e+70;
EVA1.SPADDragInterpolationMethod = Bilinear;
EVA1.SPADDragScaleFactorSigma = 1e+70;
EVA1.AtmosDensityScaleFactorSigma = 1e+70;
EVA1.ModelFile = 'aura.3ds';
EVA1.ModelOffsetX = 0;
EVA1.ModelOffsetY = 0;
EVA1.ModelOffsetZ = 0;
EVA1.ModelRotationX = 0;
EVA1.ModelRotationY = 0;
EVA1.ModelRotationZ = 0;
EVA1.ModelScale = 1;
EVA1.AttitudeDisplayStateType = 'Quaternion';
EVA1.AttitudeRateDisplayStateType = 'AngularVelocity';
EVA1.AttitudeCoordinateSystem = EarthMJ2000Eq;
EVA1.EulerAngleSequence = '321';

Create Spacecraft EVA2;
EVA2.DateFormat = UTCGregorian;
EVA2.Epoch = '01 Jan 2000 00:00:00.000';
EVA2.CoordinateSystem = MoonMJ2000Eq;
EVA2.DisplayStateType = Keplerian;
EVA2.SMA = 25000.00000000001;
EVA2.ECC = 5.661167561391995e-16;
EVA2.INC = 90;
EVA2.RAAN = 90;
EVA2.AOP = 0;
EVA2.TA = 0;
EVA2.DryMass = 850;
EVA2.Cd = 2.2;
EVA2.Cr = 1.8;
EVA2.DragArea = 15;
EVA2.SRPArea = 1;
EVA2.SPADDragScaleFactor = 1;
EVA2.SPADSRPScaleFactor = 1;
EVA2.AtmosDensityScaleFactor = 1;
EVA2.ExtendedMassPropertiesModel = 'None';
EVA2.NAIFId = -10000001;
EVA2.NAIFIdReferenceFrame = -9000001;
EVA2.OrbitColor = Red;
EVA2.TargetColor = Teal;
EVA2.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
EVA2.CdSigma = 1e+70;
EVA2.CrSigma = 1e+70;
EVA2.Id = 'SatId';
EVA2.Attitude = CoordinateSystemFixed;
EVA2.SPADSRPInterpolationMethod = Bilinear;
EVA2.SPADSRPScaleFactorSigma = 1e+70;
EVA2.SPADDragInterpolationMethod = Bilinear;
EVA2.SPADDragScaleFactorSigma = 1e+70;
EVA2.AtmosDensityScaleFactorSigma = 1e+70;
EVA2.ModelFile = 'aura.3ds';
EVA2.ModelOffsetX = 0;
EVA2.ModelOffsetY = 0;
EVA2.ModelOffsetZ = 0;
EVA2.ModelRotationX = 0;
EVA2.ModelRotationY = 0;
EVA2.ModelRotationZ = 0;
EVA2.ModelScale = 1;
EVA2.AttitudeDisplayStateType = 'Quaternion';
EVA2.AttitudeRateDisplayStateType = 'AngularVelocity';
EVA2.AttitudeCoordinateSystem = EarthMJ2000Eq;
EVA2.EulerAngleSequence = '321';

%----------------------------------------
%---------- GroundStations
%----------------------------------------

Create GroundStation Ohio;
Ohio.OrbitColor = [87 227 137];
Ohio.TargetColor = DarkGray;
Ohio.CentralBody = Earth;
Ohio.StateType = Spherical;
Ohio.HorizonReference = Sphere;
Ohio.Location1 = 40.4173;
Ohio.Location2 = 276.9071;
Ohio.Location3 = 0.33;
Ohio.Id = 'Ohio';
Ohio.IonosphereModel = 'None';
Ohio.TroposphereModel = 'None';
Ohio.DataSource = 'Constant';
Ohio.Temperature = 295.1;
Ohio.Pressure = 1013.5;
Ohio.Humidity = 55;
Ohio.MinimumElevationAngle = 7;

Create GroundStation Sydney;
Sydney.OrbitColor = [51 209 122];
Sydney.TargetColor = DarkGray;
Sydney.CentralBody = Earth;
Sydney.StateType = Spherical;
Sydney.HorizonReference = Sphere;
Sydney.Location1 = -32.242;
Sydney.Location2 = 148.6031;
Sydney.Location3 = 0.24;
Sydney.Id = 'Sydney';
Sydney.IonosphereModel = 'None';
Sydney.TroposphereModel = 'None';
Sydney.DataSource = 'Constant';
Sydney.Temperature = 295.1;
Sydney.Pressure = 1013.5;
Sydney.Humidity = 55;
Sydney.MinimumElevationAngle = 7;

Create GroundStation Dublin;
Dublin.OrbitColor = [51 209 122];
Dublin.TargetColor = [169 169 169];
Dublin.CentralBody = Earth;
Dublin.StateType = Spherical;
Dublin.HorizonReference = Sphere;
Dublin.Location1 = 53.24;
Dublin.Location2 = 6.130000000000001;
Dublin.Location3 = 0.15;
Dublin.Id = 'Dublin';
Dublin.IonosphereModel = 'None';
Dublin.TroposphereModel = 'None';
Dublin.DataSource = 'Constant';
Dublin.Temperature = 295.1;
Dublin.Pressure = 1013.5;
Dublin.Humidity = 55;
Dublin.MinimumElevationAngle = 7;

Create GroundStation HLS;
HLS.OrbitColor = Thistle;
HLS.TargetColor = DarkGray;
HLS.CentralBody = Luna;
HLS.StateType = Spherical;
HLS.HorizonReference = Sphere;
HLS.Location1 = -1;
HLS.Location2 = 0;
HLS.Location3 = 2;
HLS.Id = 'HLS';
HLS.IonosphereModel = 'None';
HLS.TroposphereModel = 'None';
HLS.DataSource = 'Constant';
HLS.Temperature = 295.1;
HLS.Pressure = 1013.5;
HLS.Humidity = 55;
HLS.MinimumElevationAngle = 7;



%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
DefaultProp_ForceModel.CentralBody = Earth;
DefaultProp_ForceModel.PointMasses = {Luna};
DefaultProp_ForceModel.Drag = None;
DefaultProp_ForceModel.SRP = Off;
DefaultProp_ForceModel.RelativisticCorrection = Off;
DefaultProp_ForceModel.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator AccurateProp;
AccurateProp.FM = DefaultProp_ForceModel;
AccurateProp.Type = RungeKutta89;
AccurateProp.InitialStepSize = 0.01;
AccurateProp.Accuracy = 9.999999999999999e-12;
AccurateProp.MinStep = 1e-05;
AccurateProp.MaxStep = 2700;
AccurateProp.MaxStepAttempts = 50;
AccurateProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
MoonMJ2000Eq.Origin = Luna;
MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem MoonMJ2000Ec;
MoonMJ2000Ec.Origin = Luna;
MoonMJ2000Ec.Axes = MJ2000Ec;

Create CoordinateSystem MoonFixed;
MoonFixed.Origin = Luna;
MoonFixed.Axes = BodyFixed;

Create CoordinateSystem MoonICRF;
MoonICRF.Origin = Luna;
MoonICRF.Axes = ICRF;

Create CoordinateSystem EarthMoon;
EarthMoon.Origin = Earth;
EarthMoon.Axes = ObjectReferenced;
EarthMoon.XAxis = R;
EarthMoon.ZAxis = N;
EarthMoon.Primary = Earth;
EarthMoon.Secondary = Luna;

%----------------------------------------
%---------- EventLocators
%----------------------------------------

Create ContactLocator EVA1Contacts;
EVA1Contacts.Target = EVA1;
EVA1Contacts.Filename = 'EVA1Contacts.txt';
EVA1Contacts.InputEpochFormat = 'TAIModJulian';
EVA1Contacts.InitialEpoch = '21545';
EVA1Contacts.StepSize = 10;
EVA1Contacts.FinalEpoch = '21545.138';
EVA1Contacts.UseLightTimeDelay = true;
EVA1Contacts.UseStellarAberration = true;
EVA1Contacts.WriteReport = true;
EVA1Contacts.RunMode = Automatic;
EVA1Contacts.UseEntireInterval = true;
EVA1Contacts.Observers = {Dublin, HLS, Ohio, Sydney};
EVA1Contacts.LightTimeDirection = Transmit;
EVA1Contacts.LeftJustified = false;
EVA1Contacts.ReportPrecision = 6;
EVA1Contacts.ReportFormat = 'Legacy';
EVA1Contacts.IntervalStepSize = 0;
EVA1Contacts.ReportTimeFormat = 'UTCGregorian';

Create ContactLocator EVA2Contacts;
EVA2Contacts.Target = EVA2;
EVA2Contacts.Filename = 'EVA2Contacts.txt';
EVA2Contacts.InputEpochFormat = 'TAIModJulian';
EVA2Contacts.InitialEpoch = '21545';
EVA2Contacts.StepSize = 10;
EVA2Contacts.FinalEpoch = '21545.138';
EVA2Contacts.UseLightTimeDelay = true;
EVA2Contacts.UseStellarAberration = true;
EVA2Contacts.WriteReport = true;
EVA2Contacts.RunMode = Automatic;
EVA2Contacts.UseEntireInterval = true;
EVA2Contacts.Observers = {Dublin, HLS, Ohio, Sydney};
EVA2Contacts.LightTimeDirection = Transmit;
EVA2Contacts.LeftJustified = false;
EVA2Contacts.ReportPrecision = 6;
EVA2Contacts.ReportFormat = 'Legacy';
EVA2Contacts.IntervalStepSize = 0;
EVA2Contacts.ReportTimeFormat = 'UTCGregorian';

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView EarthMoonOrbitView;
EarthMoonOrbitView.SolverIterations = Current;
EarthMoonOrbitView.UpperLeft = [ 0 0 ];
EarthMoonOrbitView.Size = [ 0.997289972899729 0.9766327142001199 ];
EarthMoonOrbitView.RelativeZOrder = 594;
EarthMoonOrbitView.Maximized = true;
EarthMoonOrbitView.Add = {EVA1, EVA2, Earth, Luna};
EarthMoonOrbitView.CoordinateSystem = EarthMJ2000Eq;
EarthMoonOrbitView.DrawObject = [ true true true true ];
EarthMoonOrbitView.DataCollectFrequency = 1;
EarthMoonOrbitView.UpdatePlotFrequency = 50;
EarthMoonOrbitView.NumPointsToRedraw = 0;
EarthMoonOrbitView.ShowPlot = true;
EarthMoonOrbitView.MaxPlotPoints = 20000;
EarthMoonOrbitView.ShowLabels = true;
EarthMoonOrbitView.ViewPointReference = Earth;
EarthMoonOrbitView.ViewPointVector = [ 90000 0 0 ];
EarthMoonOrbitView.ViewDirection = Luna;
EarthMoonOrbitView.ViewScaleFactor = 0.5;
EarthMoonOrbitView.ViewUpCoordinateSystem = EarthFixed;
EarthMoonOrbitView.ViewUpAxis = Z;
EarthMoonOrbitView.EclipticPlane = On;
EarthMoonOrbitView.XYPlane = Off;
EarthMoonOrbitView.WireFrame = Off;
EarthMoonOrbitView.Axes = Off;
EarthMoonOrbitView.Grid = Off;
EarthMoonOrbitView.SunLine = Off;
EarthMoonOrbitView.UseInitialView = On;
EarthMoonOrbitView.StarCount = 7000;
EarthMoonOrbitView.EnableStars = Off;
EarthMoonOrbitView.EnableConstellations = Off;

Create GroundTrackPlot EarthGroundTrackPlot;
EarthGroundTrackPlot.SolverIterations = Current;
EarthGroundTrackPlot.UpperLeft = [ 0 0 ];
EarthGroundTrackPlot.Size = [ 0.997289972899729 0.9766327142001199 ];
EarthGroundTrackPlot.RelativeZOrder = 589;
EarthGroundTrackPlot.Maximized = true;
EarthGroundTrackPlot.Add = {EVA1, EVA2, Ohio, Sydney, Dublin};
EarthGroundTrackPlot.DataCollectFrequency = 1;
EarthGroundTrackPlot.UpdatePlotFrequency = 50;
EarthGroundTrackPlot.NumPointsToRedraw = 0;
EarthGroundTrackPlot.ShowPlot = true;
EarthGroundTrackPlot.MaxPlotPoints = 20000;
EarthGroundTrackPlot.CentralBody = Earth;
EarthGroundTrackPlot.TextureMap = 'ModifiedBlueMarble.jpg';

Create GroundTrackPlot MoonGroundTrackPlot;
MoonGroundTrackPlot.SolverIterations = Current;
MoonGroundTrackPlot.UpperLeft = [ 0 0 ];
MoonGroundTrackPlot.Size = [ 0.997289972899729 0.9766327142001199 ];
MoonGroundTrackPlot.RelativeZOrder = 585;
MoonGroundTrackPlot.Maximized = true;
MoonGroundTrackPlot.Add = {EVA1, EVA2, HLS};
MoonGroundTrackPlot.DataCollectFrequency = 1;
MoonGroundTrackPlot.UpdatePlotFrequency = 50;
MoonGroundTrackPlot.NumPointsToRedraw = 0;
MoonGroundTrackPlot.ShowPlot = true;
MoonGroundTrackPlot.MaxPlotPoints = 20000;
MoonGroundTrackPlot.CentralBody = Luna;
MoonGroundTrackPlot.TextureMap = 'Moon_HermesCelestiaMotherlode.jpg';

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate AccurateProp(EVA1, EVA2) {EVA1.ElapsedDays = 3};
