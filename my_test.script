%General Mission Analysis Tool(GMAT) Script
%Created: 2025-07-29 16:55:33


%----------------------------------------
%---------- User-Defined Calculated Points
%----------------------------------------

Create Barycenter Barycenter1;
Barycenter1.OrbitColor = Gold;
Barycenter1.TargetColor = DarkGray;
Barycenter1.BodyNames = {Earth, Luna};

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
DefaultSC.DateFormat = UTCGregorian;
DefaultSC.Epoch = '01 Jan 2000 11:59:28.000';
DefaultSC.CoordinateSystem = Moon;
DefaultSC.DisplayStateType = Keplerian;
DefaultSC.SMA = 99.99999999997297;
DefaultSC.ECC = 0.009999999999787016;
DefaultSC.INC = 104.0000000000038;
DefaultSC.RAAN = 180;
DefaultSC.AOP = 44.99999999923483;
DefaultSC.TA = 90.00000000075396;
DefaultSC.DryMass = 850;
DefaultSC.Cd = 2.2;
DefaultSC.Cr = 1.8;
DefaultSC.DragArea = 15;
DefaultSC.SRPArea = 1;
DefaultSC.SPADDragScaleFactor = 1;
DefaultSC.SPADSRPScaleFactor = 1;
DefaultSC.AtmosDensityScaleFactor = 1;
DefaultSC.ExtendedMassPropertiesModel = 'None';
DefaultSC.NAIFId = -10019001;
DefaultSC.NAIFIdReferenceFrame = -9019001;
DefaultSC.OrbitColor = Red;
DefaultSC.TargetColor = Teal;
DefaultSC.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
DefaultSC.CdSigma = 1e+70;
DefaultSC.CrSigma = 1e+70;
DefaultSC.Id = 'SatId';
DefaultSC.Attitude = CoordinateSystemFixed;
DefaultSC.SPADSRPInterpolationMethod = Bilinear;
DefaultSC.SPADSRPScaleFactorSigma = 1e+70;
DefaultSC.SPADDragInterpolationMethod = Bilinear;
DefaultSC.SPADDragScaleFactorSigma = 1e+70;
DefaultSC.AtmosDensityScaleFactorSigma = 1e+70;
DefaultSC.ModelFile = 'aura.3ds';
DefaultSC.ModelOffsetX = 0;
DefaultSC.ModelOffsetY = 0;
DefaultSC.ModelOffsetZ = 0;
DefaultSC.ModelRotationX = 0;
DefaultSC.ModelRotationY = 0;
DefaultSC.ModelRotationZ = 0;
DefaultSC.ModelScale = 1;
DefaultSC.AttitudeDisplayStateType = 'Quaternion';
DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
DefaultSC.AttitudeCoordinateSystem = Moon;
DefaultSC.EulerAngleSequence = '321';

Create Spacecraft Spacecraft1;
Spacecraft1.DateFormat = TAIModJulian;
Spacecraft1.Epoch = '21545';
Spacecraft1.CoordinateSystem = EarthMJ2000Eq;
Spacecraft1.DisplayStateType = Cartesian;
Spacecraft1.X = 7100;
Spacecraft1.Y = 0;
Spacecraft1.Z = 1300;
Spacecraft1.VX = 0;
Spacecraft1.VY = 7.35;
Spacecraft1.VZ = 1;
Spacecraft1.DryMass = 850;
Spacecraft1.Cd = 2.2;
Spacecraft1.Cr = 1.8;
Spacecraft1.DragArea = 15;
Spacecraft1.SRPArea = 1;
Spacecraft1.SPADDragScaleFactor = 1;
Spacecraft1.SPADSRPScaleFactor = 1;
Spacecraft1.AtmosDensityScaleFactor = 1;
Spacecraft1.ExtendedMassPropertiesModel = 'None';
Spacecraft1.NAIFId = -10020001;
Spacecraft1.NAIFIdReferenceFrame = -9020001;
Spacecraft1.OrbitColor = Green;
Spacecraft1.TargetColor = LightGray;
Spacecraft1.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
Spacecraft1.CdSigma = 1e+70;
Spacecraft1.CrSigma = 1e+70;
Spacecraft1.Id = 'SatId';
Spacecraft1.Attitude = CoordinateSystemFixed;
Spacecraft1.SPADSRPInterpolationMethod = Bilinear;
Spacecraft1.SPADSRPScaleFactorSigma = 1e+70;
Spacecraft1.SPADDragInterpolationMethod = Bilinear;
Spacecraft1.SPADDragScaleFactorSigma = 1e+70;
Spacecraft1.AtmosDensityScaleFactorSigma = 1e+70;
Spacecraft1.ModelFile = 'aura.3ds';
Spacecraft1.ModelOffsetX = 0;
Spacecraft1.ModelOffsetY = 0;
Spacecraft1.ModelOffsetZ = 0;
Spacecraft1.ModelRotationX = 0;
Spacecraft1.ModelRotationY = 0;
Spacecraft1.ModelRotationZ = 0;
Spacecraft1.ModelScale = 1;
Spacecraft1.AttitudeDisplayStateType = 'Quaternion';
Spacecraft1.AttitudeRateDisplayStateType = 'AngularVelocity';
Spacecraft1.AttitudeCoordinateSystem = EarthMJ2000Eq;
Spacecraft1.EulerAngleSequence = '321';

Create Spacecraft Spacecraft2;
Spacecraft2.DateFormat = TAIModJulian;
Spacecraft2.Epoch = '21545';
Spacecraft2.CoordinateSystem = EarthMJ2000Eq;
Spacecraft2.DisplayStateType = Cartesian;
Spacecraft2.X = 7100;
Spacecraft2.Y = 0;
Spacecraft2.Z = 1300;
Spacecraft2.VX = 0;
Spacecraft2.VY = 7.35;
Spacecraft2.VZ = 1;
Spacecraft2.DryMass = 850;
Spacecraft2.Cd = 2.2;
Spacecraft2.Cr = 1.8;
Spacecraft2.DragArea = 15;
Spacecraft2.SRPArea = 1;
Spacecraft2.SPADDragScaleFactor = 1;
Spacecraft2.SPADSRPScaleFactor = 1;
Spacecraft2.AtmosDensityScaleFactor = 1;
Spacecraft2.ExtendedMassPropertiesModel = 'None';
Spacecraft2.NAIFId = -10020001;
Spacecraft2.NAIFIdReferenceFrame = -9020001;
Spacecraft2.OrbitColor = Green;
Spacecraft2.TargetColor = LightGray;
Spacecraft2.OrbitErrorCovariance = [ 1e+70 0 0 0 0 0 ; 0 1e+70 0 0 0 0 ; 0 0 1e+70 0 0 0 ; 0 0 0 1e+70 0 0 ; 0 0 0 0 1e+70 0 ; 0 0 0 0 0 1e+70 ];
Spacecraft2.CdSigma = 1e+70;
Spacecraft2.CrSigma = 1e+70;
Spacecraft2.Id = 'SatId';
Spacecraft2.Attitude = CoordinateSystemFixed;
Spacecraft2.SPADSRPInterpolationMethod = Bilinear;
Spacecraft2.SPADSRPScaleFactorSigma = 1e+70;
Spacecraft2.SPADDragInterpolationMethod = Bilinear;
Spacecraft2.SPADDragScaleFactorSigma = 1e+70;
Spacecraft2.AtmosDensityScaleFactorSigma = 1e+70;
Spacecraft2.ModelFile = 'aura.3ds';
Spacecraft2.ModelOffsetX = 0;
Spacecraft2.ModelOffsetY = 0;
Spacecraft2.ModelOffsetZ = 0;
Spacecraft2.ModelRotationX = 0;
Spacecraft2.ModelRotationY = 0;
Spacecraft2.ModelRotationZ = 0;
Spacecraft2.ModelScale = 1;
Spacecraft2.AttitudeDisplayStateType = 'Quaternion';
Spacecraft2.AttitudeRateDisplayStateType = 'AngularVelocity';
Spacecraft2.AttitudeCoordinateSystem = EarthMJ2000Eq;
Spacecraft2.EulerAngleSequence = '321';

%----------------------------------------
%---------- GroundStations
%----------------------------------------

Create GroundStation GroundStation1;
GroundStation1.OrbitColor = Thistle;
GroundStation1.TargetColor = DarkGray;
GroundStation1.CentralBody = Earth;
GroundStation1.StateType = Spherical;
GroundStation1.HorizonReference = Sphere;
GroundStation1.Location1 = 29.76;
GroundStation1.Location2 = 95.36;
GroundStation1.Location3 = 0.027;
GroundStation1.Id = 'Houston';
GroundStation1.IonosphereModel = 'None';
GroundStation1.TroposphereModel = 'None';
GroundStation1.DataSource = 'Constant';
GroundStation1.Temperature = 295.1;
GroundStation1.Pressure = 1013.5;
GroundStation1.Humidity = 55;
GroundStation1.MinimumElevationAngle = 7;

Create GroundStation GroundStation2;
GroundStation2.OrbitColor = Thistle;
GroundStation2.TargetColor = DarkGray;
GroundStation2.CentralBody = Earth;
GroundStation2.StateType = Spherical;
GroundStation2.HorizonReference = Sphere;
GroundStation2.Location1 = 33.87;
GroundStation2.Location2 = 151.21;
GroundStation2.Location3 = 0.039;
GroundStation2.Id = 'Sydney';
GroundStation2.IonosphereModel = 'None';
GroundStation2.TroposphereModel = 'None';
GroundStation2.DataSource = 'Constant';
GroundStation2.Temperature = 295.1;
GroundStation2.Pressure = 1013.5;
GroundStation2.Humidity = 55;
GroundStation2.MinimumElevationAngle = 7;

Create GroundStation GroundStation3;
GroundStation3.OrbitColor = Thistle;
GroundStation3.TargetColor = DarkGray;
GroundStation3.CentralBody = Earth;
GroundStation3.StateType = Spherical;
GroundStation3.HorizonReference = Sphere;
GroundStation3.Location1 = 53;
GroundStation3.Location2 = 8;
GroundStation3.Location3 = 1.038;
GroundStation3.Id = 'Ireland';
GroundStation3.IonosphereModel = 'None';
GroundStation3.TroposphereModel = 'None';
GroundStation3.DataSource = 'Constant';
GroundStation3.Temperature = 295.1;
GroundStation3.Pressure = 1013.5;
GroundStation3.Humidity = 55;
GroundStation3.MinimumElevationAngle = 7;

%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
DefaultProp_ForceModel.CentralBody = Earth;
DefaultProp_ForceModel.PrimaryBodies = {Earth};
DefaultProp_ForceModel.Drag = None;
DefaultProp_ForceModel.SRP = Off;
DefaultProp_ForceModel.RelativisticCorrection = Off;
DefaultProp_ForceModel.ErrorControl = RSSStep;
DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
DefaultProp_ForceModel.GravityField.Earth.Order = 4;
DefaultProp_ForceModel.GravityField.Earth.StmLimit = 100;
DefaultProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';

Create ForceModel Propagator1_ForceModel;
Propagator1_ForceModel.CentralBody = Luna;
Propagator1_ForceModel.PrimaryBodies = {Luna};
Propagator1_ForceModel.Drag = None;
Propagator1_ForceModel.SRP = Off;
Propagator1_ForceModel.RelativisticCorrection = Off;
Propagator1_ForceModel.ErrorControl = RSSStep;
Propagator1_ForceModel.GravityField.Luna.Degree = 4;
Propagator1_ForceModel.GravityField.Luna.Order = 4;
Propagator1_ForceModel.GravityField.Luna.StmLimit = 100;
Propagator1_ForceModel.GravityField.Luna.PotentialFile = 'LP165P.cof';
Propagator1_ForceModel.GravityField.Luna.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
DefaultProp.FM = DefaultProp_ForceModel;
DefaultProp.Type = RungeKutta89;
DefaultProp.InitialStepSize = 1;
DefaultProp.Accuracy = 9.999999999999999e-12;
DefaultProp.MinStep = 0.001;
DefaultProp.MaxStep = 2700;
DefaultProp.MaxStepAttempts = 50;
DefaultProp.StopIfAccuracyIsViolated = true;

Create Propagator Propagator1;
Propagator1.FM = Propagator1_ForceModel;
Propagator1.Type = RungeKutta89;
Propagator1.InitialStepSize = 1;
Propagator1.Accuracy = 9.999999999999999e-12;
Propagator1.MinStep = 0.001;
Propagator1.MaxStep = 2700;
Propagator1.MaxStepAttempts = 50;
Propagator1.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem EarthMoon;
EarthMoon.Origin = Barycenter1;
EarthMoon.Axes = ObjectReferenced;
EarthMoon.XAxis = R;
EarthMoon.ZAxis = N;
EarthMoon.Primary = Earth;
EarthMoon.Secondary = Luna;

Create CoordinateSystem Moon;
Moon.Origin = Luna;
Moon.Axes = MJ2000Eq;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
DefaultOrbitView.SolverIterations = Current;
DefaultOrbitView.UpperLeft = [ 0 0 ];
DefaultOrbitView.Size = [ 0.999375 0.967032967032967 ];
DefaultOrbitView.RelativeZOrder = 464;
DefaultOrbitView.Maximized = true;
DefaultOrbitView.Add = {DefaultSC, Earth, Luna};
DefaultOrbitView.CoordinateSystem = EarthMoon;
DefaultOrbitView.DrawObject = [ true true true ];
DefaultOrbitView.DataCollectFrequency = 1;
DefaultOrbitView.UpdatePlotFrequency = 50;
DefaultOrbitView.NumPointsToRedraw = 0;
DefaultOrbitView.ShowPlot = true;
DefaultOrbitView.MaxPlotPoints = 20000;
DefaultOrbitView.ShowLabels = true;
DefaultOrbitView.ViewPointReference = Earth;
DefaultOrbitView.ViewPointVector = [ 0 0 120000 ];
DefaultOrbitView.ViewDirection = Luna;
DefaultOrbitView.ViewScaleFactor = 1;
DefaultOrbitView.ViewUpCoordinateSystem = EarthMoon;
DefaultOrbitView.ViewUpAxis = Z;
DefaultOrbitView.EclipticPlane = Off;
DefaultOrbitView.XYPlane = Off;
DefaultOrbitView.WireFrame = Off;
DefaultOrbitView.Axes = On;
DefaultOrbitView.Grid = Off;
DefaultOrbitView.SunLine = Off;
DefaultOrbitView.UseInitialView = On;
DefaultOrbitView.StarCount = 7000;
DefaultOrbitView.EnableStars = Off;
DefaultOrbitView.EnableConstellations = Off;

Create GroundTrackPlot DefaultGroundTrackPlot;
DefaultGroundTrackPlot.SolverIterations = Current;
DefaultGroundTrackPlot.UpperLeft = [ 0 0 ];
DefaultGroundTrackPlot.Size = [ 0.999375 0.967032967032967 ];
DefaultGroundTrackPlot.RelativeZOrder = 444;
DefaultGroundTrackPlot.Maximized = true;
DefaultGroundTrackPlot.Add = {DefaultSC, GroundStation1, GroundStation2, GroundStation3};
DefaultGroundTrackPlot.DataCollectFrequency = 1;
DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
DefaultGroundTrackPlot.NumPointsToRedraw = 0;
DefaultGroundTrackPlot.ShowPlot = true;
DefaultGroundTrackPlot.MaxPlotPoints = 20000;
DefaultGroundTrackPlot.CentralBody = Earth;
DefaultGroundTrackPlot.TextureMap = 'ModifiedBlueMarble.jpg';


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate BackProp Synchronized DefaultProp(DefaultSC) {DefaultSC.ElapsedSecs = 1200.0};
