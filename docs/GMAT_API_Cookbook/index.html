
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>GMAT API Cookbook &#8212; API Cookbook R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css" />
    
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Propagation with the GMAT API" href="source/propagation.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="source/propagation.html" title="Propagation with the GMAT API"
             accesskey="N">next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">API Cookbook R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT API Cookbook</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="gmat-api-cookbook">
<h1>GMAT API Cookbook<a class="headerlink" href="#gmat-api-cookbook" title="Permalink to this heading">¶</a></h1>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This project is under active development.</p>
</div>
<p>The GMAT API Cookbook is a collection of use case examples that demonstrate common activities performed by GMAT API users.  This document is a “living document:” it is intended to act as a central location for API examples that previously were only available through request of knowledgeable API users.</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="source/propagation.html"><strong>Propagation with the GMAT API</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/propagation.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/propagation.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/propagation.html#discussion"><strong>Discussion</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/statemanagement.html"><strong>State Management with the GMAT API</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/statemanagement.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/statemanagement.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/statemanagement.html#discussion"><strong>Discussion</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/stmpropagation.html"><strong>STM and Covariance Propagation</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/stmpropagation.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/stmpropagation.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/stmpropagation.html#discussion"><strong>Discussion</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/stmpropagation.html#appendix-a-complete-example"><strong>Appendix: A Complete Example</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/finiteBurn.html"><strong>Executing Finite Burns</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/finiteBurn.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/finiteBurn.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/finiteBurn.html#discussion"><strong>Discussion</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/commandaccess.html">Accessing GMAT Commands</a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/commandaccess.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/commandaccess.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/commandaccess.html#discussion"><strong>Discussion</strong></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/commandfunctions.html">A Collection of Command Functions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/commandfunctions.html#the-commandfunction-code">The CommandFunction Code</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="source/solvercontrol.html"><strong>Control of Scripted Solvers</strong></a><ul>
<li class="toctree-l2"><a class="reference internal" href="source/solvercontrol.html#problem"><strong>Problem</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/solvercontrol.html#solution"><strong>Solution</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/solvercontrol.html#discussion"><strong>Discussion</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/solvercontrol.html#complete-targeter-script"><strong>Complete Targeter Script</strong></a></li>
<li class="toctree-l2"><a class="reference internal" href="source/solvercontrol.html#complete-optimizer-script"><strong>Complete Optimizer Script</strong></a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="source/propagation.html"
                          title="next chapter"><strong>Propagation with the GMAT API</strong></a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="source/propagation.html" title="Propagation with the GMAT API"
             >next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">API Cookbook R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT API Cookbook</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>