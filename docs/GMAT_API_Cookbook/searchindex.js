Search.setIndex({"docnames": ["index", "source/commandaccess", "source/commandfunctions", "source/finiteBurn", "source/propagation", "source/solvercontrol", "source/statemanagement", "source/stmpropagation"], "filenames": ["index.rst", "source/commandaccess.rst", "source/commandfunctions.rst", "source/finiteBurn.rst", "source/propagation.rst", "source/solvercontrol.rst", "source/statemanagement.rst", "source/stmpropagation.rst"], "titles": ["GMAT API Cookbook", "Accessing GMAT Commands", "A Collection of Command Functions", "<strong>Executing Finite Burns</strong>", "<strong>Propagation with the GMAT API</strong>", "<strong>Control of Scripted Solvers</strong>", "<strong>State Management with the GMAT API</strong>", "<strong>STM and Covariance Propagation</strong>"], "terms": {"thi": [0, 1, 2, 3, 4, 5, 6, 7], "project": 0, "under": [0, 4, 6], "activ": [0, 3], "develop": [0, 3], "The": [0, 1, 3, 4, 5, 6, 7], "collect": [0, 4, 5, 6], "us": [0, 1, 2, 3, 4, 6, 7], "case": [0, 3], "exampl": [0, 1, 5, 6], "demonstr": [0, 5, 6], "common": 0, "perform": [0, 1, 3, 4, 7], "user": [0, 1, 3, 4, 5, 6, 7], "document": [0, 7], "live": 0, "intend": 0, "act": [0, 3], "central": 0, "locat": [0, 1, 5, 6], "previous": [0, 6], "were": 0, "onli": [0, 5], "avail": [0, 3, 5], "through": [0, 1, 3, 4, 5, 7], "request": [0, 2, 6], "knowledg": 0, "propag": [0, 1, 3, 5], "problem": 0, "solut": 0, "discuss": 0, "state": [0, 1, 3, 4, 7], "manag": [0, 1, 3, 7], "stm": 0, "covari": 0, "appendix": 0, "A": [0, 1, 3, 5, 6], "complet": [0, 1, 4], "execut": [0, 1, 5], "finit": [0, 7], "burn": [0, 1, 7], "access": [0, 2, 3, 6, 7], "command": [0, 3, 4, 6], "function": [0, 1, 4, 5, 6], "commandfunct": [0, 5], "code": [0, 1, 4, 5, 6, 7], "control": [0, 1, 2, 3], "script": [0, 1, 2, 3, 4, 6], "solver": [0, 1, 2], "target": [0, 1], "optim": [0, 1], "s": [1, 3, 4, 5, 6, 7], "mission": [1, 2, 6], "sequenc": [1, 2, 3, 5], "mc": [1, 2, 5], "order": [1, 3, 4, 5, 7], "action": [1, 5, 7], "taken": [1, 6], "when": [1, 2, 5, 6], "simul": [1, 6, 7], "link": 1, "list": [1, 2, 5, 7], "object": [1, 3, 4, 5, 6, 7], "all": [1, 3, 4, 7], "deriv": 1, "from": [1, 2, 3, 5, 6, 7], "gmatcommand": 1, "base": 1, "class": [1, 4], "api": [1, 2, 5, 7], "mai": [1, 7], "need": [1, 3, 4, 5, 6, 7], "determin": 1, "system": [1, 3, 4, 6], "specif": [1, 2, 3, 4, 6], "node": [1, 2, 5], "page": 1, "show": [1, 4, 5, 6, 7], "how": [1, 5, 6, 7], "do": [1, 5], "ex_hohmanntransf": [1, 5], "sampl": [1, 5], "share": [1, 5], "set": [1, 2, 3, 4, 5, 6, 7], "variabl": [1, 5, 6], "vari": 1, "specifi": [1, 4, 5], "goal": [1, 3, 4, 5], "achiev": [1, 3], "minim": 1, "nonlinearconstraint": 1, "constrain": [1, 5], "chapter": [1, 2, 5, 6, 7], "hohmann": [1, 5], "transfer": [1, 5], "suppli": [1, 5], "creat": [1, 3, 4, 5, 6, 7], "folder": [1, 5], "one": [1, 4, 5, 6], "up": [1, 3, 4, 5, 6], "name": [1, 2, 4, 5, 6], "copi": [1, 2, 5, 6], "purpos": 1, "explor": 1, "shown": [1, 3, 4, 5, 6, 7], "7": [1, 4, 5], "full": [1, 5, 7], "beginmissionsequ": [1, 3], "prop": [1, 3, 4, 5], "perige": 1, "defaultprop": [1, 5], "defaultsc": [1, 5], "periapsi": 1, "veloc": [1, 3, 4, 6, 7], "direct": [1, 3, 6, 7], "reach": [1, 4], "an": [1, 3, 4, 5, 6, 7], "altern": 1, "apoapsi": [1, 4, 5], "point": [1, 3, 4, 6, 7], "rais": [1, 3, 5], "circular": [1, 5], "dc": [1, 5], "solvemod": 1, "solv": [1, 5], "exitmod": 1, "discardandcontinu": 1, "toi": [1, 5], "v": [1, 4, 5, 7], "element1": [1, 5], "0": [1, 2, 3, 4, 5, 6, 7], "5": [1, 3, 4, 5, 7], "perturb": [1, 5], "0001": [1, 5], "lower": [1, 5], "upper": [1, 5], "3": [1, 3, 4, 5, 6, 7], "14159": [1, 5], "maxstep": [1, 5], "2": [1, 2, 3, 4, 6, 7], "maneuv": [1, 3, 5, 7], "appli": [1, 3, 5, 6], "apoge": [1, 5], "rmag": [1, 5], "earth": [1, 3, 4, 5, 6, 7], "42165": [1, 5], "toler": [1, 5], "1": [1, 2, 3, 4, 6, 7], "goi": [1, 5], "ecc": [1, 4, 5, 6, 7], "endtarget": [1, 5], "For": [1, 3, 4, 5], "dai": [1, 3, 4], "elapsedsec": [1, 3], "86400": 1, "here": [1, 3, 5, 6, 7], "consist": 1, "two": 1, "piec": 1, "core": [1, 3, 7], "four": 1, "each": [1, 4, 5, 7], "line": [1, 4, 7], "instanc": [1, 2, 3], "gmatcomand": 1, "provid": [1, 3, 4, 5, 6, 7], "method": [1, 2, 4, 5, 6, 7], "getnext": [1, 2], "return": [1, 2, 3, 7], "pointer": 1, "next": [1, 4], "getprevi": 1, "preced": 1, "termin": 1, "both": [1, 3, 5], "end": [1, 5], "null": 1, "indic": [1, 5], "none": [1, 2, 5], "python": [1, 3, 5, 6], "branchcommand": 1, "ar": [1, 3, 4, 5, 6, 7], "can": [1, 3, 4, 5, 6], "pass": [1, 4, 7], "process": [1, 5, 7], "more": [1, 3, 4, 6, 7], "than": [1, 6], "either": [1, 4, 5, 7], "In": [1, 3, 4, 5, 6], "follow": [1, 2, 4, 5], "tune": [1, 5], "pair": 1, "move": 1, "spacecraft": [1, 3, 4, 6, 7], "low": [1, 4], "orbit": [1, 4, 5, 6, 7], "geosynchron": 1, "examin": [1, 4], "them": [1, 5], "manipul": [1, 5, 6], "focu": [1, 3, 4], "present": [1, 5], "later": [1, 2, 3, 4], "section": [1, 5, 6], "main": 1, "step": [1, 3, 4, 5, 6, 7], "load": [1, 2, 5, 7], "apius": 1, "loadscript": [1, 5], "load_gmat": [1, 2, 5, 7], "import": [1, 2, 4, 5, 7], "retval": [1, 5], "fals": [1, 3, 5], "print": [1, 2, 3, 4, 5, 6, 7], "fail": [1, 5], "exit": [1, 5], "statu": [1, 5], "true": [1, 3], "wa": [1, 2, 3, 5], "first": [1, 2], "made": [1, 4, 6], "moder": [1, 2], "straightforward": 1, "connect": [1, 3, 4, 5, 6, 7], "engin": [1, 5], "singleton": 1, "mod": [1, 2, 6], "convent": 1, "noop": 1, "startnod": 1, "getfirstcommand": [1, 2], "alwai": 1, "start": [1, 2, 4, 5, 6], "work": [1, 3, 4, 5, 6, 7], "have": [1, 3, 5, 6], "type": [1, 2, 4, 6], "option": [1, 3], "field": [1, 3, 4, 7], "identifi": [1, 5], "store": 1, "string": [1, 5], "assign": [1, 4, 7], "One": 1, "wai": [1, 4, 6], "attribut": [1, 5], "current": [1, 3], "simpli": [1, 3], "directli": [1, 4, 6], "individu": [1, 4, 5], "gettypenam": 1, "getnam": [1, 2, 3, 5, 6, 7], "produc": 1, "output": [1, 6], "doe": [1, 6], "so": [1, 3, 5, 6, 7], "those": [1, 5], "empti": [1, 4], "itself": [1, 2, 3, 6], "like": [1, 6], "now": [1, 3, 4, 5, 6], "verifi": 1, "expect": [1, 6], "4": [1, 3, 4, 5, 7], "previou": [1, 6], "implement": 1, "doubli": 1, "mean": [1, 6], "subsequ": [1, 5], "come": 1, "befor": [1, 2, 4, 6, 7], "abov": [1, 4, 5, 7], "predecessor": 1, "10": [1, 3, 7], "back": 1, "displai": [1, 4, 6, 7], "walkthemc": 1, "12": [1, 4, 6, 7], "descript": [1, 5], "util": [1, 2, 3, 5, 6], "def": [1, 2, 3, 7], "simpl": [1, 3, 5], "omit": 1, "children": 1, "input": [1, 2, 5, 7], "written": 1, "count": 1, "while": [1, 2, 3, 5], "indent": 1, "20": [1, 3, 5], "format": [1, 5], "whenthi": 1, "call": [1, 5, 6, 7], "ouput": 1, "13": 1, "compar": [1, 5], "see": [1, 4, 5, 6, 7], "align": 1, "includ": 1, "loop": [1, 3], "becaus": [1, 5, 6], "off": [1, 3], "That": [1, 4, 5], "apart": 1, "remain": [1, 3], "requir": [1, 3, 7], "everi": 1, "featur": [1, 7], "find": [1, 2, 5], "check": [1, 3, 5], "branchstatu": 1, "isoftyp": [1, 2], "block": [1, 7], "ha": [1, 4, 5, 6, 7], "child": [1, 2], "been": [1, 5, 6, 7], "retriev": [1, 5, 6], "getchildcommand": [1, 2], "walk": 1, "same": [1, 6], "techniqu": 1, "done": [1, 4], "updat": [1, 6, 7], "recurs": 1, "walkthemcswithbranch": 1, "depth": 1, "parent": [1, 2], "note": [1, 3, 5, 6, 7], "hack": 1, "ideal": 1, "we": [1, 3, 4, 6, 7], "would": 1, "against": 1, "refer": [1, 2, 4, 6], "swig": 1, "It": [1, 5], "windi": 1, "littl": [1, 4], "maze": 1, "room": 1, "look": 1, "unwound": 1, "time": [1, 3, 4, 6, 7], "permit": 1, "handl": 1, "kiddo": 1, "want": 1, "thei": [1, 3, 5, 6], "buri": 1, "sever": 1, "deep": 1, "easili": [1, 3, 4], "gener": [1, 4, 7], "6": [1, 5, 6, 7], "8": [1, 4, 7], "instruct": 1, "support": [1, 4, 5, 6], "report": [1, 5, 6], "last": [1, 7], "summari": 1, "text": [1, 5], "data": [1, 4, 6, 7], "popul": [1, 6], "effect": 1, "messag": 1, "getfield": [1, 3, 5, 7], "chang": [1, 5, 6], "reflect": [1, 6], "until": [1, 3], "rerun": [1, 4], "unnam": 1, "singl": [1, 2, 4, 5], "mode": 1, "did": [1, 6], "due": 1, "logic": 1, "statement": 1, "pleas": [1, 2], "valid": [1, 5], "after": [1, 4, 5, 6, 7], "contain": [1, 4, 5, 6, 7], "runscript": [1, 5], "coordin": [1, 6], "earthmj2000eq": [1, 4, 6, 7], "gregorian": 1, "modifi": [1, 3, 4, 5], "julian": [1, 6], "utc": 1, "epoch": [1, 4, 6, 7], "02": [1, 5], "jan": 1, "2000": [1, 6], "18": 1, "32": 1, "14": 1, "796": 1, "21546": 1, "2723934740": 1, "tai": 1, "46": 1, "2727638444": 1, "tt": 1, "33": [1, 6], "980": 1, "2731363444": 1, "tdb": 1, "2731363440": 1, "cartesian": [1, 6], "keplerian": [1, 4, 6, 7], "x": [1, 7], "7059": 1, "8054404060": 1, "km": 1, "sma": [1, 3, 4, 5, 6, 7], "000291122": 1, "y": [1, 7], "41006": 1, "405025033": 1, "0000007749653": 1, "z": [1, 7], "6820": 1, "5046046645": 1, "inc": [1, 4, 5, 6, 7], "852951589765": 1, "deg": 1, "vx": [1, 7], "9804993875227": 1, "sec": [1, 6], "raan": [1, 4, 6, 7], "306": 1, "15458093975": 1, "vy": [1, 7], "5912233799383": 1, "aop": [1, 4, 6, 7], "180": 1, "88186603791": 1, "vz": [1, 7], "4695068593848": 1, "ta": [1, 4, 6, 7], "312": 1, "46888463718": 1, "ma": 1, "46895014326": 1, "ea": 1, "46891739023": 1, "spheric": 1, "other": [1, 5, 6], "42164": 1, "978228331": 1, "motion": [1, 4], "291900370e": 1, "05": 1, "ra": 1, "80": [1, 3], "231521342877": 1, "energi": 1, "7266742410520": 1, "dec": 1, "9": 1, "3089268671491": 1, "c3": 1, "4533484821041": 1, "vmag": 1, "0746314210029": 1, "semilatu": 1, "rectum": 1, "000291097": 1, "azi": 1, "98": [1, 6], "901788705887": 1, "angular": 1, "momentum": 1, "129641": 1, "76692671": 1, "vfpa": 1, "90": [1, 4, 6, 7], "000032753035": 1, "beta": 1, "angl": 1, "17": [1, 5], "350375964570": 1, "rav": 1, "168": 1, "78023389434": 1, "altitud": 1, "35786": 1, "831314710": 1, "decv": 1, "7836287310829": 1, "velperiapsi": 1, "0746321949380": 1, "velapoapsi": 1, "0746274294752": 1, "period": 1, "86166": 1, "636794673": 1, "planetodet": 1, "properti": 1, "lst": 1, "231355220914": 1, "mha": 1, "19": [1, 5], "777879001376": 1, "latitud": 1, "3164365259040": 1, "longitud": 1, "60": [1, 3, 4, 7], "453476219538": 1, "35787": 1, "400879520": 1, "cd": [1, 4, 7], "200000": 1, "drag": [1, 4, 7], "area": 1, "15": [1, 4, 7], "00000": 1, "m": 1, "cr": [1, 4, 7], "800000": 1, "srp": 1, "000000": 1, "dry": [1, 3], "mass": [1, 3, 4, 7], "850": 1, "00000000000": 1, "kg": [1, 3], "total": 1, "spaddragscalefactor": 1, "spadsrpscalefactor": 1, "There": 1, "few": [1, 3, 4], "addit": 1, "worth": 1, "mention": 1, "also": [1, 3, 4, 5, 6, 7], "select": 1, "you": [1, 4], "missionsummari": 1, "overview": [1, 7], "begin": [1, 6], "associ": [1, 6], "gather": [2, 6], "togeth": [2, 3, 4, 5, 6, 7], "gmat": [2, 3, 5, 7], "These": [2, 5, 6], "file": [2, 5], "sunbsequ": 2, "just": [2, 6], "py": 2, "usr": [2, 5], "bin": [2, 5, 7], "python3": [2, 5, 7], "getmissionsequ": [2, 5], "findsolvercommand": [2, 5], "number": [2, 5, 7], "branch": 2, "counter": 2, "allow": [2, 3, 5], "solverbranchcommand": 2, "second": [2, 4, 6], "etc": [2, 3, 4], "found": [2, 3, 4, 5, 6], "retnod": 2, "break": 2, "findcommand": 2, "findcommandbynam": [2, 5], "findchild": [2, 5], "findchildbynam": [2, 5], "thruster": 3, "resourc": [3, 4, 6], "over": [3, 4, 5, 7], "durat": [3, 4], "deactiv": 3, "below": [3, 4, 5], "without": 3, "sc": 3, "8640": 3, "turn": 3, "event": 3, "endfiniteburn": 3, "fb": 3, "beginfiniteburn": 3, "elapseddai": 3, "abl": [3, 4, 5], "being": 3, "make": [3, 6], "correct": [3, 6], "desir": [3, 4, 5, 6], "To": 3, "capabl": [3, 5], "tank": 3, "finiteburn": 3, "finitethrust": 3, "guid": [3, 4, 6], "chemicaltank": 3, "chemicalthrust": 3, "electr": 3, "hardwar": 3, "attach": [3, 4], "thrust": 3, "forc": [3, 4, 7], "ad": [3, 5], "forcemodel": [3, 4, 7], "With": 3, "everyth": 3, "isfir": 3, "boolean": [3, 5], "correspond": 3, "flag": [3, 5, 6], "cover": [3, 6], "instanti": 3, "most": [3, 5], "default": [3, 6], "reduc": 3, "acceler": 3, "dure": [3, 5, 7], "setup": [3, 4, 7], "basic": [3, 4, 6], "sat": [3, 4, 5, 6, 7], "construct": [3, 4, 6, 7], "setfield": [3, 4, 5, 6, 7], "drymass": [3, 4, 7], "fm": [3, 4, 7], "epm": 3, "pointmassforc": [3, 4, 7], "addforc": [3, 4, 7], "gator": [3, 4, 7], "princedormand78": [3, 4, 7], "setrefer": [3, 4, 7], "model": [3, 4, 7], "fuel": [3, 5], "meanwhil": 3, "final": [3, 4, 5], "where": 3, "posit": [3, 4, 6, 7], "respect": [3, 6], "fuelmass": 3, "decrementmass": 3, "ensur": 3, "date": 3, "initi": [3, 4, 5, 6, 7], "build": [3, 4, 5, 7], "theburn": 3, "setsolarsystem": 3, "getsolarsystem": 3, "setspacecrafttomaneuv": 3, "setthrust": 3, "b": 3, "bf": 3, "setrefobjectnam": 3, "configmanag": 3, "addphysicalmodel": 3, "burnforc": 3, "wire": 3, "our": 3, "grab": 3, "own": 3, "decreas": 3, "addpropobject": [3, 4, 7], "prepareintern": [3, 4, 7], "clone": 3, "onto": [3, 7], "thethrust": 3, "getrefobject": 3, "totalmass": 3, "n": [3, 4, 5, 6, 7], "switch": 3, "between": [3, 5, 7], "non": 3, "fire": [3, 4], "whether": [3, 6], "what": 3, "affect": 3, "add": [3, 4, 7], "take": [3, 4], "elaps": 3, "dt": 3, "getpropag": [3, 4, 7], "minut": [3, 7], "getstat": [3, 4, 6, 7], "r": [3, 4, 5, 7], "math": 3, "sqrt": 3, "vsq": 3, "mu": 3, "i": [3, 4, 5, 7], "rang": [3, 4, 5, 7], "updatespaceobject": [3, 7], "30": 3, "ismaneuv": 3, "setpropitem": 3, "massflow": 3, "takeact": 3, "setdata": 3, "psm": [3, 7], "getpropstatemanag": [3, 7], "setproperti": [3, 7], "thruogh": 3, "undo": 3, "getodemodel": 3, "deleteforc": 3, "element": [3, 6, 7], "complex": 3, "By": 3, "stagger": 3, "could": 3, "differ": [3, 6], "run": [3, 4, 5, 6], "within": 4, "numer": [4, 5, 7], "integr": [4, 7], "ephemeri": 4, "e": 4, "kei": 4, "analyt": 4, "its": [4, 5, 6, 7], "configur": [4, 6, 7], "ll": [4, 6, 7], "some": [4, 5, 7], "leosat": [4, 7], "dateformat": [4, 7], "utcgregorian": [4, 7], "mar": [4, 7], "2020": [4, 7], "00": [4, 6, 7], "000": [4, 6, 7], "coordinatesystem": [4, 6, 7], "displaystatetyp": [4, 7], "7005": [4, 7], "008": [4, 7], "28": [4, 7], "75": [4, 6, 7], "45": [4, 6, 7], "50": [4, 6, 7], "dragarea": [4, 7], "srparea": [4, 7], "8x8": [4, 7], "potenti": 4, "sun": [4, 6, 7], "moon": 4, "jacchia": [4, 7], "robert": [4, 7], "odemodel": [4, 7], "languag": [4, 6], "accept": 4, "built": [4, 6, 7], "content": 4, "theforc": [4, 7], "help": 4, "largest": [4, 5], "graviti": [4, 7], "jgm": [4, 7], "earthgrav": [4, 7], "gravityfield": [4, 7], "bodynam": [4, 7], "degre": [4, 7], "potentialfil": [4, 7], "jgm2": [4, 7], "cof": [4, 7], "moongrav": [4, 7], "luna": [4, 5, 7], "sungrav": [4, 7], "jrdrag": [4, 7], "dragforc": [4, 7], "atmospheremodel": [4, 7], "jacchiarobert": [4, 7], "atmospher": [4, 7], "atmo": [4, 7], "getgeneratingstr": 4, "princ": 4, "dormand": 4, "rung": 4, "kutta": 4, "pdprop": [4, 7], "initialsteps": [4, 7], "accuraci": [4, 7], "0e": [4, 7], "minstep": [4, 7], "know": 4, "top": [4, 7], "level": [4, 5, 7], "interconnect": 4, "structur": [4, 6], "subsystem": [4, 7], "refresh": 4, "accumul": 4, "buffer": [4, 6, 7], "po": [4, 7], "vel": [4, 7], "gatorst": [4, 7], "t": [4, 7], "j": [4, 7], "append": [4, 5, 7], "result": [4, 5, 7], "360": 4, "matplotlib": [4, 7], "pyplot": [4, 7], "plt": [4, 7], "rcparam": [4, 7], "figur": [4, 7], "figsiz": [4, 7], "plot": [4, 7], "showcas": 4, "span": 4, "should": [4, 5, 6], "adjust": [4, 5], "size": 4, "extens": 4, "quickli": 4, "new": [4, 6, 7], "minimum": 5, "lunar": 5, "insert": 5, "relat": 5, "describ": 5, "defin": [5, 6, 7], "packag": 5, "tool": 5, "paramet": 5, "analysi": 5, "16": 5, "sometim": 5, "condit": 5, "properli": [5, 6], "lead": 5, "inabl": 5, "converg": 5, "underli": 5, "view": [5, 7], "compon": [5, 7], "additivescalefactor": 5, "multiplicativescalefactor": 5, "right": 5, "side": 5, "equal": 5, "recent": 5, "evalu": 5, "valu": [5, 7], "tabl": 5, "solvernam": 5, "initialvalu": 5, "w": 5, "comput": 5, "jacobian": 5, "maximum": 5, "magnitud": 5, "factor": 5, "scale": 5, "currentvalu": 5, "prior": 5, "prepar": 5, "numref": 5, "varyexampl": 5, "cf": 5, "solvercmd": 5, "1st": 5, "vary1": 5, "val": 5, "0005": 5, "stone": 5, "driven": 5, "open": 5, "close": 5, "least": 5, "goe": 5, "timelin": 5, "captur": 5, "succeed": 5, "targeterconverg": 5, "along": [5, 6, 7], "goalvalu": 5, "precis": 5, "rel": 5, "achievedvalu": 5, "getnumb": 5, "teh": 5, "21": 5, "illustr": 5, "achieve2": 5, "1e": 5, "write": 5, "endoptim": 5, "22": 5, "opt": 5, "ani": 5, "constraint": 5, "optimizernam": 5, "optimizerconverg": 5, "linear": 5, "nonlinear": 5, "inequ": 5, "similar": 5, "mmsref": 5, "2300": 5, "23": 5, "arg1": 5, "oper": [5, 6], "arg2": 5, "left": 5, "argument": 5, "rhsvalu": 5, "cautiou": 5, "constraintarg1": 5, "hand": 5, "lhsvalu": 5, "cannot": [5, 6], "constraintarg2": 5, "exemplifi": 5, "24": 5, "definit": 5, "mycostfunct": 5, "read": 5, "objectivenam": 5, "cost": 5, "25": 5, "simplifi": 5, "nlc": 5, "2100": 5, "mincommand": 5, "nobject": 5, "usag": 5, "trajectori": 5, "local": 5, "log": 5, "uselogfil": 5, "varexamplelog": 5, "txt": 5, "echologfil": 5, "2nd": 5, "vary2": 5, "max": 5, "enter": [5, 7], "npress": 5, "continu": 5, "ex_minfuellunartransf": 5, "showprogresswindow": 5, "optnam": 5, "getobject": 5, "index": 5, "len": [5, 7], "65": 5, "85": 5, "01": 5, "005": 5, "nconstraint": 5, "reset": [5, 6], "nta": 5, "da": 5, "variou": 6, "understand": 6, "said": 6, "bit": 6, "confus": 6, "intern": [6, 7], "convert": 6, "get": 6, "As": 6, "care": 6, "must": 6, "insid": 6, "mysat": 6, "istat": 6, "member": 6, "referenc": 6, "center": 6, "j2000": 6, "equatori": 6, "represent": 6, "gmatstat": 6, "which": 6, "seen": 6, "prompt": 6, "vector": [6, 7], "space": 6, "getepoch": [6, 7], "a1modjulian": 6, "getsiz": 6, "januari": 6, "taimodjulian": 6, "21545": 6, "00000039794": 6, "modjulian": 6, "fill": 6, "dummi": 6, "entri": 6, "999": 6, "graphic": [6, 7], "interfac": 6, "interact": 6, "similarli": 6, "statetyp": 6, "7015": 6, "0011": 6, "333": 6, "At": 6, "appear": 6, "realli": 6, "interpret": 6, "inform": [6, 7], "uniniti": 6, "origin": 6, "nor": 6, "orient": 6, "ax": 6, "addition": 6, "gravit": 6, "constant": 6, "hold": 6, "yet": 6, "fulli": 6, "post": 6, "queri": 6, "preiniti": 6, "getkeplerianst": 6, "onc": 6, "interobject": 6, "necessari": 6, "8000": 6, "getcartesianst": 6, "coupl": 6, "fix": 6, "ecf": 6, "solar": 6, "eclipt": 6, "solareclipt": 6, "bodyfix": 6, "mj2000ec": 6, "anoth": 6, "rather": 6, "scratch": 6, "framework": 6, "solsat": 6, "solarsat": 6, "ve": 6, "sinc": 6, "reassign": 6, "isiniti": 6, "bug": 6, "toggl": 6, "replac": 6, "again": 6, "And": 6, "foundat": 6, "throughout": 6, "advanc": 7, "flow": 7, "transit": 7, "matrix": 7, "matric": 7, "abbrevi": 7, "version": 7, "ident": 7, "6x6": 7, "larg": 7, "nonzero": 7, "diagon": 7, "unpropag": 7, "met": 7, "t1": 7, "phi": 7, "t0": 7, "reason": 7, "proceed": 7, "estim": 7, "intervent": 7, "latter": 7, "approach": 7, "setrealparamet": 7, "000001": 7, "0000015": 7, "5e": 7, "11": 7, "assembl": 7, "particip": 7, "gloss": 7, "synchron": 7, "item": 7, "success": 7, "sync": 7, "accomplish": 7, "push": 7, "runtim": 7, "particular": 7, "feed": 7, "getrmatrixparamet": 7, "nstate": 7, "cstr": 7, "str": 7, "getel": 7, "cov": 7, "getcovari": 7, "ncovari": 7, "cookbook": 7, "configurecoreobject": 7, "earlier": 7, "exercis": 7, "stepaminut": 7, "updatedata": 7, "els": 7, "showstatestmcov": 7, "preliminari": 7, "save": 7, "f1": 7, "f2": 7, "press": 7}, "objects": {}, "objtypes": {}, "objnames": {}, "titleterms": {"gmat": [0, 1, 4, 6], "api": [0, 3, 4, 6], "cookbook": 0, "content": 0, "access": [1, 5], "command": [1, 2, 5], "problem": [1, 3, 4, 5, 6, 7], "solut": [1, 3, 4, 5, 6, 7], "setup": 1, "some": 1, "comment": 1, "top": 1, "level": 1, "branch": 1, "view": 1, "run": 1, "result": 1, "discuss": [1, 3, 4, 5, 6, 7], "A": [2, 7], "collect": 2, "function": 2, "The": 2, "commandfunct": 2, "code": 2, "execut": 3, "finit": 3, "burn": 3, "exampl": [3, 4, 7], "propag": [4, 7], "control": 5, "script": 5, "solver": 5, "vari": 5, "field": 5, "us": 5, "case": 5, "1": 5, "target": 5, "achiev": 5, "2": 5, "optim": 5, "nonlinearconstraint": 5, "minim": 5, "complet": [5, 7], "state": 6, "manag": 6, "stm": 7, "covari": 7, "appendix": 7}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 6, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.todo": 2, "sphinx": 56}})