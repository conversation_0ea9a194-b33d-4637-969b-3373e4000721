/*
 * classic.css_t
 * ~~~~~~~~~~~~~
 *
 * Sphinx stylesheet -- classic theme.
 *
 * :copyright: Copyright 2007-2022 by the Sphinx team, see AUTHORS.
 * :license: BSD, see LICENSE for details.
 *
 */

@import url("basic.css");

/* -- page layout ----------------------------------------------------------- */

html {
    /* CSS hack for macOS's scrollbar (see #1125) */
    background-color: #FFFFFF;
}

body {
    font-family: sans-serif;
    font-size: 100%;
    background-color: #11303d;
    color: #000;
    margin: 0;
    padding: 0;
}

div.document {
    display: flex;
    background-color: #1c4e63;
}

div.documentwrapper {
    float: left;
    width: 100%;
}

div.bodywrapper {
    margin: 0 0 0 230px;
}

div.body {
    background-color: #ffffff;
    color: #000000;
    padding: 0 20px 30px 20px;
}

div.footer {
    color: #ffffff;
    width: 100%;
    padding: 9px 0 9px 0;
    text-align: center;
    font-size: 75%;
}

div.footer a {
    color: #ffffff;
    text-decoration: underline;
}

div.related {
    background-color: #133f52;
    line-height: 30px;
    color: #ffffff;
}

div.related a {
    color: #ffffff;
}

div.sphinxsidebar {
}

div.sphinxsidebar h3 {
    font-family: 'Trebuchet MS', sans-serif;
    color: #ffffff;
    font-size: 1.4em;
    font-weight: normal;
    margin: 0;
    padding: 0;
}

div.sphinxsidebar h3 a {
    color: #ffffff;
}

div.sphinxsidebar h4 {
    font-family: 'Trebuchet MS', sans-serif;
    color: #ffffff;
    font-size: 1.3em;
    font-weight: normal;
    margin: 5px 0 0 0;
    padding: 0;
}

div.sphinxsidebar p {
    color: #ffffff;
}

div.sphinxsidebar p.topless {
    margin: 5px 10px 10px 10px;
}

div.sphinxsidebar ul {
    margin: 10px;
    padding: 0;
    color: #ffffff;
}

div.sphinxsidebar a {
    color: #98dbcc;
}

div.sphinxsidebar input {
    border: 1px solid #98dbcc;
    font-family: sans-serif;
    font-size: 1em;
}



/* -- hyperlink styles ------------------------------------------------------ */

a {
    color: #355f7c;
    text-decoration: none;
}

a:visited {
    color: #355f7c;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}



/* -- body styles ----------------------------------------------------------- */

div.body h1,
div.body h2,
div.body h3,
div.body h4,
div.body h5,
div.body h6 {
    font-family: 'Trebuchet MS', sans-serif;
    background-color: #f2f2f2;
    font-weight: normal;
    color: #20435c;
    border-bottom: 1px solid #ccc;
    margin: 20px -20px 10px -20px;
    padding: 3px 0 3px 10px;
}

div.body h1 { margin-top: 0; font-size: 200%; }
div.body h2 { font-size: 160%; }
div.body h3 { font-size: 140%; }
div.body h4 { font-size: 120%; }
div.body h5 { font-size: 110%; }
div.body h6 { font-size: 100%; }

a.headerlink {
    color: #c60f0f;
    font-size: 0.8em;
    padding: 0 4px 0 4px;
    text-decoration: none;
}

a.headerlink:hover {
    background-color: #c60f0f;
    color: white;
}

div.body p, div.body dd, div.body li, div.body blockquote {
    text-align: justify;
    line-height: 130%;
}

div.admonition p.admonition-title + p {
    display: inline;
}

div.admonition p {
    margin-bottom: 5px;
}

div.admonition pre {
    margin-bottom: 5px;
}

div.admonition ul, div.admonition ol {
    margin-bottom: 5px;
}

div.note {
    background-color: #eee;
    border: 1px solid #ccc;
}

div.seealso {
    background-color: #ffc;
    border: 1px solid #ff6;
}
nav.contents,
aside.topic,

div.topic {
    background-color: #eee;
}

div.warning {
    background-color: #ffe4e4;
    border: 1px solid #f66;
}

p.admonition-title {
    display: inline;
}

p.admonition-title:after {
    content: ":";
}

pre {
    padding: 5px;
    background-color: unset;
    color: unset;
    line-height: 120%;
    border: 1px solid #ac9;
    border-left: none;
    border-right: none;
}

code {
    background-color: #ecf0f3;
    padding: 0 1px 0 1px;
    font-size: 0.95em;
}

th, dl.field-list > dt {
    background-color: #ede;
}

.warning code {
    background: #efc2c2;
}

.note code {
    background: #d6d6d6;
}

.viewcode-back {
    font-family: sans-serif;
}

div.viewcode-block:target {
    background-color: #f4debf;
    border-top: 1px solid #ac9;
    border-bottom: 1px solid #ac9;
}

div.code-block-caption {
    color: #efefef;
    background-color: #1c4e63;
}