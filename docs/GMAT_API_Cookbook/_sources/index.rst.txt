.. API Cookbook documentation master file, created by
   sphinx-quickstart on Wed Dec  6 11:34:59 2023.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

GMAT API Cookbook
=================

.. note::

   This project is under active development.

The GMAT API Cookbook is a collection of use case examples that demonstrate common activities performed by GMAT API users.  This document is a "living document:" it is intended to act as a central location for API examples that previously were only available through request of knowledgeable API users.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   source/propagation
   source/statemanagement
   source/stmpropagation
   source/finiteBurn
   source/commandaccess
   source/commandfunctions
   source/solvercontrol
