<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>LibrationPoint</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="IntrusionLocator.html" title="IntrusionLocator"><link rel="next" href="NuclearPowerSystem.html" title="NuclearPowerSystem"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">LibrationPoint</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="IntrusionLocator.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="NuclearPowerSystem.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="LibrationPoint"></a><div class="titlepage"></div><a name="N1BCA9" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">LibrationPoint</span></h2><p>LibrationPoint &mdash; An equilibrium point in the circular, restricted 3-body
    problem</p></div><div class="refsection"><a name="N1BCBA"></a><h2>Description</h2><p>A <span class="guilabel">LibrationPoint</span>, also called a Lagrange point,
    is an equilibrium point in the circular restricted three-body problem
    (CRTBP). There are five libration points, three of which are unstable in
    the CRTBP sense, and two that are stable. See the discussion below for a
    detailed explanation of the different libration points and for examples
    configuring GMAT for common libration point regimes. This resource cannot
    be modified in the Mission Sequence.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Barycenter.html" title="Barycenter"><span class="refentrytitle">Barycenter</span></a>, <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a></p></div><div class="refsection"><a name="LibrationPoint_Resource_Fields"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">OrbitColor</span></td><td><p>Allows you to set available colors on user-defined
            <span class="guilabel">LibrationPoint</span> orbits. The libration point
            orbits are drawn using the 3D <span class="guilabel">OrbitView</span>
            graphics displays. Colors on a <span class="guilabel">LibrationPoint</span>
            object can be set through a string or an integer array. For
            example: Setting a libration point's orbit color to red can be
            done in the following two ways: <code class="literal">LibrationPoint.OrbitColor
            = Red</code> or <code class="literal">LibrationPoint.OrbitColor = [255 0
            0]</code>. This field can be modified in the Mission Sequence
            as well..</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer Array or String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any color available from the Orbit Color Picker in
                    GUI. Valid predefined color name or RGB triplet value
                    between 0 and 255.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>GreenYellow</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Point </span></td><td><p>The libration point index.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">L1, L2, L3, L4</span>, or
                    <span class="guilabel">L5</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">L1</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Primary </span></td><td><p>The primary body or barycenter.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CelestialBody</span> or
                    <span class="guilabel">Barycenter</span>.
                    <span class="guilabel">Primary</span> cannot be
                    <span class="guilabel">SolarSystemBarycenter</span> and
                    <span class="guilabel">Primary</span> cannot be the same as
                    <span class="guilabel">Secondary</span>.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Sun</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Secondary </span></td><td><p>The secondary body or barycenter.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Secondary</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CelestialBody</span> or
                    <span class="guilabel">Barycenter</span>.
                    <span class="guilabel">Secondary</span> cannot be
                    <span class="guilabel">SolarSystemBarycenter</span> and
                    <span class="guilabel">Primary</span> cannot be the same as
                    <span class="guilabel">Secondary</span>.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Earth</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TargetColor</span></td><td><p>Allows you to set available colors on
            <span class="guilabel">LibrationPoint</span> object's perturbing orbital
            trajectories that are drawn during iterative processes such as
            Differential Correction or Optimization. The target color can be
            identified through a string or an integer array. For example:
            Setting a libration point's perturbing trajectory color to yellow
            can be done in following two ways:
            <code class="literal">LibrationPoint.TargetColor = Yellow</code> or
            <code class="literal">LibrationPoint.TargetColor = [255 255 0]</code>. This
            field can be modified in the Mission Sequence as well.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer Array or String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any color available from the Orbit Color Picker in
                    GUI. Valid predefined color name or RGB triplet value
                    between 0 and 255.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>DarkGray</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1BE01"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Libration_Default.png" align="middle" height="282"></td></tr></table></div></div><p>The <span class="guibutton">LibrationPoint</span> dialog box allows you to
    select the <span class="guilabel">Primary Body</span>, <span class="guilabel">Secondary
    Body</span>, and the libration point index. You can select from
    celestial bodies and barycenters. You cannot choose the
    <span class="guilabel">SolarSystemBarycenter</span> as either the
    <span class="guilabel">Primary</span> or <span class="guilabel">Secondary</span> and the
    <span class="guilabel">Primary</span> and <span class="guilabel">Secondary</span> cannot be
    the same object.</p></div><div class="refsection"><a name="N1BE27"></a><h2>Remarks</h2><p><span class="emphasis"><em>Overview of Libration Point Geometry</em></span></p><p>A <span class="guilabel">LibrationPoint</span>, also called a Lagrange point,
    is an equilibrium point in the Circular Restricted Three Body Problem
    (CRTBP). The definitions for the libration points used in GMAT are
    illustrated in the figure below where the <span class="guilabel">Primary</span> and
    <span class="guilabel">Secondary</span> bodies are shown in a rotating frame
    defined with the x-axis pointing from the <span class="guilabel">Primary</span> to
    the <span class="guilabel">Secondary</span>. GMAT is configured for the full
    ephemeris problem and computes the location of the libration points by
    assuming that at a given instant in time, the CRTBP theory developed by
    Lagrange and Szebehely can be used to compute the location of the
    libration points using the locations of the primary and secondary from the
    JPL ephemerides. The three collinear points (L1, L2, and L3) are unstable
    (even in the CRTBP) and the triangular points (L4, and L5) are stable in
    CRTBP.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/LibrationPoint_Geometry.png" align="middle" height="279"></td></tr></table></div></div><p><span class="emphasis"><em>Configuring a Libration Point</em></span></p><p>GMAT allows you to define the <span class="guilabel">Primary</span> and/or
    <span class="guilabel">Secondary</span> as a <span class="guibutton">CelestialBody</span>
    or <span class="guilabel">Barycenter</span> (except
    <span class="guilabel">SolarSystemBarycenter</span>). This allows you to set the
    <span class="guilabel">Primary</span> as the Sun, and the
    <span class="guilabel">Secondary</span> as the Earth-Moon barycenter for modeling
    Sun-Earth-Moon libration points. See the examples below for
    details.</p><div class="refsection"><a name="N1BE61"></a><h3>Setting Colors On Libration Point Orbits</h3><p>GMAT allows you to assign colors to libration point orbits that
      are drawn using the <span class="guilabel">OrbitView</span> graphics display
      windows. GMAT also allows you to assign colors to perturbing libration
      point orbital trajectories which are drawn during iterative processes
      such as differential correction or optimization. The
      <span class="guilabel">LibrationPoint</span> object's
      <span class="guilabel">OrbitColor</span> and <span class="guilabel">TargetColor</span>
      fields are used to assign colors to both orbital and perturbing
      trajectories. See the <a class="xref" href="LibrationPoint.html#LibrationPoint_Resource_Fields" title="Fields">Fields</a> section to learn more about these two fields.
      Also see <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a> documentation for discussion
      and examples on how to set colors on a libration point orbit.</p></div></div><div class="refsection"><a name="N1BE79"></a><h2>Examples</h2><div class="informalexample"><p>Create and use an Earth-Moon
      <span class="guilabel">LibrationPoint.</span></p><pre class="programlisting"><code class="code">%  Create the libration point and rotating libration point coordinate system
Create LibrationPoint EarthMoonL2
EarthMoonL2.Primary   = Earth
EarthMoonL2.Secondary = Luna
EarthMoonL2.Point     = L2

Create CoordinateSystem EarthMoonRotLibCoord
EarthMoonRotLibCoord.Origin    = EarthMoonL2
EarthMoonRotLibCoord.Axes      = ObjectReferenced
EarthMoonRotLibCoord.XAxis     = R
EarthMoonRotLibCoord.ZAxis     = N
EarthMoonRotLibCoord.Primary   = Earth
EarthMoonRotLibCoord.Secondary = Luna

%  Configure the spacecraft and propagator
Create Spacecraft aSat
aSat.DateFormat       = TAIModJulian
aSat.Epoch            = '25220.0006220895'
aSat.CoordinateSystem = EarthMoonRotLibCoord
aSat.DisplayStateType = Cartesian
aSat.X  = 9999.752137149568
aSat.Y  = 1.774296833900735e-007
aSat.Z  = 21000.02640446094
aSat.VX = -1.497748388797418e-005
aSat.VY = -0.2087816321971509
aSat.VZ = -5.42471673237177e-006

Create ForceModel EarthMoonL2Prop_ForceModel
EarthMoonL2Prop_ForceModel.PointMasses = {Earth, Luna, Sun}
Create Propagator EarthMoonL2Prop
EarthMoonL2Prop.FM = EarthMoonL2Prop_ForceModel

%  Create the orbit view
Create OrbitView ViewEarthMoonRot
ViewEarthMoonRot.Add                = {Earth, Luna, Sun,...
                                            aSat, EarthMoonL2}
ViewEarthMoonRot.CoordinateSystem   = EarthMoonRotLibCoord
ViewEarthMoonRot.ViewPointReference = EarthMoonL2
ViewEarthMoonRot.ViewDirection      = EarthMoonL2
ViewEarthMoonRot.ViewScaleFactor    = 5

Create Variable I

BeginMissionSequence

% Prop for 3 xz-plane crossings
For I = 1:3
  Propagate 'Prop to Y Crossing' EarthMoonL2Prop(aSat) ...
                      {aSat.EarthMoonRotLibCoord.Y = 0}
EndFor</code></pre></div><div class="informalexample"><p>Create and use a Sun, Earth-Moon
      <span class="guilabel">LibrationPoint</span>.</p><pre class="programlisting"><code class="code">%  Create the Earth-Moon Barycenter and Libration Point
Create Barycenter EarthMoonBary
EarthMoonBary.BodyNames = {Earth,Luna}

Create LibrationPoint SunEarthMoonL1
SunEarthMoonL1.Primary   = Sun
SunEarthMoonL1.Secondary = EarthMoonBary
SunEarthMoonL1.Point     = L1

%  Create the coordinate system
Create CoordinateSystem RotatingSEML1Coord
RotatingSEML1Coord.Origin    = SunEarthMoonL1
RotatingSEML1Coord.Axes      = ObjectReferenced
RotatingSEML1Coord.XAxis     = R
RotatingSEML1Coord.ZAxis     = N
RotatingSEML1Coord.Primary   = Sun
RotatingSEML1Coord.Secondary = EarthMoonBary

%  Create the spacecraft and propagator
Create Spacecraft aSpacecraft
aSpacecraft.DateFormat       = UTCGregorian
aSpacecraft.Epoch            = '09 Dec 2005 13:00:00.000'
aSpacecraft.CoordinateSystem = RotatingSEML1Coord
aSpacecraft.X  = -32197.88223741966
aSpacecraft.Y  = 211529.1500044117
aSpacecraft.Z  = 44708.57017366499
aSpacecraft.VX = 0.03209516489451751
aSpacecraft.VY = 0.06100386504053736
aSpacecraft.VZ = 0.0550442738917212

Create Propagator aPropagator
aPropagator.FM           = aForceModel
aPropagator.MaxStep = 86400
Create ForceModel aForceModel
aForceModel.PointMasses = {Earth,Sun,Luna}

% Create a 3-D graphic
Create OrbitView anOrbitView
anOrbitView.Add                     = {aSpacecraft,  Earth, Sun, Luna}
anOrbitView.CoordinateSystem        = RotatingSEML1Coord
anOrbitView.ViewPointReference      = SunEarthMoonL1
anOrbitView.ViewPointVector         = [-1500000 0 0 ]
anOrbitView.ViewDirection           = SunEarthMoonL1
anOrbitView.ViewUpCoordinateSystem = RotatingSEML1Coord
anOrbitView.Axes                    = Off
anOrbitView.XYPlane                 = Off

BeginMissionSequence
           
Propagate aPropagator(aSpacecraft, {aSpacecraft.ElapsedDays = 180})</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="IntrusionLocator.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="NuclearPowerSystem.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">IntrusionLocator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;NuclearPowerSystem</td></tr></table></div></body></html>