<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;19.&nbsp;Input/Output</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="RefGuide.html" title="Reference Guide"><link rel="prev" href="Propagate.html" title="Propagate"><link rel="next" href="DynamicDataDisplay.html" title="DynamicDataDisplay"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;19.&nbsp;Input/Output</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Propagate.html">Prev</a>&nbsp;</td><th align="center" width="60%">Reference Guide</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="DynamicDataDisplay.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="N22D0F"></a>Chapter&nbsp;19.&nbsp;Input/Output</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ch19.html#N22D14">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DynamicDataDisplay.html">DynamicDataDisplay</a></span><span class="refpurpose"> &mdash; A user-defined resource used in tandem with the
    <span class="guilabel">UpdateDynamicData</span> command to print current values of
    parameters to a table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="EphemerisFile.html">EphemerisFile</a></span><span class="refpurpose"> &mdash; Generate spacecraft&rsquo;s ephemeris data</span></dt><dt><span class="refentrytitle"><a href="FileInterface.html">FileInterface</a></span><span class="refpurpose"> &mdash; An interface to a data file</span></dt><dt><span class="refentrytitle"><a href="GroundTrackPlot.html">GroundTrackPlot</a></span><span class="refpurpose"> &mdash; A user-defined resource that draws longitude and latitude
    time-history of a spacecraft</span></dt><dt><span class="refentrytitle"><a href="OpenFramesInterface.html">OpenFramesInterface</a></span><span class="refpurpose"> &mdash; A user-defined resource that provides high-performance 3D interactive visualizations of GMAT missions</span></dt><dt><span class="refentrytitle"><a href="OrbitView.html">OrbitView</a></span><span class="refpurpose"> &mdash; A user-defined resource that plots 3-Dimensional
    trajectories</span></dt><dt><span class="refentrytitle"><a href="ReportFile.html">ReportFile</a></span><span class="refpurpose"> &mdash; Report data to a text file</span></dt><dt><span class="refentrytitle"><a href="XYPlot.html">XYPlot</a></span><span class="refpurpose"> &mdash; Plots data onto the X and Y axes of a graph</span></dt></dl></dd><dt><span class="section"><a href="ch19s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="ClearPlot.html">ClearPlot</a></span><span class="refpurpose"> &mdash; Allows you to clear all data from an XYPlot</span></dt><dt><span class="refentrytitle"><a href="GetEphemStates_Function.html">GetEphemStates()</a></span><span class="refpurpose"> &mdash; Function used to output initial and final spacecraft states
    from an ephemeris file</span></dt><dt><span class="refentrytitle"><a href="MarkPoint.html">MarkPoint</a></span><span class="refpurpose"> &mdash; Allows you to add a special mark point character on an
    XYPlot</span></dt><dt><span class="refentrytitle"><a href="PenUpPenDown.html">PenUpPenDown</a></span><span class="refpurpose"> &mdash; Allows you to stop or begin drawing data on a
    plot</span></dt><dt><span class="refentrytitle"><a href="Report.html">Report</a></span><span class="refpurpose"> &mdash; Allows you to write data to a text file</span></dt><dt><span class="refentrytitle"><a href="Set.html">Set</a></span><span class="refpurpose"> &mdash; Configure a resource from a data interface</span></dt><dt><span class="refentrytitle"><a href="Toggle.html">Toggle</a></span><span class="refpurpose"> &mdash; Allows you to turn data output off or on</span></dt><dt><span class="refentrytitle"><a href="UpdateDynamicData.html">UpdateDynamicData</a></span><span class="refpurpose"> &mdash; A command used in tandem with a
	<span class="guilabel">DynamicDataDisplay</span> to update the data being shown in
	the display table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="Write.html">Write</a></span><span class="refpurpose"> &mdash; Writes data to one or more of the following three
    destinations: the message window, the log file, or a
    <span class="guilabel">ReportFile</span> resource.</span></dt></dl></dd><dt><span class="section"><a href="ch19s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Color.html">Color</a></span><span class="refpurpose"> &mdash; Color support in GMAT resources and commands</span></dt></dl></dd></dl></div><p> This chapter contains documentation for Resources and Commands related to data and system I/O. </p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N22D14"></a>Resources</h2></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Propagate.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="RefGuide.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="DynamicDataDisplay.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Propagate&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;DynamicDataDisplay</td></tr></table></div></body></html>