<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Index</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="prev" href="ReleaseNotesR2011a.html" title="GMAT R2011a Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Index</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ReleaseNotesR2011a.html">Prev</a>&nbsp;</td><th align="center" width="60%">&nbsp;</th><td align="right" width="20%">&nbsp;</td></tr></table><hr></div><div class="index"><div class="titlepage"><div><div><h1 class="title"><a name="BookIndex"></a>Index</h1></div></div></div><div class="index"><div class="indexdiv"><h3>Symbols</h3><dl><dt>#Include Macro, <a class="indexterm" href="IncludeMacro.html">#Include Macro</a></dt></dl></div><div class="indexdiv"><h3>A</h3><dl><dt>AcceptFilter, <a class="indexterm" href="AcceptFilter.html">AcceptFilter</a></dt><dt>Achieve, <a class="indexterm" href="Achieve.html">Achieve</a></dt><dt>AddHardware, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>Antenna, <a class="indexterm" href="Antenna.html">Antenna</a></dt><dt>Array, <a class="indexterm" href="Array.html">Array</a></dt><dt>Assignment, <a class="indexterm" href="Assignment.html">Assignment (=)</a></dt><dt>AtmosDensityScaleFactorSigma, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt></dl></div><div class="indexdiv"><h3>B</h3><dl><dt>Barycenter, <a class="indexterm" href="Barycenter.html">Barycenter</a></dt><dt>BatchEstimator, <a class="indexterm" href="BatchEstimator.html">BatchEstimator</a></dt><dt>BeginFileThrust, <a class="indexterm" href="BeginFileThrust.html">BeginFileThrust</a></dt><dt>BeginFiniteBurn, <a class="indexterm" href="BeginFiniteBurn.html">BeginFiniteBurn</a></dt><dt>BeginMissionSequence, <a class="indexterm" href="BeginMissionSequence.html">BeginMissionSequence</a></dt><dt>BeginScript, <a class="indexterm" href="BeginScript.html">BeginScript</a></dt><dt>Breakpoint, <a class="indexterm" href="Breakpoint.html">Breakpoint</a></dt></dl></div><div class="indexdiv"><h3>C</h3><dl><dt>CalculateIODGibbs, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>CalculateIODHerrickGibbs, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>Calculation Parameters, <a class="indexterm" href="CalculationParameters.html">Calculation Parameters</a></dt><dt>CallGmatFunction, <a class="indexterm" href="CallGmatFunction.html">CallGmatFunction</a></dt><dt>CallMatlabFunction, <a class="indexterm" href="CallMatlabFunction.html">CallMatlabFunction</a></dt><dt>CallPythonFunction, <a class="indexterm" href="CallPythonFunction.html">CallPythonFunction</a></dt><dt>CdSigma, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>CelestialBody, <a class="indexterm" href="CelestialBody.html">CelestialBody</a></dt><dt>ChemicalTank, <a class="indexterm" href="FuelTank.html">ChemicalTank</a></dt><dt>ChemicalThruster, <a class="indexterm" href="Thruster.html">ChemicalThruster</a></dt><dt>ClearPlot, <a class="indexterm" href="ClearPlot.html">ClearPlot</a></dt><dt>Code500 Ephemeris Orbit Propagation, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>Color, <a class="indexterm" href="Color.html">Color</a></dt><dt>CommandEcho, <a class="indexterm" href="CommandEcho.html">CommandEcho</a></dt><dt>Command-Line Usage, <a class="indexterm" href="CommandLine.html">Command-Line Usage</a></dt><dt>Command Summary, <a class="indexterm" href="CommandSummary.html">Command Summary</a></dt><dt>ContactLocator, <a class="indexterm" href="ContactLocator.html">ContactLocator</a></dt><dt>CoordinateSystem, <a class="indexterm" href="CoordinateSystem.html">CoordinateSystem</a></dt><dt>CrSigma, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt></dl></div><div class="indexdiv"><h3>D</h3><dl><dt>DifferentialCorrector, <a class="indexterm" href="DifferentialCorrector.html">DifferentialCorrector</a></dt><dt>Drag</dt><dd><dl><dt>Drag estimation in orbit determination, <a class="indexterm" href="EstimatedParameter.html">EstimatedParameter</a></dt></dl></dd><dt>DynamicDataDisplay, <a class="indexterm" href="DynamicDataDisplay.html">DynamicDataDisplay</a></dt></dl></div><div class="indexdiv"><h3>E</h3><dl><dt>EclipseLocator, <a class="indexterm" href="EclipseLocator.html">EclipseLocator</a></dt><dt>ElectricTank, <a class="indexterm" href="ElectricTank.html">ElectricTank</a></dt><dt>ElectricThruster, <a class="indexterm" href="ElectricThruster.html">ElectricThruster</a></dt><dt>Else, <a class="indexterm" href="If.html">If</a></dt><dt>EndFileThrust, <a class="indexterm" href="EndFileThrust.html">EndFileThrust</a></dt><dt>EndFiniteBurn, <a class="indexterm" href="EndFiniteBurn.html">EndFiniteBurn</a></dt><dt>EndFor, <a class="indexterm" href="For.html">For</a></dt><dt>EndIf, <a class="indexterm" href="If.html">If</a></dt><dt>EndScript, <a class="indexterm" href="BeginScript.html">BeginScript</a></dt><dt>EndTarget, <a class="indexterm" href="Target.html">Target</a></dt><dt>EndWhile, <a class="indexterm" href="While.html">While</a></dt><dt>EphemerisFile, <a class="indexterm" href="EphemerisFile.html">EphemerisFile</a></dt><dt>Ephemeris propagator</dt><dd><dl><dt>Code500 ephemeris, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>OEM ephemeris, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>SPICE ephemeris, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>STK ephemeris, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>TLE, <a class="indexterm" href="Propagator.html">Propagator</a></dt></dl></dd><dt>Equation, <a class="indexterm" href="Assignment.html">Assignment (=)</a></dt><dt>ErrorModel, <a class="indexterm" href="ErrorModel.html">ErrorModel</a></dt><dt>Estimated parameter, <a class="indexterm" href="EstimatedParameter.html">EstimatedParameter</a></dt><dt>ExtendedKalmanFilter, <a class="indexterm" href="ExtendedKalmanFilter.html">ExtendedKalmanFilter</a></dt><dd><dl><dt>Estimation parameter modeling, <a class="indexterm" href="EstimatedParameter.html">EstimatedParameter</a></dt></dl></dd></dl></div><div class="indexdiv"><h3>F</h3><dl><dt>FieldOfView, <a class="indexterm" href="FieldOfView.html">FieldOfView</a></dt><dt>FileInterface, <a class="indexterm" href="FileInterface.html">FileInterface</a></dt><dt>FindEvents, <a class="indexterm" href="FindEvents.html">FindEvents</a></dt><dt>FiniteBurn, <a class="indexterm" href="FiniteBurn.html">FiniteBurn</a></dt><dt>FminconOptimizer, <a class="indexterm" href="FminconOptimizer.html">FminconOptimizer</a></dt><dt>For, <a class="indexterm" href="For.html">For</a></dt><dt>Force Model, <a class="indexterm" href="ForceModel.html">ForceModel</a></dt><dt>Formation, <a class="indexterm" href="Formation.html">Formation</a></dt></dl></div><div class="indexdiv"><h3>G</h3><dl><dt>GetEphemStates(), <a class="indexterm" href="GetEphemStates_Function.html">GetEphemStates()</a></dt><dt>Global, <a class="indexterm" href="Global.html">Global</a></dt><dt>gmat_startup_file.txt, <a class="indexterm" href="StartupFile.html">Startup File</a></dt><dt>GMAT command, <a class="indexterm" href="CommandLine.html">Command-Line Usage</a></dt><dt>GroundStation, <a class="indexterm" href="GroundStation.html">GroundStation</a></dt><dt>GroundTrackPlot, <a class="indexterm" href="GroundTrackPlot.html">GroundTrackPlot</a></dt></dl></div><div class="indexdiv"><h3>I</h3><dl><dt>If, <a class="indexterm" href="If.html">If</a></dt><dt>Imager, <a class="indexterm" href="Imager.html">Imager</a></dt><dt>ImpulsiveBurn, <a class="indexterm" href="ImpulsiveBurn.html">ImpulsiveBurn</a></dt><dt>InitialOrbitDetermination, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>Installation, <a class="indexterm" href="GettingStarted.html#Installation">Installation</a></dt><dt>IntrusionLocator, <a class="indexterm" href="IntrusionLocator.html">IntrusionLocator</a></dt></dl></div><div class="indexdiv"><h3>K</h3><dl><dt>Keyboard shortcuts, <a class="indexterm" href="KeyboardShortcuts.html">Keyboard Shortcuts</a></dt></dl></div><div class="indexdiv"><h3>L</h3><dl><dt>LibrationPoint, <a class="indexterm" href="LibrationPoint.html">LibrationPoint</a></dt></dl></div><div class="indexdiv"><h3>M</h3><dl><dt>Maneuver, <a class="indexterm" href="Maneuver.html">Maneuver</a></dt><dt>MarkPoint, <a class="indexterm" href="MarkPoint.html">MarkPoint</a></dt><dt>MatlabFunction, <a class="indexterm" href="MatlabFunction.html">MatlabFunction</a></dt><dt>MATLAB Interface, <a class="indexterm" href="MatlabInterface.html">MATLAB Interface</a></dt><dt>Minimize, <a class="indexterm" href="Minimize.html">Minimize</a></dt><dt>Mission Tree, <a class="indexterm" href="MissionTree.html">Mission Tree</a></dt></dl></div><div class="indexdiv"><h3>N</h3><dl><dt>NonlinearConstraint, <a class="indexterm" href="NonlinearConstraint.html">NonlinearConstraint</a></dt><dt>NuclearPowerSystem, <a class="indexterm" href="NuclearPowerSystem.html">NuclearPowerSystem</a></dt><dt>Numerical Integrator, <a class="indexterm" href="Propagator.html">Propagator</a></dt></dl></div><div class="indexdiv"><h3>O</h3><dl><dt>OpenFramesInterface, <a class="indexterm" href="OpenFramesInterface.html">OpenFramesInterface</a></dt><dt>Optimize, <a class="indexterm" href="Optimize.html">Optimize</a></dt><dt>OrbitColor, <a class="indexterm" href="Color.html">Color</a></dt><dt>Orbit Determination, <a class="indexterm" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">Simulate DSN Range and Doppler Data</a>, <a class="indexterm" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">Simulate and Estimate Inter-Spacecraft Tracking</a>, <a class="indexterm" href="TrackingDataTypes.html">Tracking Data Types for Orbit Determination</a></dt><dd><dl><dt>Batch least squares, <a class="indexterm" href="BatchEstimator.html">BatchEstimator</a></dt><dt>Extended Kalman filter, <a class="indexterm" href="ExtendedKalmanFilter.html">ExtendedKalmanFilter</a></dt><dt>Initial Orbit Determination, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>Propagator configuration, <a class="indexterm" href="NavPropagatorConfiguration.html">Configuration of Propagators for Orbit
    Determination</a></dt><dt>Smoother, <a class="indexterm" href="Smoother.html">Smoother</a></dt><dt>Spacecraft navigation, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt></dl></dd><dt>OrbitErrorCovariance, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>OrbitView, <a class="indexterm" href="OrbitView.html">OrbitView</a></dt><dt>Output Tree, <a class="indexterm" href="Output.html">Output Tree</a></dt></dl></div><div class="indexdiv"><h3>P</h3><dl><dt>PenDown, <a class="indexterm" href="PenUpPenDown.html">PenUpPenDown</a></dt><dt>PenUp, <a class="indexterm" href="PenUpPenDown.html">PenUpPenDown</a></dt><dt>PlanetographicRegion, <a class="indexterm" href="PlanetographicRegion.html">PlanetographicRegion</a></dt><dt>Plate, <a class="indexterm" href="Plate.html">Plate</a></dt><dt>ProcessNoiseModel, <a class="indexterm" href="ProcessNoiseModel.html">ProcessNoiseModel</a>, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>Propagate, <a class="indexterm" href="Propagate.html">Propagate</a></dt><dt>Propagator, <a class="indexterm" href="Propagator.html">Propagator</a>, <a class="indexterm" href="NavPropagatorConfiguration.html">Configuration of Propagators for Orbit
    Determination</a></dt><dt>Python Interface, <a class="indexterm" href="PythonInterface.html">Python Interface</a></dt></dl></div><div class="indexdiv"><h3>R</h3><dl><dt>Receiver, <a class="indexterm" href="Receiver.html">Receiver</a></dt><dt>RejectFilter, <a class="indexterm" href="RejectFilter.html">RejectFilter</a></dt><dt>Report, <a class="indexterm" href="Report.html">Report</a></dt><dt>ReportFile, <a class="indexterm" href="ReportFile.html">ReportFile</a></dt><dt>Resources Tree, <a class="indexterm" href="ResourceTree.html">Resources Tree</a></dt><dt>RunEstimator, <a class="indexterm" href="RunEstimator.html">RunEstimator</a></dt><dt>RunSimulator, <a class="indexterm" href="RunSimulator.html">RunSimulator</a></dt><dt>RunSmoother, <a class="indexterm" href="RunSmoother.html">RunSmoother</a></dt></dl></div><div class="indexdiv"><h3>S</h3><dl><dt>Sample Missions, <a class="indexterm" href="SampleMissions.html">Sample Missions</a></dt><dt>Script Editor, <a class="indexterm" href="ScriptEditor.html">Script Editor</a></dt><dt>ScriptEvent, <a class="indexterm" href="BeginScript.html">BeginScript</a></dt><dt>Script Language, <a class="indexterm" href="ScriptLanguage.html">Script Language</a></dt><dt>Set, <a class="indexterm" href="Set.html">Set</a></dt><dt>SGP4 TLE Orbit Propagation, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>Simulator, <a class="indexterm" href="Simulator.html">Simulator</a></dt><dt>Smoother, <a class="indexterm" href="Smoother.html">Smoother</a></dt><dd><dl><dt>RunSmoother, <a class="indexterm" href="RunSmoother.html">RunSmoother</a></dt></dl></dd><dt>SNOPT, <a class="indexterm" href="SNOPTOptimizer.html">SNOPT</a></dt><dt>SolarPowerSystem, <a class="indexterm" href="SolarPowerSystem.html">SolarPowerSystem</a></dt><dt>Solar Radiation Pressure</dt><dd><dl><dt>Multi-plate area modeling, <a class="indexterm" href="Plate.html">Plate</a></dt><dt>SPAD area modeling, <a class="indexterm" href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></dt><dt>Spherical area modeling, <a class="indexterm" href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></dt></dl></dd><dt>SolarSystem, <a class="indexterm" href="SolarSystem.html">SolarSystem</a></dt><dt>SolveFors, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>Spacecraft, <a class="indexterm" href="Spacecraft.html">Spacecraft</a></dt><dt>Spacecraft Attitude, <a class="indexterm" href="SpacecraftAttitude.html">Spacecraft Attitude</a></dt><dt>Spacecraft Ballistic/Mass Properties, <a class="indexterm" href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></dt><dd><dl><dt>Multi-plate area modeling, <a class="indexterm" href="Plate.html">Plate</a></dt></dl></dd><dt>Spacecraft Epoch, <a class="indexterm" href="SpacecraftEpoch.html">Spacecraft Epoch</a></dt><dt>Spacecraft Hardware, <a class="indexterm" href="SpacecraftHardware.html">Spacecraft Hardware</a></dt><dt>SpacecraftNavigation, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>Spacecraft Orbit State, <a class="indexterm" href="SpacecraftOrbitState.html">Spacecraft Orbit State</a></dt><dt>Spacecraft Visualization Properties, <a class="indexterm" href="SpacecraftVisualizationProperties.html">Spacecraft Visualization Properties</a></dt><dt>SPADDragScaleFactorSigma, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>SPAD modeling</dt><dd><dl><dt>SPAD file format, <a class="indexterm" href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></dt></dl></dd><dt>SPADSRPScaleFactorSigma, <a class="indexterm" href="SpacecraftNavigation.html">Spacecraft Navigation</a></dt><dt>SPICE Orbit Propagation, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>Startup File, <a class="indexterm" href="StartupFile.html">Startup File</a></dt><dt>State noise compensation, <a class="indexterm" href="ProcessNoiseModel.html">ProcessNoiseModel</a></dt><dt>STK Ephemeris Orbit Propagation, <a class="indexterm" href="Propagator.html">Propagator</a></dt><dt>Stop, <a class="indexterm" href="Stop.html">Stop</a></dt><dt>String, <a class="indexterm" href="String.html">String</a></dt></dl></div><div class="indexdiv"><h3>T</h3><dl><dt>Target, <a class="indexterm" href="Target.html">Target</a></dt><dt>TargetColor, <a class="indexterm" href="Color.html">Color</a></dt><dt>ThreePositionIOD, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>ThreePositionIODLean, <a class="indexterm" href="InitialOrbitDetermination.html">Initial Orbit Determination</a></dt><dt>ThrustHistoryFile, <a class="indexterm" href="ThrustHistoryFile.html">ThrustHistoryFile</a></dt><dt>ThrustSegment, <a class="indexterm" href="ThrustSegment.html">ThrustSegment</a></dt><dt>Toggle, <a class="indexterm" href="Toggle.html">Toggle</a></dt><dt>Tracking Data</dt><dd><dl><dt>Filtering, <a class="indexterm" href="AcceptFilter.html">AcceptFilter</a>, <a class="indexterm" href="RejectFilter.html">RejectFilter</a></dt><dt>Simulation, <a class="indexterm" href="Simulator.html">Simulator</a></dt><dt>Types, <a class="indexterm" href="TrackingDataTypes.html">Tracking Data Types for Orbit Determination</a></dt></dl></dd><dt>TrackingFileSet, <a class="indexterm" href="TrackingFileSet.html">TrackingFileSet</a></dt><dt>Tranponder, <a class="indexterm" href="Transponder.html">Transponder</a></dt><dt>Transmitter, <a class="indexterm" href="Transmitter.html">Transmitter</a></dt></dl></div><div class="indexdiv"><h3>U</h3><dl><dt>UpdateDynamicData, <a class="indexterm" href="UpdateDynamicData.html">UpdateDynamicData</a></dt></dl></div><div class="indexdiv"><h3>V</h3><dl><dt>Variable, <a class="indexterm" href="Variable.html">Variable</a></dt><dt>Vary, <a class="indexterm" href="Vary.html">Vary</a></dt><dt>VF13ad, <a class="indexterm" href="VF13ad.html">VF13ad</a></dt></dl></div><div class="indexdiv"><h3>W</h3><dl><dt>While, <a class="indexterm" href="While.html">While</a></dt><dt>Write, <a class="indexterm" href="Write.html">Write</a></dt></dl></div><div class="indexdiv"><h3>X</h3><dl><dt>XYPlot, <a class="indexterm" href="XYPlot.html">XYPlot</a></dt></dl></div><div class="indexdiv"><h3>Y</h3><dl><dt>Yukon, <a class="indexterm" href="Yukon.html">Yukon</a></dt></dl></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ReleaseNotesR2011a.html">Prev</a>&nbsp;</td><td align="center" width="20%">&nbsp;</td><td align="right" width="40%">&nbsp;</td></tr><tr><td valign="top" align="left" width="40%">GMAT R2011a Release Notes&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;</td></tr></table></div></body></html>