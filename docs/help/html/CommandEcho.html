<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>CommandEcho</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s02.html" title="Commands"><link rel="prev" href="CallPythonFunction.html" title="CallPythonFunction"><link rel="next" href="For.html" title="For"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">CommandEcho</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="CallPythonFunction.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="For.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="CommandEcho"></a><div class="titlepage"></div><a name="N2CBBF" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">CommandEcho</span></h2><p>CommandEcho &mdash; Toggle the use of the <span class="guilabel">Echo</span>
    command</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">CommandEcho</code> <em class="replaceable"><code>EchoSetting</code></em>   </pre></div><div class="refsection"><a name="N2CBDD"></a><h2>Description</h2><p>The <span class="guilabel">EchoCommand</span> command is used to toggle the
	use of the <span class="guilabel">Echo</span> on and off throughout a mission
	sequence. This allows for specific parts of a mission sequence to be
	displayed to the message window and the generated log file. This command
	is a part of the <span class="guilabel">ScriptTools</span> plugin.</p></div><div class="refsection"><a name="N2CBEB"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">EchoSetting</span></td><td><p>Specifies whether the current
			<span class="guilabel">EchoSetting</span> of the <span class="guilabel">Echo</span>
			command should be on or off.</p>
			
			<div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2CC2D"></a><h2>GUI</h2><p>The <span class="guilabel">CommandEcho</span> command to toggle the
	<span class="guilabel">Echo</span> on or off at any point in a mission sequence.
	Any number of this command can be placed throughout a mission sequence. 
	The message box shown below will appear when setting the
	<span class="guilabel">EchoSetting</span> through the GUI. To set the command on,
	simply replace Off with On in the text. Note that if the command is
	renamed, the new name will appear in this GUI display with quotation
	marks surrounding it.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_CommandEcho_GUI.PNG" align="middle" height="143"></td></tr></table></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="CallPythonFunction.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="For.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">CallPythonFunction&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;For</td></tr></table></div></body></html>