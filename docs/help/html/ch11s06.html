<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Further Exercises</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_EventLocation.html" title="Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts"><link rel="prev" href="ch11s05.html" title="Configure and Run the Contact Locator"><link rel="next" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Further Exercises</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch11s05.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Tut_ElectricPropulsion.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13D45"></a>Further Exercises</h2></div></div></div><p>To expand on this tutorial, try the following exercise:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>For a mission like this, you probably will want ground station
        coverage during both maneuvers. Try the following steps to make sure
        the coverage is adequate:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>Change the colors of the <span class="guilabel">Propagate</span>
            commands, so you can see visually where the burns are
            located.</p></li><li class="listitem"><p>Add <span class="guilabel">GroundStation</span> resources near the
            locations of the burns on the ground track.</p></li><li class="listitem"><p>Confirm the burn epochs in the <span class="guilabel">Command
            Summary</span> for each <span class="guilabel">Maneuver</span>
            command.</p></li><li class="listitem"><p>Confirm in the contact report that these times occur during
            a contact interval.</p></li><li class="listitem"><p>Check the eclipse report, too: you may not want to perform a
            maneuver during an eclipse!</p></li></ul></div></li></ul></div><p>This tutorial shows you the basics of adding eclipse and station
    contact location to your mission. These resources have a lot of power, and
    there are many different ways to use them. Consult the <a class="xref" href="ContactLocator.html" title="ContactLocator"><span class="refentrytitle">ContactLocator</span></a> and <a class="xref" href="EclipseLocator.html" title="EclipseLocator"><span class="refentrytitle">EclipseLocator</span></a>
    documentation for details.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch11s05.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_EventLocation.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Tut_ElectricPropulsion.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure and Run the Contact Locator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;12.&nbsp;Electric Propulsion</td></tr></table></div></body></html>