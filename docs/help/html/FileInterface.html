<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>FileInterface</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch19.html#N22D14" title="Resources"><link rel="prev" href="EphemerisFile.html" title="EphemerisFile"><link rel="next" href="GroundTrackPlot.html" title="GroundTrackPlot"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">FileInterface</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="EphemerisFile.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="GroundTrackPlot.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="FileInterface"></a><div class="titlepage"></div><a name="N236C3" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">FileInterface</span></h2><p>FileInterface &mdash; An interface to a data file</p></div><div class="refsection"><a name="N236D4"></a><h2>Description</h2><p>The <span class="guilabel">FileInterface</span> resource is an interface to a
    data file that can be used to load mission data, like
    <span class="guilabel">Spacecraft</span> state information and physical properties.
    Once an interface is established to a file, the <span class="guilabel">Set</span>
    command can be used to load the data and apply it to a destination.</p><p>The following file formats are currently supported:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><code class="literal">TVHF_ASCII</code>: ASCII format of the TCOPS
          Vector Hold File (TVHF), defined by the NASA Goddard Space Flight
          Center Flight Dynamics Facility. This file contains spacecraft state
          and physical information that can be transferred to a
          <span class="guilabel">Spacecraft</span> resource.</p></li></ul></div><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Set.html" title="Set"><span class="refentrytitle">Set</span></a></p></div><div class="refsection"><a name="N236F4"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Filename</span></td><td><p>Full path of the file to read. Relative paths are
            interpreted as relative to the directory containing the GMAT
            executable. If the path is omitted, it is assumed to be
            &ldquo;<code class="literal">./</code>&rdquo;.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file path</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Format</span></td><td><p>Format of the file to read. Currently, the only
            allowed format is
            &ldquo;<code class="literal">TVHF_ASCII</code>&rdquo;.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumerated value</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">TVHF_ASCII</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">TVHF_ASCII</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N23769"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_FileInterface_GUI_1.png" align="middle" height="176"></td></tr></table></div></div><p>The <span class="guilabel">FileInterface</span> GUI has two fields: a list of
    accepted options for <span class="guilabel">Format</span> (currently only
    <span class="guilabel">TVHF_ASCII</span>), and an input box for
    <span class="guilabel">Filename</span>. Click <span class="guibutton">Browse</span> to the
    right of the <span class="guilabel">Filename</span> box to interactively select a
    file.</p></div><div class="refsection"><a name="N23789"></a><h2>Remarks</h2><p>Each file format supported by the <span class="guilabel">FileInterface</span>
    resource exposes a set of keywords that can be used to extract certain
    data elements. These keywords can be used in the <span class="guilabel">Data</span>
    option of the <span class="guilabel">Set</span> command, as follows:</p><pre class="synopsis"><code class="literal">Set</code> <em class="replaceable"><code>destination</code></em> <em class="replaceable"><code>source</code></em> <code class="literal">(Data = {</code><em class="replaceable"><code>keyword</code></em>[<code class="literal">,</code> <em class="replaceable"><code>keyword</code></em>]<code class="literal">})</code></pre><p>If the <code class="literal">'All'</code> keyword is used, those fields with a
    checkmark in the &ldquo;All&rdquo; column are selected.</p><div class="refsection"><a name="FileInterface_TVHF_ASCII"></a><h3><code class="literal">TVHF_ASCII</code></h3><div class="informaltable"><table border="1"><colgroup><col width="36%"><col width="18%"><col width="36%"><col width="10%"></colgroup><thead><tr><th align="left">Keyword</th><th align="left">Source field</th><th align="left">Description</th><th align="left"><code class="literal">'All'</code></th></tr></thead><tbody><tr><td><code class="literal">CartesianState</code></td><td>"<code class="literal">CARTESIAN COORDINATES</code>"</td><td>Cartesian state elements (<span class="guilabel">X</span>,
              <span class="guilabel">Y</span>, <span class="guilabel">Z</span>,
              <span class="guilabel">VX</span>, <span class="guilabel">VY</span>,
              <span class="guilabel">VZ</span>)</td><td>&#10003;</td></tr><tr><td><code class="literal">Cr</code></td><td>"<code class="literal">CSUBR</code>"</td><td>Coefficient of reflectivity</td><td>&#10003;</td></tr><tr><td><code class="literal">Epoch</code></td><td>"<code class="literal">EPOCH TIME FOR ELEMENTS</code>"</td><td>Epoch of state vector</td><td>&#10003;</td></tr></tbody></table></div><div class="refsection"><a name="N2380C"></a><h4>Limitations</h4><p>The following limitations apply to the
        <code class="literal">TVHF_ASCII</code> format:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Only the J2000 coordinate system is supported.</p></li><li class="listitem"><p>Only the first record in a multiple-record file is
              loaded.</p></li></ul></div></div></div></div><div class="refsection"><a name="N2381B"></a><h2>Examples</h2><div class="informalexample"><p>Read a TVHF file and use it to configure a spacecraft.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create FileInterface tvhf
tvhf.Filename = 'statevec.txt'
tvhf.Format = 'TVHF_ASCII'

BeginMissionSequence

Set aSat tvhf</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="EphemerisFile.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch19.html#N22D14">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="GroundTrackPlot.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">EphemerisFile&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;GroundTrackPlot</td></tr></table></div></body></html>