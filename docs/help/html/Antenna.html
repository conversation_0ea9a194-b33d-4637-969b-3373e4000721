<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Antenna</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="AcceptFilter.html" title="AcceptFilter"><link rel="next" href="BatchEstimator.html" title="BatchEstimator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Antenna</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="AcceptFilter.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="BatchEstimator.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Antenna"></a><div class="titlepage"></div><a name="N27E24" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Antenna</span></h2><p>Antenna &mdash; Transmits or receives an RF signal.</p></div><div class="refsection"><a name="N27E35"></a><h2>Description</h2><p>A number of GMAT resources including
    <span class="guilabel">Spacecraft</span>, <span class="guilabel">GroundStation</span>,
    <span class="guilabel">Transponder</span>, <span class="guilabel">Receiver</span>, and
    <span class="guilabel">Transmitter</span>, use an <span class="guilabel">Antenna</span>
    resource to transmit and/or receive RF signals.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Currently, none of the <span class="guilabel">Antenna</span> parameters
      have any effect on orbit determination processing and do not need to be
      set for orbit estimation. (e.g., Light time solutions do not currently
      take into account <span class="guilabel">Antenna</span> location within the
      spacecraft body.)</p></div><p><span class="ref_seealso">See Also</span>: <a class="xref" href="GroundStation.html" title="GroundStation"><span class="refentrytitle">GroundStation</span></a>, <a class="xref" href="Transponder.html" title="Transponder"><span class="refentrytitle">Transponder</span></a>, <a class="xref" href="Receiver.html" title="Receiver"><span class="refentrytitle">Receiver</span></a>, <a class="xref" href="Transmitter.html" title="Transmitter"><span class="refentrytitle">Transmitter</span></a></p></div><div class="refsection"><a name="N27E65"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">FieldOfView</span></td><td><p>Reference to optional field-of-view object which
            models the area visible to the antenna. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>FOV Resource</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CustomFOV</span>,
                    <span class="guilabel">ConicalFOV</span>, or
                    <span class="guilabel">RectantularFOV</span> Resource.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>empty</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DirectionX</span></td><td><p>X-component of the field-of-view boresight vector
            expressed in spacecraft body coordinates. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DirectionY</span></td><td><p>Y-component of the field-of-view boresight vector
            expressed in spacecraft body coordinates. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DirectionZ</span></td><td><p>Z-component of the field-of-view boresight vector
            expressed in spacecraft body coordinates. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SecondDirectionX</span></td><td><p>X-component of the vector, expressed in the body
            frame, used to resolve the sensor's orientation about the boresite
            vector. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SecondDirectionY</span></td><td><p>Y-component of the vector, expressed in the body
            frame, used to resolve the sensor's orientation about the boresite
            vector. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SecondDirectionZ</span></td><td><p>Z-component of the vector, expressed in the body
            frame, used to resolve the sensor's orientation about the boresite
            vector. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSX</span></td><td><p>X-component of the origin of the antenna&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSY</span></td><td><p>Y-component of the origin of the antenna&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate system.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSZ</span></td><td><p>Z-component of the origin of the antenna&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2803A"></a><h2>Remarks</h2><p>The antenna model supports mask, orientation and location settings.
    The mask is provided by the object designated by
    <span class="guilabel">FieldOfView</span>. The location is the position of the
    origin of the sensor frame, expressed in the spacecraft body coordinate
    frame. The orientation is represented as a direction cosine matrix that is
    initially computed from two non-colinear vectors, provided by the user as
    <span class="guilabel">Direction</span> and <span class="guilabel">SecondDirection</span>
    components. The three axes for the sensor coordinate frame expressed in
    Body coordinates are computed as follow:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Normalize <span class="guilabel">z</span> &amp; <span class="guilabel">v</span>,
        where <span class="guilabel">z</span> is the boresight represented by
        <span class="guilabel">Direction</span> and<span class="guilabel"> v</span> is the
        <span class="guilabel">SecondDirection</span> vector.</p></li><li class="listitem"><p>Compute the normal N = z x v and its magnitude m.</p></li><li class="listitem"><p>Verify magnitude of <span class="guilabel">N</span> isn&rsquo;t 0.0, send
        message if it is too close. This will happen if one of the input
        vectors is a zero vector or if the two vectors are co-linear,
        including the case where they point in opposite directions.</p></li><li class="listitem"><p><span class="guilabel">x</span> = <span class="guilabel">N</span> / m</p></li><li class="listitem"><p><span class="guilabel">y</span> = <span class="guilabel">z</span> x
        <span class="guilabel">x</span></p></li><li class="listitem"><p>The rotation matrix <span class="guilabel">R</span>sb is constructed with
        <span class="guilabel">x</span> on the first row, <span class="guilabel">y</span> on the
        second, and <span class="guilabel">z</span> on the third. This matrix rotates
        vectors from the body frame to the sensor frame.</p></li></ol></div><p><span class="guilabel">R</span>sb is used as part of the chain checking if an
    object is in the field of view. The general approach is to have a vector
    from the antenna to the object in a given reference frame and do a series
    of rotations, the last of which would use <span class="guilabel">R</span>sb to
    rotate the vector from spacecraft to antenna coordinates.</p></div><div class="refsection"><a name="N28090"></a><h2>Examples</h2><div class="informalexample"><p>Attach an <span class="guilabel">Antenna</span> to hardware,
      <span class="guilabel">Spacecraft</span> and <span class="guilabel">GroundStation</span>
      <span class="guilabel">Resource</span> types.</p><pre class="programlisting">Create Antenna SatTranponderAntenna 
Create Antenna DSNReceiverAntenna DSNTransmitterAntenna

Create Transponder SatTransponder;
SatTransponder.PrimaryAntenna = SatTranponderAntenna

Create Spacecraft Sat
Sat.AddHardware = {SatTransponder, SatTranponderAntenna};

Create Transmitter DSNTransmitter
DSNTransmitter.PrimaryAntenna = DSNTransmitterAntenna

Create Receiver DSNReceiver
DSNReceiver.PrimaryAntenna = DSNReceiverAntenna;

Create GroundStation DSN;
DSN.AddHardware =  {DSNTransmitter, DSNReceiver}
DSN.AddHardware =  {DSNTransmitterAntenna, DSNReceiverAntenna};
BeginMissionSequence;</pre><p>Define the field-of-view, orientation, and location of an
      antenna.</p><pre class="programlisting">% Define a conical FOV
Create ConicalFOV coneFOV;
GMAT coneFOV.FieldOfViewAngle = 20;

% Create an antenna and attache a FOV
Create Antenna myAntenna;
GMAT myAntenna.FieldOfView = coneFOV;

% Define the antenna boresight direction in body coordinates
GMAT myAntenna.DirectionX = 1;
GMAT myAntenna.DirectionY = 0;
GMAT myAntenna.DirectionZ = 0;

% Define the vector to resolve orientation about boresight
GMAT myAntenna.SecondDirectionX = 0;
GMAT myAntenna.SecondDirectionY = 1;
GMAT myAntenna.SecondDirectionZ = 0;

% Define the location of antenna in body coordinates
GMAT myAntenna.HWOriginInBCSX = 100;
GMAT myAntenna.HWOriginInBCSY = -100;
GMAT myAntenna.HWOriginInBCSZ = 0;
</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="AcceptFilter.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="BatchEstimator.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">AcceptFilter&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;BatchEstimator</td></tr></table></div></body></html>