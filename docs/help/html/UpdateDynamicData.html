<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>UpdateDynamicData</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch19s02.html" title="Commands"><link rel="prev" href="Toggle.html" title="Toggle"><link rel="next" href="Write.html" title="Write"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">UpdateDynamicData</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Toggle.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Write.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="UpdateDynamicData"></a><div class="titlepage"></div><a name="N25562" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">UpdateDynamicData</span></h2><p>UpdateDynamicData &mdash; A command used in tandem with a
	<span class="guilabel">DynamicDataDisplay</span> to update the data being shown in
	the display table on the GUI.</p></div><div class="refsection"><a name="N25576"></a><h2>Description</h2><p>The <span class="guilabel">UpdateDynamicData</span> command is used to specify
	when in a mission sequence a <span class="guilabel">DynamicDataDisplay</span> will
	update its data to their current values.  This allows the user to control
	what points in the mission sequence they will see the new data.  The user
	may also specify certain data within a
	<span class="guilabel">DynamicDataDisplay</span> to update rather than the entire
	table if desired.  This command can be placed at any desired point in a
	mission sequence and multiple can be used on the same
	<span class="guilabel">DynamicDataDisplay</span>.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="DynamicDataDisplay.html" title="DynamicDataDisplay"><span class="refentrytitle">DynamicDataDisplay</span></a></p></div><div class="refsection"><a name="N2558E"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="26%"><col width="74%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">DynamicTableName</span></td><td><p> Field to set which DynamicDataDisplay object will be
			updated by this command</p>
			<div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any DynamicDataTable object</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DataList</span></td><td><p> Field to set which parameters will be updated in the
			selected table while the rest of the data remains unchanged</p>
			<div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Any parameter</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any parameter in the currently selected
					DynamicDataDisplay</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N255FD"></a><h2>GUI</h2><p>The figure below shows the <span class="guilabel">UpdateDynamicData</span>
	using the basic command GUI panel:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_UpdateDynamicData_GUI.png" align="middle" height="198"></td></tr></table></div></div></div><div class="refsection"><a name="N2560E"></a><h2>Examples</h2><div class="informalexample"><p>Creation of a <span class="guilabel">DynamicDataDisplay</span> and the
	  <span class="guilabel">UpdateDynamicData</span> command being used to update that
	  display in a mission sequence.</p><pre class="programlisting"><code class="code">Create Spacecraft DefaultSC;
Create Propagator DefaultProp;

Create DynamicDataDisplay myDisplay;
GMAT DynamicDataDisplay.AddParameters(1, DefaultSC.X, DefaultSC.Y);

BeginMissionSequence
Propagate DefaultProp(DefaultSC) (DefaultSC.ElapsedSecs = 12000.0);
UpdateDynamicData myDisplay;
Propagate DefaultProp(DefaultSC) (DefaultSC.ElapsedSecs = 24000.0);
UpdateDynamicData myDisplay DefaultSC.X;</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Toggle.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch19s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Write.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Toggle&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Write</td></tr></table></div></body></html>