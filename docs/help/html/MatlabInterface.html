<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>MATLAB Interface</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s03.html" title="System"><link rel="prev" href="IncludeMacro.html" title="#Include Macro"><link rel="next" href="PythonInterface.html" title="Python Interface"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">MATLAB Interface</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="IncludeMacro.html">Prev</a>&nbsp;</td><th align="center" width="60%">System</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="PythonInterface.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="MatlabInterface"></a><div class="titlepage"></div><a name="N2D1D2" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">MATLAB Interface</span></h2><p>MATLAB Interface &mdash; Interface to MATLAB system</p></div><div class="refsection"><a name="N2D1E3"></a><h2>Description</h2><p>The MATLAB interface provides a link to the Mathworks MATLAB
    environment, allowing GMAT to run MATLAB functions as if they were native
    functions in the GMAT script language.</p><p>The interface cannot be controlled directly through the script
    language, though it can be in the GMAT GUI. Instead, GMAT starts the
    interface automatically when it calls a MATLAB function.</p><p>There are two GMAT components that provide user access to the
    interface. For details on declaring a MATLAB function, see the
    <span class="guilabel"><a class="xref" href="MatlabFunction.html" title="MatlabFunction"><span class="refentrytitle">MatlabFunction</span></a></span> reference. For
    details on calling a function and passing data, see the <span class="guilabel"><a class="xref" href="CallMatlabFunction.html" title="CallMatlabFunction"><span class="refentrytitle">CallMatlabFunction</span></a></span> reference.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="CallMatlabFunction.html" title="CallMatlabFunction"><span class="refentrytitle">CallMatlabFunction</span></a>, <a class="xref" href="MatlabFunction.html" title="MatlabFunction"><span class="refentrytitle">MatlabFunction</span></a></p></div><div class="refsection"><a name="N2D1FE"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/System_MatlabInterface_ResourcesTree.png" align="middle" height="331"></td></tr></table></div></div><p>The MATLAB interface provides an icon in the
    <span class="guilabel">Interfaces</span> folder in the Resources tree that can be
    used to control the interface. Right-clicking the icon shows two options:
    <span class="guilabel">Open</span> and <span class="guilabel">Close</span>.</p><p>The <span class="guilabel">Open</span> menu item causes GMAT to open a
    connection to the MATLAB Engine, which in turns displays a MATLAB command
    window in the background. This connection is then used for all
    communication between GMAT and MATLAB until the connection is closed. Only
    one connection can be open at a time.</p><p>The <span class="guilabel">Close</span> menu item causes GMAT to close any
    open connection to the MATLAB Engine. If no connection is open, it has no
    effect.</p></div><div class="refsection"><a name="N2D21F"></a><h2>Remarks</h2><div class="refsection"><a name="N2D222"></a><h3>Interface
      Setup</h3><p>The following conditions must be true for GMAT to successfully
      initiate communication with MATLAB. All conditions must be true for the
      same instance of MATLAB.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Install a compatible, licensed version of MATLAB on the same
          machine on which GMAT is running. GMAT is tested with the latest
          version of MATLAB at the time of release, though versions R2006b and
          newer have been known to work.</p></li><li class="listitem"><p>The architecture (32-bit or 64-bit) of GMAT and the installed
          version of MATLAB must match. For example, the 32-bit version of
          GMAT is compatible only with the 32-bit version of MATLAB.</p></li><li class="listitem"><p>On Windows:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Add the following path (where
                <code class="filename"><em class="replaceable"><code>MATLAB</code></em></code> is the
                path to the installed version of MATLAB) to your
                <code class="envar">Path</code> environment variable (either your user
                variable, or the system variable). If you continue to have
                trouble, try putting this path at the very beginning of your
                system path.</p><p><code class="filename"><em class="replaceable"><code>MATLAB</code></em>\bin\win32</code>
                (or <code class="filename">win64</code> for use with 64-bit versions of
                GMAT)</p></li><li class="listitem"><p>Register MATLAB for use as a COM server by
                running:</p><p><strong class="userinput"><code>matlab -regserver</code></strong></p><p>This is done automatically by the MATLAB installer. To
                do it manually, open an elevated command window and run the
                command above. Make sure to run the command in the folder
                containing the executable you wish to use (i.e.
                <em class="replaceable"><code>MATLAB</code></em>\bin\win32 or
                <em class="replaceable"><code>MATLAB</code></em>\bin\win64.)</p></li></ol></div></li><li class="listitem"><p>On macOS:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Open the MacConfigure.txt in the bin directory and edit
                the MATLAB_APP_PATH field to point to the location of your
                MATLAB application bundle.</p></li><li class="listitem"><p>If the Matlab interface does not work with the
                GmatConsole command line application, you may need to set up
                your Terminal so that the system can load the Matlab libraries
                and start up MATLAB. For example, if you are using a .bashrc,
                you may need to add something like this:</p><p><strong class="userinput"><code>export MATLAB =
                &lt;path/to/MATLAB/app/location/&gt;</code></strong></p><p><strong class="userinput"><code>export
                DYLD_LIBRARY_PATH=$MATLAB/bin/maci64:$DYLD_LIBRARY_PATH</code></strong></p><p><strong class="userinput"><code>export
                PATH=$PATH:$MATLAB/bin</code></strong></p></li></ol></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>64-bit GMAT must be used to interface with MATLAB after
              version R2010a.</p></div></li><li class="listitem"><p>On Linux: </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>The MATLAB interface on Linux requires that the C shell,
                csh, be installed. This is a requirement from the MathWorks
                for startup of the MATLAB engine, used for the interface. If
                your system does not have csh installed, the package manager
                for your Linux installation should include csh as an
                installation option.</p><p>The MATLAB interface needs to be able to locate the
                MATLAB shared libraries libeng.so (and related libraries) and
                libMatlabEngine.so. For MATLAB R2019a, the former is in the
                MATLAB bin/glnxa64 folder and the latter in MATLAB's
                extern/bin/glnxa64 folder. One way to start GMAT and apply the
                library path settings is to launch the application with a
                library path. From the Linux terminal, the command (for MATLAB
                installed in the default /usr/local/MATLAB/R2019a
                folder)</p><p><strong class="userinput"><code>LD_LIBRARY_PATH=/usr/local/MATLAB/R2019a/extern/bin/glnxa64:/usr/local/MATLAB/R2019a/bin/glnxa64
                ./GMAT</code></strong></p><p>accomplishes this task, starting the GMAT GUI with the
                settings needed to run the MATLAB interface.</p></li></ul></div></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Common troubleshooting tips on Windows:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>If you are using the officially-released 32-bit version of
            GMAT, make sure you have the 32-bit version of MATLAB
            installed.</p></li><li class="listitem"><p>If the path above exists in your system <code class="envar">Path</code>
            variable, try place it at the front.</p></li><li class="listitem"><p>Make sure the same instance of MATLAB is referenced both in
            the <code class="envar">Path</code> variable and when running
            <strong class="userinput"><code>matlab -regserver</code></strong>.</p></li></ul></div></div></div><div class="refsection"><a name="N2D28F"></a><h3>MATLAB Engine Connection</h3><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Caution: GMAT does not close the MATLAB Command Window it
        creates after a run has completed. This allows manual inspection of
        the MATLAB workspace, but it can lead to confusing behavior if MATLAB
        functions or paths are changed and rerun in the same window.</p><p>We recommend closing the MATLAB Command Window by right-clicking
        Matlab in the Resources tree and clicking Close between each run if
        you are actively editing the script.</p></div><p>When GMAT runs a mission that contains a MATLAB function call, it
      opens a connection to the MATLAB engine before it makes the function
      call. It then reuses this connection for the rest of the GMAT
      session.</p><p>The MATLAB Engine can be controlled manually through the
      <span class="guilabel">Open</span> and <span class="guilabel">Close</span> options
      available by right-clicking the <span class="guilabel">Matlab</span> item in the
      Resources tree.</p></div></div><div class="refsection"><a name="N2D2A4"></a><h2>Examples</h2><p>See the <a class="xref" href="MatlabFunction.html" title="MatlabFunction"><span class="refentrytitle">MatlabFunction</span></a> reference for common
    examples.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="IncludeMacro.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s03.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="PythonInterface.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">#Include Macro&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Python Interface</td></tr></table></div></body></html>