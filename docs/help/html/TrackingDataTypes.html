<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Tracking Data Types for Orbit Determination</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21s03.html" title="System"><link rel="prev" href="ch21s03.html" title="System"><link rel="next" href="NavPropagatorConfiguration.html" title="Configuration of Propagators for Orbit Determination"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Tracking Data Types for Orbit Determination</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch21s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">System</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="NavPropagatorConfiguration.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="TrackingDataTypes"></a><div class="titlepage"></div><a name="N2AE90" class="indexterm"></a><a name="N2AE95" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></h2><p>Tracking Data Types for Orbit Determination &mdash; This section describes tracking data types and file formats
    for orbit determination.</p></div><div class="refsection"><a name="N2AEA6"></a><h2>Measurement Types Supported</h2><p>GMAT supports the following measurement types for orbit
    determination.</p><div class="informaltable"><table border="1"><colgroup><col width="21%"><col width="61%"><col width="18%"></colgroup><thead><tr><th align="center">GMAT Measurement Type Name</th><th align="center">Measurement Description</th><th align="center">Measurement Units</th></tr></thead><tbody><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_Angles" title="Angle Measurements">Azimuth,
            Elevation</a></td><td>Antenna pointing angles, for antennas using Az/El mounts.
            The geometry for Azimuth and Elevation angles is depicted in
            Figure 9-3 of Reference 1. Azimuth is defined to vary from 0 to
            360 degrees. Azimuth is 0 along the North axis and increases in
            the East direction. Elevation is defined to vary from -90 to 90
            degrees. Elevation is zero in the North/East plane and increases
            in the Zenith direction.</td><td>Degrees</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_BrtsDoppler" title="BRTS Doppler Measurements">BRTS_Doppler</a></td><td>Two-way coherent Doppler tracking from the NASA Tracking
            and Data Relay Satellite System Bilateration Ranging Transponder
            System (BRTS).</td><td>Hertz</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_BrtsRange" title="BRTS Range Measurements">BRTS_Range</a></td><td>Two-way coherent range tracking from the NASA Tracking and
            Data Relay Satellite System Bilateration Ranging Transponder
            System (BRTS).</td><td>Kilometers</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_PNRange" title="DSN Pseudo-Noise Range">DSN_PNRange</a></td><td>DSN pseudo-noise ranging (TRK-2-34 data type 14).</td><td>Range Units</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_DsnSeqRange" title="DSN Sequential Range">DSN_SeqRange</a></td><td>DSN sequential ranging (TRK-2-34 data type 7).</td><td>Range Units</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_DsnTcp" title="DSN Total Count Phase">DSN_TCP</a></td><td>DSN total count phase (TRK-2-34 data type 17) measurements,
            implemented as a derived "Doppler" type measurement using
            successive phase measurements. This measurement type is used for
            both two-way and three-way tracking. See the notes below for
            details.</td><td>Hertz</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_GpsPosVec" title="Earth-fixed Position Vectors from a Spacecraft On-board GPS Receiver">GPS_PosVec</a></td><td>Earth-fixed position vectors from a spacecraft on-board GPS
            receiver.</td><td>Kilometers</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_Range" title="Two-Way Transponder Range">Range</a></td><td>Two-way transponder range. A round-trip range measurement
            that includes the Spacecraft
            <span class="guilabel">Transponder.HardwareDelay</span>. This measurement
            type may be used for ground-to-space and inter-spacecraft (also
            called crosslink) tracking. See the notes below for
            details.</td><td>Kilometers</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_RangeSkin" title="Two-Way Non-Transponder Range">Range_Skin</a></td><td>Two-way non-transponder range. A round-trip range
            measurement that does not include the Spacecraft
            <span class="guilabel">Transponder.HardwareDelay</span>. This measurement
            type is appropriate for radar and other skin-tracking
            measurements.</td><td>Kilometers</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_RangeRate" title="Two-Way Range-Rate">RangeRate</a></td><td>Two-way coherent transponder range-rate. This is modeled as
            the difference between range measurements at the end and start of
            the Doppler count interval, divided by the length of the count
            interval. The measurement is time-tagged at the end of the
            interval. This measurement type may be used for ground-to-space
            and inter-spacecraft (space-to-space, also called crosslink)
            tracking. This measurement type is used for both two-way and
            three-way tracking. See the notes below for details.</td><td>Kilometers/sec</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_SnDoppler" title="Space Network (TDRSS) Two-way Doppler">SN_Doppler</a></td><td>Two-way coherent user (on-orbit) Doppler tracking from the
            NASA Tracking and Data Relay Satellite (Space Network)
            System.</td><td>Hertz</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_SnDopplerRtn" title="Space Network (TDRSS) One-way Return Doppler">SN_Doppler_Rtn</a></td><td>One-way non-coherent user return link Doppler tracking from
            the NASA Tracking and Data Relay Satellite (Space Network/Space
            Relay) System. SN_Doppler_Rtn is a space-to-ground Doppler
            measurement that originates at a user spacecraft, is received by a
            TDRS spacecraft, and relayed by the TDRS to a TDRS ground
            station.</td><td>Hertz</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_SnDowd" title="Space Network (TDRSS) Differenced One-way Return Doppler">SN_DOWD</a></td><td>Differenced one-way return Doppler tracking from the NASA
            Tracking and Data Relay Satellite (Space Network/Space Relay)
            System. SN_DOWD measurements are formulated by differencing
            (subtracting) simultaneous TDRS one-way return Doppler
            measurements</td><td>Hertz</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_SnRange" title="Space Network (TDRSS) Two-way Range">SN_Range</a></td><td>Two-way coherent user (on-orbit) range tracking from the
            NASA Tracking and Data Relay Satellite (Space Network)
            System.</td><td>Kilometers</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_Angles" title="Angle Measurements">XEast,
            YNorth</a></td><td>Antenna pointing angles, for antennas using an X/Y mount
            with the X rotation axis (the axis we rotate by angle XEast)
            oriented to the North. The geometry for XEast and YNorth angles is
            depicted in Figure 9-4 of Reference 1. Both angles are zero when
            the antenna is pointed to the zenith. XEast is defined to vary
            from -180 to +180 degrees. YNorth is defined to vary from -90 to
            +90 degrees.</td><td>Degrees</td></tr><tr><td><a class="link" href="TrackingDataTypes.html#TrackingDataTypes_Angles" title="Angle Measurements">XSouth,
            YEast</a></td><td>Antenna pointing angles, for antennas using an X/Y mount
            with the X rotation axis (the axis we rotate by angle XSouth)
            oriented to the East. The geometry for XSouth and YEast angles is
            depicted in Figure 9-5 of Reference 1. Both angles are zero when
            the antenna is pointed to the zenith. XSouth is defined to vary
            from -180 to +180 degrees. YEast is defined to vary from -90 to
            +90 degrees.</td><td>Degrees</td></tr></tbody></table></div><p>The GMAT measurement type names listed are the string names to be
    used in instances of <span class="guilabel">ErrorModel</span>,
    <span class="guilabel">AcceptFilter</span>, <span class="guilabel">RejectFilter</span>, and
    <span class="guilabel">TrackingFileSet</span>, and in the GMAT GMD-format tracking
    data file to identify each measurement type to GMAT.</p><div class="refsection"><a name="TrackingDataTypes_InterSpacecraftTracking"></a><h3>Inter-spacecraft Tracking Measurements</h3><p>GMAT supports simulation and estimation of inter-spacecraft (also
      called "crosslink") tracking for some measurement types. These
      measurements are tracking observations consisting of range or
      Doppler/range-rate tracking between two spacecraft orbiting the same or
      different gravitational bodies. Inter-spacecraft tracking is currently
      supported for the Range and RangeRate measurement types. The formatting
      of GMAT tracking data files for inter-spacecraft tracking is identical
      to the associated ground-to-space measurement, with the exception that
      inter-spacecraft measurements use different observation type index
      numbers, as indicated below for each supported measurement type.</p><p>To configure two spacecraft for inter-spacecraft tracking, the
      "tracking" spacecraft must be assigned <span class="guilabel">Transmitter</span>
      and <span class="guilabel">Receiver</span> objects. The tracking spacecraft
      Receiver is then configured with the proper
      <span class="guilabel">ErrorModel</span>s for the inter-spacecraft tracking. The
      measurement time tag for each inter-spacecraft measurement is the
      TAIModJulian measurement receive time at the tracking spacecraft.</p><p>See also <a class="xref" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking"><i>Simulate and Estimate Inter-Spacecraft Tracking</i></a> for a tutorial on simulating and estimating
      an orbit using inter-spacecraft tracking. Since the spacecraft
      participating in inter-spacecraft tracking may be in significantly
      different orbit regimes and each may require unique force modeling, GMAT
      supports specification of individual force models and propagators for
      each participant. See the <span class="guilabel">Propagator</span> fields of the
      <span class="guilabel">Simulator</span>, <span class="guilabel">BatchEstimator</span>, and
      <span class="guilabel">ExtendedKalmanFilter</span> resources for details on
      configuring multiple propagators.</p></div></div><div class="refsection"><a name="N2AF87"></a><h2>GMAT Tracking Data File Formats</h2><p>GMAT uses a native ASCII tracking data file format called a &ldquo;GMAT
    Measurement Data File&rdquo;, or GMD file. Each GMD file consists of a series of
    space-delimited ASCII records. Details of the GMD file format for each
    observation type are provided in the following sections. A single GMD file
    may contain one or more of the record types described below, but ramp
    records must be in a separate file.</p><p>For further details on the TRK-2-34 raw data formats referenced
    below, please consult Reference 2.</p><div class="refsection"><a name="TrackingDataTypes_Angles"></a><h3>Angle Measurements</h3><p>All angle measurement types (Azimuth, Elevation, XEast, YNorth,
      XSouth, and YEast) share the same GMD file format. The generic angle
      observation is measured in degrees and represents rotation angles in the
      topocentric reference frame depicted in Figure 9-2 of Reference 1. The
      GMD record format for angle observation data is shown in the table
      below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - Azimuth, Elevation, XEast,
              YNorth, XSouth, or YEast</td></tr><tr><td>3</td><td><p>Observation type index number:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>9016 = Azimuth, 9017 = Elevation</p></li><li class="listitem"><p>9018 = XEast, 9019 = YNorth</p></li><li class="listitem"><p>9020 = XSouth, 9021 = YEast</p></li></ul></div></td></tr><tr><td>4</td><td>Downlink Ground station pad ID</td></tr><tr><td>5</td><td>Spacecraft ID</td></tr><tr><td>6</td><td>Angle measurement in degrees</td></tr></tbody></table></div><p>A sample of GMD data records for Azimuth and Elevation data is
      shown below.</p><pre class="programlisting">%           - 1 -                - 2 -      3       4       5                - 6 -
26088.5524768518518518444200    Azimuth    9016    MAD    LEOSat    4.8265973546050333e+001
26088.5524768518518518444200    Elevation  9017    MAD    LEOSat    1.2679724467383584e+001
26088.5531712962962962888639    Azimuth    9016    MAD    LEOSat    5.9562253624641066e+001
26088.5531712962962962888639    Elevation  9017    MAD    LEOSat    1.6962633728400046e+001
26088.5538657407407407333080    Azimuth    9016    MAD    LEOSat    7.4485459451069659e+001
26088.5538657407407407333080    Elevation  9017    MAD    LEOSat    2.0586055229649272e+001
26088.5545601851851851777532    Azimuth    9016    MAD    LEOSat    9.2442509281488796e+001
26088.5545601851851851777532    Elevation  9017    MAD    LEOSat    2.2241223403621646e+001
</pre></div><div class="refsection"><a name="N2AFD2"></a><h3>Bilateration Ranging Transponder System (BRTS)
      Measurements</h3><p>The Bilateration Ranging Transponder System (BRTS) consists of a
      collection of transponders fixed to the surface of the Earth at various
      locations. TDRS satellites track the BRTS transponders as if they were
      on-orbit spacecraft. Given the known location of the BRTS transponder,
      the resulting range and Doppler measurements can be used to determine
      the TDRS orbit. BRTS measurements are effectively the same as SN User
      measurements (see below) in that the BRTS on the ground acts identically
      to an SN user in space.</p><p>Please see the <a class="link" href="TrackingDataTypes.html#TrackingDataTypes_TdrsIds" title="Note">note
      below</a> regarding rules for assigning TDRS spacecraft IDs.</p><p>The measurement path for BRTS_Range and BRTS_Doppler originates at
      a TDRSS ground terminal, proceeds along an uplink leg to a TDRSS
      spacecraft, and is relayed by the TDRS along the forward leg to a BRTS
      antenna on the ground. The BRTS transponder receives and retransmits the
      signal along a return path to (usually the same) TDRSS spacecraft, which
      then relays the signal along the downlink path to a TDRSS ground
      terminal (usually the same as the transmit ground station).</p><div class="refsection"><a name="TrackingDataTypes_BrtsDoppler"></a><h4>BRTS Doppler Measurements</h4><p>The GMD record format for BRTS_Doppler observation data is shown
        in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - BRTS_Doppler</td></tr><tr><td>3</td><td><p>Observation type index number - 9025 =
                BRTS_Doppler</p></td></tr><tr><td>4</td><td>Measurement path. The format of the measurement path is
                { XmitGroundStation ForwardTDRS BRTSGroundStation ReturnTDRS
                RecvGroundStation }</td></tr><tr><td>5</td><td>Nominal BRTS transmit frequency in Hertz</td></tr><tr><td>6</td><td>BRTS transmit frequency band indicator - 0 = unknown, 1
                = S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 =
                L-band</td></tr><tr><td>7</td><td>TDRS Service ID - 'SA1', 'SA2', or 'MA'</td></tr><tr><td>8</td><td>Tracker type for MA services (0 = legacy, 1 =
                TDRS-K)</td></tr><tr><td>9</td><td>S-Band Multiple Access Return (SMAR) downlink frequency
                ID</td></tr><tr><td>10</td><td>Doppler count interval in seconds</td></tr><tr><td>11</td><td>Doppler observation in Hertz</td></tr></tbody></table></div><p>A sample of GMD data records for BRTS Doppler data is shown
        below.</p><pre class="programlisting">%    - 1 -            - 2 -          3                     - 4 -                                 - 5 -            6     7     8   9    - 10 -              - 11 -
23585.500370370    BRTS_Doppler    9025    { 48   TDRS12    148    TDRS12    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677498921896984e+10
23585.500370370    BRTS_Doppler    9025    { 48   TDRS12    152    TDRS12    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677499155046673e+10
23585.507314814    BRTS_Doppler    9025    { 48   TDRS12    148    TDRS12    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677498755754671e+10
23585.507314814    BRTS_Doppler    9025    { 48   TDRS12    152    TDRS12    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677499026034889e+10
</pre></div><div class="refsection"><a name="TrackingDataTypes_BrtsRange"></a><h4>BRTS Range Measurements</h4><p>The GMD record format for BRTS Range observation data is shown
        in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - BRTS_Range</td></tr><tr><td>3</td><td><p>Observation type index number - 9027 =
                BRTS_Range</p></td></tr><tr><td>4</td><td>Measurement path. The format of the measurement path is
                { XmitGroundStation ForwardTDRS BRTSGroundStation ReturnTDRS
                RecvGroundStation }</td></tr><tr><td>5</td><td>Total round-trip range measurement in
                kilometers</td></tr></tbody></table></div><p>A sample of GMD data records for BRTS Range data is shown
        below.</p><pre class="programlisting">
%        - 1 -          - 2 -      3            - 4 -                   - 5 -
29002.736215277777777 BRTS_Range 9027 {47 TDRS11 1311 TDRS11 47} +1.625344877538e+05
29002.736226851851851 BRTS_Range 9027 {47 TDRS11 1311 TDRS11 47} +1.625345659996e+05
29002.736238425925925 BRTS_Range 9027 {47 TDRS11 1311 TDRS11 47} +1.625346436459e+05
29002.736250000000000 BRTS_Range 9027 {47 TDRS11 1311 TDRS11 47} +1.625347218917e+05
29002.736261574074074 BRTS_Range 9027 {47 TDRS11 1311 TDRS11 47} +1.625348010369e+05
</pre></div></div><div class="refsection"><a name="TrackingDataTypes_PNRange"></a><h3>DSN Pseudo-Noise Range</h3><p>DSN TRK-2-34 pseudo-noise ranging employs the
      <span class="guilabel">DSN_PNRange</span> measurement type. DSN_PNRange is an
      ambiguous range observation measured in range units. Computation of the
      unambiguous round-trip range is performed by GMAT internally using the
      predicted spacecraft state and the range modulo value supplied in the
      GMD file. Note that if the initial spacecraft state is very poor, it is
      possible that an incorrect number of range intervals may be computed,
      resulting in computation of an incorrect measured round-trip range. This
      can generally be remedied by supplying a more accurate initial state, if
      one is available. The GMD record format for DSN_PNRange tracking data is
      shown in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="10%"><col width="90%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - DSN TRK-2-34 type 14 Pseudo-Noise
              Range = DSN_PNRange</td></tr><tr><td>3</td><td><p>Observation type index number - 9033 =
              DSN_PNRange</p></td></tr><tr><td>4</td><td>Tracking measurement path. The format is {
              XmitGroundStation Spacecraft RecvGroundStation }</td></tr><tr><td>5</td><td>Range observable in range units
              (<span class="emphasis"><em>meas_rng</em></span> value from the TRK-2-34 PN range
              tracking CHDO)</td></tr><tr><td>6</td><td>Transmit delay in seconds. This is the sum
              <span class="emphasis"><em>ul_stn_cal</em></span> +
              <span class="emphasis"><em>ul_zheight_corr</em></span>, where
              <span class="emphasis"><em>ul_stn_cal</em></span> is obtained from the PN range
              tracking CHDO and <span class="emphasis"><em>ul_zheight_corr</em></span> is
              obtained from CHDO 134. The value of
              <span class="emphasis"><em>ul_stn_cal</em></span> should be converted from range
              units to seconds according to the method shown below.</td></tr><tr><td>7</td><td>Receive delay in seconds. This is the sum
              <span class="emphasis"><em>dl_stn_cal</em></span> +
              <span class="emphasis"><em>dl_zheight_corr</em></span>, where
              <span class="emphasis"><em>dl_stn_cal</em></span> is obtained from the PN range
              tracking CHDO and <span class="emphasis"><em>dl_zheight_corr</em></span> is
              obtained from CHDO 134. The value of
              <span class="emphasis"><em>dl_stn_cal</em></span> should be converted from range
              units to seconds according to the method shown below.</td></tr><tr><td>8</td><td>Uplink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band. This
              is the <span class="emphasis"><em>ul_band</em></span> value from CHDO 134.</td></tr><tr><td>9</td><td>Downlink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band. This
              is the <span class="emphasis"><em>vld_dl_band</em></span> value from CHDO
              134.</td></tr><tr><td>10</td><td>Transmit frequency in Hz. See notes below.</td></tr><tr><td>11</td><td>Range modulo value (<span class="emphasis"><em>rng_modulo</em></span> from
              TRK-2-34 PN range tracking CHDO)</td></tr></tbody></table></div><p>The uplink (<span class="emphasis"><em>ul_stn_cal</em></span>) and downlink
      (<span class="emphasis"><em>dl_stn_cal</em></span>) station calibration values must be
      converted to seconds and added to the respective z-height corrections
      (already in seconds). The conversion of station calibration values from
      range units to seconds requires the transmit frequency at the time of
      the observation. This is found by identifying the nearest ramp record
      prior to the observation time and using the transmit frequency and
      frequency rate from that record to determine the transmit frequency at
      the observation time. The conversion to from range units to seconds is
      then given by the equation shown below, where <span class="emphasis"><em>k</em></span> =
      1/2 for S-band and <span class="emphasis"><em>k</em></span> = 221/1498 for X-Band.</p><div class="mediaobject" align="center"><img src="../files/images/System_TrackingDataTypes_RuToSecs.PNG" align="middle"></div><p>The transmit frequency specified in the TRK-2-34 range data GMD
      file is only used if a frequency ramp table is not available. If a
      transmit frequency ramp record file is provided on the
      <span class="guilabel">TrackingFileSet.RampTable</span> field, the transmit
      frequency will be determined from the ramp table and the frequency
      specified in the range data GMD file will be ignored. A sample of GMD
      data records for TRK-2-34 Pseudo-Noise Range data is shown below.</p><pre class="programlisting">%            - 1 -                 - 2 -        3            - 4 -                   - 5 -                     - 6 -                    - 7 -              8    9           - 10 -                   - 11 -
29899.9861921296287619043142    DSN_PNRange    9033    { 26    23    26 }    3.5177653473651981e+08    8.1677625136844035e-05    8.8413709125097467e-04    1    1    2.0410472153173406e+09    5.1684864000000000e+08
29899.9863078703710925765336    DSN_PNRange    9033    { 26    23    26 }    3.5198542211253548e+08    8.1677625439244134e-05    8.8413709453168197e-04    1    1    2.0410472077438405e+09    5.1684864000000000e+08
29899.9864236111097852699459    DSN_PNRange    9033    { 26    23    26 }    3.5219423880677605e+08    8.1677625741644234e-05    8.8413709781238916e-04    1    1    2.0410472001703405e+09    5.1684864000000000e+08
29899.9865393518521159421653    DSN_PNRange    9033    { 26    23    26 }    3.5240297588308334e+08    8.1677626044044334e-05    8.8413710109309625e-04    1    1    2.0410471925968406e+09    5.1684864000000000e+08
</pre><div class="refsection"><a name="N2B0FD"></a><h4>Formulation of the Computed PN Range Value</h4><p>GMAT determines a computed PN range value by the following
        process:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>The GMD record measurement observation time represents the
              time the signal reached the receiver electronics. The time the
              signal arrived at the antenna is obtained by subtracting the GMD
              record receive delay from the GMD record observation receive
              time.</p></li><li class="listitem"><p>The transmit time and total round-trip light time are
              obtained by solving the light-time equation starting from the
              adjusted receive time. The light time solution yields the time
              the signal left the antenna at transmission.</p></li><li class="listitem"><p>The time the signal left the antenna at transmission is
              adjusted by subtracting the transmit delay to obtain the time
              the signal left the transmitting antenna electronics.</p></li><li class="listitem"><p>The frequency is integrated, using the ramp table if
              applicable, between the electronics transmit and receive times.
              This yields the total round-trip signal path in range
              units.</p></li></ol></div></div></div><div class="refsection"><a name="TrackingDataTypes_DsnSeqRange"></a><h3>DSN Sequential Range</h3><p>DSN TRK-2-34 sequential ranging employs the
      <span class="guilabel">DSN_SeqRange</span> measurement type. DSN_SeqRange is an
      ambiguous range observation measured in range units. Computation of the
      unambiguous round-trip range is performed by GMAT internally using the
      computed spacecraft state and the range modulo value supplied in the GMD
      file. Note that if the initial spacecraft state is very poor, it is
      possible that an incorrect number of range intervals may be computed,
      resulting in computation of an incorrect measured round-trip range. This
      can generally be remedied by supplying a more accurate initial state, if
      one is available. The GMD record format for DSN_SeqRange tracking data
      is shown in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - DSN TRK-2-34 Type 7 Sequential
              Range = DSN_SeqRange</td></tr><tr><td>3</td><td><p>Observation type index number - 9004 =
              DSN_SeqRange</p></td></tr><tr><td>4</td><td>Downlink Ground station pad ID or tracking spacecraft
              ID</td></tr><tr><td>5</td><td>Spacecraft ID</td></tr><tr><td>6</td><td>Range observable (<span class="emphasis"><em>meas_rng</em></span> or
              <span class="emphasis"><em>rng_obs</em></span> from TRK-2-34 Sequential Range
              CHDO, with appropriate corrections applied)</td></tr><tr><td>7</td><td>Uplink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band</td></tr><tr><td>8</td><td>Uplink frequency in Hz</td></tr><tr><td>9</td><td>Range modulo value (<span class="emphasis"><em>rng_modulo</em></span> from
              TRK-2-34 Sequential Range CHDO)</td></tr></tbody></table></div><p>The transmit frequency specified in the TRK-2-34 range data GMD
      file is only used if a frequency ramp table is not available. If a
      transmit frequency ramp record file is provided on the
      <span class="guilabel">TrackingFileSet.RampTable</span> field, the transmit
      frequency will be determined from the ramp table and the frequency
      specified in the range data GMD file will be ignored. A sample of GMD
      data records for TRK-2-34 Sequential Range data is shown below.</p><pre class="programlisting">%    - 1 -         - 2 -      3     4  5        - 6 -           7        - 8 -               - 9 -
27236.157789352 DSN_SeqRange 9004  45 59 +9.810325186004e+005   1 +2.091414432000e+009 +1.048576000000e+006
27236.158240741 DSN_SeqRange 9004  45 59 +5.813243487947e+005   1 +2.091414432000e+009 +1.048576000000e+006
27236.158692130 DSN_SeqRange 9004  45 59 +1.863046908683e+005   1 +2.091414432000e+009 +1.048576000000e+006
27236.159143519 DSN_SeqRange 9004  45 59 +8.450116485521e+005   1 +2.091414432000e+009 +1.048576000000e+006</pre><div class="refsection"><a name="N2B167"></a><h4>Formulation of the Computed Sequential Range Value</h4><p>GMAT determines a computed PN range value by the following
        process:</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:mrow>
              <m:mi>computed range = C</m:mi>

              <m:mstyle displaystyle="true">
                <m:mrow>
                  <m:munderover>
                    <m:mo>&int;</m:mo>

                    <m:mrow>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:mrow>

                    <m:mrow>
                      <m:mi>t</m:mi>

                      <m:mn>3</m:mn>
                    </m:mrow>
                  </m:munderover>

                  <m:mrow>
                    <m:msub>
                      <m:mi>f</m:mi>

                      <m:mi>T</m:mi>
                    </m:msub>

                    <m:mo stretchy="false">(</m:mo>

                    <m:mi>t</m:mi>

                    <m:mo stretchy="false">)</m:mo>

                    <m:mi>d</m:mi>

                    <m:mi>t</m:mi>
                  </m:mrow>
                </m:mrow>
              </m:mstyle>

              <m:mo>,</m:mo>

              <m:mtext>&nbsp;mod&nbsp;M&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(RU)</m:mtext>
            </m:mrow>
          </m:math></div><p>where</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mi>t</m:mi>

              <m:mn>1</m:mn>
            </m:msub>

            <m:mo>,</m:mo>

            <m:msub>
              <m:mi>t</m:mi>

              <m:mn>3</m:mn>
            </m:msub>

            <m:mo>=</m:mo>

            <m:mtext>Transmission&nbsp;and&nbsp;Reception&nbsp;epoch,&nbsp;respectively</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mi>f</m:mi>

              <m:mi>T</m:mi>
            </m:msub>

            <m:mo>=</m:mo>

            <m:mtext>Ground&nbsp;Station&nbsp;transmit&nbsp;frequency</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:mi>C</m:mi>

            <m:mo>=</m:mo>

            <m:mtext>transmitter&nbsp;dependent&nbsp;constant&nbsp;(221/1498&nbsp;for&nbsp;X-band&nbsp;and&nbsp;1/2&nbsp;for&nbsp;S-Band)</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:mtext>M&nbsp;</m:mtext>

            <m:mo>=</m:mo>

            <m:mtext>&nbsp;length&nbsp;of&nbsp;the&nbsp;ranging&nbsp;code&nbsp;in&nbsp;RU</m:mtext>
          </m:math></div></div></div><div class="refsection"><a name="TrackingDataTypes_DsnTcp"></a><h3>DSN Total Count Phase</h3><p>DSN TRK-2-34 total count phase employs the
      <span class="guilabel">DSN_TCP</span> measurement type. This measurement type is
      used for both two-way and three-way tracking. As shown below, the GMAT
      Doppler measurement type, measured in Hz, is derived from successive
      total count phase (TCP) observations.</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mrow>
            <m:mtext>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Derived&nbsp;"Doppler"&nbsp;Observation=</m:mtext>

            <m:mo>&minus;</m:mo>

            <m:mfrac>
              <m:mrow>
                <m:mrow>
                  <m:mo>[</m:mo>

                  <m:mrow>
                    <m:mi>&#981;</m:mi>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>e</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>

                    <m:mo>&minus;</m:mo>

                    <m:mi>&#981;</m:mi>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>s</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>
                  </m:mrow>

                  <m:mo>]</m:mo>
                </m:mrow>
              </m:mrow>

              <m:mrow>
                <m:msub>
                  <m:mi>t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>e</m:mi>
                  </m:mrow>
                </m:msub>

                <m:mo>&minus;</m:mo>

                <m:msub>
                  <m:mi>t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>s</m:mi>
                  </m:mrow>
                </m:msub>
              </m:mrow>
            </m:mfrac>

            <m:mtext>&nbsp;=&nbsp;</m:mtext>

            <m:mo>&minus;</m:mo>

            <m:mfrac>
              <m:mrow>
                <m:mrow>
                  <m:mo>[</m:mo>

                  <m:mrow>
                    <m:mi>&#981;</m:mi>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>e</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>

                    <m:mo>&minus;</m:mo>

                    <m:mi>&#981;</m:mi>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>s</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>
                  </m:mrow>

                  <m:mo>]</m:mo>
                </m:mrow>
              </m:mrow>

              <m:mrow>
                <m:mtext>DCI</m:mtext>
              </m:mrow>
            </m:mfrac>

            <m:mtext>&nbsp;&nbsp;(Hz)</m:mtext>
          </m:mrow>
        </m:math></div><p>where</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mtable columnalign="left">
            <m:mtr/>

            <m:mtr/>

            <m:mtr>
              <m:mtd>
                <m:msub>
                  <m:mi>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>s</m:mi>
                  </m:mrow>
                </m:msub>

                <m:mo>,</m:mo>

                <m:msub>
                  <m:mi>t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>e</m:mi>
                  </m:mrow>
                </m:msub>

                <m:mo>=</m:mo>

                <m:mtext>start&nbsp;and&nbsp;end&nbsp;of&nbsp;reception&nbsp;interval</m:mtext>
              </m:mtd>
            </m:mtr>

            <m:mtr>
              <m:mtd>
                <m:mtext>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;DCI &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=&nbsp;Doppler Count Interval in
                seconds</m:mtext>
              </m:mtd>
            </m:mtr>

            <m:mtr>
              <m:mtd>
                <m:mi>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#981;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</m:mi>

                <m:mo>=</m:mo>

                <m:mtext>Total&nbsp;Count&nbsp;Phase&nbsp;(from
                type&nbsp;17&nbsp;TRK-2-34&nbsp;record)</m:mtext>
              </m:mtd>
            </m:mtr>
          </m:mtable>
        </m:math></div><p>The GMD record format for DSN_TCP tracking data is shown in the
      table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - DSN TRK-2-34 Type 17 Total Count
              Phase = DSN_TCP</td></tr><tr><td>3</td><td><p>Observation type index number - 9006 =
              DSN_TCP</p></td></tr><tr><td>4</td><td>Tracking measurement path. The format is {
              XmitGroundStation Spacecraft RecvGroundStation }. For three-way
              tracking, the transmit and receive ground stations are
              different.</td></tr><tr><td>5</td><td>Uplink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band</td></tr><tr><td>6</td><td>Doppler count interval in seconds</td></tr><tr><td>7</td><td>Observation value - Doppler observable derived from Total
              Count Phase (TCP) TRK-2-34 Type 17 measurements</td></tr></tbody></table></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Please note the new format for representing the tracking
        measurement path. The old format <code class="code">RecvGroundStation
        Spacecraft</code> will continue to work but is deprecated and may be
        removed in a future release.</p></div><p>A sample of GMD data records for TRK-2-34 total count phase
      derived Doppler data is shown below.</p><pre class="programlisting">%    - 1 -        - 2 -     3      - 4 -       5   6            - 7 -
27226.011944444  DSN_TCP  9006 { 15 6241 15 }  1  10  -2.2445668331979342e+09
27226.012060185  DSN_TCP  9006 { 15 6241 15 }  1  10  -2.2445668330920730e+09
27226.012175926  DSN_TCP  9006 { 15 6241 15 }  1  10  -2.2445668329843016e+09
27226.012291667  DSN_TCP  9006 { 15 6241 15 }  1  10  -2.2445668328729177e+09</pre><div class="refsection"><a name="N2B375"></a><h4>Formulation of the Computed TCP Doppler Value</h4><p>GMAT determines a computed TCP Doppler value by the following
        process:</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:mrow>
              <m:mi>C</m:mi>

              <m:mo>=</m:mo>

              <m:mo>&minus;</m:mo>

              <m:mfrac>
                <m:mrow>
                  <m:msub>
                    <m:mi>M</m:mi>

                    <m:mn>2</m:mn>
                  </m:msub>
                </m:mrow>

                <m:mrow>
                  <m:mrow>
                    <m:mo>(</m:mo>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mrow>
                          <m:mn>3</m:mn>

                          <m:mi>e</m:mi>
                        </m:mrow>
                      </m:msub>

                      <m:mo>&minus;</m:mo>

                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mrow>
                          <m:mn>3</m:mn>

                          <m:mi>s</m:mi>
                        </m:mrow>
                      </m:msub>
                    </m:mrow>

                    <m:mo>)</m:mo>
                  </m:mrow>
                </m:mrow>
              </m:mfrac>

              <m:mstyle displaystyle="true">
                <m:mrow>
                  <m:munderover>
                    <m:mo>&int;</m:mo>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>

                      <m:msub>
                        <m:mrow/>

                        <m:mi>s</m:mi>
                      </m:msub>
                    </m:mrow>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>

                      <m:msub>
                        <m:mrow/>

                        <m:mi>e</m:mi>
                      </m:msub>
                    </m:mrow>
                  </m:munderover>

                  <m:mrow>
                    <m:msub>
                      <m:mi>f</m:mi>

                      <m:mi>T</m:mi>
                    </m:msub>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>

                    <m:mi>d</m:mi>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>
                  </m:mrow>
                </m:mrow>
              </m:mstyle>

              <m:mtext>&nbsp;&nbsp;=</m:mtext>

              <m:mo>&minus;</m:mo>

              <m:mfrac>
                <m:mrow>
                  <m:msub>
                    <m:mi>M</m:mi>

                    <m:mn>2</m:mn>
                  </m:msub>

                  <m:mrow>
                    <m:mo>(</m:mo>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mrow>
                          <m:mn>1</m:mn>

                          <m:mi>e</m:mi>
                        </m:mrow>
                      </m:msub>

                      <m:mo>&minus;</m:mo>

                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mrow>
                          <m:mn>1</m:mn>

                          <m:mi>s</m:mi>
                        </m:mrow>
                      </m:msub>
                    </m:mrow>

                    <m:mo>)</m:mo>
                  </m:mrow>
                </m:mrow>

                <m:mrow>
                  <m:mi>D</m:mi>

                  <m:mi>C</m:mi>

                  <m:mi>I</m:mi>
                </m:mrow>
              </m:mfrac>

              <m:msub>
                <m:mover accent="true">
                  <m:mi>f</m:mi>

                  <m:mo>&macr;</m:mo>
                </m:mover>

                <m:mi>T</m:mi>
              </m:msub>

              <m:mtext>&nbsp;&nbsp;&nbsp;&nbsp;(Hz)</m:mtext>
            </m:mrow>
          </m:math></div><p>where</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mi>t</m:mi>

              <m:mrow>
                <m:mn>1</m:mn>

                <m:mi>s</m:mi>
              </m:mrow>
            </m:msub>

            <m:mo>,</m:mo>

            <m:msub>
              <m:mi>t</m:mi>

              <m:mrow>
                <m:mn>1</m:mn>

                <m:mi>e</m:mi>
              </m:mrow>
            </m:msub>

            <m:mo>=</m:mo>

            <m:mtext>start&nbsp;and&nbsp;end&nbsp;of&nbsp;transmission&nbsp;interval</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mi>f</m:mi>

              <m:mi>T</m:mi>
            </m:msub>

            <m:mo>=</m:mo>

            <m:mtext>transmit&nbsp;frequency</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mi>M</m:mi>

              <m:mn>2</m:mn>
            </m:msub>

            <m:mo>=</m:mo>

            <m:mtext>Transponder&nbsp;turn&nbsp;around&nbsp;ratio&nbsp;(typically,&nbsp;240/221&nbsp;for&nbsp;S-band&nbsp;and&nbsp;880/749&nbsp;for&nbsp;X-band)</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:mtext>DCI&nbsp;=&nbsp;</m:mtext>

            <m:mrow>
              <m:mo>(</m:mo>

              <m:mrow>
                <m:msub>
                  <m:mi>t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>e</m:mi>
                  </m:mrow>
                </m:msub>

                <m:mo>&minus;</m:mo>

                <m:msub>
                  <m:mi>t</m:mi>

                  <m:mrow>
                    <m:mn>3</m:mn>

                    <m:mi>s</m:mi>
                  </m:mrow>
                </m:msub>
              </m:mrow>

              <m:mo>)</m:mo>
            </m:mrow>

            <m:mo>=</m:mo>

            <m:mtext>&nbsp;Doppler&nbsp;Count&nbsp;Interval</m:mtext>
          </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
            <m:msub>
              <m:mover accent="true">
                <m:mi>f</m:mi>

                <m:mo>&macr;</m:mo>
              </m:mover>

              <m:mi>T</m:mi>
            </m:msub>

            <m:mo>&equiv;</m:mo>

            <m:mfrac>
              <m:mrow>
                <m:mstyle displaystyle="true">
                  <m:mrow>
                    <m:munderover>
                      <m:mo>&int;</m:mo>

                      <m:mrow>
                        <m:msub>
                          <m:mi>t</m:mi>

                          <m:mn>1</m:mn>
                        </m:msub>

                        <m:msub>
                          <m:mrow/>

                          <m:mi>s</m:mi>
                        </m:msub>
                      </m:mrow>

                      <m:mrow>
                        <m:msub>
                          <m:mi>t</m:mi>

                          <m:mn>1</m:mn>
                        </m:msub>

                        <m:msub>
                          <m:mrow/>

                          <m:mi>e</m:mi>
                        </m:msub>
                      </m:mrow>
                    </m:munderover>

                    <m:mrow>
                      <m:msub>
                        <m:mi>f</m:mi>

                        <m:mi>T</m:mi>
                      </m:msub>

                      <m:mo stretchy="false">(</m:mo>

                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>

                      <m:mo stretchy="false">)</m:mo>

                      <m:mi>d</m:mi>

                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>
                    </m:mrow>
                  </m:mrow>
                </m:mstyle>
              </m:mrow>

              <m:mrow>
                <m:mrow>
                  <m:mo>(</m:mo>

                  <m:mrow>
                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>1</m:mn>

                        <m:mi>e</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo>&minus;</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>1</m:mn>

                        <m:mi>s</m:mi>
                      </m:mrow>
                    </m:msub>
                  </m:mrow>

                  <m:mo>)</m:mo>
                </m:mrow>
              </m:mrow>
            </m:mfrac>

            <m:mtext>&nbsp;</m:mtext>

            <m:mo>=</m:mo>

            <m:mtext>average&nbsp;transmit&nbsp;frequency&nbsp;</m:mtext>
          </m:math></div><p>Note that (t3e - t3s) is known as the Doppler Count Interval.
        This value is read from the GMD file record (for estimation) and is an
        input field, <span class="guilabel">SimDopplerCountInterval</span> on the
        <span class="guilabel">TrackingFileSet</span> resource for simulation.</p></div></div><div class="refsection"><a name="TrackingDataTypes_DsnRampTable"></a><h3>Transmit Frequency Ramp Records</h3><p>GMAT supports DSN tracking utilizing both constant and ramped
      transmit frequencies. If the transmit frequency is constant, GMAT will
      use the transmit frequency specified on the DSN_SeqRange or DSN_PNRange
      measurement records for the computation of the range observation and a
      ramp table file is not required. If the transmit frequency is ramped,
      the user must generate a GMD file of ramp records from TRK-2-34 Type 9
      raw data, and provide the GMD ramp table on the
      <span class="guilabel">TrackingFileSet.RampTable</span> object field. If a ramp
      table is provided, GMAT ignores the frequency specified on the range
      records and instead computes the transmit frequency from the ramp
      records.</p><p>The record format for ground-based range-rate tracking data is
      shown in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Ramp record epoch in TAIModJulian</td></tr><tr><td>2</td><td>Ground station pad ID</td></tr><tr><td>3</td><td>Spacecraft ID</td></tr><tr><td>4</td><td>Uplink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band. (This
              is the <span class="emphasis"><em>ul_band</em></span> value from CHDO
              132).</td></tr><tr><td>5</td><td>Ramp type - 0 = snap, 1 = start of new ramp, 2 = medial
              report, 3 = periodic report, 4 = end of ramps, 5 = ramping
              terminated by operator, 6 = invalid/unknown
              (<span class="emphasis"><em>ramp_type</em></span> from the TRK-2-34 ramp tracking
              CHDO)</td></tr><tr><td>6</td><td>Ramp frequency in Hz (<span class="emphasis"><em>ramp_freq</em></span> from
              the TRK-2-34 ramp tracking CHDO)</td></tr><tr><td>7</td><td>Ramp rate in Hz/sec (<span class="emphasis"><em>ramp_rate</em></span> from
              the TRK-2-34 ramp tracking CHDO)</td></tr></tbody></table></div><p>A sample GMD ramp file is shown below.</p><pre class="programlisting">
%     - 1 -      2  3    4   5         - 6 -              - 7 -
27238.640625000 34 234   2   1 +7.186571173393e+09 +6.010599999990e-01
27238.654513889 34 234   2   1 +7.186571894665e+09 +5.822699999990e-01
27238.659664352 34 234   2   3 +7.186572153775e+09 +5.822699999990e-01
27238.668402778 34 234   2   1 +7.186572593389e+09 +5.590199999990e-01
27238.682291667 34 234   2   1 +7.186573264213e+09 +5.315100000000e-01</pre></div><div class="refsection"><a name="TrackingDataTypes_GpsPosVec"></a><h3>Earth-fixed Position Vectors from a Spacecraft On-board GPS
      Receiver</h3><p>GPS-derived Earth-fixed position vectors employ the
      <span class="guilabel">GPS_PosVec</span> measurement type. The fixed frame
      assumed for the vector components is GMAT's EarthFixed reference frame
      (see <a class="xref" href="CoordinateSystem.html" title="CoordinateSystem"><span class="refentrytitle">CoordinateSystem</span></a>). The record format for
      GPS_PosVec tracking data is shown in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - GPS_PosVec</td></tr><tr><td>3</td><td>Observation type index number - 9014 = GPS_PosVec</td></tr><tr><td>4</td><td>GPS receiver ID</td></tr><tr><td>5</td><td>Earth-fixed position X component (km)</td></tr><tr><td>6</td><td>Earth-fixed position Y component (km)</td></tr><tr><td>7</td><td>Earth-fixed position Z component (km)</td></tr></tbody></table></div><p>The GMAT user should be aware that the GPS_PosVec measurement is
      currently treated as a vector quantity. The vector components are not
      treated as independent observations. If any component of a vector
      observation (X, Y, or Z) is edited from the solution by the user or by
      autonomous sigma editing, the other components associated with that
      observation will also be edited out, regardless of their quality.</p><p>A sample GMD GPS_PosVec file is shown below.</p><pre class="programlisting">
%     - 1 -           - 2 -     3   4              - 5 -               - 6 -                 - 7 -
26112.586516203704 GPS_PosVec 9014 800         -3575.594419         -5758.828897          1440.891615
26112.587210648147 GPS_PosVec 9014 800         -3257.134099         -5984.420574          1265.579859
26112.587905092594 GPS_PosVec 9014 800         -2926.558570         -6187.149174          1084.793371
26112.588599537037 GPS_PosVec 9014 800         -2585.076391         -6366.230816           899.311591
26112.589293981480 GPS_PosVec 9014 800         -2233.950454         -6520.997704           709.941434
</pre></div><div class="refsection"><a name="TrackingDataTypes_Range"></a><h3>Two-Way Transponder Range</h3><p>Two-way range measurements that pass through a Spacecraft
      transponder use the <span class="guilabel">Range</span> measurement type.
      <span class="guilabel">Range</span> is an unambiguous round-trip range
      observation measured in kilometers. The measurement model in GMAT will
      include the Spacecraft <span class="guilabel">Transponder.HardwareDelay</span>,
      but the HardwareDelay may be set to zero. The GMD record format for
      Range data is shown in the table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - Range</td></tr><tr><td>3</td><td><p>Observation type index number</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>9002 = Ground-to-space Range</p></li><li class="listitem"><p>9029 = Inter-spacecraft (crosslink) Range</p></li></ul></div></td></tr><tr><td>4</td><td>Downlink ground station pad ID or tracking spacecraft
              ID</td></tr><tr><td>5</td><td>Spacecraft ID</td></tr><tr><td>6</td><td>Two-way (round-trip) range observation in
              kilometers</td></tr></tbody></table></div><p>A sample of GMD data records for Range data is shown below.</p><pre class="programlisting">%    - 1 -         - 2 -  3    4   5         - 6 -
27182.022395833334 Range 9002 117 322 +7.447171160686e+04
27182.022511574076 Range 9002 117 322 +7.447456623065e+04
27182.022627314815 Range 9002 117 322 +7.447742325277e+04
27182.022743055557 Range 9002 117 322 +7.448028087448e+04</pre></div><div class="refsection"><a name="TrackingDataTypes_RangeSkin"></a><h3>Two-Way Non-Transponder Range</h3><p>Two-way range measurements that do not pass through a Spacecraft
      transponder use the <span class="guilabel">Range_Skin</span> measurement type.
      Range_Skin is an unambiguous round-trip range observation measured in
      kilometers. Users of tracking data in raw 46-character or NORAD B3
      format should be aware that these formats encode a one-way range value,
      so the reported range measurement must be converted to a two-way value
      (by multiplying the observed range by 2) when formatting this data into
      a GMD file. The GMD record format for Range_Skin data is shown in the
      table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - Range_Skin</td></tr><tr><td>3</td><td><p>Observation type index number - 9024 =
              Range_Skin</p></td></tr><tr><td>4</td><td>Receive ground station pad ID</td></tr><tr><td>5</td><td>Spacecraft ID</td></tr><tr><td>6</td><td>Two-way (round-trip) range observation in
              kilometers</td></tr></tbody></table></div><p>A sample of GMD data records for Range_Skin data is shown
      below.</p><pre class="programlisting">%    - 1 -           - 2 -     3    4   5         - 6 -
27182.022395833334 Range_Skin 9024 117 322 +7.447171160686e+04
27182.022511574076 Range_Skin 9024 117 322 +7.447456623065e+04
27182.022627314815 Range_Skin 9024 117 322 +7.447742325277e+04
27182.022743055557 Range_Skin 9024 117 322 +7.448028087448e+04</pre></div><div class="refsection"><a name="TrackingDataTypes_RangeRate"></a><h3>Two-Way Range-Rate</h3><p>Two-way coherent range-rate tracking uses the
      <span class="guilabel">RangeRate</span> measurement type. RangeRate is the
      difference of the range observation at the end of the averaging interval
      and the start of the averaging interval, divided by the averaging
      interval duration. The time tag is at the end of the averaging interval.
      The GMD record format for RangeRate data is shown in the table
      below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - RangeRate</td></tr><tr><td>3</td><td><p>Observation type index number</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>9012 = Ground-to-space RangeRate</p></li><li class="listitem"><p>9030 = Inter-spacecraft (crosslink) RangeRate</p></li></ul></div></td></tr><tr><td>4</td><td>Tracking measurement path. The format is {
              XmitGroundStation Spacecraft RecvGroundStation }. For three-way
              tracking, the transmit and receive stations are
              different.</td></tr><tr><td>5</td><td>Uplink frequency band indicator - 0 = unknown, 1 =
              S-band, 2 = X-band, 3 = Ka-band, 4 = Ku-band, 5 = L-band</td></tr><tr><td>6</td><td>Doppler averaging interval in seconds</td></tr><tr><td>7</td><td>Range-rate observation in kilometers/second</td></tr></tbody></table></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Please note the new format for representing the tracking
        measurement path. The old format <code class="code">RecvGroundStation
        Spacecraft</code> will continue to work but is deprecated and may be
        removed in a future release.</p></div><p>A sample of GMD data records for RangeRate data is shown
      below.</p><pre class="programlisting">%    -  - 1 -           - 2 -        3             - 4 -             5    6             - 7 -
21544.500370370370    RangeRate    9012    { CAN    SAT    USPS }    1    10    -3.8108162251433930e-02
21544.501064814814    RangeRate    9012    { CAN    SAT    USPS }    1    10    -3.7958430795696073e-02
21544.501759259259    RangeRate    9012    { CAN    SAT    USPS }    1    10    -3.7805598218522536e-02
21544.502453703703    RangeRate    9012    { CAN    SAT    USPS }    1    10    -3.7655205945919069e-02
</pre></div><div class="refsection"><a name="TrackingDataTypes_SN"></a><h3>Space Network (Tracking and Data Relay Satellite System)
      Measurements</h3><p>The Tracking and Data Relay Satellite System (TDRSS) consists of a
      constellation of spacecraft in geosynchronous orbit that provides
      near-continuous coverage of low-earth orbit. The TDRSS spacecraft are
      used to relay data from low-earth orbiting spacecraft to the ground.
      TDRSS spacecraft are also used to track low-earth spacecraft, and the
      TDRSS provides a number of measurement types that are supported for OD
      in GMAT.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title"><a name="TrackingDataTypes_TdrsIds"></a>Note</h3><p>Certain constants in the TDRSS measurement model depend on the
        user service (SA1, SA2, or MA) and TDRS spacecraft ID. The user
        service is explicitly indicated in the GMD file tracking data records.
        GMAT will attempt to determine the TDRS spacecraft ID from the
        <span class="guilabel">Spacecraft</span> resource <span class="guilabel">Id</span>
        parameter. For TDRS measurements to function properly in all cases,
        the user must observe one of the following conventions when assigning
        the <span class="guilabel">Spacecraft.Id</span> parameter for a TDRS
        spacecraft.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The user may specify the TDRS SIC as the TDRS spacecraft Id.
            GMAT will infer the TDRS ID from the proper TDRS spacecraft
            SIC.</p></li><li class="listitem"><p>Optionally, the user may specify the TDRS
            <span class="guilabel">Spacecraft.Id</span> in the format
            "<span class="emphasis"><em>&lt;string&gt;&lt;nn&gt;</em></span>", where
            <span class="emphasis"><em>&lt;nn&gt;</em></span> is a one- or two-digit number
            indicating the TDRS ID. The <span class="emphasis"><em>&lt;string&gt;</em></span>
            portion of the ID may be any string. For example, setting the
            spacecraft <span class="guilabel">Id</span> parameter to "TDRS09", "TD09",
            or "TDRS9" all properly identify the spacecraft as TDRS-9 (TDRS ID
            = 9).</p></li></ul></div><p>Failing to follow one of these conventions may result in some
        Doppler measurements being computed incorrectly. GMAT will issue a
        warning message in the log file if it is unable to determine the TDRS
        ID from the spacecraft <span class="guilabel">Id</span> parameter.</p></div><p>For all TDRS measurement types, media (ionosphere and troposphere)
      corrections are only applied on the TDRS to ground tracking leg. No
      media corrections are computed on user to TDRS (space to space) tracking
      legs.</p><div class="refsection"><a name="TrackingDataTypes_SnDoppler"></a><h4>Space Network (TDRSS) Two-way Doppler</h4><p>TDRSS coherent two-way Doppler tracking uses the
        <span class="guilabel">SN_Doppler</span> measurement type. The measurement path
        for SN_Doppler originates at a TDRSS ground terminal, proceeds along
        an uplink leg to a TDRSS spacecraft, and is relayed by the TDRS along
        the forward leg to the user spacecraft. The user spacecraft receives
        and retransmits the signal along a return path to (usually the same)
        TDRSS spacecraft, which then relays the signal along the downlink path
        to a TDRSS ground terminal (usually the same as the transmit ground
        station). The GMD record format for SN_Doppler data is shown in the
        table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - SN_Doppler</td></tr><tr><td>3</td><td><p>Observation type index number - 9013 =
                SN_Doppler</p></td></tr><tr><td>4</td><td>Measurement path; the format is { XmitGroundStation
                ForwardTDRS UserSpacecraft ReturnTDRS RecvGroundStation
                }.</td></tr><tr><td>5</td><td>Nominal user transmit frequency in Hertz</td></tr><tr><td>6</td><td>User transmit frequency band indicator - 0 =
                unspecified, 1 = S-band, 3 = K-band</td></tr><tr><td>7</td><td>TDRS service identifier; SA1, SA2, or MA.</td></tr><tr><td>8</td><td>Tracker type for MA services (0 = legacy, 1 =
                TDRS-K)</td></tr><tr><td>9</td><td>S-Band Multiple Access Return (SMAR) downlink frequency
                ID</td></tr><tr><td>10</td><td>Doppler count interval in seconds</td></tr><tr><td>11</td><td>Doppler measurement in Hertz</td></tr></tbody></table></div><p>A sample of GMD data records for SN_Doppler data is shown
        below.</p><pre class="programlisting">
%       - 1 -               - 2 -     3                      - 4 -                            - 5 -           6     7    8    9     - 10 -         - 11 -    
27235.503981481481481    SN_Doppler  9013    { 47    TDRS10    1874    TDRS10    47 }   2.287500960000e+09    1    SA2   1    0    1.000000      81582.008000
27235.503993055555555    SN_Doppler  9013    { 47    TDRS10    1874    TDRS10    47 }   2.287500960000e+09    1    SA2   1    0    1.000000      81491.748000
27235.504004629629629    SN_Doppler  9013    { 47    TDRS10    1874    TDRS10    47 }   2.287500960000e+09    1    SA2   1    0    1.000000      81401.316000
27235.504016203703703    SN_Doppler  9013    { 47    TDRS10    1874    TDRS10    47 }   2.287500960000e+09    1    SA2   1    0    1.000000      81310.708000
27235.504027777777777    SN_Doppler  9013    { 47    TDRS10    1874    TDRS10    47 }   2.287500960000e+09    1    SA2   1    0    1.000000      81219.995000
</pre></div><div class="refsection"><a name="TrackingDataTypes_SnDopplerRtn"></a><h4>Space Network (TDRSS) One-way Return Doppler</h4><p>TDRSS non-coherent one-way user return Doppler tracking uses the
        <span class="guilabel">SN_Doppler_Rtn</span> measurement type. The measurement
        path for SN_Doppler_Rtn originates at an on-orbit user spacecraft,
        proceeds along a return leg to a TDRSS spacecraft, and is relayed by
        the TDRS along the downlink leg to TDRSS ground terminal.</p><p>This measurement is formulated as a combination of the one-way
        average range-rate from the user spacecraft to the TDRS to the ground,
        and the two-way average range-rate from the ground to TDRS to ground.
        Please note that GMAT formulates the light-time solution in the Solar
        System Barycentric (SSB) frame and this requires modeling of the
        ET-TAI correction on the one-way user to TDRS to ground leg. GMAT will
        automatically apply ET-TAI on that leg regardless of the setting of
        the <span class="guilabel">TrackingFileSet</span>
        <span class="guilabel">UseETminusTAI</span> flag. This flag will apply as set
        by the user (True or False) to the two-way range-rate component of the
        measurement.</p><p>The GMD record format for SN_Doppler_Rtn data is shown in the
        table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - SN_Doppler_Rtn</td></tr><tr><td>3</td><td><p>Observation type index number -
                9026</p></td></tr><tr><td>4</td><td>Measurement path; the format is { UserSpacecraft,
                ReturnTDRS, RecvGroundStation }</td></tr><tr><td>5</td><td>Nominal user transmit frequency in Hertz</td></tr><tr><td>6</td><td>User transmit frequency band indicator - 0 =
                unspecified, 1 = S-band, 3 = K-band</td></tr><tr><td>7</td><td>TDRS service identifier; 'SA1', 'SA2', or 'MA'.</td></tr><tr><td>8</td><td>Tracker type for MA services (0 = legacy, 1 =
                TDRS-K)</td></tr><tr><td>9</td><td>S-Band Multiple Access Return (SMAR) downlink frequency
                ID</td></tr><tr><td>10</td><td>Doppler count interval in seconds</td></tr><tr><td>11</td><td>Doppler measurement in Hertz</td></tr></tbody></table></div><p>A sample of GMD data records for SN_Doppler_Rtn data is shown
        below.</p><pre class="programlisting">%       - 1 -                        - 2 -          3              - 4 -                        - 5 -            6     7     8   9     - 10 -            - 11 -    
23585.5184259259259259259262    SN_Doppler_Rtn    9026    { 1874    1411    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677461877950760e+10
23585.5198148148148148148142    SN_Doppler_Rtn    9026    { 1874    1411    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677458521391161e+10
23585.5212037037037037037036    SN_Doppler_Rtn    9026    { 1874    1411    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677455761216986e+10
23585.5225925925925925925927    SN_Doppler_Rtn    9026    { 1874    1411    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677453673831347e+10
23585.5239814814814814814820    SN_Doppler_Rtn    9026    { 1874    1411    48 }        3.000000000000000e+09    1    SA1    0   0   10.000000   -1.3677452330975290e+10
</pre></div><div class="refsection"><a name="TrackingDataTypes_SnDowd"></a><h4>Space Network (TDRSS) Differenced One-way Return
        Doppler</h4><p>TDRSS differenced one-way return Doppler tracking uses the
        <span class="guilabel">SN_DOWD</span> measurement type. SN_DOWD is formulated
        by differencing simultaneous SN one-way return Doppler measurements.
        Formulation of SN_DOWD requires two simultaneous SN one-way return
        tracking passes that have at least some coincident time tags. GMAT
        models each individual one-way return Doppler strand using the
        SN_Doppler_Rtn measurement model and then computes the arithmetic
        difference of the modeled one-way Doppler measurements to obtain the
        computed DOWD measurement.</p><p>The GMD record format for SN_DOWD data is shown in the table
        below. Note that the differencing of simultaneous one-way measurements
        must be performed external to GMAT during formulation of the GMD
        records.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - SN_DOWD</td></tr><tr><td>3</td><td><p>Observation type index number -
                9028</p></td></tr><tr><td>4</td><td>Measurement path; the format is { UserSpacecraft,
                ReturnTDRS1, RecvGroundStation1, ReturnTDRS2,
                RecvGroundStation2 }</td></tr><tr><td>5</td><td>ReturnTDRS1 path nominal user transmit frequency in
                Hertz</td></tr><tr><td>6</td><td>ReturnTDRS1 path user transmit frequency band indicator
                - 0 = unspecified, 1 = S-band, 3 = K-band</td></tr><tr><td>7</td><td>ReturnTDRS1 path TDRS service identifier; 'SA1', 'SA2',
                or 'MA'.</td></tr><tr><td>8</td><td>ReturnTDRS1 path tracker type for MA services (0 =
                legacy, 1 = TDRS-K)</td></tr><tr><td>9</td><td>ReturnTDRS1 path S-Band Multiple Access Return (SMAR)
                downlink frequency ID</td></tr><tr><td>10</td><td>ReturnTDRS1 path Doppler count interval in
                seconds</td></tr><tr><td>11</td><td>ReturnTDRS2 path nominal user transmit frequency in
                Hertz</td></tr><tr><td>12</td><td>ReturnTDRS2 path user transmit frequency band indicator
                - 0 = unspecified, 1 = S-band, 3 = K-band</td></tr><tr><td>13</td><td>ReturnTDRS2 path TDRS service identifier; 'SA1', 'SA2',
                or 'MA'.</td></tr><tr><td>14</td><td>ReturnTDRS2 path tracker type for MA services (0 =
                legacy, 1 = TDRS-K)</td></tr><tr><td>15</td><td>ReturnTDRS2 path S-Band Multiple Access Return (SMAR)
                downlink frequency ID</td></tr><tr><td>16</td><td>ReturnTDRS2 path Doppler count interval in
                seconds</td></tr><tr><td>17</td><td>DOWD measurement in Hertz</td></tr></tbody></table></div><p>A sample of GMD data records for SN_DOWD data is shown
        below.</p><pre class="programlisting">%       - 1 -             - 2 -     3                   - 4 -                         - 5 -            6     7    8    9     - 10 -         - 11 -          12     13  14   15     - 16 -        - 17 -    
29222.842465277779411    SN_DOWD  9028    { 7369    1411    49    1306    94 }   2.282300000000e+09    1    SA2   0    0    1.000000   2.282300000000e+09    1    SA1   0    0    1.000000 -7.6810850000E+03
29222.842476851852552    SN_DOWD  9028    { 7369    1411    49    1306    94 }   2.282300000000e+09    1    SA2   0    0    1.000000   2.282300000000e+09    1    SA1   0    0    1.000000 -7.7030220000E+03
29222.842488425925694    SN_DOWD  9028    { 7369    1411    49    1306    94 }   2.282300000000e+09    1    SA2   0    0    1.000000   2.282300000000e+09    1    SA1   0    0    1.000000 -7.7249500000E+03
29222.842499999998836    SN_DOWD  9028    { 7369    1411    49    1306    94 }   2.282300000000e+09    1    SA2   0    0    1.000000   2.282300000000e+09    1    SA1   0    0    1.000000 -7.7467780000E+03
29222.842511574075615    SN_DOWD  9028    { 7369    1411    49    1306    94 }   2.282300000000e+09    1    SA2   0    0    1.000000   2.282300000000e+09    1    SA1   0    0    1.000000 -7.7686560000E+03
</pre></div><div class="refsection"><a name="TrackingDataTypes_SnRange"></a><h4>Space Network (TDRSS) Two-way Range</h4><p>TDRSS coherent two-way range tracking uses the
        <span class="guilabel">SN_Range</span> measurement type. The measurement path
        for SN_Range originates at a TDRSS ground terminal, proceeds along an
        uplink leg to a TDRSS spacecraft, and is relayed by the TDRS along the
        forward leg to the user spacecraft. The user spacecraft receives and
        retransmits the signal along a return path to (usually the same) TDRSS
        spacecraft, which then relays the signal along the downlink path to a
        TDRSS ground terminal (usually the same as the transmit ground
        station). The GMD record format for SN_Range data is shown in the
        table below.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th align="center">Field</th><th align="center">Description</th></tr></thead><tbody><tr><td>1</td><td>Observation receive time in TAIModJulian</td></tr><tr><td>2</td><td>Observation type name - SN_Range</td></tr><tr><td>3</td><td><p>Observation type index number - 9000 =
                  SN_Range</p></td></tr><tr><td>4</td><td>Measurement path; the format is { XmitGroundStation,
                  ForwardTDRS, UserSpacecraft, ReturnTDRS, RecvGroundStation
                  }</td></tr><tr><td>5</td><td>Total round-trip range measurement in
                  kilometers</td></tr></tbody></table></div><p>A sample of GMD data records for SN_Range data is shown
        below.</p><pre class="programlisting">
%         - 1 -         - 2 -    3             - 4 -                 - 5 -
27235.503958333333333 SN_Range 9000 {47 TDRS10 1874 TDRS10 47} +1.551416168318e+05
27235.503969907407407 SN_Range 9000 {47 TDRS10 1874 TDRS10 47} +1.551309765979e+05
27235.503981481481481 SN_Range 9000 {47 TDRS10 1874 TDRS10 47} +1.551203453578e+05
27235.503993055555555 SN_Range 9000 {47 TDRS10 1874 TDRS10 47} +1.551097282079e+05
27235.504004629629629 SN_Range 9000 {47 TDRS10 1874 TDRS10 47} +1.550991239491e+05
</pre></div></div></div><div class="refsection"><a name="N2B8CF"></a><h2>References</h2><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Moyer, Theodore D., <span class="emphasis"><em>Formulation for Observed and
        Computed Values of Deep Space Network Data Types for
        Navigation,</em></span> (JPL Publication 00-7), Jet Propulsion
        Laboratory, California Institute of Technology, October 2000.</p></li><li class="listitem"><p>Shin, Dong., <span class="emphasis"><em>DSN Tracking System Data Archival
        Format</em></span>, DSN Document 820-013, Module TRK-2-34, Rev. P,
        2017.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch21s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21s03.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="NavPropagatorConfiguration.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">System&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configuration of Propagators for Orbit
    Determination</td></tr></table></div></body></html>