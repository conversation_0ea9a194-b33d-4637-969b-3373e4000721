<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Release Notes</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="prev" href="StartupFile.html" title="Startup File"><link rel="next" href="ReleaseNotesR2022a.html" title="GMAT R2022a Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Release Notes</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="StartupFile.html">Prev</a>&nbsp;</td><th align="center" width="60%">&nbsp;</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReleaseNotesR2022a.html">Next</a></td></tr></table><hr></div><div class="appendix"><div class="titlepage"><div><div><h1 class="title"><a name="ReleaseNotes"></a>Release Notes</h1></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ReleaseNotes.html#ReleaseNotesR2024a">GMAT R2025a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotes.html#N3091F">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N30934">Major Improvements and Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309D5">Other Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309E6">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309F0">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2022a.html">GMAT R2022a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30A70">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30A79">Major Improvements and Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BB9">Other Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BC4">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BE0">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2020a.html">GMAT R2020a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30C43">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30C59">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30E53">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30E8B">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30EA5">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2018a.html">GMAT R2018a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30F56">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30F6E">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30FB5">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30FEB">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N31004">Upcoming Changes in R2019a</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N31009">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2017a.html">GMAT R2017a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2017a.html#N31060">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N31097">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310A5">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310C6">GMAT Stuff</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310D6">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2016a.html">GMAT R2016a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2016a.html#N31163">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311A1">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311BE">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311CB">Development and Tools</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311E7">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2015a.html">GMAT R2015a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3125C">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N312FB">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3133A">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N31365">Development and Tools</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3138C">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2014a.html">GMAT R2014a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2014a.html#N3140F">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N31536">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N3157B">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N315A0">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2013b.html">GMAT R2013b Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2013b.html#N3162A">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N3168A">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N31721">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N31765">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2013a.html">GMAT R2013a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2013a.html#N317F0">Licensing</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N317FD">Major Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N31854">Minor Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N31894">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N318D7">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2012a.html">GMAT R2012a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31961">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N319C4">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31AA6">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31B2B">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2011a.html">GMAT R2011a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31B65">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31C8D">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31CE5">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31E62">Fixed Issues</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31E6A">Known Issues</a></span></dt></dl></dd></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ReleaseNotesR2024a"></a>GMAT R2025a Release Notes</h2></div></div></div><p>The General Mission Analysis Tool (GMAT) version R2025a was released
  in April 2025. This is the first public release since January 2023 and is
  the 15th release for the project. This is a major project release that
  includes numerous major improvements and enhancements in general
  capabilities and models, navigation and orbit determination, scripting and
  API interfaces, and visualization. Below, we give a summary of key changes
  in this release.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N3091F"></a>Milestones and Accomplishments</h3></div></div></div><p>We are excited that GMAT continues to see significant adoption for
    operational mission support.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>GMAT is being used as the primary operational tool for maneuver
        planning by the Space Weather Follow On (SWFO) flight dynamics
        team.</p></li><li class="listitem"><p>GMAT is being used as the primary operational tool for maneuver
        planning by the Roman Space Telescope (RST) flight dynamics
        team.</p></li><li class="listitem"><p>GMAT orbit determination is being used by the NASA GSFC Flight
        Dynamics Facility (FDF) to support various missions.</p></li></ul></div><p>GMAT was featured in the Spark Volume 22 | Number 4 | Fall 2024 GMAT
    Article that can be accessed <a class="link" href="https://partnerships.gsfc.nasa.gov/wp-content/uploads/The_Spark_Fall_2024_DIGITAL-S.pdf#page=10" target="_top">here</a>.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30934"></a>Major Improvements and Enhancements</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30937"></a>General Capabilities and Models</h4></div></div></div><p>The following general capabilities are new to R2025a:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Support for Moon Mean Earth (ME) frame (GMT-8201/GMT-7604).
          GMAT now allows the user to assign the Lunar Mean Earth (ME) frame
          as the Moon (Luna) body-fixed frame. The Moon ME frame will then be
          used when computing Lunar latitude and longitude or when assigning
          the BodyFixed axes for a user-defined CoordinateSystem with Luna as
          the origin. See the code snippet provided in the CelestialBody
          resource chapter of the User&rsquo;s Guide for an example of configuring
          and using the Moon ME frame.</p></li><li class="listitem"><p>Addition of Lunar fixed frame from user-supplied BPC kernel
          (GMT-8000). The definition of the lunar-fixed frame for computing
          selenodetic coordinates and the Luna BodyFixed axes can be specified
          using SPICE kernels and an appropriate SPICE frame name on the Luna
          body in GMAT. See the code snippet provided in the CelestialBody
          resource chapter of the User&rsquo;s Guide for an example of using a
          binary SPICE orientation kernel to provide the lunar orientation
          model.</p></li><li class="listitem"><p>Addition of True Equator Mean Earth (TEME) axes to the
          CoordinateSystem resource (GMT-8166). True Equator Mean Earth (TEME)
          axes are now available in the CoordinateSystem resource.</p></li><li class="listitem"><p>Expansion of CSSI space weather file option for drag
          prediction (GMT-7777/GMT-7772). The user may now specify
          &lsquo;CSSISpaceWeatherFile&rsquo; on the propagator Drag.PredictedWeatherSource
          parameter. This allows the user to model predicted drag using the
          latest available solar activity predictions. See the example
          provided in the &ldquo;CSSI Space Weather Data&rdquo; section of the Force Model
          chapter of the User&rsquo;s Guide.</p></li><li class="listitem"><p>Addition of multi-body harmonic gravity for mission design in
          production mode (GMT-8254). GMAT now accepts and models full field
          gravity for multiple bodies simultaneously. This capability is
          available for mission design and maneuver planning tasks. See the
          script Ex_R2025a_MultibodySphericalHarmonicGravity in the
          application samples folder for an example of this capability.</p></li><li class="listitem"><p>Allow users to set a negative Cd and Cr (GMT-8181). GMAT now
          allows negative values of Cd and Cr on the Spacecraft resource.
          While it is not typical or physical for these values to be negative,
          it may be useful for them to be negative to alias other unmodeled
          forces, or they may sometimes be negative if used as optimization
          parameters.</p></li><li class="listitem"><p>Additional optimizer monitoring and controls (GMT-8151).
          GMAT&rsquo;s VF13ad and SNOPT optimizers now provide optional monitoring
          of the optimization process iteration by iteration. This enables
          user defined physical tolerances on constraint values outside of the
          scaling performed by the optimizers. See also the sample script
          Ex_R2025a_MonitoredLunarTransfer.script in the samples/NeedVF13ad
          folder.</p></li><li class="listitem"><p>Additional AzimuthElevationRangeRangeRate output added to
          Contact Locator Report (GMT-7810). The new
          AzimuthElevationRangeRangeRate ContactLocator report type provides
          an output format that reports pass number, station name, azimuth,
          elevation, range, and range-rate on each line for a specified time
          step within each computed pass.</p></li><li class="listitem"><p>Analytic mass property modeling (GMT-7983). GMAT now includes
          analytic modeling of the center of mass and moments of inertia for
          spacecraft objects. Mass properties are calculated dynamically
          during mass-loss events like finite maneuvers and available for
          reporting. See the script Ex_R2025a_AnalyticMassProperties in the
          application samples folder for an example of this capability.</p></li><li class="listitem"><p>Updates to External force model plugin (GMT-8022). Use of this
          capability requires proper configuration of the GMAT Python
          interface. This adds the capability to define and use an
          acceleration in an external user-created python file. The python
          code providing the acceleration model must follow certain
          conventions. See the details provided in the External Force Model
          remarks in the ForceModel chapter of the User&rsquo;s Guide. For examples
          of this capability, see the Ex_R2025a_ExternalForceModel and
          Ex_R2025a_ExternalForceModel_NoAPI scripts in the samples
          folder.</p></li><li class="listitem"><p>GMAT&rsquo;s factory management system now allows the replacement of
          internal components with customized replacement objects provided in
          GMAT plugins (GMT-8265). Users of this capability are responsible
          for testing of any plugin-based replacements that use the new
          support.</p></li></ul></div><div class="section"><div class="titlepage"><div><div><h5 class="title"><a name="N3095E"></a>Beta- and Alpha-level Capabilities</h5></div></div></div><p>These are new features that have not yet undergone full testing
        and may have bugs. To access some of these features, you may need to
        edit the <code class="filename">bin/gmat_startup_file.txt</code> file to load
        the desired alpha plug-in and/or activate features only available in
        testing mode (set RUN_MODE = TESTING in the file). These features
        should be used with caution.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Inclusion of GMD formatting tools for binary UTDF and CCSDS
            TDM tracking data formats (GMT-7992). Python scripts are available
            in the utilities/python/navigation folder for the conversion of a
            limited selection of UTDF and CCSDS TDM data types to GMD format.
            The utdf_to_gmd.py script supports conversion of UTDF three-way
            RangeRate, TDRS one-way return Doppler, and TDRS DOWD to GMD. The
            tdm_to_gmd.py script supports conversion of CCSDS TDM
            DOPPLER_INTEGRATED to GMAT RangeRate, TRANSMIT_FREQUENCY to GMAT
            ramp records, RECEIVE_FREQUENCY to GMAT DSN_TCP, and RANGE to GMAT
            Range or DSN_SeqRange records.</p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N3096B"></a>Navigation and Orbit Determination (OD)</h4></div></div></div><p>The following navigation and orbit determination capabilities are
      new to R2025a:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>New operational data types for orbit determination
          (navigation)</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>Addition of GN/DTE three-way range-rate measurements
              (GMT-8222). This provides support for ground-based tracking
              measurements in km/sec using the RangeRate data type for which
              the transmit and receive stations are different. See the sample
              script Ex_R2025a_Estimate_RangeRangeRate3Way in the application
              samples/Navigation folder.</p></li><li class="listitem"><p>Addition of DSN Pseudo-Noise (PN) range measurements
              (GMT-8218). This provides support for DSN PN range measurements
              (DSN data type 14) in range units.</p></li><li class="listitem"><p>Addition of DSN Three-way Total Count Phase (TCP) Doppler
              measurements (GMT-8136). This provides support for averaged
              phase difference measurements (derived from DSN data type 17) in
              Hz for which the receive and transmit stations are
              different.</p></li><li class="listitem"><p>Addition of TDRS User Doppler One-way Return Doppler
              measurements (GMT-8068). This provides support for Tracking and
              Data Relay Satellite (TDRS) user one-way return Doppler
              measurements. See also the script
              Ex_R2025a_Estimate_TDRSOneWayReturnDoppler in the application
              samples/Navigation folder.</p></li><li class="listitem"><p>Addition of TDRS user Range and Doppler measurements
              (GMT-7549). This provides support for Tracking and Data Relay
              Satellite (TDRS) user two-way return range and Doppler
              measurements. See also the scripts
              Ex_R2025a_Estimate_TDRSUserTracking and
              Ex_R2025a_Estimate_TDRSAndUser in the application
              samples/Navigation folder.</p></li><li class="listitem"><p>Addition of TDRS Differenced One-Way Doppler (GMT-7467).
              This provides support for Tracking and Data Relay Satellite
              (TDRS) user differenced one-way return Doppler (DOWD)
              measurements. DOWD measurements are formulated by differencing
              the measurements from simultaneous one-way return Doppler
              tracking events.</p></li><li class="listitem"><p>Addition of TDRS Bilateration Transponder Ranging System
              (BRTS) range and Doppler (GMT-7327). This provides support for
              Tracking and Data Relay Satellite (TDRS) BRTS range and Doppler
              tracking. BRTS tracking is similar to TDRS user range and
              Doppler tracking. The BRTS transponder acts like a TDRS user
              fixed to a known location on the Earth. BRTS tracking is used to
              precisely determine the orbit of the TDRS spacecraft. See the
              script Ex_R2025a_Estimate_BRTSTracking in the application
              samples/Navigation folder.</p></li></ul></div></li><li class="listitem"><p>Addition of a JSON output option for the BLS estimator data
          file containing observation and residual data records (GMT-7899).
          This adds a JSON format data file that contains the batch estimator
          measurements, residuals, and other data to full numerical precision.
          This new capability replaces the batch estimator MatlabFile option
          and makes this capability freely available without MATLAB license
          restrictions. With this update, the MatlabFile option is deprecated,
          but is still currently available. See the sample script
          Ex_R2025a_Estimate_RangeRangeRate3Way in the application
          samples/Navigation folder.</p></li><li class="listitem"><p>K-band frequency support (GMT-6339). K-band frequencies are
          now supported for computation of the ionosphere correction on the
          ground-to-space or space-to-ground tracking legs.</p></li></ul></div><p>The following navigation and orbit determination capabilities have
      changed in R2025a:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Use of DSN_SeqRange and DSN_TCP as cross-link tracking types
          is only allowed in testing mode.</p></li><li class="listitem"><p>To provide support for three-way tracking paths, the GMD
          tracking data record format has changed for the RangeRate and
          DSN_TCP measurement types. The old format will still currently work.
          Please see the User&rsquo;s Guide Reference Guide &gt; Orbit Determination
          &gt; System &gt; Tracking Data Types for Orbit Determination for
          details on the changes.</p></li></ul></div><p>The following navigation and orbit determination capabilities are
      deprecated beginning with R2025a:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The BatchEstimator MatlabFile output option is deprecated and
          replaced by the new JSON-format DataFile option. See the sample
          script Ex_R2025a_Estimate_RangeRangeRate3Way in the application
          samples/Navigation folder for an example demonstrating generation of
          the JSON batch estimator data file.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N309A0"></a>Scripting and API Interface</h4></div></div></div><p>The following Scripting and API Interface improvements were
      implemented in R2025a:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Addition of the API Cookbook (GMT-8268). GMAT now provides use
          cases and examples of the Python API in a new document, the GMAT API
          Cookbook. See also the sample API use cases in the GMAT &ldquo;api&rdquo;
          folder.</p></li><li class="listitem"><p>Support for commands in the API (GMT-7003). The GMAT API now
          provides access to the mission control sequence using the API. See
          the sample scripts Ex_R2025a_BasicTarget and Ex_R2025a_MarsBPlane in
          the GMAT "api" folder.</p></li><li class="listitem"><p>Expansion of Python access to vector and matrix data using
          GetNumber (GMT-8083). GMAT fields and variables were accessible as
          floating-point values in the GMAT API in R2020a and R2022a. This
          capability is also available for vectors and matrices in R2025a. See
          also the examples in the GMAT &ldquo;api&rdquo; folder.</p></li><li class="listitem"><p>Enforcement of required &ldquo;three-component&rdquo; syntax for the force
          model object (GMT-7685). Force models in GMAT releases prior to
          R2025a ignored the inner delimiter for fields of the form
          &ldquo;ForceModel.InnerDelimiter.FieldName.&rdquo; The inner delimiters are now
          parsed correctly, so that the settings for multiple gravitational
          fields (e.g. the degree and order of the harmonics) can be assigned
          to the correct forces. See the script
          Ex_R2025a_MultibodySphericalHarmonicGravity in the application
          samples folder for an example of this capability.</p></li><li class="listitem"><p>Expansion of Include command to allow a string as a path
          (GMT-8075). Users can now script the names of include files into
          GMAT strings and later use those strings to specify the included
          file.</p></li><li class="listitem"><p>Change in script creation to not include the &ldquo;GMAT&rdquo; prefix by
          default (GMT-8034). The optional &ldquo;GMAT&rdquo; prefix for field
          specification and assignment is no longer the default setting in
          GMAT scripts. See the R2025a sample scripts for examples of the new
          default scripting.</p></li><li class="listitem"><p>Enhancement of Python script interface to support
          multi-dimensional arrays (GMT-7965). GMAT can now exchange
          multidimensional arrays with external Python code through the script
          Python interface.</p></li><li class="listitem"><p>Expansion of Write/Report commands to be able to append to an
          existing file (GMT-7813). A new ReportFile parameter
          AppendToExistingFile is available. This parameter may be set to True
          to allow GMAT to write to a report file in append mode instead of
          overwriting the file each time.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N309BF"></a>Optimal Control</h4></div></div></div><div class="section"><div class="titlepage"><div><div><h5 class="title"><a name="N309C2"></a>Beta Capabilities</h5></div></div></div><p>The GMAT Optimal Control capability remains a Beta feature for
        R2025a.</p></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N309C7"></a>OpenFramesInterface Visualizations</h4></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The OFI continues to be the primary 3D visualization component
        in GMAT. <span class="guilabel">OrbitView</span> will continue to be supported
        for backward compatibility purposes but will only be modified for
        critical bug fixes.</p></div><p>The complete set of documentation for OFI is available at <a class="link" href="https://gitlab.com/EmergentSpaceTechnologies/OpenFramesInterface/-/wikis/home" target="_top">the
      OFI Wiki</a></p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N309D5"></a>Other Improvements</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Removed restriction of the Array dimensions limit (GMT-8108).
        GMAT vectors and arrays previously had an upper size restriction of
        1000 elements (for vectors) and 1000 rows and columns (for arrays).
        That restriction has been removed.</p></li><li class="listitem"><p>Improvement to error message output (GMT-8104).</p></li><li class="listitem"><p>The GMAT users guide, previously called help-letter, was moved
        to &lt;GMAT-top-level-folder&gt;/docs/ and renamed to GMAT_UsersGuide
        to match other users guides.</p></li><li class="listitem"><p>The Help drop-down menu has been changed (GMT-8067). Previously,
        Help-&gt;Online Help would take you to an online user guide. This has
        been renamed to Help-&gt;User Guide and now links to a local HTML copy
        of the users guide that is packaged with GMAT.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N309E6"></a>Compatibility Changes</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Support for wxWidgets 3.2.6 (GMT-5612) and Apple Silicon
        workstations. GMAT R2022a was built using wxWidgets 3.0.4. The GUI
        components have been updated to build with wxWidgets 3.2.6. This
        update to the toolkit adds support for Apple Silicon based
        workstations running recent releases of MacOS.</p></li><li class="listitem"><p>GMAT R2025a is compatible with Python 3.10, 3.11, and 3.12
        (GMT-8277). The GMAT build packages now include bindings for Python
        3.6 through 3.12.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N309F0"></a>Fixed &amp; Known Issues</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N309F3"></a>Fixed Issues</h4></div></div></div><p>Over 50 high priority bugs were closed in this release. See the
      "<a class="link" href="https://gmat.atlassian.net/issues/?filter=15153" target="_top">Critical
      Issues Fixed in R2025a</a>" report for details. Some significant
      fixes in R2025a include:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8153" target="_top">GMT-8153</a></td><td align="left">Optimizers Falsely Claim Convergence</td></tr><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8023" target="_top">GMT-8023</a></td><td align="left">Incorrect Application of Hardware Delay
              During Crosslink Measurement</td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30A1D"></a>Known Issues</h4></div></div></div><p>See the "<a class="link" href="https://gmat.atlassian.net/issues/?filter=15155" target="_top">All Known
      Critical Issues for R2025a</a>" report for a list of all known major
      issues in R2025a. There are several known issues in this release that we
      consider to be significant:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8024" target="_top">GMT-8024</a></td><td align="left">Windows Defender pop-up may appear
                preventing users from initially opening the GMAT application
                and require additional user actions.</td></tr><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8243" target="_top">GMT-8243</a></td><td align="left">Windows help contents may appear blank and
                require additional user actions.</td></tr><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8322" target="_top">GMT-8322</a></td><td align="left">Replace OrbitView with OFI by mapping the
                OV scripting to an OFI window, and rework the ground track
                (The OrbitView and Groundtrack plot components do not work
                correctly on MacOS 14 and later.)</td></tr><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8339" target="_top">GMT-8339</a></td><td align="left">R2025a is not notarized on Mac. Some users
                may have trouble opening the r2025a GMAT GUI on Mac. Scripts
                can be run on the GMAT console through a terminal window
                without issue.</td></tr><tr><td align="left"><a class="link" href="https://gmat.atlassian.net/browse/GMT-8340" target="_top">GMT-8340</a></td><td align="left">Support the Mac Intel machines. The Mac
                systems at the moment can either be using an Intel based
                chipset or a Silcon (Apple) based chipset. Currently the GMAT
                development team is supporting Silcon based chipset
                builds.</td></tr></tbody></table></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="StartupFile.html">Prev</a>&nbsp;</td><td align="center" width="20%">&nbsp;</td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReleaseNotesR2022a.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Startup File&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;GMAT R2022a Release Notes</td></tr></table></div></body></html>