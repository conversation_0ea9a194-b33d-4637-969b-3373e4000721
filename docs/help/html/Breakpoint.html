<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Breakpoint</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s02.html" title="Commands"><link rel="prev" href="BeginScript.html" title="BeginScript"><link rel="next" href="CallGmatFunction.html" title="CallGmatFunction"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Breakpoint</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="BeginScript.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="CallGmatFunction.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Breakpoint"></a><div class="titlepage"></div><a name="N2C798" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Breakpoint</span></h2><p>Breakpoint &mdash; 
      Pause a run and let the user examine the state of objects.
    </p></div><div class="refsection"><a name="N2C7A9"></a><h2>Description</h2><p>The Breakpoint command causes GMAT to pause the current run at 
      a set point in the mission sequence.  When the command triggers, the 
      GMAT GUI opens an object inspection window.  The user can examine the
      current state of any object in the run from that window.  The run can 
      then be stepped forward command by command and the objects examined to
      see the effect of the command that was executed.  The user can also
      resume execution of the run or terminate the run from the object 
      inspector window.        
    </p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="BeginScript.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="CallGmatFunction.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">BeginScript&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;CallGmatFunction</td></tr></table></div></body></html>