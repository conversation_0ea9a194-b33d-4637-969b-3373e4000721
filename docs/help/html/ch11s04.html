<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure and Run the Eclipse Locator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_EventLocation.html" title="Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts"><link rel="prev" href="ch11s03.html" title="Configure GMAT for Event Location"><link rel="next" href="ch11s05.html" title="Configure and Run the Contact Locator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure and Run the Eclipse Locator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch11s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch11s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13B54"></a>Configure and Run the Eclipse Locator</h2></div></div></div><p>Now we are ready to search for eclipses in our mission. We do this
    by creating an EclipseLocator resource that holds the search
    configuration. Then we can perform a search by running the FindEvents
    command, but GMAT does this automatically at the end of the mission unless
    you configure it otherwise. In this case, we will use the automatic
    option.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13B59"></a>Create and Configure the EclipseLocator</h3></div></div></div><p>First we create the <span class="guilabel">EclipseLocator</span>:</p><div class="procedure"><ul class="procedure"><li class="step"><p>On the <span class="guilabel">Resources</span> tab, right-click the
          <span class="guilabel">Event Locators</span> folder, point to
          <span class="guilabel">Add</span>, and click
          <span class="guilabel">EclipseLocator</span>.</p></li></ul></div><p>This will result in a new resource called
      <span class="guilabel">EclipseLocator1</span>.</p><div class="figure"><a name="N13B76"></a><p class="title"><b>Figure&nbsp;11.4.&nbsp;Location of EclipseLocator</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_EventLocation_CreateEclipseLocator.png" align="middle" height="125" alt="Location of EclipseLocator"></td></tr></table></div></div></div></div><br class="figure-break"><p>Next, we need to configure the new resource for our
      mission:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">EclipseLocator1</span> to edit the
          configuration.</p><p>Note the following default settings:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><span class="guilabel">Spacecraft</span> is set to
              <span class="guilabel">DefaultSC</span>, the name of our
              spacecraft.</p></li><li class="listitem"><p><span class="guilabel">OccultingBodies</span> is set to
              <span class="guilabel">Earth</span> and <span class="guilabel">Luna</span>. These
              are the two bodies that will be searched for eclipses.</p></li><li class="listitem"><p><span class="guilabel">EclipseTypes</span> is set to search for all
              eclipse types (umbra or total, penumbra or partial, and antumbra
              or annular)</p></li><li class="listitem"><p><span class="guilabel">Run Mode</span> is set to
              <span class="guilabel">Automatic</span> mode, which means the eclipse
              search will be run automatically at the end of the
              mission.</p></li><li class="listitem"><p><span class="guilabel">Use Entire Interval</span> is checked, so
              the entire mission time span will be searched.</p></li><li class="listitem"><p>Light-time delay and stellar aberration are both enabled,
              so eclipse times will be adjusted appropriately.</p></li><li class="listitem"><p><span class="guilabel">Step size</span> is set to 10 s. This is the
              minimum-duration eclipse (or gap between eclipses) that this
              locator is guaranteed to find.</p></li></ul></div></li><li class="step"><p>Click <span class="guilabel">OK</span> to accept the default settings.
          They are fine for our purposes.</p></li></ol></div><p>The final configuration should match the following
      screenshot.</p><div class="figure"><a name="N13BC3"></a><p class="title"><b>Figure&nbsp;11.5.&nbsp;EclipseLocator Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_EventLocation_EclipseLocator.png" align="middle" height="461" alt="EclipseLocator Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13BCF"></a>Run the Mission</h3></div></div></div><p>Now it's time to run the mission and look at the results.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>) to run the mission.</p><p>The eclipse search will take a few seconds. As it progresses,
          you'll see the following message in the message window at the bottom
          of the screen:</p><pre class="programlisting">Finding events for EclipseLocator EclipseLocator1 ...
Celestial body properties are provided by SPICE kernels.</pre></li><li class="step"><p>When the run is complete, click the
          <span class="guilabel">Output</span> tab to view the available output.</p></li><li class="step"><p>Double-click <span class="guilabel">EclipseLocator1</span> to view the
          eclipse report.</p></li></ol></div><p>You'll see a report that looks similar to this:</p><div class="figure"><a name="N13BF5"></a><p class="title"><b>Figure&nbsp;11.6.&nbsp;EclipseLocator Report</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_EventLocation_EclipseReport.png" align="middle" height="359" alt="EclipseLocator Report"></td></tr></table></div></div></div></div><p><br class="figure-break"></p><p>Three eclipses were found, all part of a single "total" eclipse
      event totalling about 35 minutes. A total event consists of all adjacent
      and overlapping portions, such as penumbra eclipses occuring adjacent to
      umbra eclipses as in this case.</p><div class="procedure"><ul class="procedure"><li class="step"><p>Click <span class="guilabel">Close</span> to close the report. The
          report text is still available as
          <code class="filename">EclipseLocator1.txt</code> in the GMAT
          <code class="filename">output</code> folder.</p></li></ul></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch11s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_EventLocation.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch11s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure GMAT for Event Location&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure and Run the Contact Locator</td></tr></table></div></body></html>