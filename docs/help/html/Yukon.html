<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Yukon</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch20.html#N25D9B" title="Resources"><link rel="prev" href="VF13ad.html" title="VF13ad"><link rel="next" href="ch20s02.html" title="Commands"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Yukon</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="VF13ad.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch20s02.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Yukon"></a><div class="titlepage"></div><a name="N268B6" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Yukon</span></h2><p>Yukon &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    Yukon</p></div><div class="refsection"><a name="N268C7"></a><h2>Description</h2><p>The <span class="guilabel">Yukon</span> optimizer is a SQP-based Non-Linear
    Programming solver that uses an active-set line search algorithm method
    and a modified BFGS update to approximate the Hessian matrix.</p><p><span class="guilabel">Yukon</span> performs nonlinear constrained
    optimization and supports both linear and nonlinear constraints. To use
    this solver, you must configure the solver options including convergence
    criteria, maximum iterations, and gradient computation method. In the
    mission sequence, you implement an optimizer such as Yukon by using an
    <span class="guilabel">Optimize</span>/<span class="guilabel">EndOptimize</span> sequence.
    Within this sequence, you define optimization variables by using the
    <span class="guilabel">Vary</span> command, and define cost and constraints by
    using the <span class="guilabel">Minimize</span> and
    <span class="guilabel">NonlinearConstraint</span> commands respectively.</p><p>This resource cannot be modified in the Mission Sequence.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="FminconOptimizer.html" title="FminconOptimizer"><span class="refentrytitle">FminconOptimizer</span></a>, <a class="xref" href="VF13ad.html" title="VF13ad"><span class="refentrytitle">VF13ad</span></a>, <a class="xref" href="Optimize.html" title="Optimize"><span class="refentrytitle">Optimize</span></a>,<a class="xref" href="Vary.html" title="Vary"><span class="refentrytitle">Vary</span></a>, <a class="xref" href="NonlinearConstraint.html" title="NonlinearConstraint"><span class="refentrytitle">NonlinearConstraint</span></a>, <a class="xref" href="Minimize.html" title="Minimize"><span class="refentrytitle">Minimize</span></a></p></div><div class="refsection"><a name="N268FA"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="26%"><col width="74%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">FeasibilityTolerance</span></td><td><p>The tolerance on the maximum non-dimensional
            constraint violation that must be satisfied for convergence.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1e-4</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FunctionTolerance</span></td><td><p>The tolerance on the change in the cost function
            value to trigger convergence. If the change in the cost function
            from one iteration to the next is less than FunctionTolerance, and
            the maximum (non-dimensional) constraint violation is less than
            <span class="guilabel">OptimalityTolerance</span>, then the algorithm
            terminates. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1e-4</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HessianUpdateMethod</span></td><td><p>The method used to approximate the Hessian of the
            Lagrangian. These methods are based on the BFGS but are more
            robust to possible numerical issues that can occur using BFGS
            updates with finite precision arithmetic. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">DampedBFGS</span>,
                    <span class="guilabel">SelfScaledBFGS</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">SelfScaledBFGS</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MaximumElasticWeight</span></td><td><p>The maximum elastic weight allowed when attempting to
            minimize constraint infeasiblities if the problem appears to be
            infeasible. When possible infeasibility is detected, the elastic
            weight is initialized to zero, and increases by a factor of 10 for
            every failed iterations, until the
            <span class="guilabel">MaximumElasticWeight</span> setting is reached and
            the algorithm terminates. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>10000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MaximumFunctionEvals</span></td><td><p> Number of passes through the control sequence before
            termination. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MaximumIterations</span></td><td><p> The maximum number of optimizer iterations allowed
            before termination. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>200</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OptimalityTolerance</span></td><td><p>The tolerance on the change in the gradient of the
            Lagrangian to trigger convergence. If the gradient of the
            Lagrangian is less than <span class="guilabel">FeasibilityTolerance</span>
            and the maximum (non-dimensional) constraint violation is less
            than <span class="guilabel">Optimality Tolerance</span>, then the algorithm
            terminates.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1e-4</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ReportFile</span></td><td><p> Contains the path and file name of the report file
            containing iteration and convergence information. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any user-defined file name</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">YukonOptimizer.data</code>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ReportStyle</span></td><td><p>Determines the amount and type of data written to the
            message window and to the report specified by field
            <span class="guilabel">ReportFile</span> for each iteration of the solver
            (when <span class="guilabel">ShowProgress</span> is true). Currently, the
            <span class="guilabel">Normal</span>, <span class="guilabel">Debug</span>, and
            <span class="guilabel">Concise</span> options contain the same information:
            the values for the control variables, the constraints, and the
            objective function. In addition to this information, the
            <span class="guilabel">Verbose</span> option also contains values of the
            optimizer-scaled control variables and the constraint Jacobian.
            The constraint Jacobian values are useful when scaling
            optimization problems. See the Remarks section for more
            information. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Normal</span>,
                    <span class="guilabel">Concise</span>,
                    <span class="guilabel">Verbose</span>,
                    <span class="guilabel">Debug</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Normal</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShowProgress</span></td><td><p>Determines whether data pertaining to iterations of
            the solver is both displayed in the message window and written to
            the report specified by the <span class="guilabel">ReportFile</span> field.
            When <span class="guilabel">ShowProgress</span> is true, the amount of
            information contained in the message window and written in the
            report is controlled by the <span class="guilabel">ReportStyle</span>
            field. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><code class="literal">true</code>,
                    <code class="literal">false</code></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">true</code>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseCentralDifferences</span></td><td><p>Allows you to choose whether or not to use central
            differencing for numerically determining the derivative. For the
            default, 'false' value of this field, forward differencing is used
            to calculate the derivative. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><code class="literal">true</code>,
                    <code class="literal">false</code></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">false</code>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N26B4A"></a><h2>GUI</h2><p>The <span class="guilabel">Yukon</span> dialog box allows you to specify
    properties of a <span class="guilabel">Yukon</span> such as as maximum iterations,
    cost function tolerance, feasibility tolerance, choice of reporting
    options, and choice of whether or not to use the central difference
    derivative method.</p><p>To create a <span class="guilabel">Yukon</span> resource, navigate to the
    <span class="guilabel">Resources</span> tree, expand the
    <span class="guilabel">Solvers</span> folder, highlight and then right-click on the
    <span class="guilabel">Optimizers</span> sub-folder, point to <span class="guilabel">Add
    </span>and then select <span class="guilabel">Yukon</span>. This will create a
    new <span class="guilabel">Yukon</span> resource, Yukon1. Double-click on Yukon1 to
    bring up the <span class="guilabel">Yukon</span> dialog box shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_YukonOptimizer_GUI.png" align="middle" height="495"></td></tr></table></div></div></div><div class="refsection"><a name="N26B78"></a><h2>Remarks</h2><div class="refsection"><a name="N26B7B"></a><h3>Yukon Optimizer Availability</h3><p>This optimizer is distributed in the public and internal
      distribution.</p></div><div class="refsection"><a name="N26B80"></a><h3>Resource and Command Interactions</h3><p>The <span class="guilabel">Yukon</span> resource can only be used in the
      context of optimization-type commands. Please see the documentation for
      <span class="guilabel">Optimize</span>, <span class="guilabel">Vary</span>,
      <span class="guilabel">NonlinearConstraint</span>, and
      <span class="guilabel">Minimize</span> for more information and worked
      examples.</p></div></div><div class="refsection"><a name="N26B94"></a><h2>Examples</h2><div class="informalexample"><p>Create a <span class="guilabel">Yukon</span> resource named Yukon1.</p><pre class="programlisting"><code class="code">Create Yukon Yukon1
GMAT Yukon1.ShowProgress = true;
GMAT Yukon1.ReportStyle = Normal;
GMAT Yukon1.ReportFile = 'YukonYukon1.data';
GMAT Yukon1.MaximumIterations = 200;
GMAT Yukon1.UseCentralDifferences = false;
GMAT Yukon1.FeasibilityTolerance = 0.0001;
GMAT Yukon1.HessianUpdateMethod = SelfScaledBFGS;
GMAT Yukon1.MaximumFunctionEvals = 1000;
GMAT Yukon1.OptimalityTolerance = 0.0001;
GMAT Yukon1.FunctionTolerance = 0.0001;
GMAT Yukon1.MaximumElasticWeight = 10000;</code>      </pre></div><div class="informalexample"><p>Below is a simple optimization example with a nonlinear constraint
      configured to use the Yukon optimizer.</p><pre class="programlisting">%------ Create and Setup the Optimizer
Create Yukon NLPSolver;

%------ Arrays, Variables, Strings
Create Variable X1 X2 J G;

%------ Mission Sequence
BeginMissionSequence;

Optimize NLPSolver {SolveMode = Solve, ExitMode = DiscardAndContinue};
   
   %  Vary the independent variables
   Vary 'Vary X1' NLPSolver(X1 = 0, {Perturbation = 0.0000001});
   Vary 'Vary X2' NLPSolver(X2 = 0, {Perturbation = 0.0000001});
   
   %  The cost function and Minimize command
   GMAT 'Compute Cost (J)' J = ( X1 - 2 )^2 + ( X2 - 2 )^2;
   Minimize 'Minimize Cost (J)' NLPSolver(J);
   
   %  Calculate constraint and use NonLinearConstraint command
   GMAT 'Compute Constraint (G)' G = X2 + X1;
   NonlinearConstraint 'G = 8' NLPSolver(G =8);

EndOptimize;  % For optimizer NLPSolver
    </pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="VF13ad.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch20.html#N25D9B">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch20s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">VF13ad&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Commands</td></tr></table></div></body></html>