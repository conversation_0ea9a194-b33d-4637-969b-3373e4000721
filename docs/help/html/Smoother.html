<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Smoother</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="Simulator.html" title="Simulator"><link rel="next" href="SpacecraftNavigation.html" title="Spacecraft Navigation"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Smoother</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Simulator.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SpacecraftNavigation.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Smoother"></a><div class="titlepage"></div><a name="N29BBA" class="indexterm"></a><a name="N29BBD" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Smoother</span></h2><p>Smoother &mdash; A backwards filter yielding an improved estimate of states
    through weighted combination of forward and reverse sequential
    estimation.</p></div><div class="refsection"><a name="N29BD0"></a><h2>Description</h2><p>A fixed-interval backward-running sequential estimator utilizing the
    Fraser-Potter algorithm for fusion of forward and reverse estimated
    states.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="ExtendedKalmanFilter.html" title="ExtendedKalmanFilter"><span class="refentrytitle">ExtendedKalmanFilter</span></a>, <a class="xref" href="RunSmoother.html" title="RunSmoother"><span class="refentrytitle">RunSmoother</span></a></p></div><div class="refsection"><a name="N29BDF"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">AddPredictToMatlabFile</span></td><td><p>Set to True to include the data from the Smoother
            prediction span in the MATLAB file. Predicted data is only
            generated if <span class="guilabel">PredictTimeSpan</span> is non-zero.
            Setting this parameter to True will add the predicted states,
            covariance, process noise, and state transition matrix to the
            Smoother MATLAB data file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>True/False</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True or False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">False</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DelayRectifyTimeSpan</span></td><td><p>Defines a period of time, beginning from warm start
            or initialization, for which the reference trajectory is generated
            from the a-priori or initial state. As always, any measurements
            are used to update the estimated state, but the measurements will
            not be applied to the reference trajectory used for linearization.
            After the delay rectify time span has elapsed, the a-priori state
            is discarded, and the current filter estimated state is used as
            the reference trajectory for linearization, in accordance with
            normal operation of an extended Kalman filter. </p><p>This
            parameter applies to the backward filter when using the
            Fraser-Potter smoothing algorithm. A similar parameter exists for
            the forward filter on the
            <span class="guilabel">ExtendedKalmanFilter</span> resource. See the
            Remarks below for further notes on this
            parameter.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Seconds</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Filter</span></td><td><p>The forward-run <span class="guilabel">Filter</span> instance
            containing the states and covariances to process in the
            smoother.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>User defined instance of an
                    <span class="guilabel">ExtendedKalmanFilter</span> resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MatlabFile</span></td><td><p>File name for the output MATLAB data file. Leaving
            this parameter unset means that no MATLAB file will be generated.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing a valid file name.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MeasDeweightingCoefficient</span></td><td><p>Coefficient used for Measurement Underweighting for
            the backward propagating EKF used by the smoother. Using the
            default value, Measurement Underweighting is effectively turned
            off. We use Lear&rsquo;s Measurement Underweighting method described by
            Eq. (4.36) in Reference 1. See the Remarks for the
            <span class="guilabel">ExtendedKalmanFilter</span> resource for additional
            details. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">0.0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MeasDeweightingSigmaThreshold</span></td><td><p>1-sigma RSS position threshold used for Measurement
            Underweighting processing. Measurement Underweighting is only
            performed if the 1-sigma RSS position threshold, derived from the
            error covariance matrix, is greater than this threshold AND the
            <span class="guilabel">MeasDeweightingCoefficient</span> is greater than 0.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1.0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PredictTimeSpan</span></td><td><p>Time span in seconds for the smoother ephemeris
            prediction after processing the last
            measurement.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Seconds</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ReportFile</span></td><td><p>Smoother output report file name.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing a valid file name</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">'Smoother' + instancename +
                    '.data'</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ReportStyle</span></td><td><p>Specifies the level of report output from the
            smoother. If Normal mode is selected, only the smoother report
            file is generated. If Verbose mode is selected, an additional
            output report file will be generated from the smoother backward
            filter run. Selection of Verbose mode requires the user to assign
            RUN_MODE = Testing in the GMAT startup file. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Normal, Verbose</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Normal</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N29D9B"></a><h2>Remarks</h2><div class="refsection"><a name="N29D9E"></a><h3>Fraser-Potter Smoother</h3><p>The <span class="guilabel">Smoother</span> resource implements a
      Fraser-Potter (FP) fixed-interval smoother as described in Reference 1
      and Reference 2. The FP smoother first runs a filter backward (from the
      latest to earliest measurement) over the same span as the filter
      "forward" run. The backward filter initial state is set to the forward
      filter final state. The backward filter initial covariance is set to the
      diagonal elements of the forward filter end covariance, with the
      position and velocity variances multiplied by a scale factor of 1e+10
      and other estimated parameter variances multiplied by a factor of 1e+4.
      The off-diagonal elements of the backward filter initial covariance are
      zeroed out. The smoother estimate at each time or measurement update is
      formulated as a weighted linear combination of the forward filter and
      backward smoother estimates. See the references for further mathematical
      details.</p><p>The inflation of the backwards filter initial covariance makes it
      susceptible to divergence and other numerical errors during its
      convergence span. If the smoother fails, it is strongly recommended to
      attempt another smoother run with
      <span class="guilabel">DelayRecitfyTimeSpan</span> set to a time span that would
      normally allow the filter to converge. In Normal mode, the smoother
      output report file does not include the results of the backward filter
      pass, but you can set the Smoother <span class="guilabel">ReportStyle</span> to
      <span class="guilabel">Verbose</span> (requires setting RUN_MODE = TESTING in
      gmat_startup_file.txt) to produce a report of the backward filter run to
      assist troubleshooting.</p></div><div class="refsection"><a name="N29DB1"></a><h3>Navigation Requires Use of Fixed Step Numerical
      Integration</h3><p>GMAT navigation requires use of fixed stepped propagation. The
      <span class="guilabel">ExtendedKalmanFilter</span> resource has a
      <span class="guilabel">Propagator</span> field containing the name of the
      <span class="guilabel">Propagator</span> resource that will be used during the
      estimation process. As shown in the <span class="guilabel">Note</span> below,
      there are some hard restrictions on the choice of error control
      specified for the <span class="guilabel">ForceModel</span> resource associated
      with your propagator.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The <span class="guilabel">ErrorControl</span> parameter specified for
        the <span class="guilabel">ForceModel</span> resource associated with the
        <span class="guilabel">ExtendedKalmanFilter</span>
        <span class="guilabel">Propagator</span> must be set to '<code class="code">None</code>.' Of
        course, when using fixed step control, the user must choose a step
        size, as given by the <span class="guilabel">Propagator</span>
        <span class="guilabel">InitialStepSize</span> field, for the chosen orbit
        regime and force profile, that yields the desired accuracy.</p></div></div><div class="refsection"><a name="N29DDD"></a><h3>Smoother MATLAB Data File</h3><p>If MATLAB is installed and properly configured to interface with
      GMAT (see <a class="xref" href="MatlabInterface.html" title="MATLAB Interface"><span class="refentrytitle">MATLAB Interface</span></a>), the user may generate a
      mat-file containing useful analysis data from the
      <span class="guilabel">Smoother</span> run. This option is enabled by specifying
      a path and filename for the output file on the
      <span class="guilabel">MatlabFile</span> field of the configured
      <span class="guilabel">Smoother</span> resource. Except as noted in the tables
      below, the units for data in the mat-file are the same as those in the
      Smoother output file; seconds, km, km/sec, DSN Range Units, Hz, and
      degrees.</p><p>The Smoother MATLAB file <span class="guilabel">EstimationConfig</span>,
      and <span class="guilabel">Observed</span> structures are identical to those from
      the Filter resource and are described in the Filter resource
      documentation (see <a class="xref" href="ExtendedKalmanFilter.html#ExtendedKalmanFilter_MatlabFile" title="Kalman Filter MATLAB Data File">the section called &ldquo;Kalman Filter MATLAB Data File&rdquo;</a>).
      The output from the smoother backward filter run is stored in the
      <span class="guilabel">BackwardComputed</span> and
      <span class="guilabel">BackwardFilter</span> structures of the smoother output
      file. These have the same structure and contents as the
      <span class="guilabel">Computed</span> and <span class="guilabel">Filter</span> structures
      in the forward filter MATLAB file, and again are described in the Filter
      resource documentation.</p><p>The smoother MATLAB file <span class="guilabel">Computed</span> structure
      differs slightly from its Filter counterpart. Contents of the
      smoother<span class="guilabel"> Computed</span> structure are described in the
      table below.</p><div class="informaltable"><table border="1"><colgroup><col align="left" width="33%"><col align="left" width="67%"></colgroup><thead><tr><th align="center">Variable</th><th align="center">Description</th></tr></thead><tbody><tr><td align="left">Elevation</td><td align="left">Computed elevation in degrees at the measurement epoch.
              Does not apply (set to 0) for GPS_PosVec data.</td></tr><tr><td align="left">IonosphericCorrection</td><td align="left">The magnitude of the ionospheric measurement correction.
              Units are the same as the measurement. See note below regarding
              media corrections for X/Y angle measurements.</td></tr><tr><td align="left">Measurement</td><td align="left">Computed measurements. For GPS_PosVec, each cell holds a
              1x3 column vector of X, Y, Z computed measurements.</td></tr><tr><td align="left">MeasurementEditFlag</td><td align="left">The string observation edit flag. 'N' indicates
              unedited/accepted observations.</td></tr><tr><td align="left">MeasurementNumber</td><td align="left">Measurement record number.</td></tr><tr><td align="left">MeasurementPartials</td><td align="left">A cell array of matrices. Each member is a matrix of the
              partial derivatives of the measurement with respect to each
              element of the state. The partials are taken with respect to the
              element type selected on the Spacecraft
              <span class="guilabel">SolveFors</span> field. For example, if the user
              has chosen to estimate the KeplerianState, the partials are with
              respect to the Keplerian elements. The order of elements matches
              that given in the EstimationConfig state names. For GPS_PosVec
              data, each cell holds a 3xN matrix, where N is the number of
              estimated states. Each row contains the partials with respect to
              the X, Y, and Z GPS_PosVec measurement in order.</td></tr><tr><td align="left">PreUpdateCovariance</td><td align="left">Smoother state covariance prior to measurement
              update.</td></tr><tr><td align="left">PreUpdateCovarianceVNB</td><td align="left">Smoother state covariance prior to measurement update, in
              the VNB reference frame.</td></tr><tr><td align="left">Residual</td><td align="left">Smoother state residual prior to measurement
              update.</td></tr><tr><td align="left">PreUpdateState</td><td align="left">Smoother state prior to measurement update.</td></tr><tr><td align="left">ScaledResidual</td><td align="left">A unitless value representing the raw residual divided by
              the sum of state noise (appropriately transformed) and
              measurement noise. Schematically, Scaled Residual = Raw Residual
              / (HPH' + R).</td></tr><tr><td align="left">TroposphericCorrection</td><td align="left">The magnitude of the tropospheric measurement correction.
              Units are the same as the measurement. See note below regarding
              media corrections for X/Y angle measurements.</td></tr></tbody></table></div><p>Contents of the <span class="guilabel">Smoother</span> structure are
      described in the table below.</p><div class="informaltable"><table border="1"><colgroup><col align="left" width="33%"><col align="left" width="67%"></colgroup><thead><tr><th align="center">Variable</th><th align="center">Description</th></tr></thead><tbody><tr><td align="left">Covariance</td><td align="left">The smoother state covariance (Cartesian), after the
              measurement or time update.</td></tr><tr><td align="left">CovarianceVNB</td><td align="left">The smoother state covariance (VNB coordinates), after
              the measurement or time update.</td></tr><tr><td align="left">EpochTAI</td><td align="left">The TAI epoch of each smoother record. Each member is a
              column vector where the first row is the epoch as a MATLAB
              datenum and the second row is the epoch as a GMAT TAIModJulian
              date.</td></tr><tr><td align="left">EpochUTC</td><td align="left">The UTC epoch of each smoother record. Each member is a
              column vector where the first row is the epoch as a MATLAB
              datenum and the second row is the epoch as a GMAT UTCModJulian
              date.</td></tr><tr><td align="left">MeasurementNumber</td><td align="left">Measurement record number. Set to NaN for initial or time
              update steps.</td></tr><tr><td align="left">State</td><td align="left">Smoother state after the measurement or time
              update.</td></tr><tr><td align="left">UpdateType</td><td align="left">Filter update mode (Initial, Time, or
              Measurement).</td></tr></tbody></table></div></div></div><div class="refsection"><a name="N29E96"></a><h2>Examples</h2><div class="informalexample"><p>Below is an example of a configured <span class="guilabel">Smoother</span>
      instance. In this example, <span class="guilabel">EKF</span> is an instance of an
      <span class="guilabel">ExtendedKalmanFilter</span>.</p><pre class="programlisting">Create Smoother SMO;

SMO.Filter          = EKF;
SMO.PredictTimeSpan = 86400.;
SMO.ReportFile      = 'smoother_run.txt';
SMO.MatlabFile      = 'smoother_run.mat';

BeginMissionSequence;

RunSmoother SMO;
</pre></div></div><div class="refsection"><a name="N29EA7"></a><h2>References</h2><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Carpenter, Russell and Chris D'Souza. <span class="emphasis"><em>Navigation
        Filter Best Practices</em></span>. Technical Report TP-2018-219822,
        NASA, April 2018.</p></li><li class="listitem"><p>D. C. Fraser and J. E. Potter, "The Optimum Linear Smoother as a
        Combination of Two Optimum Linear Filters", <span class="emphasis"><em>IEEE Trans.
        Automat. Contr.</em></span>, vol. AC-14, no. 4, pp. 387-390, Aug.
        1969.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Simulator.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SpacecraftNavigation.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Simulator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Spacecraft Navigation</td></tr></table></div></body></html>