<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>General Mission Analysis Tool (GMAT)</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="next" href="Preface.html" title="Documentation Overview"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">General Mission Analysis Tool (GMAT)</th></tr><tr><td align="left" width="20%">&nbsp;</td><th align="center" width="60%">&nbsp;</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Preface.html">Next</a></td></tr></table><hr></div><div class="book"><div class="titlepage"><div><div><h1 class="title"><a name="N10001"></a>General Mission Analysis Tool (GMAT)</h1></div><div><h2 class="subtitle">
      <span class="phrase">User Guide</span>
      
    </h2></div><div><div class="author"><h3 class="author">The GMAT Development Team</h3></div></div></div><hr></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="preface"><a href="Preface.html">Documentation Overview</a></span></dt><dd><dl><dt><span class="section"><a href="Preface.html#Preface_UsingGmat">Using GMAT</a></span></dt><dt><span class="section"><a href="pr01s02.html">Tutorials</a></span></dt><dt><span class="section"><a href="pr01s03.html">Reference Guide</a></span></dt></dl></dd><dt><span class="part"><a href="UsingGmat.html">Using GMAT</a></span></dt><dd><dl><dt><span class="chapter"><a href="WelcomeToGmat.html">1. Welcome to GMAT</a></span></dt><dd><dl><dt><span class="section"><a href="WelcomeToGmat.html#N100F3">Features Overview</a></span></dt><dd><dl><dt><span class="section"><a href="WelcomeToGmat.html#N100F8">Dynamics and Environment Modeling</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10117">Plotting, Reporting and Product Generation</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N1012A">Optimization and Targeting</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10140">Programming Infrastructure</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10159">Orbit Determination Infrastructure</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N1017E">Interfaces</a></span></dt></dl></dd><dt><span class="section"><a href="ch01s02.html">Heritage</a></span></dt><dt><span class="section"><a href="ch01s03.html">Licensing</a></span></dt><dt><span class="section"><a href="ch01s04.html">Platform Support</a></span></dt><dt><span class="section"><a href="ch01s05.html">Component Status</a></span></dt><dt><span class="section"><a href="ch01s06.html">Contributors</a></span></dt></dl></dd><dt><span class="chapter"><a href="GettingStarted.html">2. Getting Started</a></span></dt><dd><dl><dt><span class="section"><a href="GettingStarted.html#Installation">Installation</a></span></dt><dt><span class="section"><a href="RunningGmat.html">Running GMAT</a></span></dt><dd><dl><dt><span class="section"><a href="RunningGmat.html#N102DD">Starting GMAT</a></span></dt><dt><span class="section"><a href="RunningGmat.html#N10300">Exiting GMAT</a></span></dt></dl></dd><dt><span class="section"><a href="SampleMissions.html">Sample Missions</a></span></dt><dt><span class="section"><a href="GettingHelp.html">Getting Help</a></span></dt></dl></dd><dt><span class="chapter"><a href="TourOfGmat.html">3. Tour of GMAT</a></span></dt><dd><dl><dt><span class="section"><a href="TourOfGmat.html#UserInterfaces">User Interfaces Overview</a></span></dt><dd><dl><dt><span class="section"><a href="TourOfGmat.html#N10377">GUI Overview</a></span></dt><dt><span class="section"><a href="TourOfGmat.html#N10468">Script Interface Overview</a></span></dt><dt><span class="section"><a href="TourOfGmat.html#N104C4">GUI/Script Interface Interactions and Rules</a></span></dt></dl></dd><dt><span class="section"><a href="ResourceTree.html">Resources Tree</a></span></dt><dd><dl><dt><span class="section"><a href="ResourceTree.html#N105A3">Organization</a></span></dt><dt><span class="section"><a href="ResourceTree.html#N105B4">Folder Menus</a></span></dt><dt><span class="section"><a href="ResourceTree.html#N1060F">Resource Menus</a></span></dt></dl></dd><dt><span class="section"><a href="MissionTree.html">Mission Tree</a></span></dt><dd><dl><dt><span class="section"><a href="MissionTree.html#N106F4">Mission Tree Display</a></span></dt><dt><span class="section"><a href="MissionTree.html#N10746">View Filters Toolbar</a></span></dt><dt><span class="section"><a href="MissionTree.html#N107EB">Mission Sequence Menu</a></span></dt><dt><span class="section"><a href="MissionTree.html#N10894">Command Menu</a></span></dt><dt><span class="section"><a href="MissionTree.html#MissionTree_Docking">Docking/Undocking/Placement</a></span></dt></dl></dd><dt><span class="section"><a href="CommandSummary.html">Command Summary</a></span></dt><dd><dl><dt><span class="section"><a href="CommandSummary.html#N109BC">Data Availability</a></span></dt><dt><span class="section"><a href="CommandSummary.html#N109CA">Data Contents</a></span></dt><dt><span class="section"><a href="CommandSummary.html#N109E5">Supported Commands</a></span></dt><dt><span class="section"><a href="CommandSummary.html#N109EA">Coordinate Systems</a></span></dt></dl></dd><dt><span class="section"><a href="Output.html">Output Tree</a></span></dt><dt><span class="section"><a href="ScriptEditor.html">Script Editor</a></span></dt><dd><dl><dt><span class="section"><a href="ScriptEditor.html#N10A65">Active Script</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10A7B">GUI/Script Synchronization</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10AF9">Scripts List</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10B2B">Edit Window</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10BA9">Find and Replace</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10C02">File Controls</a></span></dt><dt><span class="section"><a href="ScriptEditor.html#N10C12">Save Status Indicator</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ConfiguringGmat.html">4. Configuring GMAT</a></span></dt><dd><dl><dt><span class="section"><a href="ConfiguringGmat.html#N10C22">File Structure</a></span></dt><dd><dl><dt><span class="section"><a href="ConfiguringGmat.html#N10C3A"><code class="filename">api</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10C46"><code class="filename">bin</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10C70"><code class="filename">data</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10CFB"><code class="filename">docs</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D07"><code class="filename">extras</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D10"><code class="filename">matlab</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D1C"><code class="filename">output</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D25"><code class="filename">plugins</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D31"><code class="filename">samples</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D40"><code class="filename">userfunctions</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D49"><code class="filename">userincludes</code></a></span></dt><dt><span class="section"><a href="ConfiguringGmat.html#N10D59"><code class="filename">utilities</code></a></span></dt></dl></dd><dt><span class="section"><a href="ConfiguringGmat_DataFiles.html">Configuring Data Files</a></span></dt><dd><dl><dt><span class="section"><a href="ConfiguringGmat_DataFiles.html#N10DD7">Loading Custom Plugins</a></span></dt><dt><span class="section"><a href="ConfiguringGmat_DataFiles.html#N10DE2">Configuring the MATLAB Interface</a></span></dt><dt><span class="section"><a href="ConfiguringGmat_DataFiles.html#N10DEA">Configuring the Python Interface</a></span></dt><dt><span class="section"><a href="ConfiguringGmat_DataFiles.html#N10DF2">User-defined Function Paths</a></span></dt></dl></dd></dl></dd></dl></dd><dt><span class="part"><a href="Tutorials.html">Tutorials</a></span></dt><dd><dl><dt><span class="chapter"><a href="SimulatingAnOrbit.html">5. Simulating an Orbit</a></span></dt><dd><dl><dt><span class="section"><a href="SimulatingAnOrbit.html#N10E53">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch05s02.html">Configure the Spacecraft</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s02.html#N10E97">Rename the Spacecraft</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EB4">Set the Spacecraft Epoch</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EE3">Set the Keplerian Orbital Elements</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s03.html">Configure the Propagator</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s03.html#N10F61">Rename the Propagator</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10F7E">Configure the Force Model</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10FF5">Configuring the Orbit View Plot</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s04.html">Configure the Propagate Command</a></span></dt><dt><span class="section"><a href="ch05s05.html">Run and Analyze the Results</a></span></dt></dl></dd><dt><span class="chapter"><a href="SimpleOrbitTransfer.html">6. Simple Orbit Transfer</a></span></dt><dd><dl><dt><span class="section"><a href="SimpleOrbitTransfer.html#N1113D">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch06s02.html">Configure Maneuvers, Differential Corrector, and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s02.html#N1117F">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111A3">Modify the Default Orbit View</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111FB">Create the Maneuvers.</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s03.html#N1124B">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1126A">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1132D">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1137A">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s04.html">Run the Mission</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_TargetFiniteBurn.html">7. Target Finite Burn to Raise Apogee</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_TargetFiniteBurn.html#N11577">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch07s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s02.html#N115C7">Create a Thruster and a Fuel Tank</a></span></dt><dt><span class="section"><a href="ch07s02.html#N11659">Modify Thruster1 Thrust Coefficients</a></span></dt><dt><span class="section"><a href="ch07s02.html#N1169C">Attach ChemicalTank1 and Thruster1 to DefaultSC</a></span></dt><dt><span class="section"><a href="ch07s02.html#N116F7">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s03.html">Create the Differential Corrector and Target Control
    Variable</a></span></dt><dt><span class="section"><a href="ch07s04.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s04.html#N117B6">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch07s04.html#N117E4">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch07s04.html#N11880">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s05.html">Run the Mission</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s05.html#N119F2">Inspect Orbit View and Message Window</a></span></dt><dt><span class="section"><a href="ch07s05.html#N11A13">Explore the Command Summary Reports</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="Mars_B_Plane_Targeting.html">8. Mars B-Plane Targeting</a></span></dt><dd><dl><dt><span class="section"><a href="Mars_B_Plane_Targeting.html#N11AA6">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch08s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s02.html#N11B1F">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11B91">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11C5A">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11CE9">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11EF7">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11F29">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11FA1">Create the Orbit Views</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s03.html#N1212E">Create the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1222A">Configure the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12232">Configure the Target desired B-plane Coordinates Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12259">Configure the Prop 3 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12298">Configure the Prop 12 Days to TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N122DA">Configure the Vary TCM.V Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1232E">Configure the Vary TCM.N Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N123C0">Configure the Vary TCM.B Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12452">Configure the Apply TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12470">Configure the Prop 280 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124B2">Configure the Prop to Mars Periapsis Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124E8">Configure the Achieve BdotT Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1253F">Configure the Achieve BdotR Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s04.html#N1268C">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N1276C">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127DA">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127E2">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12809">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12895">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128BC">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128F2">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></dd><dt><span class="chapter"><a href="OptimalLunarFlyby.html">9. Optimal Lunar Flyby using Multiple Shooting</a></span></dt><dd><dl><dt><span class="section"><a href="OptimalLunarFlyby.html#N12A34">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch09s02.html">Configure Coordinate Systems, Spacecraft, Optimizer, Propagators,
    Maneuvers, Variables, and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s02.html#N12AB9">Create a Moon-centered Coordinate System</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12AC8">Create the Spacecraft</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B09">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B22">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B37">Create the User Variables</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B46">Create the Optimizer</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B55">Create the 3-D Graphics</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B73">Create XYPlots and Reports</a></span></dt></dl></dd><dt><span class="section"><a href="ch09s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s03.html#N12B9A">Overview of the Mission Sequence</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BA3">Define Initial Guesses</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BC3">Initialize Variables</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BED">Vary and Set Spacecraft Epochs</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C0D">Vary Control Point States</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C37">Apply Constraints at Control Points</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C43">Propagate the Segments</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C79">Compute Some Quantities and Apply Patch Constraints</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C88">Apply Patch Point Constraints</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C94">Apply Constraints on Mission Orbit</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12CA0">Apply Cost Function</a></span></dt></dl></dd><dt><span class="section"><a href="ch09s04.html">Design the Trajectory</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s04.html#N12CAF">Overview</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12CC5">Step 1: Verify Your Configuration</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12D0B">Step 2: Find a Smooth Trajectory</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12D8F">Step 5: Apply a New Constraint</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="Tut_UsingGMATFunctions.html">10. Mars B-Plane Targeting Using GMAT Functions</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_UsingGMATFunctions.html#N12DC0">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch10s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s02.html#N12E53">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12EC5">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12F8E">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1301D">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1322B">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1325D">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch10s02.html#N132D5">Create the Orbit Views</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1344F">Create single Report File</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1348A">Create a GMAT Function</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s03.html#N134F3">Create Commands to Initiate the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N1358F">Configure the Mission Tree to Run the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N13597">Configure the Make Objects Global Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135BE">Configure the Target Desired B-Plane Coord. From Inside Function Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135E5">Configure the Report Parameters Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s04.html#N1369D">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13783">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F1">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F9">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13820">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138AC">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138D3">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13909">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_EventLocation.html">11. Finding Eclipses and Station Contacts</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_EventLocation.html#N13A1E">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch11s02.html">Load the Mission</a></span></dt><dt><span class="section"><a href="ch11s03.html">Configure GMAT for Event Location</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s03.html#N13A94">Verify SolarSystem Configuration</a></span></dt><dt><span class="section"><a href="ch11s03.html#N13AE1">Configure CelestialBody Resources</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s04.html">Configure and Run the Eclipse Locator</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s04.html#N13B59">Create and Configure the EclipseLocator</a></span></dt><dt><span class="section"><a href="ch11s04.html#N13BCF">Run the Mission</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s05.html">Configure and Run the Contact Locator</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s05.html#N13C15">Create and Configure a Ground Station</a></span></dt><dt><span class="section"><a href="ch11s05.html#N13CAD">Create and Configure the ContactLocator</a></span></dt><dt><span class="section"><a href="ch11s05.html#N13D04">Run the Mission</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s06.html">Further Exercises</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_ElectricPropulsion.html">12. Electric Propulsion</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_ElectricPropulsion.html#N13D95">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch12s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s02.html#N13DD9">Create a Thruster, Fuel Tank, and Solar Power System</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13E21">Configure the Hardware</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13EB0">Attach Hardware to the Spacecraft</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13F2C">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s03.html#N13F7E">Create the Commands</a></span></dt><dt><span class="section"><a href="ch12s03.html#N13FCC">Configure the Propagate Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s04.html">Run the Mission</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">13. Simulate DSN Range and Doppler Data</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_Simulate_DSN_Range_and_Doppler_Data.html#N14023">Objective and Overview</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N1407E">Create a satellite and set its epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14091">Create a Transponder object and attach it to our
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html">Create and configure the Ground Station and related
    parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N140DA">Create Ground Station Transmitter, Receiver, and Antenna
      objects</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N14113">Create Ground Station</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N14125">Create Ground Station Error Models</a></span></dt></dl></dd><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated.html">Define the types of measurements to be simulated</a></span></dt><dt><span class="section"><a href="Create_and_configure_Force_model_and_propagator.html">Create and configure Force model and propagator</a></span></dt><dt><span class="section"><a href="Create_and_configure_Simulator_object.html">Create and configure Simulator object</a></span></dt><dt><span class="section"><a href="Run_the_mission_and_analyze_the_results.html">Run the mission and analyze the results</a></span></dt><dt><span class="section"><a href="Create_Realistic_GMD.html">Create a more realistic GMAT Measurement Data (GMD)</a></span></dt><dt><span class="section"><a href="ch13s09.html">References</a></span></dt><dt><span class="section"><a href="Appendix_A_Determination_of_Measurement_Noise_Values.html">Appendix A &ndash; Determination of Measurement Noise Values</a></span></dt></dl></dd><dt><span class="chapter"><a href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html">14. Orbit Estimation using DSN Range and Doppler Data</a></span></dt><dd><dl><dt><span class="section"><a href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html#N14D17">Objective and Overview</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D6F">Create a satellite and set its epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D8D">Create a Transponder object and attach it to our
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html">Create and configure the Ground Station and related
    parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14DDC">Create Ground Station Transmitter, Receiver, and Antenna
      objects</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E26">Create Ground Station</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E54">Create Ground Station Error Models</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html">Define the types of measurements that will be processed</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_Force_model_and_propagator.html">Create and configure Force model and propagator</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html">Create and configure BatchEstimator object</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html">Run the mission and analyze the results</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Message_Window_Output">Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Plots_of_Observation_Residuals">Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Batch_Estimator_Output_Report">Batch Estimator Output Report</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Matlab_Output_File">Matlab Output File</a></span></dt></dl></dd><dt><span class="section"><a href="ch14s08.html">References</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_A.html">Appendix A &ndash; GMAT Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_B.html">Appendix B &ndash; Zeroth Iteration Plots of Observation
    Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_C.html">Appendix C &ndash; First Iteration Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_D.html">Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</a></span></dt></dl></dd><dt><span class="chapter"><a href="FilterSmoother_GpsPosVec.html">15. Filter and Smoother Orbit Determination using GPS_PosVec Data</a></span></dt><dd><dl><dt><span class="section"><a href="FilterSmoother_GpsPosVec.html#N151E3">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch15s02.html">Simulate GPS_PosVec measurements</a></span></dt><dt><span class="section"><a href="ch15s03.html">Estimate the orbit</a></span></dt><dt><span class="section"><a href="ch15s04.html">Review and quality check the filter run</a></span></dt><dt><span class="section"><a href="ch15s05.html">Modify the estimation script to use a smoother to improve the
    estimates</a></span></dt><dt><span class="section"><a href="ch15s06.html">Review and quality check the smoother run</a></span></dt><dt><span class="section"><a href="ch15s07.html">Warm-start the filter</a></span></dt><dt><span class="section"><a href="ch15s08.html">A few words about filter tuning</a></span></dt><dt><span class="section"><a href="ch15s09.html">References</a></span></dt><dt><span class="section"><a href="ch15s10.html">Appendix A. Generate an ephemeris while running the filter and
    smoother</a></span></dt><dt><span class="section"><a href="ch15s11.html">Appendix B. Run the script from the command-line</a></span></dt><dt><span class="section"><a href="ch15s12.html">Appendix C. Check covariance matrix conditioning</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">16. Simulate and Estimate Inter-Spacecraft Tracking</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html#N1564D">Objective and overview</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Create and configure the spacecraft, spacecraft hardware, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156A3">Create
      the simulation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156B3">Create
      the estimation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156D2">Create
      a Transponder object and attach it to the spacecraft</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#Create_a_Receiver_object_and_attach_it_to_the_measurement_spacecraft">Create a Receiver object and attach it to the measurement
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html">Define the types of measurements to be simulated and their
    associated error models</a></span></dt><dd><dl><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#N1572A">Define
      the TrackingFileSets for the simulation and the estimator</a></span></dt><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#Create_measurement_error_models">Create the measurement error models</a></span></dt></dl></dd><dt><span class="section"><a href="Create_and_configure_force_model_and_propagator.html">Create and configure force model and propagator</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html">Create and configure the simulator and batch estimator
    objects</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_simulator_object">Create the simulator object</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_batch_estimator_object">Create the batch estimator object</a></span></dt></dl></dd><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html">Run the mission and review the output</a></span></dt><dd><dl><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N15832">Review the simulated
      measurements</a></span></dt><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N1589B">Review the estimator
      results</a></span></dt></dl></dd><dt><span class="section"><a href="ch16s07.html">References</a></span></dt></dl></dd></dl></dd><dt><span class="part"><a href="RefGuide.html">Reference Guide</a></span></dt><dd><dl><dt><span class="chapter"><a href="ch17.html">17. API</a></span></dt><dd><dl><dt><span class="section"><a href="ch17.html#N158E4">User Guide</a></span></dt></dl></dd><dt><span class="chapter"><a href="ch18.html">18. Dynamics and Modeling</a></span></dt><dd><dl><dt><span class="section"><a href="ch18.html#N158F2">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Barycenter.html">Barycenter</a></span><span class="refpurpose"> &mdash; The center of mass of selected celestial bodies</span></dt><dt><span class="refentrytitle"><a href="CelestialBody.html">CelestialBody</a></span><span class="refpurpose"> &mdash; Modeling of Moon, Planet, Asteroid, and Comet
    objects</span></dt><dt><span class="refentrytitle"><a href="FuelTank.html">ChemicalTank</a></span><span class="refpurpose"> &mdash; Model of a chemical fuel tank</span></dt><dt><span class="refentrytitle"><a href="Thruster.html">ChemicalThruster</a></span><span class="refpurpose"> &mdash; A chemical thruster model</span></dt><dt><span class="refentrytitle"><a href="ContactLocator.html">ContactLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">Spacecraft</span> and a <span class="guilabel">GroundStation</span>
    or <span class="guilabel">PlanetographicRegion</span>.</span></dt><dt><span class="refentrytitle"><a href="CoordinateSystem.html">CoordinateSystem</a></span><span class="refpurpose"> &mdash; An axis and origin pair</span></dt><dt><span class="refentrytitle"><a href="EclipseLocator.html">EclipseLocator</a></span><span class="refpurpose"> &mdash; A <span class="guilabel">Spacecraft</span> eclipse event
    locator</span></dt><dt><span class="refentrytitle"><a href="ElectricTank.html">ElectricTank</a></span><span class="refpurpose"> &mdash; A model of a tank containing fuel for an electric propulsion
    system</span></dt><dt><span class="refentrytitle"><a href="ElectricThruster.html">ElectricThruster</a></span><span class="refpurpose"> &mdash; An electric thruster model</span></dt><dt><span class="refentrytitle"><a href="FiniteBurn.html">FiniteBurn</a></span><span class="refpurpose"> &mdash; A finite burn</span></dt><dt><span class="refentrytitle"><a href="FieldOfView.html">FieldOfView</a></span><span class="refpurpose"> &mdash; Models the mask, or field-of-view, of a hardware
    <span class="guilabel">Resource</span>.</span></dt><dt><span class="refentrytitle"><a href="ForceModel.html">ForceModel</a></span><span class="refpurpose"> &mdash; Used to specify force modeling options such as gravity, drag,
    solar radiation pressure, and non-central bodies for
    propagation.</span></dt><dt><span class="refentrytitle"><a href="Formation.html">Formation</a></span><span class="refpurpose"> &mdash; A collection of spacecraft.</span></dt><dt><span class="refentrytitle"><a href="GroundStation.html">GroundStation</a></span><span class="refpurpose"> &mdash; A ground station model.</span></dt><dt><span class="refentrytitle"><a href="Imager.html">Imager</a></span><span class="refpurpose"> &mdash; An imager with a defined field of view.</span></dt><dt><span class="refentrytitle"><a href="ImpulsiveBurn.html">ImpulsiveBurn</a></span><span class="refpurpose"> &mdash; An impulsive maneuver</span></dt><dt><span class="refentrytitle"><a href="IntrusionLocator.html">IntrusionLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">CelestialBody</span> and an observer
    <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="LibrationPoint.html">LibrationPoint</a></span><span class="refpurpose"> &mdash; An equilibrium point in the circular, restricted 3-body
    problem</span></dt><dt><span class="refentrytitle"><a href="NuclearPowerSystem.html">NuclearPowerSystem</a></span><span class="refpurpose"> &mdash; A nuclear power system</span></dt><dt><span class="refentrytitle"><a href="PlanetographicRegion.html">PlanetographicRegion</a></span><span class="refpurpose"> &mdash; Define an area on a celestial body's surface as a target for
    an observing <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="Plate.html">Plate</a></span><span class="refpurpose"> &mdash; Used to specify the properties of a single spacecraft surface
    (body panel, solar array side, or other surface) for high-fidelity solar
    radiation pressure modeling, including specular, diffuse, and absorptive
    effects.</span></dt><dt><span class="refentrytitle"><a href="Propagator.html">Propagator</a></span><span class="refpurpose"> &mdash; A propagator models spacecraft motion</span></dt><dt><span class="refentrytitle"><a href="SolarPowerSystem.html">SolarPowerSystem</a></span><span class="refpurpose"> &mdash; A solar power system model</span></dt><dt><span class="refentrytitle"><a href="SolarSystem.html">SolarSystem</a></span><span class="refpurpose"> &mdash; High level solar system configuration options</span></dt><dt><span class="refentrytitle"><a href="Spacecraft.html">Spacecraft</a></span><span class="refpurpose"> &mdash; A spacecraft model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftAttitude.html">Spacecraft Attitude</a></span><span class="refpurpose"> &mdash; The spacecraft attitude model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></span><span class="refpurpose"> &mdash; The physical properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftEpoch.html">Spacecraft Epoch</a></span><span class="refpurpose"> &mdash; The spacecraft epoch</span></dt><dt><span class="refentrytitle"><a href="SpacecraftHardware.html">Spacecraft Hardware</a></span><span class="refpurpose"> &mdash; Add hardware to a spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftOrbitState.html">Spacecraft Orbit State</a></span><span class="refpurpose"> &mdash; The orbital initial conditions</span></dt><dt><span class="refentrytitle"><a href="SpacecraftVisualizationProperties.html">Spacecraft Visualization Properties</a></span><span class="refpurpose"> &mdash; The visual properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="ThrustHistoryFile.html">ThrustHistoryFile</a></span><span class="refpurpose"> &mdash; A time history of input thrust/acceleration vectors and mass
    flow rate</span></dt><dt><span class="refentrytitle"><a href="ThrustSegment.html">ThrustSegment</a></span><span class="refpurpose"> &mdash; One or more <span class="guilabel">ThrustSegments</span> define how
    data in a thrust history file are used.</span></dt></dl></dd><dt><span class="section"><a href="ch18s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="BeginFileThrust.html">BeginFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="BeginFiniteBurn.html">BeginFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers</span></dt><dt><span class="refentrytitle"><a href="EndFileThrust.html">EndFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="EndFiniteBurn.html">EndFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers in the mission
    sequence</span></dt><dt><span class="refentrytitle"><a href="FindEvents.html">FindEvents</a></span><span class="refpurpose"> &mdash; Execute an event location search</span></dt><dt><span class="refentrytitle"><a href="Maneuver.html">Maneuver</a></span><span class="refpurpose"> &mdash; Perform an impulsive (instantaneous) maneuver</span></dt><dt><span class="refentrytitle"><a href="Propagate.html">Propagate</a></span><span class="refpurpose"> &mdash; Propagates spacecraft to a requested stopping
    condition</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch19.html">19. Input/Output</a></span></dt><dd><dl><dt><span class="section"><a href="ch19.html#N22D14">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DynamicDataDisplay.html">DynamicDataDisplay</a></span><span class="refpurpose"> &mdash; A user-defined resource used in tandem with the
    <span class="guilabel">UpdateDynamicData</span> command to print current values of
    parameters to a table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="EphemerisFile.html">EphemerisFile</a></span><span class="refpurpose"> &mdash; Generate spacecraft&rsquo;s ephemeris data</span></dt><dt><span class="refentrytitle"><a href="FileInterface.html">FileInterface</a></span><span class="refpurpose"> &mdash; An interface to a data file</span></dt><dt><span class="refentrytitle"><a href="GroundTrackPlot.html">GroundTrackPlot</a></span><span class="refpurpose"> &mdash; A user-defined resource that draws longitude and latitude
    time-history of a spacecraft</span></dt><dt><span class="refentrytitle"><a href="OpenFramesInterface.html">OpenFramesInterface</a></span><span class="refpurpose"> &mdash; A user-defined resource that provides high-performance 3D interactive visualizations of GMAT missions</span></dt><dt><span class="refentrytitle"><a href="OrbitView.html">OrbitView</a></span><span class="refpurpose"> &mdash; A user-defined resource that plots 3-Dimensional
    trajectories</span></dt><dt><span class="refentrytitle"><a href="ReportFile.html">ReportFile</a></span><span class="refpurpose"> &mdash; Report data to a text file</span></dt><dt><span class="refentrytitle"><a href="XYPlot.html">XYPlot</a></span><span class="refpurpose"> &mdash; Plots data onto the X and Y axes of a graph</span></dt></dl></dd><dt><span class="section"><a href="ch19s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="ClearPlot.html">ClearPlot</a></span><span class="refpurpose"> &mdash; Allows you to clear all data from an XYPlot</span></dt><dt><span class="refentrytitle"><a href="GetEphemStates_Function.html">GetEphemStates()</a></span><span class="refpurpose"> &mdash; Function used to output initial and final spacecraft states
    from an ephemeris file</span></dt><dt><span class="refentrytitle"><a href="MarkPoint.html">MarkPoint</a></span><span class="refpurpose"> &mdash; Allows you to add a special mark point character on an
    XYPlot</span></dt><dt><span class="refentrytitle"><a href="PenUpPenDown.html">PenUpPenDown</a></span><span class="refpurpose"> &mdash; Allows you to stop or begin drawing data on a
    plot</span></dt><dt><span class="refentrytitle"><a href="Report.html">Report</a></span><span class="refpurpose"> &mdash; Allows you to write data to a text file</span></dt><dt><span class="refentrytitle"><a href="Set.html">Set</a></span><span class="refpurpose"> &mdash; Configure a resource from a data interface</span></dt><dt><span class="refentrytitle"><a href="Toggle.html">Toggle</a></span><span class="refpurpose"> &mdash; Allows you to turn data output off or on</span></dt><dt><span class="refentrytitle"><a href="UpdateDynamicData.html">UpdateDynamicData</a></span><span class="refpurpose"> &mdash; A command used in tandem with a
	<span class="guilabel">DynamicDataDisplay</span> to update the data being shown in
	the display table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="Write.html">Write</a></span><span class="refpurpose"> &mdash; Writes data to one or more of the following three
    destinations: the message window, the log file, or a
    <span class="guilabel">ReportFile</span> resource.</span></dt></dl></dd><dt><span class="section"><a href="ch19s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Color.html">Color</a></span><span class="refpurpose"> &mdash; Color support in GMAT resources and commands</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch20.html">20. Targeting/Parameter Optimization</a></span></dt><dd><dl><dt><span class="section"><a href="ch20.html#N25D9B">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DifferentialCorrector.html">DifferentialCorrector</a></span><span class="refpurpose"> &mdash; A numerical solver</span></dt><dt><span class="refentrytitle"><a href="FminconOptimizer.html">FminconOptimizer</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    fmincon</span></dt><dt><span class="refentrytitle"><a href="SNOPTOptimizer.html">SNOPT</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    SNOPT</span></dt><dt><span class="refentrytitle"><a href="VF13ad.html">VF13ad</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    VF13ad</span></dt><dt><span class="refentrytitle"><a href="Yukon.html">Yukon</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    Yukon</span></dt></dl></dd><dt><span class="section"><a href="ch20s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Achieve.html">Achieve</a></span><span class="refpurpose"> &mdash; Specify a goal for a <span class="guilabel">Target</span>
    sequence</span></dt><dt><span class="refentrytitle"><a href="Minimize.html">Minimize</a></span><span class="refpurpose"> &mdash; Define the cost function to minimize</span></dt><dt><span class="refentrytitle"><a href="NonlinearConstraint.html">NonlinearConstraint</a></span><span class="refpurpose"> &mdash; Specify a constraint used during optimization, with an optional tolerance used when checking physical constraint values</span></dt><dt><span class="refentrytitle"><a href="Optimize.html">Optimize</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Target.html">Target</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Vary.html">Vary</a></span><span class="refpurpose"> &mdash; Specifies variables used by a solver</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch21.html">21. Orbit Determination</a></span></dt><dd><dl><dt><span class="section"><a href="ch21.html#N27B20">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="AcceptFilter.html">AcceptFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Antenna.html">Antenna</a></span><span class="refpurpose"> &mdash; Transmits or receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="BatchEstimator.html">BatchEstimator</a></span><span class="refpurpose"> &mdash; A batch least squares estimator</span></dt><dt><span class="refentrytitle"><a href="ErrorModel.html">ErrorModel</a></span><span class="refpurpose"> &mdash; Used to specify measurement noise for simulation and
    estimation, and to apply or estimate measurement biases.</span></dt><dt><span class="refentrytitle"><a href="EstimatedParameter.html">EstimatedParameter</a></span><span class="refpurpose"> &mdash; Used for modeling of dynamically estimated parameters in the
    Extended Kalman Filter.</span></dt><dt><span class="refentrytitle"><a href="ExtendedKalmanFilter.html">ExtendedKalmanFilter</a></span><span class="refpurpose"> &mdash; An extended Kalman filter orbit determination
    estimator</span></dt><dt><span class="refentrytitle"><a href="ProcessNoiseModel.html">ProcessNoiseModel</a></span><span class="refpurpose"> &mdash; Used to specify process noise for estimation when using the
    ExtendedKalmanFilter estimator.</span></dt><dt><span class="refentrytitle"><a href="Receiver.html">Receiver</a></span><span class="refpurpose"> &mdash; Hardware that receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="RejectFilter.html">RejectFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Simulator.html">Simulator</a></span><span class="refpurpose"> &mdash; Configures the generation of simulated tracking data
    measurements.</span></dt><dt><span class="refentrytitle"><a href="Smoother.html">Smoother</a></span><span class="refpurpose"> &mdash; A backwards filter yielding an improved estimate of states
    through weighted combination of forward and reverse sequential
    estimation.</span></dt><dt><span class="refentrytitle"><a href="SpacecraftNavigation.html">Spacecraft Navigation</a></span><span class="refpurpose"> &mdash; There are a number of <span class="guilabel">Spacecraft</span> fields
    that are used exclusively to support GMAT's navigation (orbit
    determination) capability.</span></dt><dt><span class="refentrytitle"><a href="TrackingFileSet.html">TrackingFileSet</a></span><span class="refpurpose"> &mdash; Manages the observation data contained in one or more external
    tracking data files.</span></dt><dt><span class="refentrytitle"><a href="Transmitter.html">Transmitter</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, attached to a
    <span class="guilabel">GroundStation</span> or <span class="guilabel">Spacecraft</span>
    resource, that transmits an RF signal.</span></dt><dt><span class="refentrytitle"><a href="Transponder.html">Transponder</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, typically attached to a
    spacecraft, that receives and automatically re-transmits an incoming
    signal.</span></dt></dl></dd><dt><span class="section"><a href="ch21s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="RunEstimator.html">RunEstimator</a></span><span class="refpurpose"> &mdash; Ingests navigation measurements and generates an estimated
    state vector</span></dt><dt><span class="refentrytitle"><a href="RunSimulator.html">RunSimulator</a></span><span class="refpurpose"> &mdash; Generates simulated navigation measurements</span></dt><dt><span class="refentrytitle"><a href="RunSmoother.html">RunSmoother</a></span><span class="refpurpose"> &mdash; Runs a sequential smoother estimator.</span></dt></dl></dd><dt><span class="section"><a href="ch21s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="TrackingDataTypes.html">Tracking Data Types for Orbit Determination</a></span><span class="refpurpose"> &mdash; This section describes tracking data types and file formats
    for orbit determination.</span></dt><dt><span class="refentrytitle"><a href="NavPropagatorConfiguration.html">Configuration of Propagators for Orbit
    Determination</a></span><span class="refpurpose"> &mdash; This section describes some special considerations for
    configuration of numerical and ephemeris propagators for GMAT's
    estimators.</span></dt><dt><span class="refentrytitle"><a href="InitialOrbitDetermination.html">Initial Orbit Determination</a></span><span class="refpurpose"> &mdash; A set of python functions to support early orbit
    operations</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch22.html">22. Programming</a></span></dt><dd><dl><dt><span class="section"><a href="ch22.html#N2BAB5">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Array.html">Array</a></span><span class="refpurpose"> &mdash; A user-defined one- or two-dimensional array
    variable</span></dt><dt><span class="refentrytitle"><a href="GmatFunction.html">GMATFunction</a></span><span class="refpurpose"> &mdash; Declaration of a GMAT function</span></dt><dt><span class="refentrytitle"><a href="MatlabFunction.html">MatlabFunction</a></span><span class="refpurpose"> &mdash; Declaration of an external MATLAB function</span></dt><dt><span class="refentrytitle"><a href="String.html">String</a></span><span class="refpurpose"> &mdash; A user-defined string variable</span></dt><dt><span class="refentrytitle"><a href="Variable.html">Variable</a></span><span class="refpurpose"> &mdash; A user-defined numeric variable</span></dt></dl></dd><dt><span class="section"><a href="ch22s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Assignment.html">Assignment (<code class="literal">=</code>)</a></span><span class="refpurpose"> &mdash; Set a variable or resource field to a value, possibly using
    mathematical expressions</span></dt><dt><span class="refentrytitle"><a href="BeginMissionSequence.html">BeginMissionSequence</a></span><span class="refpurpose"> &mdash; Begin the mission sequence portion of a script</span></dt><dt><span class="refentrytitle"><a href="BeginScript.html">BeginScript</a></span><span class="refpurpose"> &mdash; Execute free-form script commands</span></dt><dt><span class="refentrytitle"><a href="Breakpoint.html">Breakpoint</a></span><span class="refpurpose"> &mdash; 
      Pause a run and let the user examine the state of objects.
    </span></dt><dt><span class="refentrytitle"><a href="CallGmatFunction.html">CallGmatFunction</a></span><span class="refpurpose"> &mdash; Call a GMAT function</span></dt><dt><span class="refentrytitle"><a href="CallMatlabFunction.html">CallMatlabFunction</a></span><span class="refpurpose"> &mdash; Call a MATLAB function</span></dt><dt><span class="refentrytitle"><a href="CallPythonFunction.html">CallPythonFunction</a></span><span class="refpurpose"> &mdash; Call a Python function</span></dt><dt><span class="refentrytitle"><a href="CommandEcho.html">CommandEcho</a></span><span class="refpurpose"> &mdash; Toggle the use of the <span class="guilabel">Echo</span>
    command</span></dt><dt><span class="refentrytitle"><a href="For.html">For</a></span><span class="refpurpose"> &mdash; Execute a series of commands a specified number of
    times</span></dt><dt><span class="refentrytitle"><a href="Global.html">Global</a></span><span class="refpurpose"> &mdash; Declare Objects as global</span></dt><dt><span class="refentrytitle"><a href="If.html">If</a></span><span class="refpurpose"> &mdash; Conditionally execute a series of commands</span></dt><dt><span class="refentrytitle"><a href="Stop.html">Stop</a></span><span class="refpurpose"> &mdash; Stop mission execution</span></dt><dt><span class="refentrytitle"><a href="While.html">While</a></span><span class="refpurpose"> &mdash; Execute a series of commands repeatedly while a condition is
    met</span></dt></dl></dd><dt><span class="section"><a href="ch22s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="IncludeMacro.html">#Include Macro</a></span><span class="refpurpose"> &mdash; Load or import a script snippet</span></dt><dt><span class="refentrytitle"><a href="MatlabInterface.html">MATLAB Interface</a></span><span class="refpurpose"> &mdash; Interface to MATLAB system</span></dt><dt><span class="refentrytitle"><a href="PythonInterface.html">Python Interface</a></span><span class="refpurpose"> &mdash; Interface to the Python programming language</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch23.html">23. Optimal Control</a></span></dt><dd><dl><dt><span class="section"><a href="ch23.html#N2D3D4">User Guide</a></span></dt></dl></dd><dt><span class="chapter"><a href="ch24.html">24. System</a></span></dt><dd><dl><dt><span class="section"><a href="ch24.html#N2D3E2">System Level Components</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="CalculationParameters.html">Calculation Parameters</a></span><span class="refpurpose"> &mdash; Resource properties available for use by commands and
    output</span></dt><dt><span class="refentrytitle"><a href="CommandLine.html">Command-Line Usage</a></span><span class="refpurpose"> &mdash; Starting the <code class="filename">GMAT</code> application from the
    command line</span></dt><dt><span class="refentrytitle"><a href="KeyboardShortcuts.html">Keyboard Shortcuts</a></span><span class="refpurpose"> &mdash; Keyboard shortcuts in the graphical user
    interface</span></dt><dt><span class="refentrytitle"><a href="ScriptLanguage.html">Script Language</a></span><span class="refpurpose"> &mdash; The GMAT script language</span></dt><dt><span class="refentrytitle"><a href="StartupFile.html">Startup File</a></span><span class="refpurpose"> &mdash; The <code class="filename">gmat_startup_file.txt</code> configuration
    file</span></dt></dl></dd></dl></dd></dl></dd><dt><span class="appendix"><a href="ReleaseNotes.html">Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotes.html#ReleaseNotesR2024a">GMAT R2025a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotes.html#N3091F">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N30934">Major Improvements and Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309D5">Other Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309E6">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotes.html#N309F0">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2022a.html">GMAT R2022a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30A70">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30A79">Major Improvements and Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BB9">Other Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BC4">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2022a.html#N30BE0">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2020a.html">GMAT R2020a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30C43">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30C59">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30E53">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30E8B">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2020a.html#N30EA5">Fixed &amp; Known Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2018a.html">GMAT R2018a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30F56">Milestones and Accomplishments</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30F6E">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30FB5">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N30FEB">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N31004">Upcoming Changes in R2019a</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2018a.html#N31009">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2017a.html">GMAT R2017a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2017a.html#N31060">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N31097">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310A5">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310C6">GMAT Stuff</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2017a.html#N310D6">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2016a.html">GMAT R2016a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2016a.html#N31163">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311A1">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311BE">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311CB">Development and Tools</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2016a.html#N311E7">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2015a.html">GMAT R2015a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3125C">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N312FB">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3133A">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N31365">Development and Tools</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2015a.html#N3138C">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2014a.html">GMAT R2014a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2014a.html#N3140F">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N31536">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N3157B">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2014a.html#N315A0">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2013b.html">GMAT R2013b Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2013b.html#N3162A">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N3168A">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N31721">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013b.html#N31765">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2013a.html">GMAT R2013a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2013a.html#N317F0">Licensing</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N317FD">Major Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N31854">Minor Enhancements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N31894">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2013a.html#N318D7">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2012a.html">GMAT R2012a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31961">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N319C4">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31AA6">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2012a.html#N31B2B">Known &amp; Fixed Issues</a></span></dt></dl></dd><dt><span class="section"><a href="ReleaseNotesR2011a.html">GMAT R2011a Release Notes</a></span></dt><dd><dl><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31B65">New Features</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31C8D">Improvements</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31CE5">Compatibility Changes</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31E62">Fixed Issues</a></span></dt><dt><span class="section"><a href="ReleaseNotesR2011a.html#N31E6A">Known Issues</a></span></dt></dl></dd></dl></dd><dt><span class="index"><a href="BookIndex.html">Index</a></span></dt></dl></div><div class="list-of-figures"><p><b>List of Figures</b></p><dl><dt>3.1. <a href="TourOfGmat.html#N10384">GMAT Desktop (Windows)</a></dt><dt>3.2. <a href="TourOfGmat.html#N1043C">Undocked Mission Tree</a></dt><dt>3.3. <a href="TourOfGmat.html#Fig_ScriptEditor">GMAT Script Editor</a></dt><dt>3.4. <a href="ResourceTree.html#ResourcesTree_Fig_Default">Default Resources tree</a></dt><dt>3.5. <a href="ResourceTree.html#ResourcesTree_Fig_AddSpacecraft">Folder menu for <span class="guilabel">Spacecraft</span></a></dt><dt>3.6. <a href="ResourceTree.html#ResourcesTree_Fig_AddFuelTank">Folder menu for <span class="guilabel">Hardware</span></a></dt><dt>3.7. <a href="ResourceTree.html#ResourcesTree_Fig_ResourceMenu">Resource menu</a></dt><dt>3.8. <a href="ScriptEditor.html#ScriptEditor_Fig1">Parts of the script editor</a></dt><dt>3.9. <a href="ScriptEditor.html#N10A6D">Active script indicators</a></dt><dt>4.1. <a href="ConfiguringGmat.html#GmatRootDirectoryStructure">GMAT Root Directory Structure</a></dt><dt>4.2. <a href="ConfiguringGmat.html#GmatDataDirectoryStructure">GMAT Data Directory Structure</a></dt><dt>5.1. <a href="ch05s02.html#Tut_PropASpacecraft_OrbitDialog">Spacecraft State Setup</a></dt><dt>5.2. <a href="ch05s03.html#Tut_PropASpacecraft_PropSetUp">Force Model Configuration</a></dt><dt>5.3. <a href="ch05s03.html#Tut_PropASpacecraft_OrbitViewDialog">DefaultOrbitView Configuration</a></dt><dt>5.4. <a href="ch05s04.html#Tut_PropASpacecraft_StopSetUp">Propagate Command ParameterSelectDialog Configuration</a></dt><dt>5.5. <a href="ch05s04.html#Tut_PropASpacecraft_Propagate1Dialog">Propagate Command Configuration</a></dt><dt>5.6. <a href="ch05s05.html#Tut_PropASpacecraft_FinalResults">Orbit View Plot after Mission Run</a></dt><dt>6.1. <a href="ch06s03.html#Tut_HohmannTransfer_TargetSequence">Final Mission Sequence for the Hohmann Transfer</a></dt><dt>6.2. <a href="ch06s03.html#N1136C"><span class="guilabel">Prop One Day</span> Command Configuration</a></dt><dt>6.3. <a href="ch06s03.html#Tut_HohmannTransfer_VaryTOI"><span class="guilabel">Vary TOI</span> Command Configuration</a></dt><dt>6.4. <a href="ch06s03.html#N113D3"><span class="guilabel">Perform TOI</span> Command
          Configuration</a></dt><dt>6.5. <a href="ch06s03.html#N113FE"><span class="guilabel">Prop to Apoapsis</span> Command
          Configuration</a></dt><dt>6.6. <a href="ch06s03.html#N11431"><span class="guilabel">Achieve RMAG = 42165</span> Command
          Configuration</a></dt><dt>6.7. <a href="ch06s03.html#N11467"><span class="guilabel">Vary GOI</span> Parameter Selection</a></dt><dt>6.8. <a href="ch06s03.html#N1148D"><span class="guilabel">Vary GOI</span> Command Configuration</a></dt><dt>6.9. <a href="ch06s03.html#N114B4"><span class="guilabel">Perform GOI</span> Command
          Configuration</a></dt><dt>6.10. <a href="ch06s03.html#N11500"><span class="guilabel">Achieve ECC = 0.005</span> Command
          Configuration</a></dt><dt>6.11. <a href="ch06s04.html#N1152A">3D View of Hohmann Transfer</a></dt><dt>7.1. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig1_FuelTank1_Configuration"><span class="guilabel">ChemicalTank1</span> Configuration</a></dt><dt>7.2. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig2_Thruster1_Configuration"><span class="guilabel">ChemicalThruster1</span> Configuration</a></dt><dt>7.3. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig3_Thruster1_Thrust_Coefficients"><span class="guilabel">ChemicalThruster1</span> Thrust
        Coefficients</a></dt><dt>7.4. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig4_Attach_FuelTank1_to_DefaultSC">Attach <span class="guilabel">ChemicalTank1</span> to
        <span class="guilabel">DefaultSC</span></a></dt><dt>7.5. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig5_Attach_Thruster1_to_DefaultSC">Attach <span class="guilabel">ChemicalThruster1</span> to
        <span class="guilabel">DefaultSC</span></a></dt><dt>7.6. <a href="ch07s02.html#Tut_TargetFiniteBurn_Fig6_Creation_of_FiniteBurn_Resource_FiniteBurn1">Creation of <span class="guilabel">FiniteBurn</span> Resource
        <span class="guilabel">FiniteBurn1</span></a></dt><dt>7.7. <a href="ch07s03.html#Tut_TargetFiniteBurn_Fig7_Creation_of_Variable_Resource_BurnDuration">Creation of <span class="guilabel">Variable</span> Resource,
      <span class="guilabel">BurnDuration</span></a></dt><dt>7.8. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig8"><span class="guilabel">Prop To Perigee </span>Command
        Configuration</a></dt><dt>7.9. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig9_Final_Mission_Sequence">Final Mission Sequence</a></dt><dt>7.10. <a href="ch07s04.html#N118A1"><span class="guilabel">Raise Apogee</span> Command
          Configuration</a></dt><dt>7.11. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig11_Vary_Burn_Duration_Command_Configuration"><span class="guilabel">Vary Burn Duration</span> Command
          Configuration</a></dt><dt>7.12. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig12_Turn_Thruster_On_Command_Configuration"><span class="guilabel">Turn Thruster On</span> Command
          Configuration</a></dt><dt>7.13. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig13_Prop_BurnDuration_Command_Configuration"><span class="guilabel">Prop BurnDuration</span> Command
          Configuration</a></dt><dt>7.14. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig14_Turn_Thruster_Off_Command_Configuration"><span class="guilabel">Turn Thruster Off</span> Command
          Configuration</a></dt><dt>7.15. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig15_Prop_To_Apogee_Command_Configuration"><span class="guilabel">Prop To Apogee</span> Command
          Configuration</a></dt><dt>7.16. <a href="ch07s04.html#Tut_TargetFiniteBurn_Fig16_Achieve_Apogee_Radius_12000_Command_Configuration"><span class="guilabel">Achieve Apogee Radius = 12000</span> Command
          Configuration</a></dt><dt>7.17. <a href="ch07s05.html#Tut_TargetFiniteBurn_Fig17_3D_View_of_Finite_Burn_to_Raise_Apogee">3D View of <span class="guilabel">Finite Burn to Raise
        Apogee</span></a></dt><dt>8.1. <a href="Mars_B_Plane_Targeting.html#Tut_Mars_B_Plane_Targeting_B_Plane_1"><span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane</a></dt><dt>8.2. <a href="Mars_B_Plane_Targeting.html#Tut_Mars_B_Plane_Targeting_B_Plane_2"><span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane</a></dt><dt>8.3. <a href="ch08s03.html#Tut_Mars_B_Plane_Targeting_B_MissionTree_1">Mission Sequence for the First Target sequence</a></dt><dt>8.4. <a href="ch08s03.html#N1224B"><span class="guilabel">Target desired B-plane</span> Coordinates Command
        Configuration</a></dt><dt>8.5. <a href="ch08s03.html#N1228A"><span class="guilabel">Prop 3 Days</span> Command Configuration</a></dt><dt>8.6. <a href="ch08s03.html#N122CC"><span class="guilabel">Prop 12 Days</span> to TCM Command
        Configuration</a></dt><dt>8.7. <a href="ch08s03.html#N12320"><span class="guilabel">Vary TCM.V</span> Command Configuration</a></dt><dt>8.8. <a href="ch08s03.html#N123A4"><span class="guilabel">Vary TCM.N</span> Parameter Selection</a></dt><dt>8.9. <a href="ch08s03.html#N123B2"><span class="guilabel">Vary TCM.N</span> Command Configuration</a></dt><dt>8.10. <a href="ch08s03.html#N12436"><span class="guilabel">Vary TCM.B</span> Parameter Selection</a></dt><dt>8.11. <a href="ch08s03.html#N12444"><span class="guilabel">Vary TCM.N</span> Command Configuration</a></dt><dt>8.12. <a href="ch08s03.html#N12462"><span class="guilabel">Apply TCM</span> Command Configuration</a></dt><dt>8.13. <a href="ch08s03.html#N124A4"><span class="guilabel">Prop 280 Days</span> Command
        Configuration</a></dt><dt>8.14. <a href="ch08s03.html#N124DA"><span class="guilabel">Prop to Mars Periapsis</span> Command
        Configuration</a></dt><dt>8.15. <a href="ch08s03.html#N12531"><span class="guilabel">Achieve BdotT</span> Command
        Configuration</a></dt><dt>8.16. <a href="ch08s03.html#N12588"><span class="guilabel">Achieve BdotR</span> Command
        Configuration</a></dt><dt>8.17. <a href="ch08s04.html#N125BA">3D View of departure hyperbolic trajectory (EarthView)</a></dt><dt>8.18. <a href="ch08s04.html#N125C6">3D View of heliocentric transfer trajectory
      (SolarSystemView)</a></dt><dt>8.19. <a href="ch08s04.html#N125D2">3D View of approach hyperbolic trajectory. MAVEN stopped at
      periapsis (MarsView)</a></dt><dt>8.20. <a href="ch08s04.html#Tut_Mars_B_Plane_Targeting_B_MissionTree_2">Mission Sequence showing first and second Target
        sequences</a></dt><dt>8.21. <a href="ch08s04.html#N127CC"><span class="guilabel">Prop for 1 day</span> Command
        Configuration</a></dt><dt>8.22. <a href="ch08s04.html#N127FB"><span class="guilabel">Mars Capture</span> Command
        Configuration</a></dt><dt>8.23. <a href="ch08s04.html#N12879"><span class="guilabel">Vary MOI</span> Parameter Selection</a></dt><dt>8.24. <a href="ch08s04.html#N12887"><span class="guilabel">Vary MOI</span> Command Configuration</a></dt><dt>8.25. <a href="ch08s04.html#N128AE"><span class="guilabel">Apply MOI</span> Command
        Configuration</a></dt><dt>8.26. <a href="ch08s04.html#N128E4"><span class="guilabel">Prop to Mars Apoapsis</span> Command
        Configuration</a></dt><dt>8.27. <a href="ch08s04.html#N12932"><span class="guilabel">Achieve RMAG</span> Command
        Configuration</a></dt><dt>8.28. <a href="ch08s05.html#N12975">3D view of Mars Capture orbit after MOI maneuver (MarsView)</a></dt><dt>9.1. <a href="OptimalLunarFlyby.html#Tut_OptimalFlyby_J2000View">View of Lunar Flyby from Normal to Earth Equator</a></dt><dt>9.2. <a href="OptimalLunarFlyby.html#Tut_OptimalFlyby_J2000View2">View of Lunar Flyby Geometry</a></dt><dt>9.3. <a href="OptimalLunarFlyby.html#Tut_OptimalFlyby_Patchpoints">Definition of Control and Patch Points</a></dt><dt>9.4. <a href="ch09s04.html#Tut_OptimalFlyby_VerfiyConfig">View of Discontinuous Trajectory</a></dt><dt>9.5. <a href="ch09s04.html#Tut_OptimalFlyby_VerifyConfig2">Alternate View (1) of Discontinuous Trajectory</a></dt><dt>9.6. <a href="ch09s04.html#Tut_OptimalFlyby_VerifyConfig3">Alternate View (2) of Discontinuous Trajectory</a></dt><dt>9.7. <a href="ch09s04.html#Tut_OptimalFlyby_SmoothTrajectory">Smooth Trajectory Solution</a></dt><dt>9.8. <a href="ch09s04.html#Tut_OptimalFlyby_OptimalTrajectory">Optimal Trajectory Solution</a></dt><dt>9.9. <a href="ch09s04.html#Tut_OptimalFlyby_NewGuess">Solution Using New Guess</a></dt><dt>10.1. <a href="Tut_UsingGMATFunctions.html#Tut_UsingGMATFunctions_ID_1"><span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane</a></dt><dt>10.2. <a href="Tut_UsingGMATFunctions.html#Tut_UsingGMATFunctions_ID_2"><span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane</a></dt><dt>10.3. <a href="ch10s03.html#Tut_UsingGMATFunctions_1">Mission Sequence for the First Target sequence</a></dt><dt>10.4. <a href="ch10s03.html#N135B0"><span class="guilabel">Make Objects Global</span> Command
        Configuration</a></dt><dt>10.5. <a href="ch10s03.html#N135D7"><span class="guilabel">Target Desired B-Plane Coord. From Inside Function</span> Command Configuration</a></dt><dt>10.6. <a href="ch10s03.html#N1360D"><span class="guilabel">Report Parameters</span> Command Configuration</a></dt><dt>10.7. <a href="ch10s04.html#N1363F">3D View of departure hyperbolic trajectory (EarthView)</a></dt><dt>10.8. <a href="ch10s04.html#N1364B">3D View of heliocentric transfer trajectory
      (SolarSystemView)</a></dt><dt>10.9. <a href="ch10s04.html#N13657">3D View of approach hyperbolic trajectory. MAVEN stopped at
      periapsis (MarsView)</a></dt><dt>10.10. <a href="ch10s04.html#Tut_UsingGMATFunctions_8">Mission Sequence showing first and second Target
        sequences</a></dt><dt>10.11. <a href="ch10s04.html#N137E3"><span class="guilabel">Prop for 1 day</span> Command
        Configuration</a></dt><dt>10.12. <a href="ch10s04.html#N13812"><span class="guilabel">Mars Capture</span> Command Configuration</a></dt><dt>10.13. <a href="ch10s04.html#N13890"><span class="guilabel">Vary MOI</span> Parameter Selection</a></dt><dt>10.14. <a href="ch10s04.html#N1389E"><span class="guilabel">Vary MOI</span> Command Configuration</a></dt><dt>10.15. <a href="ch10s04.html#N138C5"><span class="guilabel">Apply MOI</span> Command Configuration</a></dt><dt>10.16. <a href="ch10s04.html#N138FB"><span class="guilabel">Prop to Mars Apoapsis</span> Command
        Configuration</a></dt><dt>10.17. <a href="ch10s04.html#N13949"><span class="guilabel">Achieve RMAG</span> Command Configuration</a></dt><dt>10.18. <a href="ch10s05.html#N13989">3D view of Mars Capture orbit after MOI maneuver
      (MarsView)</a></dt><dt>11.1. <a href="ch11s02.html#N13A75"><span class="guilabel">DefaultOrbitView</span> window</a></dt><dt>11.2. <a href="ch11s03.html#N13AAB">SolarSystem Panel</a></dt><dt>11.3. <a href="ch11s03.html#N13B48">Earth Panel </a></dt><dt>11.4. <a href="ch11s04.html#N13B76">Location of EclipseLocator</a></dt><dt>11.5. <a href="ch11s04.html#N13BC3">EclipseLocator Configuration</a></dt><dt>11.6. <a href="ch11s04.html#N13BF5">EclipseLocator Report</a></dt><dt>11.7. <a href="ch11s05.html#N13C8D">GroundStation Panel</a></dt><dt>11.8. <a href="ch11s05.html#N13CA1">Ground Track Plot Window</a></dt><dt>11.9. <a href="ch11s05.html#N13CF8">ContactLocator Configuration Window</a></dt><dt>11.10. <a href="ch11s05.html#N13D2A">ContactLocator Report</a></dt><dt>12.1. <a href="ch12s02.html#Tut_ElectricPropulsion_Fig2_Thruster1_Configuration"><span class="guilabel">ElectricThruster1</span> Configuration</a></dt><dt>12.2. <a href="ch12s02.html#Tut_ElectricPropulsion_Fig1_FuelTank1_Configuration"><span class="guilabel">ElectricTank1</span> Configuration</a></dt><dt>12.3. <a href="ch12s02.html#Tut_ElectricPropulsion_PowerSystemConfig"><span class="guilabel">SolarPowerSystem1</span> Configuration</a></dt><dt>12.4. <a href="ch12s02.html#Tut_ElectricPropulsion_Fig4_Attach_FuelTank1_to_DefaultSC">Attach <span class="guilabel">ElectricTank1</span> to
        <span class="guilabel">DefaultSC</span></a></dt><dt>12.5. <a href="ch12s02.html#Tut_ElectricPropulsion_Fig5_Attach_Thruster1_to_DefaultSC">Attach <span class="guilabel">ElectricThruster1</span> to
        <span class="guilabel">DefaultSC</span></a></dt><dt>12.6. <a href="ch12s02.html#Tut_ElectricPropulsion_AddPowerSystemToSat">Attach <span class="guilabel">SolarPowerSystem1</span> to
        <span class="guilabel">DefaultSC</span></a></dt><dt>12.7. <a href="ch12s02.html#Tut_ElectricPropulsion_Fig6_Creation_of_FiniteBurn_Resource_FiniteBurn1">Creation of <span class="guilabel">FiniteBurn</span> Resource
        <span class="guilabel">FiniteBurn1</span></a></dt><dt>12.8. <a href="ch12s03.html#Tut_ElectricPropulsion_Fig9_Final_Mission_Sequence">Final Mission Sequence</a></dt><dt>12.9. <a href="ch12s03.html#Tut_ElectricPropulsion_Fig8"><span class="guilabel">Prop To Perigee </span>Command
        Configuration</a></dt><dt>12.10. <a href="ch12s04.html#Tut_ElectricPropulsion_Fig17_3D_View_of_Finite_Burn_to_Raise_Apogee">3D View of <span class="guilabel">Finite Electric Maneuver</span></a></dt><dt>121. <a href="ReleaseNotesR2020a.html#R2020a_OFI_SensorCones">Visualization of sensor cones.</a></dt><dt>122. <a href="ReleaseNotesR2020a.html#R2020a_OFI_MoonRise">Earth rise from the Moon.</a></dt><dt>123. <a href="ReleaseNotesR2020a.html#R2020a_OFI_Shadows">Rendering of complex bodies with shadows.</a></dt><dt>124. <a href="ReleaseNotesR2020a.html#R2020a_OFI_MOCGraphics">Near real-time graphics example with inertial and body views
        and dynamic data display.</a></dt></dl></div><div class="list-of-tables"><p><b>List of Tables</b></p><dl><dt>5.1. <a href="ch05s02.html#N10F01"><span class="guilabel">Sat</span> Orbit State Settings</a></dt><dt>6.1. <a href="ch06s02.html#N111B9"><span class="guilabel">DefaultOrbitView</span> settings</a></dt><dt>6.2. <a href="ch06s03.html#Tut_HohmannTransfer_CommandTable">Additional <span class="guilabel">Target</span> Sequence
              Commands</a></dt><dt>7.1. <a href="ch07s04.html#Additional_Target_Sequence_Commands">Additional Target Sequence Commands</a></dt><dt>8.1. <a href="ch08s02.html#N11B59"><span class="guilabel">MainTank</span> settings</a></dt><dt>8.2. <a href="ch08s02.html#N11BC2"><span class="guilabel">MAVEN</span> settings</a></dt><dt>8.3. <a href="ch08s02.html#N11D14"><span class="guilabel">NearEarth</span> settings</a></dt><dt>8.4. <a href="ch08s02.html#N11DBF"><span class="guilabel">DeepSpace</span> settings</a></dt><dt>8.5. <a href="ch08s02.html#N11E58"><span class="guilabel">NearMars</span> settings</a></dt><dt>8.6. <a href="ch08s02.html#N11FDB"><span class="guilabel">EarthView</span> settings</a></dt><dt>8.7. <a href="ch08s02.html#N12032"><span class="guilabel">SolarSystemView</span> settings</a></dt><dt>8.8. <a href="ch08s02.html#N120BA"><span class="guilabel">MarsView</span> settings</a></dt><dt>8.9. <a href="ch08s03.html#Tut_Mars_B_Plane_Targeting_CommandTable">Additional First <span class="guilabel">Target</span> Sequence
              Commands</a></dt><dt>8.10. <a href="ch08s04.html#Tut_Mars_B_Plane_Second_Targeting_CommandTable">Additional Second <span class="guilabel">Target</span> Sequence
              Commands</a></dt><dt>10.1. <a href="ch10s02.html#N12E8D"><span class="guilabel">MainTank</span> settings</a></dt><dt>10.2. <a href="ch10s02.html#N12EF6"><span class="guilabel">MAVEN</span> settings</a></dt><dt>10.3. <a href="ch10s02.html#N13048"><span class="guilabel">NearEarth</span> settings</a></dt><dt>10.4. <a href="ch10s02.html#N130F3"><span class="guilabel">DeepSpace</span> settings</a></dt><dt>10.5. <a href="ch10s02.html#N1318C"><span class="guilabel">NearMars</span> settings</a></dt><dt>10.6. <a href="ch10s02.html#N1330F"><span class="guilabel">EarthView</span> settings</a></dt><dt>10.7. <a href="ch10s02.html#N13366"><span class="guilabel">SolarSystemView</span> settings</a></dt><dt>10.8. <a href="ch10s02.html#N133EE"><span class="guilabel">MarsView</span> settings</a></dt><dt>10.9. <a href="ch10s04.html#Tut_Mars_B_Plane_Second_Targeting_CommandTable_ID">Additional Second <span class="guilabel">Target</span> Sequence
              Commands</a></dt><dt>24. <a href="ReleaseNotesR2011a.html#N31E73">Multiple platforms</a></dt><dt>25. <a href="ReleaseNotesR2011a.html#N31EFE">Windows</a></dt><dt>26. <a href="ReleaseNotesR2011a.html#N31F6D">Mac OS X</a></dt><dt>27. <a href="ReleaseNotesR2011a.html#N31F9D">Linux</a></dt></dl></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%">&nbsp;</td><td align="center" width="20%">&nbsp;</td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Preface.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">&nbsp;</td><td align="center" width="20%">&nbsp;</td><td valign="top" align="right" width="40%">&nbsp;Documentation Overview</td></tr></table></div></body></html>