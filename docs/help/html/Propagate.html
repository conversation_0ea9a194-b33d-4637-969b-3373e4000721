<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Propagate</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18s02.html" title="Commands"><link rel="prev" href="Maneuver.html" title="Maneuver"><link rel="next" href="ch19.html" title="Chapter&nbsp;19.&nbsp;Input/Output"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Propagate</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Maneuver.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch19.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Propagate"></a><div class="titlepage"></div><a name="N226DF" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Propagate</span></h2><p>Propagate &mdash; Propagates spacecraft to a requested stopping
    condition</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><p>The <span class="guilabel">Propagate</span> command is a complex command that
    supports multiple <span class="guilabel">Propagators</span>, multiple
    <span class="guilabel">Spacecraft</span>, and multiple stopping conditions. In the
    syntax definition below, <code class="literal">SatList</code> is a comma separated
    list of spacecraft and <code class="literal">StopList</code> is a comma separated
    list of stopping conditions. The general syntax of the
    <span class="guilabel">Propagate</span> command is:</p><pre class="synopsis">
<code class="literal">Propagate</code> [Mode] [BackProp] <em class="replaceable"><code>Propagator1Name</code></em>(<em class="replaceable"><code>SatList1</code></em>,{<em class="replaceable"><code>StopList1</code></em>})...
                 <em class="replaceable"><code>Propagator2Name</code></em>(<em class="replaceable"><code>SatList2</code></em>,{<em class="replaceable"><code>StopList2</code></em>}

or

<code class="literal">Propagate</code> [Mode] [BackProp] <em class="replaceable"><code>Propagator1Name</code></em>(<em class="replaceable"><code>SatList1</code></em>)...
                 <em class="replaceable"><code>Propagator2Name</code></em>(<em class="replaceable"><code>SatList2</code></em>){<em class="replaceable"><code>StopList</code></em>}
    </pre><p>Most applications propagate a single
    <span class="guilabel">Spacecraft</span>, forward, to a single stopping condition.
    In that case, the syntax simplifies to:</p><pre class="synopsis">
<code class="literal">Propagate</code> <em class="replaceable"><code>PropagatorName</code></em>(<em class="replaceable"><code>SatName</code></em>,{<em class="replaceable"><code>StopCond</code></em>});

or

<code class="literal">Propagate</code> <em class="replaceable"><code>PropagatorName</code></em>(<em class="replaceable"><code>SatName</code></em>){<em class="replaceable"><code>StopCond</code></em>};
    </pre><p>In GMAT, syntax for setting orbit color on a
    <span class="guilabel">Propagate</span> command for a single
    <span class="guilabel">Spacecraft</span> propagating forward to a single stopping
    condition can be done by either identifying orbit color through ColorName
    or via RGB triplet value:</p><pre class="synopsis">
<code class="literal">Propagate</code> <em class="replaceable"><code>PropagatorName</code></em>(<em class="replaceable"><code>SatName</code></em>),{<em class="replaceable"><code>StopCond, OrbitColor = ColorName</code></em>};

or

<code class="literal">Propagate</code> <em class="replaceable"><code>PropagatorName</code></em>(<em class="replaceable"><code>SatName</code></em>),{<em class="replaceable"><code>StopCond, OrbitColor = [RGB triplet value]</code></em>};
    </pre></div><div class="refsection"><a name="N22771"></a><h2>Description</h2><p>The <span class="guilabel">Propagate</span> command controls the time
    evolution of spacecraft. GMAT allows you to propagate single
    <span class="guilabel">Spacecraft</span>, multiple non-cooperative
    <span class="guilabel">Spacecraft</span>, and <span class="guilabel">Formations</span> in a
    single <span class="guilabel">Propagate</span> command. The
    <span class="guilabel">Propagate</span> command is complex and controls the
    following aspects of the temporal modeling of spacecraft:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The <span class="guilabel">Spacecraft</span> to be propagated</p></li><li class="listitem"><p>The model(s) used for the propagation (numerical integration,
        ephemeris interpolation)</p></li><li class="listitem"><p>The condition(s) to be satisfied at the termination of
        propagation</p></li><li class="listitem"><p>The direction of propagation (forwards or backwards in
        time)</p></li><li class="listitem"><p>The time synchronization of multiple
        <span class="guilabel">Spacecraft</span></p></li><li class="listitem"><p>Propagation of STM and computation of state Jacobian
        (A-matrix)</p></li><li class="listitem"><p>Setting unique colors on different
        <span class="guilabel">Spacecraft</span> trajectory segments through
        <span class="guilabel">Propagate</span> commands</p></li></ul></div><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a>, <a class="xref" href="Formation.html" title="Formation"><span class="refentrytitle">Formation</span></a>, <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a></p></div><div class="refsection"><a name="Propagate_Options"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Mode</span></td><td><p>Optional flag to time-synchronize propagation of
            <span class="guilabel">Spacecraft</span> performed by multiple
            <span class="guilabel">Propagators</span> in a single
            <span class="guilabel">Propagate</span> command. See <a class="xref" href="Propagate.html#Propagate_Remarks" title="Remarks">the section called &ldquo;Remarks&rdquo;</a> for more details. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>
                      <span class="guilabel">Synchronized </span>
                    </p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Not used</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BackProp</span></td><td><p> Optional flag to propagate all
            <span class="guilabel">Spacecraft</span> in a
            <span class="guilabel">Propagate</span> command backwards in time. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>
                      <span class="guilabel">BackProp</span>
                    </p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Not used</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">StopList</span></td><td><p>A comma separated list of stopping conditions.
            Stopping conditions must be parameters of propagated
            <span class="guibutton">Spacecraft</span> in
            <span class="guibutton">SatList</span>. See <a class="xref" href="Propagate.html#Propagate_Remarks" title="Remarks">the section called &ldquo;Remarks&rdquo;</a> for more details. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid list of stopping conditions</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">ElapsedSecs = 12000</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SatList</span></td><td><p> A comma separated list of
            <span class="guilabel">Spacecraft</span>. For SPK type
            <span class="guilabel">Propagators</span>, the
            <span class="guilabel">Spacecraft</span> must be configured with valid SPK
            kernels. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Resource array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid list of spacecraft and/or formations</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultSC</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PropagatorName</span></td><td><p> A propagator name. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>
                      <span class="guilabel">Propagator</span>
                    </p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid <span class="guilabel">Propagator</span> name</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultProp</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">StopTolerance</span></td><td><p> Tolerance on the stopping condition root location.
            See <a class="xref" href="Propagate.html#Propagate_Remarks" title="Remarks">the section called &ldquo;Remarks&rdquo;</a> for more details. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real number &gt; 0</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.0000001</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">STM</span></td><td><p> Optional flag to propagate the orbit STM. STM
            propagation only occurs for numerical integrator type propagators.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>
                      <span class="guilabel">STM</span>
                    </p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Not used</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AMatrix</span></td><td><p> The Jacobian of the orbital acceleration. The
            partial of the first order acceleration vector with respect to the
            state vector.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>
                      <span class="guilabel">AMatrix</span>
                    </p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Not used</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Covariance</span></td><td><p> Optional flag to propagate the orbit Covariance.
            Covariance propagation only occurs for numerical integrator type
            propagators. Selecting this option will also propagate the STM.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>
                      <span class="guilabel">Covariance</span>
                    </p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Not used</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitColor</span></td><td><p>Sets orbit color on a <span class="guilabel">Propagate</span>
            command. Default color on <span class="guilabel">Propagate</span> segment
            is seeded from color that is set on
            <span class="guilabel">Spacecraft.OrbitColor</span> field. To set unique
            colors on <span class="guilabel">Propagate</span> command in script mode:
            Enter ColorName or RGB triplet value for the color of your choice.
            In GUI mode, select unique color of your choice on the
            <span class="guilabel">Propagate</span> command by clicking on Orbit Color
            Selectbox. For Example: Setting yellow color on
            <span class="guilabel">Propagate</span> segment in script mode can be done
            in either of the following two ways: <code class="literal">Propagate
            DefaultProp(DefaultSC) {DefaultSC.Earth.Apoapsis, OrbitColor =
            Yellow}</code> or <code class="literal">Propagate DefaultProp(DefaultSC)
            {DefaultSC.Earth.Apoapsis, OrbitColor = [255 255
            0]}</code>.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Integer Array or String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any color available from the Orbit Color Picker in
                    GUI. Valid predefined color name or RGB triplet value
                    between 0 and 255.</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Default color on <span class="guilabel">Propagate</span>
                    command is color that is first set on
                    <span class="guilabel">Spacecraft.OrbitColor</span> field. Default
                    color on <span class="guilabel">Spacecraft.OrbitColor</span> is
                    Red. Therefore default color for
                    <span class="guilabel">Propagate</span> command is Red.</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N229BE"></a><h2>GUI</h2><div class="refsection"><a name="Propagate_GUI_Introduction"></a><h3>Introduction</h3><p>The <span class="guilabel">Propagate</span> command GUI provides an
      interface to assign <span class="guilabel">Spacecraft</span> to
      <span class="guilabel">Propagators</span> used for propagation and to define a
      set of conditions to terminate propagation. The GUI also allows you to
      define the direction of propagation, the synchronization mode for
      multiple spacecraft, and whether or not to propagate the STM and compute
      the A-Matrix.</p><p>To follow the examples below, you can load the following script
      snippet or create a new mission with three spacecraft (named
      <span class="guilabel">sat1</span>, <span class="guilabel">sat2</span>, and
      <span class="guilabel">sat3</span>) and two propagators (named
      <span class="guilabel">prop1</span> and <span class="guilabel">prop2</span>).</p><pre class="programlisting"><code class="code">Create Spacecraft sat1 sat2 sat3
Create Propagator prop1 prop2
BeginMissionSequence</code>r</pre></div><div class="refsection"><a name="N229E5"></a><h3>Defining Spacecraft and Propagators</h3><p>To demonstrate how to define a set of propagators and
      <span class="guilabel">Spacecraft</span> for propagation, you will set up a
      <span class="guilabel">Propagate</span> command to propagate a
      <span class="guilabel">Spacecraft</span> named <span class="guilabel">sat1</span> using a
      <span class="guilabel">Propagator</span> named <span class="guilabel">prop1</span> and
      <span class="guilabel">Spacecraft</span> named <span class="guilabel">sat2</span> and
      <span class="guilabel">sat3</span> using a <span class="guilabel">Propagator</span> named
      <span class="guilabel">prop2</span>. You will configure the command to propagate
      for 1 day or until <span class="guilabel">sat2</span> reaches periapsis,
      whichever happens first. You will need to configure GMAT as described in
      the <a class="xref" href="Propagate.html#Propagate_GUI_Introduction" title="Introduction">the section called &ldquo;Introduction&rdquo;</a> section and add a new
      <span class="guilabel">Propagate</span> command to your mission sequence. GMAT
      auto-populates the <span class="guilabel">Propagate</span> command GUI with the
      first <span class="guilabel">Propagator</span> in the GUI list and the first
      <span class="guilabel">Spacecraft</span> when you add a new
      <span class="guilabel">Propagate</span> command so you should start from this
      point.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Propagate_GUI_1A.png" align="middle" height="481"></td></tr></table></div></div><p>To add a second <span class="guilabel">Propagator</span> to propagate
      <span class="guilabel">sat2</span> and <span class="guilabel">sat3</span> using
      <span class="guilabel">prop2</span>:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>In the <span class="guilabel">Propagator</span> list, click the
          ellipsis button in the second row to open the <span class="guilabel">Propagator
          Select Dialog</span>.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Propagate_GUI_2.png" align="middle" height="210"></td></tr></table></div></div></li><li class="listitem"><p>In the <span class="guilabel">Available Propagators</span> list, click
          on <span class="guilabel">prop2</span>, and click
          <span class="guilabel">OK</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">Spacecraft</span><span class="guilabel">
          List</span>, click the ellipsis button in the second row to open
          the <span class="guilabel">Space Object Select</span> dialog.</p></li><li class="listitem"><p>Click the right-arrow twice to add <span class="guilabel">sat2</span>
          and <span class="guilabel">sat3</span> to the list of selected spacecraft and
          click <span class="guilabel">Ok</span>.</p></li></ol></div><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Propagate_GUI_3A.png" align="middle" height="482"></td></tr></table></div></div></div><div class="refsection"><a name="N22A76"></a><h3>Stopping conditions</h3><p>Continuing with the example above, now you will configure GMAT to
      propagate for one elapsed day or until <span class="guilabel">sat2</span> reaches
      periapsis.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>In the <span class="guilabel">Parameter</span> list, click the ellipsis
          button in the first row to bring up the <span class="guilabel">Parameter Select
          Dialog</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">ObjectProperties</span> list, double
          click <span class="guilabel">ElapsedDays</span>, and click<span class="guilabel">
          OK</span>.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Propagate_GUI_4.png" align="middle" height="416"></td></tr></table></div></div></li><li class="listitem"><p>In the <span class="guilabel">Condition</span> list, double click the
          first row containing <code class="literal">12000</code>, type
          <code class="literal">1</code>, and click OK.</p></li><li class="listitem"><p>In the <span class="guilabel">Parameter</span> list, click the ellipsis
          button in the second row to bring up the <span class="guilabel">Parameter Select
          Dialog</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">Object</span> list, click
          <span class="guilabel">Sat2</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">ObjectProperties</span> list, double
          click <span class="guilabel">Periapsis</span> and click
          <span class="guilabel">OK</span>.</p></li></ol></div><p>The <span class="guilabel">Propagate1</span> dialog should now look like
      the image below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Propagate_GUI_5A.png" align="middle" height="479"></td></tr></table></div></div><p></p></div></div><div class="refsection"><a name="Propagate_Remarks"></a><h2>Remarks</h2><div class="refsection"><a name="Propagate_Remarks_Introduction"></a><h3>Introduction</h3><p>The <span class="guilabel">Propagate</span> command documentation below
      describes how to propagate single and multiple
      <span class="guilabel">Spacecraft</span> to desired conditions forward and
      backwards in time. To streamline the script examples, the objects
      <span class="guilabel">numSat</span>, <span class="guilabel">spkSat</span>,
      <span class="guilabel">numProp</span>, and <span class="guilabel">spkProp</span> are
      assumed to be configured as shown below. GMAT is distributed with the
      SPK kernels used in the examples.</p><pre class="programlisting"><code class="code">Create Spacecraft spkSat;
spkSat.Epoch.UTCGregorian   = '02 Jun 2004 12:00:00.000'
spkSat.NAIFId               = -123456789;
spkSat.OrbitSpiceKernelName = {'..\data\vehicle\ephem\spk\GEOSat.bsp'};

Create Spacecraft numSat
numSat.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'

Create Propagator spkProp;
spkProp.Type       = SPK;
spkProp.StartEpoch = FromSpacecraft

Create Propagator numProp
numProp.Type = PrinceDormand78

BeginMissionSequence</code></pre></div><div class="refsection"><a name="N22AF5"></a><h3>How to Propagate a Single Spacecraft</h3><p><code class="literal">Note:</code> See the <a class="xref" href="Propagate.html#Propagate_Remarks_Introduction" title="Introduction">the section called &ldquo;Introduction&rdquo;</a> section for a script snippet
      to configure GMAT to execute the examples in this section.</p><p>The <span class="guilabel">Propagate</span> command provides a simple
      interface to propagate a <span class="guilabel">Spacecraft</span> to a stopping
      condition or to take a single propagation step. To propagate a single
      <span class="guilabel">Spacecraft</span> you must specify the desired
      <span class="guilabel">Propagator</span>, the <span class="guilabel">Spacecraft</span> to
      propagate, and if desired, the stopping condition. The
      <span class="guilabel">Propagate</span> command supports numerical integrator and
      ephemeris type propagators. For single <span class="guilabel">Spacecraft</span>
      propagation, the syntax is the same regardless of propagator type. For
      example, to propagate a <span class="guilabel">Spacecraft</span> using a
      numerical integrator, you can use the following script snippet:</p><pre class="programlisting"><code class="code">Propagate numProp(numSat){numSat.Periapsis}
% or
Propagate numProp(numSat,{numSat.Periapsis})</code></pre><p>To propagate a single <span class="guilabel">Spacecraft</span> using a
      <span class="guilabel">Propagato</span>r configured to use an SPK kernel use the
      following:</p><pre class="programlisting"><code class="code">Propagate spkProp(spkSat){spkSat.TA = 90}
% or
Propagate spkProp(spkSat,{spkSat.TA = 90})</code></pre><p>To take a single propagation step, simply omit the stopping
      conditions as shown below. The <span class="guilabel">Propagator</span> will take
      a step based on its step size control algorithm. See the <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a> documentation for more information on step size
      control.</p><pre class="programlisting"><code class="code">Propagate numProp(numSat)
% or
Propagate spkProp(spkSat) </code></pre></div><div class="refsection"><a name="N22B32"></a><h3>How to Propagate Multiple Spacecraft</h3><p>The <span class="guilabel">Propagate</span> command allows you to propagate
      multiple <span class="guilabel">Spacecraft</span> by including a list of
      <span class="guilabel">Spacecraft</span> in a single
      <span class="guilabel">Propagator</span>, by including a
      <span class="guilabel">Formation</span> in a <span class="guilabel">Propagator</span>,
      and/or by including multiple <span class="guilabel">Propagators</span> in a
      single command. For example purposes, here is a script snippet that
      propagates multiple <span class="guilabel">Spacecraft</span>.</p><pre class="programlisting"><code class="code">Propagate Synchronized Prop1(Sat1,Sat2) Prop2(Sat3,Sat4)...
Prop3(aFormation){Sat1.Earth.Periapsis}</code></pre><p>In the script line above <span class="guilabel">Sat1</span> and
      <span class="guilabel">Sat2</span> are propagated using
      <span class="guilabel">Prop1</span>; <span class="guilabel">Prop2</span> is used to
      propagate <span class="guilabel">Sat3</span> and <span class="guilabel">Sat4</span>; all
      <span class="guilabel">Spacecraft</span> added to <span class="guilabel">aFormation</span>
      are propagated using<span class="guilabel"> Prop3</span>. The
      <span class="guilabel">Propagate</span> command configured above propagates all
      <span class="guilabel">Spacecraft</span> until <span class="guilabel">Sat1</span> reaches
      Earth periapsis.</p><p>All <span class="guilabel">Spacecraft</span> propagated by the same
      <span class="guilabel">Propagator</span> are time synchronized during
      propagation. By time synchronization, we mean that all
      <span class="guilabel">Spacecraft</span> are propagated across the same time
      step. The <span class="guilabel">Synchronized</span> keyword tells GMAT to keep
      <span class="guilabel">Spacecraft</span> propagated by different
      <span class="guilabel">Propagators</span> synchronized in time during
      propagation. Time synchronization among multiple
      <span class="guilabel">Propagators</span> is performed by taking a single step
      for all <span class="guilabel">Spacecraft</span> controlled by the first
      <span class="guilabel">Propagator</span> (<span class="guilabel">Prop1</span> in the above
      example), and then stepping all other <span class="guilabel">Propagators</span>
      to that time. When the <span class="guilabel">Synchronized</span> keyword is
      omitted, <span class="guilabel">Spacecraft</span> propagated by different
      <span class="guilabel">Propagators</span> are not synchronized in time. In that
      case, each <span class="guilabel">Propagator</span> takes steps determined by its
      step size control algorithm without regard to the other
      <span class="guilabel">Propagators</span> in the <span class="guilabel">Propagate</span>
      command. Time synchronization is particularly useful if you need
      ephemeris files for multiple spacecraft with consistent time tags, or if
      you are visualizing multiple spacecraft in an
      <span class="guilabel">OrbitView</span>.</p><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Caution: When using a <span class="guilabel">Propagator</span> configured
        to use SPK kernels, you can only have one
        <span class="guilabel">Spacecraft</span> per
        <span class="guilabel">Propagator</span>.</p><p>This is supported: <code class="literal"></code></p><p><code class="literal">Propagate numProp(numSat) spkProp(spkSat1)
        spkProp(spkSat2)</code></p><p>This is NOT supported!</p><p><code class="literal">Propagate numProp(numSat)
        spkProp(spkSat1,spkSat2)</code></p></div></div><div class="refsection"><a name="N22BC7"></a><h3>Behavior of Stopping Conditions</h3><p>GMAT allows you to define a set of stopping conditions when
      propagating <span class="guilabel">Spacecraft</span> that define conditions that
      must be satisfied at the termination of the
      <span class="guilabel">Propagate</span> command. For example, it is often useful
      to propagate to an orbital location such as Apogee. When no stopping
      condition is provided, the <span class="guilabel">Propagate</span> command takes
      a single step. When given a set of stopping conditions, the
      <span class="guilabel">Propagate</span> command propagates the
      <span class="guilabel">Spacecraft </span>to the condition that occurs first in
      elapsed propagation time and terminates propagation. There are several
      ways to define stopping conditions via the script interface. One is to
      include a comma separated list of stopping conditions with each
      <span class="guilabel">Propagator</span> like this.</p><pre class="programlisting"><code class="code">Propagate Prop1(Sat1,{Sat1.Periapsis}) Prop2(Sat2,{Sat2.Periapsis}) </code></pre><p>A second approach is to define a comma separated list of stopping
      conditions at the end of the <span class="guilabel">Propagate</span> command like
      this.</p><pre class="programlisting"><code class="code">Propagate Prop1(Sat1) Prop2(Sat2) {Sat1.Periapsis,Sat2.Periapsis}</code></pre><p>Note that the above two methods result in the same stopping epoch.
      When you provide a set of stopping conditions, regardless of where in
      the command the stopping condition is defined, GMAT builds a list of all
      conditions and tracks them until the first condition occurs.</p><p>The <span class="guilabel">Propagate</span> command currently requires that
      the left hand side of a stopping condition is a valid
      <span class="guilabel">Spacecraft</span> parameter. For example, the first line
      in the following example is supported and the second line is not
      supported.</p><pre class="programlisting"><code class="code">Propagate Prop1(Sat1) {Sat1.TA = 45}  % Supported
Propagate Prop1(Sat1) {45 = Sat1.TA}  % Not supported </code></pre><p>GMAT supports special built-in stopping conditions for apoapsis
      and periapsis like this:</p><pre class="programlisting"><code class="code">Propagate Prop1(Sat1) {Sat1.Apoapsis}
Propagate Prop1(Sat1) {Sat1.Mars.Periapsis} </code></pre><p>You can
      define the tolerance on the stopping condition by including the
      <span class="guilabel">StopTolerance</span> keyword in the
      <span class="guilabel">Propagate</span> command as shown below. In this example,
      GMAT will propagate until the true anomaly of <span class="guilabel">Sat1</span>
      is 90 degrees to within +/- 1e-5 degrees.</p><pre class="programlisting"><code class="code">Propagate Prop1(Sat1) {Sat1.TA = 90, StopTolerance = 1e-5}</code></pre><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Caution: GMAT currently propagates
        <span class="guilabel">Spacecraft</span> to a time quantization of a few
        microseconds. Depending upon the rate of the stopping condition
        function, it may not be possible to locate the stopping condition to
        the requested <span class="guilabel">StopTolerance</span>. In that case, GMAT
        throws a warning to alert you that the tolerance was not satisfied and
        provides information on the achieved stopping value and the requested
        tolerance.</p><p>Note: GMAT does not currently support tolerances on a per
        stopping condition basis. If you include
        <span class="guilabel">StopTolerance</span> multiple times in a single
        <span class="guilabel">Propagate</span> command, GMAT uses the last value
        provided.</p></div><p>The <span class="guilabel">Propagate</span> command uses an algorithm
      called the First Step Algorithm (FSA) when back-to-back propagations
      occur and both propagations have at least one stopping condition that is
      the same in both commands. For example:</p><pre class="programlisting"><code class="code">Propagate prop1(Sat1) {Sat1.TA = 90}
Propagate prop1(Sat1) {Sat1.TA = 90, StopTolerance = 1e-4}</code></pre><p>The <span class="guilabel">FSA</span> determines the behavior of the first
      step when the last propagation performed on a
      <span class="guilabel">Spacecraft</span> was terminated using a stopping
      condition listed in the current command. If the error in the stopping
      condition at the initial epoch of the second
      <span class="guilabel">Propagate</span> command is less than
      SafetyFactor*<span class="guilabel">StopTolerance</span>, the propagate command
      will take one integration step before attempting to locate the stopping
      condition again. In the FSA, SafetyFactor = 10, and the
      <span class="guilabel">StopTolerance</span> is from the second
      <span class="guilabel">Propagate</span> command. Continuing with the example
      above, if abs(TA_Achieved - TA_Desired) &lt; 1e-3 -- where TA_Achieved
      is the TA after the first <span class="guilabel">Propagate</span> command and
      TA_Desired is the requested value of TA in the second
      <span class="guilabel">Propagate</span> command -- then the
      <span class="guilabel">Propagate</span> command will take one step before
      attempting to locate the stopping condition. The first step algorithm
      works the same way for forward propagation, backwards propagation, and
      changing propagation directions.</p><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Caution: It is possible to specify a
        <span class="guilabel">StopTolerance</span> that cannot be satisfied by the
        stopping condition root locators and in that case, a warning is
        thrown. However, subsequent <span class="guilabel">Propagate</span> commands
        using the same stopping conditions may not behave as desired. For the
        FSA algorithm to work as designed, you must provide
        <span class="guilabel">StopTolerance</span> values that are achievable.</p></div></div><div class="refsection"><a name="N22C4B"></a><h3>How to Propagate Backwards</h3><p>To propagate backwards using the script interface, include the
      keyword <span class="guilabel">BackProp</span> between the
      <span class="guilabel">Propagate</span> command and the first
      <span class="guilabel">Propagator</span> in the command as shown below. All
      <span class="guilabel">Propagators</span> in the command will propagate
      backwards.</p><pre class="programlisting"><code class="code">Propagate Synchronized BackProp Prop1(Sat1,Sat2) Prop2(Sat3,Sat4)...
           Prop3(aFormation){Sat1.Earth.Periapsis}

Propagate Backprop numProp(numSat){numSat.Periapsis}</code></pre></div><div class="refsection"><a name="N22C5F"></a><h3>How to Propagate the STM, Covariance, and Compute the Jacobian
      (A-matrix)</h3><p>GMAT propagates the STM and Covariance for all
      <span class="guilabel">Spacecraft</span> propagated using numerical integrators
      by including the <code class="literal">STM</code> and
      <code class="literal">Covariance</code> keyword in a
      <span class="guilabel">Propagate</span> command as shown below. If the STM or
      Covariance keyword is included anywhere in a
      <span class="guilabel">Propagate</span> command, the STM and Covariance is
      propagated for all spacecraft using numerical propagators. When
      propagating multiple spacecraft with
      <span class="guilabel">ProcessNoiseModels</span> assigned, each <span class="bold"><strong>ProcessNoiseModel</strong></span> must have the same value of the
      <span class="guilabel">UpdateTimeStep</span> parameter.</p><pre class="programlisting"><code class="code">Propagate Backprop numProp(numSat, 'STM', 'Covariance') {numSat.Periapsis}</code></pre><p>GMAT does not currently support propagating the STM or Covariance
      when propagating <span class="guilabel">Formation</span> resources or when using
      ephemeris type propagators.</p></div><div class="refsection"><a name="N22C85"></a><h3>Special Considerations for Covariance Propagation</h3><p>Covariance is propagated using a linearized model identical to
      that employed in the Extended Kalman Filter resource. The initial
      covariance is specified on the <span class="guilabel">Spacecraft</span>
      <span class="guilabel">OrbitErrorCovariance</span> parameter. If no process noise
      model is assigned to the spacecraft object, the covariance propagation
      steps are the integrator steps and the initial covariance is propagated
      without the inclusion of process noise. To include process noise in the
      covariance propagation, define a <span class="guilabel">ProcessNoiseModel</span>
      and assign it on the <span class="guilabel">Spacecraft</span>
      <span class="guilabel">ProcessNoiseModel</span> parameter.</p><p>When process noise is included, the covariance propagation steps
      depend on both the integrator step size and the
      <span class="guilabel">ProcessNoiseModel</span>
      <span class="guilabel">UpdateTimeStep</span> parameter. If UpdateTimeStep is set
      to 0, the covariance propagation steps solely at the integration step
      size. If UpdateTimeStep is non-zero, the UpdateTimeSteps form a grid of
      stopping points for the integrator. Propagation of the state and
      covariance will proceed to each UpdateTimeStep stopping point using the
      integrator step size setting. In particular, for example, if
      UpdateTimeStep is smaller than the integrator step size, the integrator
      will step at only the UpdateTimeStep intervals. If UpdateTimeStep is
      larger than the integration step size, the integrator will step to each
      UpdateTimeStep stopping point at steps equal to, or smaller than the
      integrator step size, depending on the difference between the current
      propagation epoch and the next stopping point.</p><p>No consider parameters are currently implemented for covariance
      propagation. GMAT will only propagate the 6x6 position/velocity
      covariance. Covariance propagation is currently only allowed when
      specifying the initial state and initial
      <span class="guilabel">OrbitErrorCovariance</span> in a reference frame using
      J2000Eq axes.</p></div><div class="refsection"><a name="N22CA6"></a><h3>Limitations of the Propagate Command</h3><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>When using an SPK-type <span class="guilabel">Propagator</span>, only a
          single <span class="guilabel">Spacecraft</span> can be propagated by a given
          <span class="guilabel">Propagator</span>.</p></li><li class="listitem"><p>GMAT does not currently support propagating the STM when
          propagating <span class="guilabel">Formation</span> objects. Covariance
          cannot be propagated for a Formation object.</p></li><li class="listitem"><p>When computing the A-matrix during propagation, the A-matrix
          values are only accessible via the C-Interface.</p></li></ul></div></div><div class="refsection"><a name="N22CBF"></a><h3>Setting Colors on the Propagate Command</h3><p>GMAT allows you to assign unique colors to
      <span class="guilabel">Spacecraft</span> trajectory segments by setting orbital
      colors on each <span class="guilabel">Propagate</span> command. If you do not set
      unique colors on each <span class="guilabel">Propagate</span> command, then by
      default, the color on each propagate segment is seeded from color that
      is set on <span class="guilabel">Spacecraft.OrbitColor</span> field. See the
      <a class="xref" href="Propagate.html#Propagate_Options" title="Options">Options</a> section for
      <span class="guilabel">OrbitColor</span> option that lets you set colors on the
      <span class="guilabel">Propagate</span> command. Also see <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a>
      documentation for discussion and examples on how to set unique colors on
      orbital trajectory segments through GMAT's
      <span class="guilabel">Propagate</span> command.</p></div></div><div class="refsection"><a name="N22CE0"></a><h2>Examples</h2><div class="informalexample"><p>Propagate a single <span class="guilabel">Spacecraft </span>to Earth
      periapsis</p><pre class="programlisting"><code class="code">Create Spacecraft numSat
numSat.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'

Create Propagator numProp
numProp.Type = PrinceDormand78

BeginMissionSequence

Propagate numProp(numSat) {numSat.Earth.Periapsis}</code></pre></div><div class="informalexample"><p>Propagate a single <span class="guilabel">Spacecraft</span> for one
      day.</p><pre class="programlisting"><code class="code">Create Spacecraft numSat
numSat.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'

Create Propagator numProp
numProp.Type = PrinceDormand78

BeginMissionSequence

Propagate numProp(numSat) {numSat.ElapsedDays = 1}</code></pre></div><div class="informalexample"><p>Propagate a single <span class="guilabel">Spacecraft </span>backwards to
      true anomaly of 90 degrees.</p><pre class="programlisting"><code class="code">Create Spacecraft numSat
numSat.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'

Create Propagator numProp
numProp.Type = PrinceDormand78

BeginMissionSequence

Propagate BackProp numProp(numSat) {numSat.TA = 90}</code></pre></div><div class="informalexample"><p>Propagate two <span class="guilabel">Spacecraft</span>, each using a
      different <span class="guilabel">Propagator</span>, but keep the
      <span class="guilabel">Spacecraft</span> synchronized in time. Propagate until
      either <span class="guilabel">Spacecraft</span> reaches a mean anomaly of 45
      degrees.</p><pre class="programlisting">Create Spacecraft aSat1 aSat2
aSat1.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'
aSat2.Epoch.UTCGregorian = '02 Jun 2004 12:00:00.000'
aSat2.TA = 0;

Create Propagator aProp1
aProp1.Type = PrinceDormand78
Create Propagator aProp2
aProp2.Type = PrinceDormand78

BeginMissionSequence

Propagate Synchronized aProp1(aSat1) aProp2(aSat2) ...
                      {aSat1.MA = 45,aSat2.MA = 45} 
</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Maneuver.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch19.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Maneuver&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;19.&nbsp;Input/Output</td></tr></table></div></body></html>