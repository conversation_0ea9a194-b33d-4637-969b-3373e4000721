<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="DSN_Estimation_Appendix_D.html" title="Appendix D &ndash; Change Scripts to use Ground Network (GN) Data"><link rel="next" href="ch15s02.html" title="Simulate GPS_PosVec measurements"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="DSN_Estimation_Appendix_D.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch15s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="FilterSmoother_GpsPosVec"></a>Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="FilterSmoother_GpsPosVec.html#N151E3">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch15s02.html">Simulate GPS_PosVec measurements</a></span></dt><dt><span class="section"><a href="ch15s03.html">Estimate the orbit</a></span></dt><dt><span class="section"><a href="ch15s04.html">Review and quality check the filter run</a></span></dt><dt><span class="section"><a href="ch15s05.html">Modify the estimation script to use a smoother to improve the
    estimates</a></span></dt><dt><span class="section"><a href="ch15s06.html">Review and quality check the smoother run</a></span></dt><dt><span class="section"><a href="ch15s07.html">Warm-start the filter</a></span></dt><dt><span class="section"><a href="ch15s08.html">A few words about filter tuning</a></span></dt><dt><span class="section"><a href="ch15s09.html">References</a></span></dt><dt><span class="section"><a href="ch15s10.html">Appendix A. Generate an ephemeris while running the filter and
    smoother</a></span></dt><dt><span class="section"><a href="ch15s11.html">Appendix B. Run the script from the command-line</a></span></dt><dt><span class="section"><a href="ch15s12.html">Appendix C. Check covariance matrix conditioning</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Advanced</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>2 hours</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Ex_R2022a_FilterSmoother_GpsPosVec.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N151E3"></a>Objective and Overview</h2></div></div></div><p>This exercise will demonstrate the use of GMAT for orbit
    determination using spacecraft on-board GPS position estimates. We will go
    through the following steps:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Create a script to simulate GPS measurements,</p></li><li class="listitem"><p>Create a script to use an Extended Kalman Filter to estimate the
        orbit state using the simulated measurements,</p></li><li class="listitem"><p>Review and quality check the filter run,</p></li><li class="listitem"><p>Modify the estimation script to use a Fraser-Potter smoother to
        improve the estimates by backward filtering and forward smoothing the
        filter solution,</p></li><li class="listitem"><p>Review the output and execute analysis scripts to evaluate the
        smoother run,</p></li><li class="listitem"><p>Explore a demonstration of &ldquo;warm-starting&rdquo; the filter.</p></li></ol></div><p>We&rsquo;ll wrap up at the end with a few words about filter
    tuning.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="DSN_Estimation_Appendix_D.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch15s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix D &ndash; Change Scripts to use Ground Network (GN) Data&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Simulate GPS_PosVec measurements</td></tr></table></div></body></html>