<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>OpenFramesInterface</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch19.html#N22D14" title="Resources"><link rel="prev" href="GroundTrackPlot.html" title="GroundTrackPlot"><link rel="next" href="OrbitView.html" title="OrbitView"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">OpenFramesInterface</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="GroundTrackPlot.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="OrbitView.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="OpenFramesInterface"></a><div class="titlepage"></div><a name="N23BB8" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">OpenFramesInterface</span></h2><p>OpenFramesInterface &mdash; A user-defined resource that provides high-performance 3D interactive visualizations of GMAT missions</p></div><div class="refsection"><a name="N23BC9"></a><h2>Description</h2><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>
      The main <span class="guilabel">OpenFramesInterface</span> documentation is available on its online <a class="link" href="https://gitlab.com/EmergentSpaceTechnologies/OpenFramesInterface/wikis/home" target="_top">GitLab Wiki</a>.
      You can directly access relevant Wiki sections by pressing the <span class="guibutton">Help</span> button on
      any GUI panel.
    </p></div><p>The <span class="guilabel">OpenFramesInterface</span> <span class="guilabel">(OFI)</span> resource allows you to
      visualize GMAT missions using interactive 3D graphics that are high-performance, customizable, and easy to use.
      <span class="guilabel">OFI</span> is developed as a replacement for <span class="guilabel">OrbitView</span>, so it provides
      greater functionality and performance while retaining a similar GUI and script format. You will be able to
      easily use the <span class="guilabel">OpenFramesInterface</span> regardless of your comfort level
      with <span class="guilabel">OrbitView</span>!
    </p><p>Features and benefits of <span class="guilabel">OFI</span> include:
      </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Create multiple interactive views per window. Each view can follow spacecraft or other
          bodies, and can even automatically rotate to track another object.</p></li><li class="listitem"><p>Control simulation time, animate the scene at any desired rate (including realtime and backwards time),
          and synchronize time between multiple windows.</p></li><li class="listitem"><p>Many visualization changes apply instantly without requiring a mission re-run.</p></li><li class="listitem"><p>Use various 3D model formats for spacecraft and bodies: 3ds, lwo, obj, and more.</p></li><li class="listitem"><p>View any GMAT mission in Virtual Reality using headsets such as the Oculus Rift or HTC Vive.
          VR provides information about nonplanar trajectories that is difficult to glean on a
          traditional monitor.</p></li></ul></div><p>
    </p><div class="tip" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Tip</h3><p>
      The GMAT <code class="code">samples/NeedOpenFramesInterface</code> folder has examples of using <span class="guilabel">OFI</span>
      based on similarly-named <span class="guilabel">OrbitView</span> sample scripts.
    </p></div><p><span class="ref_seealso">See Also</span>: <a class="xref" href="OrbitView.html" title="OrbitView"><span class="refentrytitle">OrbitView</span></a></p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="GroundTrackPlot.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch19.html#N22D14">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="OrbitView.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">GroundTrackPlot&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;OrbitView</td></tr></table></div></body></html>