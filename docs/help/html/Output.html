<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Output Tree</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="TourOfGmat.html" title="Chapter&nbsp;3.&nbsp;Tour of GMAT"><link rel="prev" href="CommandSummary.html" title="Command Summary"><link rel="next" href="ScriptEditor.html" title="Script Editor"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Output Tree</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="CommandSummary.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;3.&nbsp;Tour of GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ScriptEditor.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Output"></a>Output Tree</h2></div></div></div><a name="N10A10" class="indexterm"></a><p>The Output tree contains data files and plots after a mission is
  executed. Files consist of output from
  <span class="guilabel">Report</span><span class="guilabel">File</span> and
  <span class="guilabel">EphemerisFile</span> resources. Plots consist of graphical
  <span class="guilabel">OrbitView</span>,<span class="guilabel"> GroundTrackPlot</span>, and
  <span class="guilabel">XYPlots</span> windows.</p><p>To display the contents of an output file, double-click the name in
  the Output tree. A simple text display window will appear with the contents
  of the file.</p><p>Graphical output is automatically displayed during the mission run,
  but double-clicking the name of the output window in the Output tree will
  bring that display to the front. If you close the display window, however,
  you must rerun the mission to display it again.</p><p>A populated Output tree is shown in the following figure.</p><div class="screenshot"><div class="mediaobject"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td><img src="../files/images/Using_OutputTree_1.png" height="281"></td></tr></table></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="CommandSummary.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="TourOfGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ScriptEditor.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Command Summary&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Script Editor</td></tr></table></div></body></html>