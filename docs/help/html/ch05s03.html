<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Propagator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><link rel="prev" href="ch05s02.html" title="Configure the Spacecraft"><link rel="next" href="ch05s04.html" title="Configure the Propagate Command"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Propagator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch05s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;5.&nbsp;Simulating an Orbit</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch05s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N10F59"></a>Configure the Propagator</h2></div></div></div><p>In this section you&rsquo;ll rename the default
    <code class="classname">Propagator</code> and configure the force model.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10F61"></a>Rename the Propagator</h3></div></div></div><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">Resources</span> tree, right-click
          <span class="guilabel">DefaultProp</span> and click
          <span class="guimenuitem">Rename</span>.</p></li><li class="listitem"><p>Type <strong class="userinput"><code>LowEarthProp</code></strong>.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10F7E"></a>Configure the Force Model</h3></div></div></div><p>For this tutorial you will use an Earth 10&times;10 spherical harmonic
      model, the Jacchia-Roberts atmospheric model, solar radiation pressure,
      and point mass perturbations from the Sun and Moon.</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">Resources</span> tree, double-click
          <span class="guilabel">LowEarthProp</span>.</p></li><li class="listitem"><p>Under <span class="guilabel">Gravity</span>, in the
          <span class="guilabel">Degree</span> box, type
          <strong class="userinput"><code>10</code></strong>.</p></li><li class="listitem"><p>In the <span class="guilabel">Order</span> box, type
          <strong class="userinput"><code>10</code></strong>.</p></li><li class="listitem"><p>In <span class="guilabel">Atmosphere Model</span> list, click
          <span class="guilabel">JacchiaRoberts</span>.</p></li><li class="listitem"><p>Click the <span class="guilabel">Select</span> button next to the
          <span class="guilabel">Point Masses</span> box. This opens the
          <span class="guilabel">CelesBodySelectDialog</span> window.</p></li><li class="listitem"><p>In the <span class="guilabel">Available Bodies</span> list, click
          <span class="guilabel">Sun</span>, then click <span class="guilabel">-&gt; </span> to
          add <span class="guilabel">Sun </span>to the <span class="guilabel">Selected
          Bodies</span> list.</p></li><li class="listitem"><p>Add the moon (named <span class="guilabel">Luna</span> in GMAT) in the
          same way.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span> to close the
          <span class="guilabel">CelesBodySelectDialog</span>.</p></li><li class="listitem"><p>Select <span class="guilabel">Use Solar Radiation Pressure</span> to
          toggle it on. Your screen should now match <a class="xref" href="ch05s03.html#Tut_PropASpacecraft_PropSetUp" title="Figure&nbsp;5.2.&nbsp;Force Model Configuration">Figure&nbsp;5.2, &ldquo;Force Model Configuration&rdquo;</a>.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_PropASpacecraft_PropSetUp"></a><p class="title"><b>Figure&nbsp;5.2.&nbsp;Force Model Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_PropSpacecraft_ForceModelConfiguration.png" align="middle" height="482" alt="Force Model Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10FF5"></a>Configuring the Orbit View Plot</h3></div></div></div><p>Now you will configure an <code class="function">OrbitView</code> plot so
      you can visualize <span class="guilabel">Sat</span> and its trajectory. The orbit
      of <span class="guilabel">Sat</span> is highly eccentric. To view the entire
      orbit at once, we need to adjust the settings of
      <span class="guilabel">DefaultOrbitView</span>.</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">Resources</span> tree, double-click
          <span class="guilabel">DefaultOrbitView</span>.</p></li><li class="listitem"><p>In the three boxes to the right of <span class="guilabel">View Point
          Vector</span>, type the values <strong class="userinput"><code>-60000</code></strong>,
          <strong class="userinput"><code>30000</code></strong>, and <strong class="userinput"><code>20000</code></strong>
          respectively.</p></li><li class="listitem"><p>Under <span class="guilabel">Drawing Option</span> to the left, clear
          <span class="guilabel">Draw XY Plane</span>. Your screen should now match
          <a class="xref" href="ch05s03.html#Tut_PropASpacecraft_OrbitViewDialog" title="Figure&nbsp;5.3.&nbsp;DefaultOrbitView Configuration">Figure&nbsp;5.3, &ldquo;DefaultOrbitView Configuration&rdquo;</a>.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_PropASpacecraft_OrbitViewDialog"></a><p class="title"><b>Figure&nbsp;5.3.&nbsp;DefaultOrbitView Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_PropSpacecraft_DefaultOrbitviewConfiguration.png" align="middle" height="582" alt="DefaultOrbitView Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch05s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="SimulatingAnOrbit.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch05s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure the Spacecraft&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Propagate Command</td></tr></table></div></body></html>