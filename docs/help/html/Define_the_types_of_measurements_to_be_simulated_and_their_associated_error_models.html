<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Define the types of measurements to be simulated and their associated error models</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking"><link rel="prev" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html" title="Create and configure the spacecraft, spacecraft hardware, and related parameters"><link rel="next" href="Create_and_configure_force_model_and_propagator.html" title="Create and configure force model and propagator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Define the types of measurements to be simulated and their
    associated error models</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Create_and_configure_force_model_and_propagator.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models"></a>Define the types of measurements to be simulated and their
    associated error models</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1572A"></a>Define
      the TrackingFileSets for the simulation and the estimator</h3></div></div></div><p>Now we will create and configure a
      <span class="guilabel">TrackingFileSet</span> resource. This resource defines the
      type of data to be simulated, the ground stations that will be used, and
      the file name of the output GMD file which will contain the simulated
      data. In addition, the <span class="guilabel">TrackingFileSet</span> resource
      will define needed simulation parameters for the various data types. A
      similar <span class="guilabel">TrackingFileSet</span> is created for the
      estimation, with inputs corresponding to the estimated
      Spacecraft.</p><pre class="programlisting">%
%   Tracking file sets
%

Create TrackingFileSet simData

simData.AddTrackingConfig       = {{SimTrackSat, SimSat, SimTrackSat}, 'Range', 'RangeRate'}
simData.FileName                = {'InterSpacecraft_Range_and_RangeRate.gmd'}
simData.UseLightTime            = True
simData.UseRelativityCorrection = True
simData.UseETminusTAI           = True
simData.SimDopplerCountInterval = 10.

Create TrackingFileSet estData

estData.AddTrackingConfig       = {{EstTrackSat, EstSat, EstTrackSat}, 'Range', 'RangeRate'}
estData.FileName                = {'InterSpacecraft_Range_and_RangeRate.gmd'}
estData.UseLightTime            = True
estData.UseRelativityCorrection = True
estData.UseETminusTAI           = True
</pre><p>For each <span class="guilabel">TrackingFileSet</span>, the script lines
      are broken into three sections. In the first section (the first two
      lines of each <span class="guilabel">TrackingFileSet</span>), the resource name,
      is declared, the data types are defined, and the output file name is
      specified. <span class="guilabel">AddTrackingConfig</span> is the field that is
      used to define the data types. The
      <span class="guilabel">AddTrackingConfig</span> line tells GMAT to simulate Range
      and RangeRate two-way measurements for the <span class="guilabel">TrackSat</span>
      to <span class="guilabel">Sat</span> to <span class="guilabel">TrackSat</span> measurement
      strand.</p><p>The second section (the next three lines) sets some simulation
      parameters that apply to both the range and Doppler measurements. We set
      <span class="guilabel">UseLightTime</span> to True in order to generate realistic
      measurements where GMAT takes into account the finite speed of light.
      The last two parameters in this section,
      <span class="guilabel">UseRelativityCorrection</span> and
      <span class="guilabel">UseETminusTAI</span>, are set to True so that general
      relativistic corrections, as described in Moyer [2000], are applied to
      the light time equations.</p><p>The third section (the last line of the
      <span class="guilabel">simData</span> configuration) above sets simulation
      parameters that apply to a specific measurement type.
      <span class="guilabel">SimDopplerCountInterval</span> applies only to RangeRate
      measurements. We note that the &ldquo;Sim&rdquo; in the field names is used to
      indicate that these fields only are applicable when GMAT is in
      simulation mode (i.e., when using the <span class="guilabel">RunSimulator</span>
      command) and not when GMAT is in estimation mode (i.e., when using the
      <span class="guilabel">RunEstimator</span> command).
      <span class="guilabel">SimDopplerCountInterval</span>, the Doppler Count
      Interval, is set to 10 seconds. This parameter is only applicable to the
      tracking file set used by the simulator and is not set on the tracking
      file set used by the estimator. See the
      <span class="guilabel">RunSimulator</span> help for a description of how this
      parameter is used to calculate the measurement values.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="Create_measurement_error_models"></a>Create the measurement error models</h3></div></div></div><p>All measurement types have random noise and/or biases associated
      with them. For inter-spacecraft measurements, these effects are modelled
      using error models on the receiver of the tracking Spacecraft. Since we
      have already created the <span class="guilabel">Spacecraft</span> object and its
      related hardware, we now create the receiver error models. Since we wish
      to simulate both Range and RangeRate data, we need to create two error
      models as shown below, one for range measurements and one for range-rate
      measurements.</p><pre class="programlisting">%
%   Spacecraft error models
%

Create ErrorModel Range

Range.Type           = 'Range'
Range.NoiseSigma     = 0.010
Range.Bias           = 0.0
Range.SolveFors      = {}

Create ErrorModel RangeRate

RangeRate.Type       = 'RangeRate'
RangeRate.NoiseSigma = 0.000001
RangeRate.Bias       = 0.0
RangeRate.SolveFors  = {}

MeasurementReceiver.ErrorModels = {Range, RangeRate}</pre><p>The script segment above is broken into three sections. The first
      section defines an <span class="guilabel">ErrorModel</span> named
      <span class="guilabel">Range</span>. The error model Type is Range which
      indicates that it is an error model for two-way range measurements. The
      one-sigma standard deviation of the Gaussian white noise is set to 10
      meters (0.010 km) and the measurement bias is set to 0 km.</p><p>The second section above defines an
      <span class="guilabel">ErrorModel</span> named <span class="guilabel">RangeRate</span>.
      The error model Type is RangeRate which indicates that it is an error
      model for two-way averaged range-rate measurements. The one-sigma
      standard deviation of the Gaussian white noise is set to 1 mm/sec and
      the measurement bias is set to 0 km/sec.</p><p>The third section above attaches the two
      <span class="guilabel">ErrorModel</span> resources we have just created to the
      <span class="guilabel">MeasurementReceiver</span> <span class="guilabel">Receiver</span>.
      For inter-spacecraft measurement, the measurement noise or bias is
      defined on the Receiver hardware. Each measurement performed by a
      Spacecraft containing that Receiver must have an associated ErrorModel.
      Thus, any range measurement error involving a
      <span class="guilabel">Spacecraft</span> with the
      <span class="guilabel">MeasurementReceiver</span> <span class="guilabel">Receiver</span>
      is defined by the <span class="guilabel">Range</span>
      <span class="guilabel">ErrorModel</span> and any range-rate measurement error
      involving a <span class="guilabel">Spacecraft</span> with the
      <span class="guilabel">MeasurementReceiver</span> <span class="guilabel">Receiver</span>
      is defined by the <span class="guilabel">RangeRate</span>
      <span class="guilabel">ErrorModel</span>. Note that since GMAT currently only
      models two way measurements where the transmitting and receiving
      Spacecraft are the same, we do not have to consider the case where the
      transmitting and receiving Spacecraft are different.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Create_and_configure_force_model_and_propagator.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create and configure the spacecraft, spacecraft hardware, and
    related parameters&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and configure force model and propagator</td></tr></table></div></body></html>