<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create the Differential Corrector and Target Control Variable</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_TargetFiniteBurn.html" title="Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee"><link rel="prev" href="ch07s02.html" title="Create and Configure Spacecraft Hardware and Finite Burn"><link rel="next" href="ch07s04.html" title="Configure the Mission Sequence"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create the Differential Corrector and Target Control
    Variable</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch07s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch07s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N11733"></a>Create the Differential Corrector and Target Control
    Variable</h2></div></div></div><p>The <span class="guilabel">Target</span> sequence we will create later needs
    a <span class="guilabel">DifferentialCorrector</span> resource to operate, so let&rsquo;s
    create one now. We'll leave the settings at their defaults.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Resources</span> tree, expand the
        <span class="guilabel">Solvers</span> folder if it isn&rsquo;t already.</p></li><li class="step"><p>Right-click the <span class="guilabel">Boundary Value Solvers</span>
        folder, point to <span class="guilabel">Add</span>, and click
        <span class="guilabel">DifferentialCorrector</span>. A new resource called
        <span class="guilabel">DC1</span> will be created.</p></li></ol></div><p>The <span class="guilabel">Target</span> sequence we will later create uses
    the <span class="guilabel">Vary</span> command to adjust a user defined target
    control variable in order to achieve the desired orbital goal of raising
    apogee to <code class="literal">12000</code> km. We must first create this variable
    which we will name <span class="guilabel">BurnDuration</span>.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Resources</span> tree, right-click the
        <span class="guilabel">Variables/Arrays/Strings</span> folder, point to
        <span class="guilabel">Add</span>, and click <span class="guilabel">Variable</span>. A
        new window will come up with two input fields, <span class="guilabel">Variable
        Name</span> and <span class="guilabel">Variable Value</span>. For
        <span class="guilabel">Variable Name</span>, input
        <span class="guilabel">BurnDuration</span> and for <span class="guilabel">Variable
        Value</span>, input <code class="literal">0.</code> Click the
        <span class="guibutton">=&gt;</span> button to create the variable, then click
        <span class="guibutton">Close</span>.</p></li><li class="step"><p>To verify that we have created this new variable correctly,
        double-click <span class="guilabel">BurnDuration</span> to view its
        properties.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig7_Creation_of_Variable_Resource_BurnDuration"></a><p class="title"><b>Figure&nbsp;7.7.&nbsp;Creation of <span class="guilabel">Variable</span> Resource,
      <span class="guilabel">BurnDuration</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig7_Creation_of_Variable_Resource_BurnDuration.png" align="middle" height="331" alt="Creation of Variable Resource, BurnDuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch07s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_TargetFiniteBurn.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch07s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create and Configure Spacecraft Hardware and Finite Burn&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Mission Sequence</td></tr></table></div></body></html>