<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Mission Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="SimpleOrbitTransfer.html" title="Chapter&nbsp;6.&nbsp;Simple Orbit Transfer"><link rel="prev" href="ch06s02.html" title="Configure Maneuvers, Differential Corrector, and Graphics"><link rel="next" href="ch06s04.html" title="Run the Mission"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Mission Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch06s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;6.&nbsp;Simple Orbit Transfer</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch06s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N11234"></a>Configure the Mission Sequence</h2></div></div></div><p>Now we will configure a <code class="function">Target</code> sequence to
    solve for the maneuver values required to raise the orbit to
    geosynchronous altitude and circularize the orbit. We&rsquo;ll begin by creating
    an initial <code class="function">Propagate</code> command, then the
    <code class="function">Target</code> sequence itself, then the final
    <code class="function">Propagate</code> command. To allow us to focus on the
    <code class="classname">Target</code> sequence, we&rsquo;ll assume you have already
    learned how to propagate an orbit to a desired condition by working
    through the <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit">Chapter&nbsp;5, <i>Simulating an Orbit</i></a> tutorial.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1124B"></a>Configure the Initial Propagate Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>Configure <span class="guilabel">Propagate1</span> to propagate to
          <strong class="userinput"><code>DefaultSC.Earth.Periapsis</code></strong>.</p></li><li class="step"><p>Rename <span class="guilabel">Propagate1</span> to <strong class="userinput"><code>Prop To
          Periapsis</code></strong>.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1126A"></a>Create the Target Sequence</h3></div></div></div><p>Now create the commands necessary to perform the
      <code class="function">Target</code> sequence. <a class="xref" href="ch06s03.html#Tut_HohmannTransfer_TargetSequence" title="Figure&nbsp;6.1.&nbsp;Final Mission Sequence for the Hohmann Transfer">Figure&nbsp;6.1, &ldquo;Final Mission Sequence for the Hohmann Transfer&rdquo;</a> illustrates the
      configuration of the <span class="guilabel">Mission</span> tree after you have
      completed the steps in this section. We&rsquo;ll discuss the
      <code class="function">Target</code> sequence after it has been created.</p><div class="figure"><a name="Tut_HohmannTransfer_TargetSequence"></a><p class="title"><b>Figure&nbsp;6.1.&nbsp;Final Mission Sequence for the Hohmann Transfer</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_MissionTree.png" align="middle" height="219" alt="Final Mission Sequence for the Hohmann Transfer"></td></tr></table></div></div></div></div><br class="figure-break"><p>To create the <code class="function">Target</code> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
          <span class="guilabel">Prop To Periapsis</span>, point to <span class="guilabel">Insert
          After</span>, and click <span class="guilabel">Target</span>. This will
          insert two separate commands: <span class="guilabel">Target1</span> and
          <span class="guilabel">EndTarget1</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target1</span> and click
          <span class="guimenuitem">Rename</span>.</p></li><li class="step"><p>Type <strong class="userinput"><code>Hohmann Transfer</code></strong> and click
          <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Hohmann Transfer</span>, point to
          <span class="guilabel">Append</span>, and click
          <span class="guilabel">Vary</span>.</p></li><li class="step"><p>Rename <span class="guilabel">Vary1</span> to <strong class="userinput"><code>Vary
          TOI</code></strong>.</p></li><li class="step"><p>Complete the <span class="guilabel">Target</span> sequence by appending
          the commands in <a class="xref" href="ch06s03.html#Tut_HohmannTransfer_CommandTable" title="Table&nbsp;6.2.&nbsp;Additional Target Sequence Commands">Table&nbsp;6.2, &ldquo;Additional <span class="guilabel">Target</span> Sequence
              Commands&rdquo;</a>.</p><div class="table"><a name="Tut_HohmannTransfer_CommandTable"></a><p class="title"><b>Table&nbsp;6.2.&nbsp;Additional <span class="guilabel">Target</span> Sequence
              Commands</b></p><div class="table-contents"><table summary="Additional Target Sequence
              Commands" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Command</th><th>Name</th></tr></thead><tbody><tr><td><span class="guilabel">Maneuver</span></td><td><strong class="userinput"><code>Perform TOI</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop To Apoapsis</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve RMAG = 42165</code></strong></td></tr><tr><td><span class="guilabel">Vary</span></td><td><strong class="userinput"><code>Vary GOI</code></strong></td></tr><tr><td><span class="guilabel">Maneuver</span></td><td><strong class="userinput"><code>Perform GOI</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve ECC = 0.005</code></strong></td></tr></tbody></table></div></div><p><br class="table-break"></p></li></ol></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Let&rsquo;s discuss what the <code class="function">Target</code> sequence
        does. We know that two maneuvers are required to perform the Hohmann
        transfer. We also know that for our current mission, the final orbit
        radius must be 42,165 km and the final orbital eccentricity must be
        0.005. However, we don&rsquo;t know the size (or &#916;V magnitudes) of the
        maneuvers that precisely achieve the desired orbital conditions. You
        use the <code class="function">Target</code> sequence to solve for those
        precise maneuver values. You must tell GMAT what controls are
        available (in this case, two maneuvers) and what conditions must be
        satisfied (in this case, a specific orbital radius and eccentricity).
        You accomplish this using the <code class="function">Vary</code> and
        <code class="function">Achieve</code> commands. Using the
        <code class="function">Vary</code> command, you tell GMAT what to solve for&mdash;in
        this case, the &#916;V values for <code class="classname">TOI</code> and
        <code class="classname">GOI</code>. You use the <code class="function">Achieve</code>
        command to tell GMAT what conditions the solution must satisfy&mdash;in this
        case, the final orbital conditions.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1132D"></a>Create the Final Propagate Command</h3></div></div></div><p>We need a <code class="function">Propagate</code> command after the
      <code class="function">Target</code> sequence so that we can see our final
      orbit.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
          <span class="guilabel">End Hohmann Transfer</span>, point to <span class="guimenuitem">Insert
          After</span>, and click <span class="guimenuitem">Propagate</span>.
          A new <span class="guilabel">Propagate3</span> command will appear.</p></li><li class="step"><p>Rename <span class="guilabel">Propagate3</span> to <strong class="userinput"><code>Prop One
          Day</code></strong>.</p></li><li class="step"><p>Double-click <span class="guilabel">Prop One Day</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace the value
          <strong class="userinput"><code>12000.0</code></strong> with <strong class="userinput"><code>86400</code></strong>,
          the number of seconds in one day.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N1136C"></a><p class="title"><b>Figure&nbsp;6.2.&nbsp;<span class="guilabel">Prop One Day</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_PropOneDay.png" align="middle" height="457" alt="Prop One Day Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1137A"></a>Configure the Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure the
      various parts of the <code class="function">Target</code> sequence to do what we
      want.</p><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11382"></a>Configure the <span class="guilabel">Vary TOI</span> Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary TOI</span> to edit its
            properties. Notice that the variable in the
            <span class="guilabel">Variable</span> box is
            <strong class="userinput"><code>TOI.Element1</code></strong>, which by default is the
            velocity component of TOI in the local Velocity-Normal-Binormal
            (VNB) coordinate system. That&rsquo;s what we need, so we&rsquo;ll keep
            it.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
            <strong class="userinput"><code>1.0</code></strong>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
            <strong class="userinput"><code>0.5</code></strong>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="Tut_HohmannTransfer_VaryTOI"></a><p class="title"><b>Figure&nbsp;6.3.&nbsp;<span class="guilabel">Vary TOI</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_VaryTOI.png" align="middle" width="461" alt="Vary TOI Command Configuration"></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N113BA"></a>Configure the <span class="guilabel">Perform TOI</span> Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Perform TOI</span> to edit its
            properties. Notice that the command is already set to apply the
            <span class="guilabel">TOI</span> burn to the
            <span class="guilabel">DefaultSC</span> spacecraft, so we don&rsquo;t need to
            change anything here.</p></li><li class="step"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div><div class="figure"><a name="N113D3"></a><p class="title"><b>Figure&nbsp;6.4.&nbsp;<span class="guilabel">Perform TOI</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_PerformTOI.png" align="middle" width="376" alt="Perform TOI Command Configuration"></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N113DF"></a>Configure the <span class="guilabel">Prop to Apo</span>apsis
        Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop to Apoapsis</span> to edit
            its properties.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
            <strong class="userinput"><code>DefaultSC.ElapsedSecs</code></strong> with
            <strong class="userinput"><code>DefaultSC.Earth.Apoapsis</code></strong>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N113FE"></a><p class="title"><b>Figure&nbsp;6.5.&nbsp;<span class="guilabel">Prop to Apoapsis</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_PropToApoapsis.png" align="middle" height="457" alt="Prop to Apoapsis Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N1140C"></a>Configure the <span class="guilabel">Achieve RMAG = 42165</span>
        Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve RMAG = 42165</span> to
            edit its properties.</p></li><li class="step"><p>Notice that <span class="guilabel">Goal</span> is set to
            <span class="guilabel">DefaultSC.Earth.RMAG</span>. This is what we need,
            so we make no changes here.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type
            <span class="guilabel">42164.169</span>, a more precise number for the
            radius of a geosynchronous orbit (in kilometers).</p></li><li class="step"><p>Click <span class="guibutton">OK</span> to save these
            changes.</p></li></ol></div><div class="figure"><a name="N11431"></a><p class="title"><b>Figure&nbsp;6.6.&nbsp;<span class="guilabel">Achieve RMAG = 42165</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_AchieveRMAG.png" align="middle" width="321" alt="Achieve RMAG = 42165 Command Configuration"></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N1143D"></a>Configure the <span class="guilabel">Vary GOI Command</span></h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary GOI</span> to edit its
            properties.</p></li><li class="step"><p>Next to <span class="guilabel">Variable</span>, click the
            <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>Under <span class="guilabel">Object List</span>, click
            <span class="guilabel">GOI</span>.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
            double-click <span class="guilabel">Element1</span> to move it to the
            <span class="guilabel">Selected Value(s)</span> list. See the image below
            for results.</p><div class="figure"><a name="N11467"></a><p class="title"><b>Figure&nbsp;6.7.&nbsp;<span class="guilabel">Vary GOI</span> Parameter Selection</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_VaryGOI_ParamSelect.png" align="middle" width="488" alt="Vary GOI Parameter Selection"></div></div></div></div><br class="figure-break"><p></p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
            <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
            <strong class="userinput"><code>1.0</code></strong>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N1148D"></a><p class="title"><b>Figure&nbsp;6.8.&nbsp;<span class="guilabel">Vary GOI</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_VaryGOI.png" align="middle" width="461" alt="Vary GOI Command Configuration"></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11499"></a>Configure the <span class="guilabel">Perform GOI Command</span></h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Perform GOI</span> to edit its
            properties.</p></li><li class="step"><p>In the <span class="guilabel">Burn</span> list, click
            <span class="guilabel">GOI</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N114B4"></a><p class="title"><b>Figure&nbsp;6.9.&nbsp;<span class="guilabel">Perform GOI</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_PerformGOI.png" align="middle" width="321" alt="Perform GOI Command Configuration"></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N114C0"></a>Configure the <span class="guilabel">Achieve ECC = 0.005</span>
        Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve ECC = 0.005</span> to
            edit its properties.</p></li><li class="step"><p>Next to <span class="guilabel">Goal</span>, click the
            <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
            double-click<span class="guilabel"> ECC</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
            <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Value </span>box, type
            <strong class="userinput"><code>0.005</code></strong>.</p></li><li class="step"><p>In the <span class="guilabel">Tolerance </span> box, type
            <strong class="userinput"><code>0.0001</code></strong>.</p></li><li class="step"><p>Click <span class="guibutton">OK</span> to save these
            changes.</p></li></ol></div><div class="figure"><a name="N11500"></a><p class="title"><b>Figure&nbsp;6.10.&nbsp;<span class="guilabel">Achieve ECC = 0.005</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_SimpleOrbitTransfer_AchieveECC.png" align="middle" width="321" alt="Achieve ECC = 0.005 Command Configuration"></div></div></div></div><br class="figure-break"></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch06s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="SimpleOrbitTransfer.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch06s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure Maneuvers, Differential Corrector, and Graphics&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission</td></tr></table></div></body></html>