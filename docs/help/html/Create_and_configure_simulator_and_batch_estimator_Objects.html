<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create and configure the simulator and batch estimator objects</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking"><link rel="prev" href="Create_and_configure_force_model_and_propagator.html" title="Create and configure force model and propagator"><link rel="next" href="Run_the_mission_and_analyze_the_output.html" title="Run the mission and review the output"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create and configure the simulator and batch estimator
    objects</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Create_and_configure_force_model_and_propagator.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Run_the_mission_and_analyze_the_output.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Create_and_configure_simulator_and_batch_estimator_Objects"></a>Create and configure the simulator and batch estimator
    objects</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="Create_the_simulator_object"></a>Create the simulator object</h3></div></div></div><p>As shown below, we create and configure the
      <span class="guilabel">Simulator</span> object used to define our
      simulation.</p><pre class="programlisting">%
%   Simulator
%

Create Simulator sim

sim.AddData                    = {simData}
sim.EpochFormat                = 'UTCGregorian'
sim.InitialEpoch               = '10 Jun 2010 00:00:00.000'
sim.FinalEpoch                 = '11 Jun 2010 00:00:00.000'
sim.MeasurementTimeStep        = 60
sim.Propagator                 = Prop
sim.AddNoise                   = On</pre><p>In the first script line above, we create a
      <span class="guilabel">Simulator</span> object, <span class="guilabel">sim</span>. The
      next field set is <span class="guilabel">AddData</span> which is used to specify
      which <span class="guilabel">TrackingFileSet</span> should be used. Recall that
      the <span class="guilabel">TrackingFileSet</span> specifies the type of data to
      be simulated and the file name specifying where to store the data. The
      <span class="guilabel">TrackingFileSet</span>, <span class="guilabel">simData</span>, that
      we created in the <a class="xref" href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html" title="Define the types of measurements to be simulated and their associated error models">Define the types of measurements to be simulated and their
    associated error models</a> section, specified that we wanted to simulate
      two-way range and range-rate data that involved the
      <span class="guilabel">SimMeasureSat</span>
      <span class="guilabel">Spacecraft</span>.</p><p>The next three script lines, which set the
      <span class="guilabel">EpochFormat</span>, <span class="guilabel">InitialEpoch</span>, and
      <span class="guilabel">FinalEpoch</span> fields, specify the time period of the
      simulation. Here, we choose a two-day duration.</p><p>The next script line sets the
      <span class="guilabel">MeasurementTimeStep</span> field which specifies the
      requested time between measurements. We choose a value of 1
      minute.</p><p>The next script line sets the <span class="guilabel">Propagator</span>
      field which specifies which <span class="guilabel">Propagator</span> object
      should be used. We set this field to the <span class="guilabel">Prop</span>
      object which we created in the <a class="xref" href="Create_and_configure_force_model_and_propagator.html" title="Create and configure force model and propagator">Create and configure force model and propagator</a> section.</p><p>Finally, in the last line of the script segment, we set the
      <span class="guilabel">AddNoise</span> field which specifies whether or not we
      want to add noise to our simulated measurements. The noise added is
      defined by the <span class="guilabel">ErrorModel</span> objects that we created
      in the <a class="xref" href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#Create_measurement_error_models" title="Create the measurement error models">Create the measurement error models</a> section. As discussed in the <a class="xref" href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#Create_measurement_error_models" title="Create the measurement error models">Create the measurement error models</a>
      section, the noise added to the range measurements will be Gaussian with
      a one-sigma value of 10 meters, and the noise added to the range-rate
      measurements will be Gaussian with a one sigma value of 1 mm/sec.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="Create_the_batch_estimator_object"></a>Create the batch estimator object</h3></div></div></div><p>In order to estimate the true initial state of the observed
      spacecraft, we define the <span class="guilabel">BatchEstimator bat</span> in the
      script below.</p><pre class="programlisting">%
%   Estimator
%

Create BatchEstimator bat

bat.ShowProgress               = True
bat.Measurements               = {estData} 
bat.AbsoluteTol                = 0.000001
bat.RelativeTol                = 0.005
bat.MaximumIterations          = 10
bat.MaxConsecutiveDivergences  = 3
bat.Propagator                 = Prop
bat.ShowAllResiduals           = On
bat.OLSEInitialRMSSigma        = 3000
bat.OLSEMultiplicativeConstant = 3
bat.OLSEAdditiveConstant       = 0
bat.ReportFile                 = 'InterSpacecraft_Range_and_RangeRate.txt'</pre><p>For more information on the parameters involved in defining the
      BatchEstimator object, see the documentation for the <a class="link" href="BatchEstimator.html" title="BatchEstimator">BatchEstimator.</a></p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Create_and_configure_force_model_and_propagator.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Run_the_mission_and_analyze_the_output.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create and configure force model and propagator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the mission and review the output</td></tr></table></div></body></html>