<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Transponder</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="Transmitter.html" title="Transmitter"><link rel="next" href="ch21s02.html" title="Commands"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Transponder</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Transmitter.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch21s02.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Transponder"></a><div class="titlepage"></div><a name="N2A7EB" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Transponder</span></h2><p>Transponder &mdash; Defines the electronics hardware, typically attached to a
    spacecraft, that receives and automatically re-transmits an incoming
    signal.</p></div><div class="refsection"><a name="N2A7FC"></a><h2>Description</h2><p>The spacecraft <span class="guilabel">Transponder</span> model is required
    for modeling two-way coherent range and Doppler data types. The
    <span class="guilabel">Transponder</span> object includes modeling of a
    retransmission delay due to the spacecraft transponder electronics. You
    can also specify a turn around ratio which is a multiplicative ratio
    describing how the frequency of the retransmitted signal differs from the
    received frequency. The incoming and outgoing frequencies are designed to
    be different so as to avoid RF interference between the signal transmitted
    by the ground station to the spacecraft and the return signal from the
    spacecraft to the ground station.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="GroundStation.html" title="GroundStation"><span class="refentrytitle">GroundStation</span></a>, <a class="xref" href="Antenna.html" title="Antenna"><span class="refentrytitle">Antenna</span></a></p></div><div class="refsection"><a name="N2A811"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">HardwareDelay</span></td><td><p>Transponder electronics delay between receiving time
            and transmitting time at the transponder. It is applied for both
            simulation and estimation, with or without ramp table use.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">0</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>seconds</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PrimaryAntenna</span></td><td><p><span class="guilabel">Antenna</span> resource used by the
            <span class="guilabel">Transponder</span> resource</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p><span class="guilabel">Antenna</span> Object</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any valid <span class="guilabel">Antenna</span> object</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">None</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TurnAroundRatio</span></td><td><p>Transponder turn around ratio which is used in both
            simulation and estimation. For the DSN Doppler data type where an
            input ramp table is not used, changing the transponder turn around
            ratio appreciably changes the measurement. For all DSN data types,
            changing the turn around ratio affects the media correction
            calculations which will typically result in a small change in the
            measurement. See the <span class="guilabel">RunSimulator</span> and
            <span class="guilabel">RunEstimator</span> help for additional details.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>STRING_TYPE</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>A string in form of 'a/b' where a and b are real
                    numbers</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">'240/221'</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2A8C6"></a><h2>Remarks</h2><div class="refsection"><a name="N2A8C9"></a><h3>Turn around ratio affects media correction calculations</h3><p>Suppose you are given a signal with multiple &lsquo;n&rsquo; legs. In order to
      calculate the media (ionosphere) corrections for a given leg, we need to
      know the associated frequency for that leg. The turn-around ratio is
      used to calculate the frequency for legs 2 through n. If media
      corrections are modeled, then, for both DSN range and Doppler
      measurements, the value of the turn-around ratio, as set in the
      <span class="guilabel">Transponder</span> resource, will have an effect on the
      measurements and thus both simulation and estimation processes will be
      affected.</p></div><div class="refsection"><a name="N2A8D1"></a><h3>Independent of media corrections, how does the turn around ratio,
      as set in the <span class="guilabel">Transponder</span> resource, affect DSN
      measurements?</h3><p>Assume that media corrections are turned off so that we can ignore
      any, typically small, changes to the DSN measurements caused by media
      corrections. We make the following observations.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>The value of <span class="guilabel">Transponder.TurnAroundRatio</span>
          has no effect on DSN range measurements.</p></li><li class="listitem"><p>If a ramp table is provided, then the value of
          <span class="guilabel">Transponder.TurnAroundRatio</span> has no effect on
          DSN Doppler measurements. In this case, the multiplicative turn
          around ratio used to calculate the computed measurement is based
          upon the Uplink Band given in the ramp table. (240/221 for S-band
          and 880/749 for X band)</p></li><li class="listitem"><p>If a ramp table is not provided, then the value of
          <span class="guilabel">Transponder.TurnAroundRatio</span> has a proportional
          effect on DSN Doppler measurements. For example, if the turn around
          ratio is doubled, then so is the DSN Doppler measurement in
          Hz.</p></li></ol></div><p>For additional discussion on how the
      <span class="guilabel">Transponder.TurnAroundRatio</span> field affects the DSN
      measurements, see the <span class="guilabel">RunSimulator</span> and
      <span class="guilabel">RunEstimator</span> help.</p></div><div class="refsection"><a name="N2A8F7"></a><h3>Custom turn-around ratios for DSN Doppler data</h3><p>As mentioned above, the DSN Doppler (TRK-2-34 Type 17) data type
      observation value depends upon the transponder turn-around ratio. As
      shown in the tables in the <span class="guilabel">RunSimulator</span> and
      <span class="guilabel">RunEstimator</span> help, for ramped Doppler data, GMAT
      only allows for the use of the standard S-band (240/221) and X-band
      (880/749) turn-around ratios. For Doppler data where a ramp table is not
      used, setting the <span class="guilabel">Transponder</span> turn-around ratio
      will correctly model the <span class="guilabel">Doppler</span> data. GMAT cannot
      currently accommodate custom turn-around ratios for ramped Doppler
      data.</p></div></div><div class="refsection"><a name="N2A908"></a><h2>Examples</h2><div class="informalexample"><pre class="programlisting">% Create and configure a Transponder object

Create Spacecraft Sat1;
Create Antenna HGA;
Create Transponder Transponder1;

Transponder1.PrimaryAntenna  = HGA;
Transponder1.HardwareDelay   = 0.0;
Transponder1.TurnAroundRatio = '240/221';

Sat1.AddHardware = {HGA, Transponder1};
BeginMissionSequence;</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Transmitter.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch21s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Transmitter&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Commands</td></tr></table></div></body></html>