<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Run the Mission with first Target Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_UsingGMATFunctions.html" title="Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions"><link rel="prev" href="ch10s03.html" title="Configure the Mission Sequence"><link rel="next" href="ch10s05.html" title="Run the Mission with first and second Target Sequences"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Run the Mission with first Target Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch10s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch10s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N1361B"></a>Run the Mission with first Target Sequence</h2></div></div></div><p>Before running the mission, click <span class="guilabel">Save</span>
    (<span class="inlinemediaobject"><img src="../files/images/icons/SaveMission.png" align="middle" height="10"></span>) and save the mission to a file of your choice. Now
    click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>). As the mission runs, you will see GMAT solve the
    targeting problem. Each iteration and perturbation is shown in
    <span class="guilabel">EarthView</span>, <span class="guilabel">SolarSystemView</span> and
    <span class="guilabel">MarsView</span> windows in light blue, and the final
    solution is shown in red. After the mission completes, the 3D views should
    appear as in the images shown below. You may want to run the mission
    several times to see the targeting in progress.</p><div class="figure"><a name="N1363F"></a><p class="title"><b>Figure&nbsp;10.7.&nbsp;3D View of departure hyperbolic trajectory (EarthView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_5.png" align="middle" height="841" alt="3D View of departure hyperbolic trajectory (EarthView)"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N1364B"></a><p class="title"><b>Figure&nbsp;10.8.&nbsp;3D View of heliocentric transfer trajectory
      (SolarSystemView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_6.png" align="middle" height="841" alt="3D View of heliocentric transfer trajectory (SolarSystemView)"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N13657"></a><p class="title"><b>Figure&nbsp;10.9.&nbsp;3D View of approach hyperbolic trajectory. MAVEN stopped at
      periapsis (MarsView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_7.png" align="middle" height="841" alt="3D View of approach hyperbolic trajectory. MAVEN stopped at periapsis (MarsView)"></td></tr></table></div></div></div></div><br class="figure-break"><p>Now go to the <span class="guilabel">Output</span> tree and open <span class="guilabel">rf</span>. Recall that <span class="guilabel">rf</span> was declared
as a global object both inside the function and in the main script. Notice that both the controls (i.e. <span class="guilabel">TCM</span> burn elements) and constraints (i.e. <span class="guilabel">BdotT</span>, <span class="guilabel">BdotR</span>) are reported as 
well as <span class="guilabel">MAVEN</span> inclination relative to <span class="guilabel">MarsInertial</span> coordinate system. The desired constraints that were set in the first targeter sequence have been successfully achieved.</p><p>Now go back to <span class="guilabel">Mission</span> tree and right click on <span class="guilabel">Target Desired B-Plane Coord. From Inside Function</span> command and click on <span class="guilabel">Command Summary</span> option.
 Under <span class="guilabel">Coordinate System</span> drop down menu, select <span class="guilabel">MarsIntertial</span> and study the command summary. This command summary
corresponds to the very last <span class="guilabel">Propagate</span> command (i.e. 'Prop to Mars Periapsis') from inside the GMAT function. Under <span class="guilabel">Hyperbolic Parameters</span>, notice
the values of <span class="guilabel">BdotT</span> and <span class="guilabel">BdotR</span>. These are the constraints that have been achieved on the very last 'Prop to Mars Periapsis' <span class="guilabel">Propagate</span> command from the first targeter which
was set up inside the GMAT function.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1369D"></a>Create the Second Target Sequence</h3></div></div></div><p>Recall that we still need to create second
      <span class="guilabel">Target</span> sequence in order to perform Mars Orbit
      Insertion maneuver to achieve the desired capture orbit. In the
      <span class="guilabel">Mission</span> tree, we will create the second
      <span class="guilabel">Target</span> sequence right after the first
      <span class="guilabel">Target</span> sequence which was defined inside the GMAT function <span class="guilabel">TargeterInsideFunction</span>.</p><p>Now let&rsquo;s create the commands necessary to perform the second
      <span class="guilabel">Target</span> sequence. <a class="xref" href="ch10s04.html#Tut_UsingGMATFunctions_8" title="Figure&nbsp;10.10.&nbsp;Mission Sequence showing first and second Target sequences">Figure&nbsp;10.10, &ldquo;Mission Sequence showing first and second Target
        sequences&rdquo;</a> illustrates the
      configuration of the <span class="guilabel">Mission</span> tree after you have
      completed the steps in this section. Notice that in <a class="xref" href="ch10s04.html#Tut_UsingGMATFunctions_8" title="Figure&nbsp;10.10.&nbsp;Mission Sequence showing first and second Target sequences">Figure&nbsp;10.10, &ldquo;Mission Sequence showing first and second Target
        sequences&rdquo;</a>, the second
      <span class="guilabel">Target</span> sequence is created after the first
      <span class="guilabel">Target</span> sequence which was called via the <span class="guilabel">CallGmatFunction</span> command. We&rsquo;ll discuss the second
      <span class="guilabel">Target</span> sequence after it has been created.</p><div class="figure"><a name="Tut_UsingGMATFunctions_8"></a><p class="title"><b>Figure&nbsp;10.10.&nbsp;Mission Sequence showing first and second Target
        sequences</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_8.png" align="middle" height="226" alt="Mission Sequence showing first and second Target sequences"></td></tr></table></div></div></div></div><br class="figure-break"><p>To create the second <span class="guilabel">Target</span> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click on
          <span class="guilabel">Mission Sequence</span> folder, point to
          <span class="guilabel">Append</span>, and click <span class="guilabel">Target</span>.
          This will insert two separate commands: <span class="guilabel">Target1</span>
          and <span class="guilabel">EndTarget1</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target1</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>Type <span class="guilabel">Mars Capture</span> and click
          <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Mars Capture</span>, point to
          <span class="guilabel">Append</span>, and click <span class="guilabel">Vary</span>. A
          new command called <span class="guilabel">Vary4</span> will be
          created.</p></li><li class="step"><p>Right-click <span class="guilabel">Vary4</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Vary
          MOI.V</span> and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Complete the <span class="guilabel">Target</span> sequence by appending
          the commands in <a class="xref" href="ch10s04.html#Tut_Mars_B_Plane_Second_Targeting_CommandTable_ID" title="Table&nbsp;10.9.&nbsp;Additional Second Target Sequence Commands">Table&nbsp;10.9, &ldquo;Additional Second <span class="guilabel">Target</span> Sequence
              Commands&rdquo;</a>.</p><div class="table"><a name="Tut_Mars_B_Plane_Second_Targeting_CommandTable_ID"></a><p class="title"><b>Table&nbsp;10.9.&nbsp;Additional Second <span class="guilabel">Target</span> Sequence
              Commands</b></p><div class="table-contents"><table summary="Additional Second Target Sequence
              Commands" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Command</th><th>Name</th></tr></thead><tbody><tr><td><span class="guilabel">Maneuver</span></td><td><strong class="userinput"><code>Apply MOI</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop to Mars
                    Apoapsis</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve RMAG</code></strong></td></tr></tbody></table></div></div><p><br class="table-break"></p></li></ol></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Let&rsquo;s discuss what the second <span class="guilabel">Target</span>
        sequence does. We know that a maneuver is required for the Mars
        capture orbit. We also know that the desired radius of capture orbit
        at apoapsis must be 12,000 km. However, we don&rsquo;t know the size (or &#916;V
        magnitude) of the <span class="guilabel">MOI</span> maneuver that will
        precisely achieve the desired orbital conditions. You use the second
        <span class="guilabel">Target</span> sequence to solve for that precise
        maneuver value. You must tell GMAT what controls are available (in
        this case, a single maneuver) and what conditions must be satisfied
        (in this case, radius magnitude value). Once again, just like in the
        first <span class="guilabel">Target</span> sequence, here we accomplish this by
        using the <span class="guilabel">Vary</span> and <span class="guilabel">Achieve</span>
        commands. Using the <span class="guilabel">Vary</span> command, you tell GMAT
        what to solve for&mdash;in this case, the &#916;V value for
        <span class="guilabel">MOI</span>. You use the <span class="guilabel">Achieve</span>
        command to tell GMAT what conditions the solution must satisfy&mdash;in this
        case, RMAG value of 12,000 km.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13783"></a>Create the Final Propagate Command</h3></div></div></div><p>We need a <span class="guilabel">Propagate</span> command after the second
      <span class="guilabel">Target</span> sequence so that we can see our final
      orbit.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
          <span class="guilabel">End Mars Capture</span>, point to <span class="guilabel">Insert
          After</span>, and click <span class="guilabel">Propagate</span>. A new
          <span class="guilabel">Propagate3</span> command will appear.</p></li><li class="step"><p>Right-click <span class="guilabel">Propagate6</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>Type <span class="guilabel">Prop for 1 day</span> and click
          <span class="guilabel">OK</span>.</p></li><li class="step"><p>Double-click <span class="guilabel">Prop for 1 day</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">NearMars</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.ElapsedDays</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace the value
          <span class="guilabel">0.0</span> with <span class="guilabel">1</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes</p></li></ol></div><div class="figure"><a name="N137E3"></a><p class="title"><b>Figure&nbsp;10.11.&nbsp;<span class="guilabel">Prop for 1 day</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Prop For 1 Day_better.png" align="middle" height="475" alt="Prop for 1 day Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N137F1"></a>Configure the second Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure various
      parts of the second <span class="guilabel">Target</span> sequence to do what we
      want.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N137F9"></a>Configure the Mars Capture Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Mars Capture</span> to edit its
          properties.</p></li><li class="step"><p>In the <span class="guilabel">ExitMode</span> list, click
          <span class="guilabel">SaveAndContinue</span>. This instructs GMAT to save
          the final solution of the targeting problem after you run it.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes</p></li></ol></div><div class="figure"><a name="N13812"></a><p class="title"><b>Figure&nbsp;10.12.&nbsp;<span class="guilabel">Mars Capture</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Mars Capture.png" align="middle" height="315" alt="Mars Capture Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13820"></a>Configure the Vary MOI.V Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary MOI.V</span> to edit its
          properties. Notice that the variable in the
          <span class="guilabel">Variable</span> box is
          <span class="guilabel">TCM.Element1</span>. We want
          <span class="guilabel">MOI.Element1</span> which is the velocity component of
          <span class="guilabel">MOI</span> in the local VNB coordinate system. So
          let&rsquo;s change that.</p></li><li class="step"><p>Next to <span class="guilabel">Variable</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>Under <span class="guilabel">Object</span> List, click
          <span class="guilabel">MOI</span>.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
          double-click <span class="guilabel">Element1</span> to move it to the
          <span class="guilabel">Selected Value(s)</span> list. See the image below for
          results.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog window</span>.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
          <span class="guilabel">-1.0</span>.</p></li><li class="step"><p>In the <span class="guilabel">Perturbation</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>In the <span class="guilabel">Lower</span> box, type
          <span class="guilabel">-10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
          <span class="guilabel">10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
          <span class="guilabel">0.1</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N13890"></a><p class="title"><b>Figure&nbsp;10.13.&nbsp;<span class="guilabel">Vary MOI</span> Parameter Selection</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Vary MOI_V Parameter Select.png" align="middle" height="431" alt="Vary MOI Parameter Selection"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N1389E"></a><p class="title"><b>Figure&nbsp;10.14.&nbsp;<span class="guilabel">Vary MOI</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Vary MOI_V Command.png" align="middle" height="310" alt="Vary MOI Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N138AC"></a>Configure the Apply MOI Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Apply MOI</span> to edit its
          properties.</p></li><li class="step"><p>In the <span class="guilabel">Burn</span> list, click
          <span class="guilabel">MOI</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N138C5"></a><p class="title"><b>Figure&nbsp;10.15.&nbsp;<span class="guilabel">Apply MOI</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Apply MOI.png" align="middle" height="240" alt="Apply MOI Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N138D3"></a>Configure the Prop to Mars Apoapsis Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop to Mars Apoapsis</span> to
          edit its properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">NearMars</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.Mars.Apoapsis</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N138FB"></a><p class="title"><b>Figure&nbsp;10.16.&nbsp;<span class="guilabel">Prop to Mars Apoapsis</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Prop to Mars Apoapsis_better.png" align="middle" height="474" alt="Prop to Mars Apoapsis Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13909"></a>Configure the Achieve RMAG Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve RMAG</span> to edit its
          properties.</p></li><li class="step"><p>Next to <span class="guilabel">Goal</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list, click
          <span class="guilabel">RMAG</span>.</p></li><li class="step"><p>Under <span class="guilabel">Central Body</span>, select
          <span class="guilabel">Mars</span> and double-click on
          <span class="guilabel">RMAG</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type
          <span class="guilabel">12000</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N13949"></a><p class="title"><b>Figure&nbsp;10.17.&nbsp;<span class="guilabel">Achieve RMAG</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Achieve RMAG.png" align="middle" height="230" alt="Achieve RMAG Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch10s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_UsingGMATFunctions.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch10s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure the Mission Sequence&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission with first and second Target Sequences</td></tr></table></div></body></html>