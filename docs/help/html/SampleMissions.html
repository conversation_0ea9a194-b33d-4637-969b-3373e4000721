<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Sample Missions</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="GettingStarted.html" title="Chapter&nbsp;2.&nbsp;Getting Started"><link rel="prev" href="RunningGmat.html" title="Running GMAT"><link rel="next" href="GettingHelp.html" title="Getting Help"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Sample Missions</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="RunningGmat.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;2.&nbsp;Getting Started</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="GettingHelp.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="SampleMissions"></a>Sample Missions</h2></div></div></div><a name="N1031E" class="indexterm"></a><p>The GMAT distribution includes more than 30 sample missions. These
  samples show how to apply GMAT to problems ranging from the Hohmann transfer
  to libration point station-keeping to trajectory optimization. To locate and
  run a sample mission:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Open GMAT.</p></li><li class="listitem"><p>On the toolbar click <span class="guiicon">Open</span>.</p></li><li class="listitem"><p>Navigate to the <code class="filename">samples</code> folder located in the
      GMAT root directory.</p></li><li class="listitem"><p>Double-click a script file of your choice.</p></li><li class="listitem"><p>Click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>).</p></li></ol></div><p>Some optimization missions require MATLAB, the MATLAB optimization
  toolbox, the VF13 optimizer, or the Yukon optimizer. Some of these are
  proprietary libraries and are not distributed with GMAT. For sample missions
  employing optimizers that are not available to you, you may try substituting
  the Yukon optimizer which comes with GMAT. The MATLAB
  <code class="function">fmincon</code> optimizer currently only works with GMAT on the
  Windows platform. See <a class="xref" href="MatlabInterface.html" title="MATLAB Interface"><span class="refentrytitle">MATLAB Interface</span></a> for details on
  configuring the MATLAB optimizer.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="RunningGmat.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="GettingStarted.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="GettingHelp.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Running GMAT&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Getting Help</td></tr></table></div></body></html>