<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>SolarPowerSystem</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="Propagator.html" title="Propagator"><link rel="next" href="SolarSystem.html" title="SolarSystem"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">SolarPowerSystem</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Propagator.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SolarSystem.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="SolarPowerSystem"></a><div class="titlepage"></div><a name="N1D489" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">SolarPowerSystem</span></h2><p>SolarPowerSystem &mdash; A solar power system model</p></div><div class="refsection"><a name="N1D49A"></a><h2>Description</h2><p>The <span class="guilabel">SolarPowerSystem</span> models a solar power
    system including power generated as function of time and distance from the
    sun, and includes shadow modeling by celestial bodies. The model allows
    you to configure the power generated by the solar arrays, and the power
    required by the spacecraft bus.</p><p>For a complete descripton of how to configure all Resources required
    for electric propulsion modeling, see the Tutorial named <a class="xref" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion">Chapter&nbsp;12, <i>Electric Propulsion</i></a></p><p>See Also <a class="xref" href="ElectricTank.html" title="ElectricTank"><span class="refentrytitle">ElectricTank</span></a>, <a class="xref" href="ElectricThruster.html" title="ElectricThruster"><span class="refentrytitle">ElectricThruster</span></a>, <a class="xref" href="NuclearPowerSystem.html" title="NuclearPowerSystem"><span class="refentrytitle">NuclearPowerSystem</span></a></p></div><div class="refsection"><a name="N1D4B0"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">AnnualDecayRate</span></td><td><p>The annual decay rate of the power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 &lt;=Real &lt;= 100</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>5</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Percent/Year</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BusCoeff1</span></td><td><p>Coefficient of power required by spacecraft
            bus.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.3</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kW</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BusCoeff2</span></td><td><p>Coefficient of power required by spacecraft
            bus.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kW*AU</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BusCoeff3</span></td><td><p>Coefficient of power required by spacecraft
            bus.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kw*AU<sup>2</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EpochFormat</span></td><td><p>The epoch format for the PowerInitialEpoch
            field.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid Epoch format.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>UTCGregorian</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">InitialEpoch</span></td><td><p>The initial epoch of the system used to define power
            system elapsed lifetime.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid GMAT Epoch consistent with
                    PowerInitialEpochFormat</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 11:59:27.966</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">InitialMaxPower</span></td><td><p>The maximum power generated at the
            <span class="guilabel">PowerInitialEpoch</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.2</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kW</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Margin</span></td><td><p>The required margin between power left after power
            bus, and power used by the propulsion system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 &lt;=Real &lt;= 100</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>5</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Percent</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShadowBodies</span></td><td><p>A list of celestial bodies for use in the shadow
            computation. A body cannot be added more than
            once.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String List</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>A list of celestial bodies.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Earth</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShadowModel</span></td><td><p>The model used for shadow computation in the Solar
            System Power Model.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>None, DualCone</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>DualCone</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolarCoeff1</span></td><td><p>Coefficient of power created by solar power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.32077</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Remarks</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolarCoeff2</span></td><td><p>Coefficient of power created by solar power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>-0.10848</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Remarks</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolarCoeff3</span></td><td><p>Coefficient of power created by solar power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>-0.11665</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Remarks</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolarCoeff4</span></td><td><p>Coefficient of power created by solar power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.10843</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Remarks</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolarCoeff5</span></td><td><p>Coefficient of power created by solar power
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>-0.01279</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Remarks</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1D75E"></a><h2>GUI</h2><p>The GUI for the <span class="guilabel">SolarPowerSystem</span> is shown
    below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SolarPowerSystem.png" align="middle" height="427"></td></tr></table></div></div></div><div class="refsection"><a name="N1D76F"></a><h2>Remarks</h2><div class="refsection"><a name="N1D772"></a><h3>Computation of Base Power</h3><p>The <span class="guilabel">SolarPowerSystem</span> models power degradation
      as a function of time. You must provide a power system initial epoch,
      the power generated at that epoch, and an annual power decay rate.
      Additionally, the <span class="guilabel">AnnualDecayRate</span> field models the
      power degredation on a per year basis. The base power is computed
      using</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SolarPowerSystem_BasePowerEq.png" align="middle" height="65"></td></tr></table></div></div><p>where "tau" is the power <span class="guilabel">AnnualDecayRate</span>, P_0
      is <span class="guilabel">InitialMaxPower</span>, and "delta t" is the elapsed
      time between the simulation epoch and
      <span class="guilabel">InitialEpoch</span>.</p></div><div class="refsection"><a name="N1D791"></a><h3>Computation of Bus Power</h3><p>The power required by the spacecraft bus for all subsystems other
      than the propulsion system is computed using</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SolarPowerSystem_BusPowerEq.png" align="middle" height="48"></td></tr></table></div></div><p>where A_Bus, B_Bus, and C_Bus are <span class="guilabel">BusCoeff1</span>,
      <span class="guilabel">BusCoeff2</span>, and <span class="guilabel">BusCoeff3</span>
      respectively and r is the distance from the Sun in Au.</p></div><div class="refsection"><a name="N1D7AA"></a><h3>Computation of Power Available for Propulsion</h3><p>The solar power model scales the base power based on a polynomial
      function in terms of the solar distance. Total power is compute
      using</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SolarPowerSystem_TotalPowerEq.png" align="middle" height="75"></td></tr></table></div></div><p>where P_Sun is the percent sun ( full sun is 1.0, no sun is 0.0),
      r is the distance from the Sun in Au, and C_1 is
      <span class="guilabel">SolarCoeff1</span> and so on. Thrust power available for
      electric propulsion is finaly computed using</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SolarPowerSystem_ThrustPowerAvailable.png" align="middle" height="57"></td></tr></table></div></div><p>Where "delta M" is power <span class="guilabel">Margin</span>.</p></div><div class="refsection"><a name="N1D7CB"></a><h3>Shadow Modelling and Discontinuities</h3><p>Note that when modeling shadows for a solar power system,
      discontinuities in the force model can occur when the power avialable
      for propulsion is less than a thruster's minimum useable power setting.
      As a spacecraft passes from penumbra to umbra, and power avialable for
      thusting goes to zero, thrust power causes thrust acceleration to
      discontinuously terminate, causing issues when using adaptive step
      integrators. In this case, there are a few options. You can configure
      any itegrator to use fixed step integration by setting the
      <span class="guilabel">ErrorControl</span> to <span class="guilabel">None</span>. Or you
      can configure the integrator to continue propagating if a bad step, in
      this case a small discontinuity, occurs. See the <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a> reference material for more information.</p></div></div><div class="refsection"><a name="N1D7D9"></a><h2>Examples</h2><div class="informalexample"><p>Create a <span class="guilabel">SolarPowerSystem</span> and attach it to a
      <span class="guilabel">Spacecraft.</span></p><pre class="programlisting"><code class="code">%  Create the Solar Power System
Create SolarPowerSystem SolarPowerSystem1

%  Create a spacecraft an attach the Solar Power System
Create Spacecraft DefaultSC
DefaultSC.PowerSystem = SolarPowerSystem1

BeginMissionSequence
</code></pre><p>For a complete descripton of how to configure all Resources
      required for electric propulsion modeling, see the Tutorial named <a class="xref" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion">Chapter&nbsp;12, <i>Electric Propulsion</i></a>.</p></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Propagator.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SolarSystem.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Propagator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;SolarSystem</td></tr></table></div></body></html>