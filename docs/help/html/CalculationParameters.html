<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Calculation Parameters</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch24.html#N2D3E2" title="System Level Components"><link rel="prev" href="ch24.html" title="Chapter&nbsp;24.&nbsp;System"><link rel="next" href="CommandLine.html" title="Command-Line Usage"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Calculation Parameters</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch24.html">Prev</a>&nbsp;</td><th align="center" width="60%">System Level Components</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="CommandLine.html">Next</a></td></tr></table><hr></div><div lang="en-US" class="refentry"><a name="CalculationParameters"></a><div class="titlepage"></div><a name="N2D3EA" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Calculation Parameters</span></h2><p>Calculation Parameters &mdash; Resource properties available for use by commands and
    output</p></div><div class="refsection"><a name="N2D3FB"></a><h2>Description</h2><p>Parameters are named resource properties that can be used to obtain
    data for use by Mission Sequence commands or by output resources. Some
    parameters, such as the <span class="guilabel">Altitude</span> parameter of
    <span class="guilabel">Spacecraft</span>, are calculated values that can only be
    used to retrieve data. They cannot be set directly. Others, such as the
    <span class="guilabel">Element1</span> parameter of
    <span class="guilabel">ImpulsiveBurn</span>, share the same name as a resource
    field and can be used both to set data and retrieve it. Parameters are
    distinguished from resource fields by their extra functionality: fields
    are static resource properties that are usually set in initialization (or
    in the GUI Resources tree), while parameters can be calculated on the fly
    and used in plots, reports, and mathematical expressions.</p><p>Parameters are classified as one of four types:
    central-body-dependent parameters, coordinate-system-dependent parameters,
    attached-hardware parameters, and standalone parameters. Standalone
    parameters are the simplest type, as they have no dependencies. The
    <span class="guilabel">ElapsedSecs</span> parameter of
    <span class="guilabel">Spacecraft</span> is an example of this; it is simply
    referenced as
    <code class="literal"><em class="replaceable"><code>Spacecraft</code></em>.ElapsedSecs</code>.</p><p>Central-body-dependent parameters, as the name suggests, have a
    value that is dependent on the chosen celestial body. The
    <span class="guilabel">Altitude</span> parameter of <span class="guilabel">Spacecraft</span>
    is an example of this. To reference this parameter, you must specify a
    central body, such as
    <code class="literal"><em class="replaceable"><code>Spacecraft</code></em>.Mars.Altitude</code>.
    Any built-in central body or user-defined <span class="guilabel">Asteroid</span>,
    <span class="guilabel">Comet</span>, <span class="guilabel">Moon</span>, or
    <span class="guilabel">Planet</span> is valid as a dependency.</p><p>Likewise, coordinate-system-dependent parameters have a value that
    is dependent on the chosen coordinate system. The <span class="guilabel">DEC</span>
    parameter of <span class="guilabel">Spacecraft</span> is an example of this. To
    reference this parameter, you must specify the name of a
    <span class="guilabel">CoordinateSystem</span> resource, such as
    <code class="literal"><em class="replaceable"><code>Spacecraft</code></em>.EarthFixed.DEC</code>.
    Any default or user-defined <span class="guilabel">CoordinateSystem</span> resource
    is valid as a dependency.</p><p>If a dependency is used when retrieving the value of the parameter,
    as in the following line, the value of <span class="guilabel">Altitude</span> is
    calculated at Mars before setting it to the variable <code class="literal">x</code>.
    If the dependency is omitted, <span class="guilabel">Earth</span> and
    <span class="guilabel">EarthMJ2000Eq</span> are assumed unless noted
    otherwise.</p><pre class="programlisting">x = DefaultSC.Mars.Altitude</pre><p>If a dependency is used when setting the value of a parameter, the
    value of the parameter is first converted based on the value of the
    dependency, then the value is set. For example, in the following line, the
    value of <span class="guilabel">SMA</span> is first calculated at Mars, then it is
    set to the value <code class="literal">10000</code> in that context. If the
    dependency is omitted when setting the value, the default is assumed to be
    the central body or coordinate system of the parent resource (in this
    case, <span class="guilabel">DefaultSC</span>).</p><pre class="programlisting">DefaultSC.Mars.SMA = 10000</pre><p>Attached-hardware parameters have no dependencies, but are
    themselves dependent on being attached to a
    <span class="guilabel">Spacecraft</span>. <span class="guilabel">ChemicalTank</span> and
    <span class="guilabel">ChemicalThruster</span> parameters are examples of this. The
    <span class="guilabel">FuelMass</span> parameter of
    <span class="guilabel">ChemicalTank</span> cannot be referenced without first
    attaching the <span class="guilabel">ChemicalTank</span> to a
    <span class="guilabel">Spacecraft</span>. Then, the parameter can be referenced as:
    <code class="literal"><em class="replaceable"><code>Spacecraft</code></em>.FuelTank.FuelMass</code>.</p><p>The individual parameters are resource-specific, and are documented
    in the tables below. The GUI has a parameter selection interface that is
    common to all parameters. This interface is documented in GUI,
    below.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Array.html" title="Array"><span class="refentrytitle">Array</span></a>, <a class="xref" href="FuelTank.html" title="ChemicalTank"><span class="refentrytitle">ChemicalTank</span></a>, <a class="xref" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="refentrytitle">ImpulsiveBurn</span></a>, <a class="xref" href="FiniteBurn.html" title="FiniteBurn"><span class="refentrytitle">FiniteBurn</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a>, <a class="xref" href="String.html" title="String"><span class="refentrytitle">String</span></a>, <a class="xref" href="Thruster.html" title="ChemicalThruster"><span class="refentrytitle">ChemicalThruster</span></a>, <a class="xref" href="Variable.html" title="Variable"><span class="refentrytitle">Variable</span></a></p></div><div class="refsection"><a name="N2D49D"></a><h2>GUI</h2><p>Parameters can be used as input in several places throughout GMAT,
    such as the <span class="guilabel">ReportFile</span> and
    <span class="guilabel">XYPlot</span> resources and the
    <span class="guilabel">If</span>/<span class="guilabel">Else</span>,
    <span class="guilabel">Propagate</span>, and <span class="guilabel">Report</span> commands.
    In the GUI, all of these use a common interface called the
    <span class="guilabel">ParameterSelectDialog</span> that allows for interactive
    parameter selection. A basic <span class="guilabel">ParameterSelectDialog</span>
    window looks like the following:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_CalculationParameters_GUI.png" align="middle" height="416"></td></tr></table></div></div><p>The <span class="guilabel">ParameterSelectDialog</span> window is used to
    build a parameter, along with any dependencies, for use in a command or
    resource. Some resources and commands have different requirements for the
    types of parameters that can be used, so the
    <span class="guilabel">ParameterSelectDialog</span> can take slightly different
    forms, depending on where it's used. This section will describe the
    generic interface, then mention any resource- or command-specific
    exceptions.</p><div class="refsection"><a name="N2D4CB"></a><h3>General Usage</h3><p>The first step in choosing a parameter is to select the object (or
      resource) type from the <span class="guilabel">Object Type</span> list in the
      upper left. Seven types can appear in this list:
      <span class="guilabel">Spacecraft</span>, <span class="guilabel">SpacePoint</span>,
      <span class="guilabel">ImpulsiveBurn</span>, <span class="guilabel">FiniteBurn</span>,
      <span class="guilabel">Variable</span>, <span class="guilabel">Array</span>, and
      <span class="guilabel">String</span>.</p><p>Once you've selected a type, The <span class="guilabel">Object List</span>
      box is populated with all existing resources of that type. Use this list
      to choose the specific resource you'd like to reference.</p><p>If the <span class="guilabel">Spacecraft</span> type is selected, the
      <span class="guilabel">Attached Hardware List</span> appears below the
      <span class="guilabel">Object List</span>. This list displays any hardware (such
      as <span class="guilabel">ChemicalTank</span> or
      <span class="guilabel">ChemicalThruster</span> resources) attached to the
      selected <span class="guilabel">Spacecraft</span>. If the
      <span class="guilabel">Array</span> type is selected, <span class="guilabel">Row</span>
      and <span class="guilabel">Col</span> boxes appear. Use these to specify a row
      and column to select an individual array element, or check
      <span class="guilabel">Select Entire Object</span> to choose the entire
      array.</p><p>Once a resource is selected, the <span class="guilabel">Object
      Properties</span> list is populated with all available parameters
      provided by that resource. Some resources, such as instances of
      <span class="guilabel">Variable</span> or <span class="guilabel">Array</span>, are
      themselves parameters, so this list remains empty.</p><p>Parameters with different dependency types are commingled in the
      <span class="guilabel">Object Properties</span> list. When you select one, the
      appropriate dependency (if any) appears below the list. For example,
      after selecting the <span class="guilabel">Spacecraft AOP</span> parameter, a
      <span class="guilabel">CoordinateSystem</span> list appears. After selecting the
      <span class="guilabel">Spacecraft Apoapsis</span> parameter, a <span class="guilabel">Central
      Body</span> list appears. And after selecting the Spacecraft Cd
      parameter, no dependency list appears. To select a range of parameters
      from the <span class="guilabel">Object Properties</span> list, hold down the
      Shift key while selecting the second endpoint of the range. To select
      multiple individual parameters, hold down the <span class="keycap"><strong>Ctrl</strong></span> key
      while making each selection.</p><p>To select a parameter, select the appropriate <span class="guilabel">Object
      Type</span>, the specific resource from the <span class="guilabel">Object
      List</span> or <span class="guilabel">Attached Hardware List</span>, the
      desired parameter from the <span class="guilabel">Object Properties list</span>,
      and the required dependency, and add it to the <span class="guilabel">Selected
      Value(s)</span> list on the right. There are six buttons available
      to control this list:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><span class="guibutton">UP</span>: Move the selected item in the
          <span class="guibutton">Selected Value(s)</span> list up one position (if
          allowed).</p></li><li class="listitem"><p><span class="guibutton">DN</span>: Move the selected item in the
          <span class="guibutton">Selected Value(s)</span> list down one position (if
          allowed).</p></li><li class="listitem"><p><span class="guibutton">-&gt;</span>: Add the selected item in the
          <span class="guibutton">Object Properties</span> list to the
          <span class="guibutton">Selected Value(s)</span> list.</p></li><li class="listitem"><p><span class="guibutton">&lt;-</span>: Remove the selected item in the
          <span class="guibutton">Selected Value(s)</span> list.</p></li><li class="listitem"><p><span class="guibutton">=&gt;</span>: Add all items to the
          <span class="guibutton">Selected Value(s)</span> list.</p></li><li class="listitem"><p><span class="guibutton">&lt;=</span>: Remove all items from the
          <span class="guibutton">Selected Value(s</span>) list.</p></li></ul></div><p>When finished, the <span class="guilabel">Selected Value(s)</span> list
      contains the final selected parameters. Click <span class="guilabel">OK</span> to
      accept the selection.</p><p>The ordering of the <span class="guilabel">Selected Value(s)</span> list is
      significant in certain circumstances (such as in the
      <span class="guilabel">Add</span> field of <span class="guilabel">ReportFile</span>), but
      not in others. See the documentation for each resource or command for
      details.</p></div><div class="refsection"><a name="N2D587"></a><h3>Special Considerations</h3><p>Some resources and commands (such as the
      <span class="guilabel">Propagate</span> command <span class="guilabel">Parameter</span>
      argument) only accept a single parameter as input; in this context the
      <span class="guilabel">ParameterSelectDialog</span> only allows one parameter in
      the <span class="guilabel">Selected Value(s)</span> list and does not allow use
      of the <span class="guibutton">UP</span>, <span class="guibutton">DN</span>, and
      <span class="guibutton">=&gt;</span> buttons.</p><p>In some instances (such as in the <span class="guilabel">Vary</span>
      command), only parameters that are also fields (and so can be set in the
      <span class="guilabel">Mission Sequence</span>) can be used. In this case only
      the allowed parameters will be shown in the <span class="guilabel">Object
      Properties</span> list.</p><p>In the <span class="guilabel">Propagate</span> command
      <span class="guilabel">Parameter</span> argument, only parameters of
      <span class="guilabel">Spacecraft</span> can be used. In this case only
      <span class="guilabel">Spacecraft</span> will be shown in the <span class="guilabel">Object
      Type</span> list.</p></div></div><div class="refsection"><a name="N2D5BD"></a><h2>Parameters</h2><div class="refsection"><a name="N2D5C0"></a><h3>Spacecraft</h3><div class="informaltable"><table border="1"><colgroup><col width="18%"><col width="6%"><col width="7%"><col width="69%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">A1Gregorian</span></td><td>Y</td><td>N</td><td><p>Spacecraft epoch in the A.1 system and the
              Gregorian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">A1ModJulian</span></td><td>Y</td><td>Y</td><td><p>Spacecraft epoch in the A.1 system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Acceleration</span></td><td>N</td><td>Y</td><td><p>The total acceleration with respect to the inertial
              system computed using the <span class="guilabel">ForceModel</span>
              selected for the dependency.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s^2</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AccelerationX</span></td><td>N</td><td>Y</td><td><p> The x-component of acceleration with respect to
              the inertial system computed using the
              <span class="guilabel">ForceModel</span> selected for the
              dependency.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s^2</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AccelerationY</span></td><td>N</td><td>Y</td><td><p> The y-component of acceleration with respect to
              the inertial system computed using the
              <span class="guilabel">ForceModel</span> selected for the
              dependency.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s^2</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AccelerationZ</span></td><td>N</td><td>Y</td><td><p> The z-component of acceleration with respect to
              the inertial system computed using the
              <span class="guilabel">ForceModel</span> selected for the
              dependency.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s^2</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AltEquinoctialP</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AltEquinoctialP</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AltEquinoctialQ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AltEquinoctialQ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Altitude</span></td><td>N</td><td>Y</td><td><p>Distance to the plane tangent to the surface of the
              specified celestial body at the sub-satellite point. GMAT
              assumes the body is an ellipsoid.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AngularVelocityX</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AngularVelocityX</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AngularVelocityY</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AngularVelocityY</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AngularVelocityZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AngularVelocityZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AOP</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AOP</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">AOP</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Apoapsis</span></td><td>N</td><td>Y</td><td><p>A parameter that equals zero when the spacecraft is
              at orbit apoapsis. This parameter can only be used as a stopping
              condition in the <span class="guilabel">Propagate</span>
              command.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AtmosDensity</span></td><td>N</td><td>Y</td><td><p> The atmospheric density at the current
              <span class="guilabel">Spacecraft</span> epoch and location computed
              using the <span class="guilabel">ForceModel</span> selected for the
              dependency.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg/km^3</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AZI</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">AZI</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">AZI</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BdotR</span></td><td>N</td><td>Y</td><td><p>B-plane <span class="mathphrase">B&middot;R</span> magnitude.</p><p>GMAT computes the
              B-plane coordinates in the coordinate system specified in the
              dependency. In many implementations, the B-plane coordinates are
              computed in a pseudo-rotating coordinate system where the &#969;&times;r
              term is not applied when transforming velocity vectors. GMAT
              does apply the &#969;&times;r term in the velocity transformation. When
              computing B-plane coordinates in inertial systems, this term is
              identically zero. For rotating systems such as the Sun-Earth
              body-body rotating system, the effect of including &#969;&times;r is small
              but noticeable when comparing results between systems. When the
              rotation of the selected coordinate system is "fast", the values
              may differ significantly.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BdotT</span></td><td>N</td><td>Y</td><td><p>B-plane <span class="mathphrase">B&middot;T</span> magnitude. See the
              <span class="guilabel">BdotR</span> parameter for notes on this
              calculation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BetaAngle</span></td><td>N</td><td>Y</td><td><p>The angle between the orbital plane and the vector
              from the celestial body to the sun.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">BetaAngle</span> &le; 90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongAOP</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongAOP</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerLongAOP</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongECC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongECC</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongINC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongINC</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerLongINC</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongMA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongMA</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerLongMA</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongRAAN</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongRAAN</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerLongRAAN</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerLongSMA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerLongSMA</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortAOP</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortAOP</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerShortAOP</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortECC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortECC</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortINC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortINC</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerShortINC</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortMA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortMA</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerShortMA</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortRAAN</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortRAAN</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">BrouwerShortRAAN</span> &le;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BrouwerShortSMA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">BrouwerShortSMA</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BurnTorque</span></td><td>N</td><td>N</td><td><p>The torque occurring on the spacecraft about its
              center of mass due to firing thrusters. The torque is reported
              in the spacecraft body frame.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Array (1x3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N*m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BVectorAngle</span></td><td>N</td><td>Y</td><td><p>B-plane angle between the <span class="mathphrase">B</span> vector and the <span class="mathphrase">T</span> unit vector. See the
              <span class="guilabel">BdotR</span> parameter for notes on this
              calculation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">BVectorAngle</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BVectorMag</span></td><td>N</td><td>Y</td><td><p>B-plane <span class="mathphrase">B</span> vector magnitude. See the
              <span class="guilabel">BdotR</span> parameter for notes on this
              calculation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C3Energy</span></td><td>N</td><td>Y</td><td><p>C<sub>3</sub> (characteristic)
              energy.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>MJ/kg
                      (km<sup>2</sup>/s<sup>2</sup>)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Cd</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Cd</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Cr</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Cr</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CurrA1MJD</span></td><td>Y</td><td>Y</td><td><p><span class="emphasis"><em>Deprecated</em></span>. Spacecraft epoch
              in the A.1 system and the Modified Julian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM11</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM11</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM12</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM12</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM13</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM13</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM21</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM21</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM22</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM22</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM23</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM23</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM31</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM31</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM32</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM32</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DCM33</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DCM33</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DEC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DEC</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">DEC</span> &le; 90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DECV</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DECV</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">DECV</span> &le; 90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Delaunayg</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Delaunayg</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">Delaunayg</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DelaunayG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DelaunayG</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Delaunayh</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Delaunayh</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">Delaunayh</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DelaunayH</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DelaunayH</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Delaunayl</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Delaunayl</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">Delaunayl</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DelaunayL</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DelaunayL</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DLA</span></td><td>N</td><td>Y</td><td><p>Declination of the outgoing hyperbolic
              asymptote.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">DLA</span> &le; 90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DragArea</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DragArea</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassX</span></td><td>Y</td><td>y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryCenterOfMassX</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassY</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryCenterOfMassY</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryCenterOfMassZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m<sup></sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMass</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMass</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXX</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaXX</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXY</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaXY</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaXZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaYY</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaYY</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaYZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaYZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaZZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">DryMomentOfInertiaZZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EA</span></td><td>N</td><td>Y</td><td><p>Eccentric anomaly.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">EA</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ECC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">ECC</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ElapsedDays</span></td><td>N</td><td>Y</td><td><p>The time in elapsed days. Elapsed time is computed
              based on context. See <a class="link" href="CalculationParameters.html#ElapsedTimeParameters" title="Elapsed Time Parameters"><span class="guilabel">Elapsed Time
              Parameters</span></a> for more
              information.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ElapsedSecs</span></td><td>N</td><td>Y</td><td><p>The time in elapsed seconds. Elapsed time is
              computed based on context. See <a class="link" href="CalculationParameters.html#ElapsedTimeParameters" title="Elapsed Time Parameters"><span class="guilabel">Elapsed Time
              Parameters</span></a> for more
              information.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Energy</span></td><td>N</td><td>Y</td><td><p>Specific orbital energy.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>MJ/kg
                      (km<sup>2</sup>/s<sup>2</sup>)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialH</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EquinoctialH</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialHDot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's
              Equinoctial H element due to perturbing forces. </p>
              <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s^-1</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialK</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EquinoctialK</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialKDot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's
              Equinoctial K element due to perturbing forces. </p>
              <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s^-1</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialP</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EquinoctialP</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialPDot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's
              Equinoctial P element due to perturbing forces. </p>
              <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s^-1</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialQ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EquinoctialQ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EquinoctialQDot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's
              Equinoctial Q element due to perturbing forces. </p>
              <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s^-1</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngle1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngle1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">EulerAngle1</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngle2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngle2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">EulerAngle2</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngle3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngle3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">EulerAngle3</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngleRate1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngleRate1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngleRate2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngleRate2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EulerAngleRate3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">EulerAngleRate3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FPA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">FPA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">FPA</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityTorque</span></td><td>N</td><td>N</td><td><p>The torque occurring on the spacecraft about its
              center of mass due to gravity fields and point masses from the
              selected force model. The torque is reported in the spacecraft
              body frame.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Array (1x3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N*m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HA</span></td><td>N</td><td>Y</td><td><p>Hyperbolic anomaly.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-&infin; &lt; <span class="guilabel">HA</span> &lt; &infin;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HMAG</span></td><td>N</td><td>Y</td><td><p>Magnitude of the angular momentum
              vector.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HX</span></td><td>N</td><td>Y</td><td><p>X component of the angular momentum
              vector.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HY</span></td><td>N</td><td>Y</td><td><p>Y component of the angular momentum
              vector.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HZ</span></td><td>N</td><td>Y</td><td><p>Z component of the angular momentum
              vector.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km<sup>2</sup>/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">INC</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">INC</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">INC</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IncomingBVAZI</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">IncomingBVAZI</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">IncomingBVAZI</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IncomingC3Energy</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">IncomingC3Energy</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>MJ/kg
                      (km<sup>2</sup>/s<sup>2</sup>)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IncomingDHA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">IncomingDHA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">IncomingDHA</span> &le;
                      90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IncomingRadPer</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">IncomingRadPer</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IncomingRHA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">IncomingRHA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">IncomingRHA</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Latitude</span></td><td>N</td><td>Y</td><td><p>Planetodetic latitude.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">Latitude</span> &le; 90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Longitude</span></td><td>N</td><td>Y</td><td><p>Planetodetic longitude.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">Longitude</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">LST</span></td><td>N</td><td>Y</td><td><p>Local sidereal time of the spacecraft from the
              celestial body's inertial x-axis.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">LST</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MA</span></td><td>N</td><td>Y</td><td><p>Mean anomaly.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">MA</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MHA</span></td><td>N</td><td>Y</td><td><p>Angle between celestial body's body-fixed and
              inertial axes. For Earth, this is the Greenwich Hour
              Angle.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">MHA</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MLONG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">MLONG</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">MLONG</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MM</span></td><td>N</td><td>Y</td><td><p>Mean motion.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>rad/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ModEquinoctialF</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">ModEquinoctialF</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ModEquinoctialG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">ModEquinoctialG</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ModEquinoctialH</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">ModEquinoctialH</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ModEquinoctialK</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">ModEquinoctialK</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MRP1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">MRP1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MRP2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">MRP2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MRP3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">MRP3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitPeriod</span></td><td>N</td><td>Y</td><td><p>Osculating orbit period.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitSTM</span></td><td>N</td><td>N</td><td><p>State transition matrix with respect to the
              origin-independent MJ2000Eq axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (6&times;6)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitSTMA</span></td><td>N</td><td>N</td><td><p>Upper-left quadrant of the state transition matrix,
              with respect to the origin-independent MJ2000Eq
              axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (3&times;3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitSTMB</span></td><td>N</td><td>N</td><td><p>Upper-right quadrant of the state transition
              matrix, with respect to the origin-independent MJ2000Eq
              axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (3&times;3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitSTMC</span></td><td>N</td><td>N</td><td><p>Lower-left quadrant of the state transition matrix,
              with respect to the origin-independent MJ2000Eq
              axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (3&times;3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitSTMD</span></td><td>N</td><td>N</td><td><p>Lower-right quadrant of the state transition
              matrix, with respect to the origin-independent MJ2000Eq
              axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (3&times;3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitTime</span></td><td>N</td><td>N</td><td><p>Local Time of the current
              state.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>CoordinateSystem</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OutgoingBVAZI</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">OutgoingBVAZI</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">OutgoingBVAZI</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OutgoingC3Energy</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">OutgoingC3Energy</span></a>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>MJ/kg
                      (km<sup>2</sup>/s<sup>2</sup>)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OutgoingDHA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">OutgoingDHA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">OutgoingRHA</span> &le;
                      90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OutgoingRadPer</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">OutgoingRadPer</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OutgoingRHA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">OutgoingRHA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">OutgoingRHA</span> &lt;
                      360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Periapsis</span></td><td>N</td><td>Y</td><td><p>A parameter that equals zero when the spacecraft is
              at orbit periapsis. This parameter can only be used as a
              stopping condition in the <span class="guilabel">Propagate</span>
              command.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticAZI</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticAZI</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">PlanetodeticAZI</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticHFPA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticHFPA</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-90&deg; &le; <span class="guilabel">PlanetodeticHFPA</span> &le;
                      90&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticLAT</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticLAT</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">PlanetodeticLAT</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticLON</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticLON</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">PlanetodeticLON</span> &le;
                      180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticRMAG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticRMAG</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PlanetodeticVMAG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">PlanetodeticVMAG</span></a>.
              This parameter must be used with a
              <span class="guilabel">CoordinateSystem</span> with
              <span class="guilabel">BodyFixed</span> axes.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span> (with
                      <span class="guilabel">BodyFixed</span> axes)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Q1</span></td><td>N</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Q1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Q2</span></td><td>N</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Q2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Q3</span></td><td>N</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Q3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Q4</span></td><td>N</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Q4</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Quaternion</span></td><td>Y</td><td>N</td><td><p>Attitude quaternion.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array (1&times;4)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">RA</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RAAN</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RAAN</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">RAAN</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RadApo</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RadApo</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RadPer</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RadPer</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RAV</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RAV</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">RAV</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RLA</span></td><td>N</td><td>Y</td><td><p>Right ascension of the outgoing hyperbolic
              asymptote.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>-180&deg; &le; <span class="guilabel">RLA</span> &le; 180&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RMAG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">RMAG</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SemilatusRectum</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">SemilatusRectum</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SemilatusRectum</span></td><td>N</td><td>Y</td><td><p>Semilatus rectum of the osculating
              orbit.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SMA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">SMA</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SMADot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's
              semimajor axis due to perturbing forces. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRPArea</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">SRPArea</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRPTorque</span></td><td>N</td><td>N</td><td><p>The torque occurring on the spacecraft about its
              center of mass due to solar radiation pressure from the selected
              force model. The torque is reported in the spacecraft body
              frame.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Array (1x3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N*m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemCenterOfMassX</span></td><td>N</td><td>Y</td><td><p>X-component of the overall system's center of mass in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft mass and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemCenterOfMassY</span></td><td>N</td><td>Y</td><td><p>Y-component of the overall system's center of mass in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft mass and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemCenterOfMassZ</span></td><td>N</td><td>Y</td><td><p>Z-component of the overall system's center of mass in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft mass and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaXX</span></td><td>N</td><td>Y</td><td><p>XX-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaXY</span></td><td>N</td><td>Y</td><td><p>XY-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaXZ</span></td><td>N</td><td>Y</td><td><p>XZ-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaYY</span></td><td>N</td><td>Y</td><td><p>YY-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaYZ</span></td><td>N</td><td>Y</td><td><p>YZ-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SystemMomentOfInertiaZZ</span></td><td>N</td><td>Y</td><td><p>ZZ-component of the overall system's moment of inertia in
              the spacecraft's body coordinate system. This incorporates the
              spacecraft MOI and any attached fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TA</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">TA</span>.</a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">TA</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIGregorian</span></td><td>Y</td><td>N</td><td><p>Spacecraft epoch in the TAI system and the
              Gregorian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIModJulian</span></td><td>Y</td><td>Y</td><td><p>Spacecraft epoch in the TAI system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBGregorian</span></td><td>Y</td><td>N</td><td><p>Spacecraft epoch in the TDB system and the
              Gregorian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBModJulian</span></td><td>Y</td><td>Y</td><td><p>Spacecraft epoch in the TDB system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TLONG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="guilabel">Spacecraft</span>.<span class="guilabel">TLONG</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p>0&deg; &le; <span class="guilabel">TLONG</span> &lt; 360&deg;</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TLONGDot</span></td><td>N</td><td>Y</td><td><p> The time rate of change of the spacecraft's true
              inertial longitude (RAAN + AOP + TA) due to perturbing forces.
              </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>
                        <span class="guilabel">ForceModel</span>
                      </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalMass</span></td><td>N</td><td>Y</td><td><p>Total mass, including fuel mass from attached
              <span class="guilabel">ChemicalTank</span> resources.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalTorque</span></td><td>N</td><td>N</td><td><p>The torque occurring on the spacecraft about its
              center of mass due to all forces from the selected force model.
              The torque is reported in the spacecraft body
              frame.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Array (1x3)</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">ForceModel</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N*m</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTGregorian</span></td><td>Y</td><td>N</td><td><p>Spacecraft epoch in the TT system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTModJulian</span></td><td>Y</td><td>Y</td><td><p>Spacecraft epoch in the TT system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCGregorian</span></td><td>Y</td><td>N</td><td><p>Spacecraft epoch in the UTC system and the
              Gregorian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCModJulian</span></td><td>Y</td><td>Y</td><td><p>Spacecraft epoch in the UTC system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VelApoapsis</span></td><td>N</td><td>Y</td><td><p>Scalar velocity at apoapsis.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VelPeriapsis</span></td><td>N</td><td>Y</td><td><p>Scalar velocity at periapsis.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VMAG</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">VMAG</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Output Range</span></p></td><td><p></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VX</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">VX</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VY</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">VY</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VZ</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">VZ</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">X</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">X</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Y</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Y</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Z</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Spacecraft.html" title="Spacecraft"><span class="guilabel">Spacecraft</span>.<span class="guilabel">Z</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2F129"></a><h3>FuelTank</h3><p><span class="guilabel">ChemicalTank</span> parameters are accessible only
      after attaching the <span class="guilabel">ChemicalTank</span> resource to a
      <span class="guilabel">Spacecraft</span>, like so:</p><pre class="programlisting"><code class="code">Create FuelTank aTank
Create Spacecraft aSat
aSat.Tanks = {aTank}</code></pre><p>Then, <span class="guilabel">ChemicalTank</span> parameters are accessible
      by specifying the <span class="guilabel">ChemicalTank</span> name as the
      parameter dependency:</p><pre class="programlisting"><code class="code">Create ReportFile aReport
aReport.Add = {aSat.aTank.FuelMass}</code></pre><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">FuelDensity</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">FuelDensity</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg/m<sup>3</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FuelMass</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">FuelMass</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Pressure</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">Pressure</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RefTemperature</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">RefTemperature</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>&deg;C</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Temperature</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">Temperature</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>&deg;C</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Volume</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="FuelTank.html" title="ChemicalTank"><span class="guilabel">ChemicalTank</span>.<span class="guilabel">Volume</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m<sup>3</sup></p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2F241"></a><h3>Space Point Parameters</h3><p>All Resources that have coordinates in space have Cartesian
      position and velocity parameters, so you can access ephemeris
      information. This includes all built-in solar system bodies and other
      Resources such as
      <span class="guilabel">CelestialBody</span>,<span class="guilabel">Planet</span>,
      <span class="guilabel">Moon</span>, <span class="guilabel">Asteroid</span>,
      <span class="guilabel">Comet</span>, <span class="guilabel">Barycenter</span>,
      <span class="guilabel">LibrationPoint</span>, and
      <span class="guilabel">GroundStation</span> :</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.X</code></p></li><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.Y</code></p></li><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.Z</code></p></li><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.VX</code></p></li><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.VY</code></p></li><li class="listitem"><p><code class="literal"><em class="replaceable"><code>CelestialBody</code></em>.<em class="replaceable"><code>CoordinateSystem</code></em>.VZ</code></p></li></ul></div><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Note that to use these parameters, you must first set the epoch
        of the Resource to the desired epoch at which you want the data.
        Additionally, the epoch should be set after the
        <span class="guilabel">BeginMissionSequence</span> Command. See the following
        example.</p></div><pre class="programlisting"><code class="code">Create ReportFile rf

BeginMissionSequence

Luna.Epoch.A1ModJulian = 21545
Report rf Luna.EarthMJ2000Eq.X Luna.EarthMJ2000Eq.Y Luna.EarthMJ2000Eq.Z ...
       Luna.EarthMJ2000Eq.VX Luna.EarthMJ2000Eq.VY Luna.EarthMJ2000Eq.VZ</code></pre><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Spacecraft parameters are treated slightly different than Space
        Point parameters primarly because <span class="guilabel">Spacecraft</span>
        Cartesian state parameters are settable, and all other Space Point
        Cartesian parameters are only gettable. When requesting state
        information for Space Points other than
        <span class="guilabel">Spacecraft</span>, the coordinates are computed based on
        the model configured for that Resource. Additionally, not all epoch
        configuration options supported for <span class="guilabel">Spacecraft
        </span>are supported for Space Points (i.e.
        <span class="guilabel">Epoch</span> and
        <span class="guilabel">DateFormat</span>).</p></div><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">A1Gregorian</span></td><td>Y</td><td>N</td><td><p>Resource epoch in the A.1 system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">A1ModJulian</span></td><td>Y</td><td>Y</td><td><p>Resource epoch in the A.1 system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIGregorian</span></td><td>Y</td><td>N</td><td><p>Resource epoch in the TAI system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIModJulian</span></td><td>Y</td><td>Y</td><td><p>Resource epoch in the TAI system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBGregorian</span></td><td>Y</td><td>N</td><td><p>Resource epoch in the TDB system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBModJulian</span></td><td>Y</td><td>Y</td><td><p>Resource epoch in the TDB system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTGregorian</span></td><td>Y</td><td>N</td><td><p>Resource epoch in the TT system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTModJulian</span></td><td>Y</td><td>Y</td><td><p>Resource epoch in the TT system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCGregorian</span></td><td>Y</td><td>N</td><td><p>Resource epoch in the UTC system and the Gregorian
              format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(N/A)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCModJulian</span></td><td>Y</td><td>Y</td><td><p>Resource epoch in the UTC system and the Modified
              Julian format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>d</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VX</span></td><td>N</td><td>Y</td><td><p>The x-component of velocity with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VY</span></td><td>N</td><td>Y</td><td><p>The y-component of velocity with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">VZ</span></td><td>N</td><td>Y</td><td><p>The z-component of velocity with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">X</span></td><td>N</td><td>Y</td><td><p>The x-component of position with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Y</span></td><td>N</td><td>Y</td><td><p>The y-component of position with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Z</span></td><td>N</td><td>Y</td><td><p>The z-component of position with respect to the
              <span class="guilabel">CoordinateSystem</span> chosen as the dependency.
              When no dependency is selected,
              <span class="guilabel">EarthMJ2000Eq</span> is used. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2F4D6"></a><h3>Thruster</h3><p><span class="guilabel">ChemicalThruster</span> parameters are accessible
      only after attaching the <span class="guilabel">ChemicalThruster</span> resource
      to a <span class="guilabel">Spacecraft</span>, like so:</p><pre class="programlisting"><code class="code">Create Thruster aThruster
Create Spacecraft aSat
aSat.Thrusters = {aThruster}</code></pre><p>Then, <span class="guilabel">ChemicalThruster</span> parameters are
      accessible by specifying the <span class="guilabel">ChemicalThruster</span> name
      as the parameter dependency:</p><pre class="programlisting"><code class="code">Create ReportFile aReport
aReport.Add = {aSat.aThruster.DutyCycle}</code></pre><p>The table below shows reportable thruster based parameters:</p><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">C1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C4</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C4</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C5</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C5</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C6</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C6</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C7</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C7</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C7</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C8</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C8</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C9</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C9</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C9</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C10</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C10</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C11</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C11</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C11</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C12</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C12</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C13</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C13</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C14</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C14</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C15</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C15</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C16</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">C16</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DutyCycle</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">DutyCycle</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravitationalAccel</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">GravitationalAccel</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m/s<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Isp</span></td><td>Y</td><td>Y</td><td><p>Specific impulse of an individual thruster. When
              thruster(s) is not turned on, GMAT will report zeros to a report
              file. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K4</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K4</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K5</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K5</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K6</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K6</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C7</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K7</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K7</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K8</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K8</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C9</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K9</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K9</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K10</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K10</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C11</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K11</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K11</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K12</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K12</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K13</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K13</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K14</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K14</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K15</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K15</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K16</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">K16</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MassFlowRate</span></td><td>N</td><td>Y</td><td><p>Mass flow rate from an individual thruster. When
              thruster(s) is not turned on, GMAT will report zeros to a report
              file. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">ThrustDirection1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">ThrustDirection2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">ThrustDirection3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustMagnitude</span></td><td>Y</td><td>Y</td><td><p>Magnitude of the thrust from an individual
              thruster. When thruster(s) is not turned on, GMAT will report
              zeros to a report file. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Newtons</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustScaleFactor</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="Thruster.html" title="ChemicalThruster"><span class="guilabel">ChemicalThruster</span>.<span class="guilabel">ThrustScaleFactor</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2FAF8"></a><h3>ImpulsiveBurn</h3><p>To compute <span class="guilabel">ImpulsiveBurn</span> parameters, GMAT
      requires that an <span class="guilabel">ImpulsiveBurn</span> has been executed
      using a <span class="guilabel">Maneuver</span> command like this:</p><pre class="programlisting"><code class="code">Maneuver myImpulsiveBurn(mySat)  </code></pre><p>In the case that an <span class="guilabel">ImpulsiveBurn</span> has not
      been applied, GMAT will output zeros for the
      <span class="guilabel">ImpulsiveBurn</span> components and issue a
      warning.</p><p>We recommended that you evaluate
      <span class="guilabel">ImpulsiveBurn</span> parameters immediately after the
      <span class="guilabel">ImpulsiveBurn</span> is applied using the
      <span class="guilabel">Maneuver</span> command like this:</p><pre class="programlisting"><code class="code">Maneuver myImpulsiveBurn(mySat) 
myVar =  mySat.MyCoordinateSystem.Element1 </code></pre><p>The above usage avoids issues that may occur if the
      <span class="guilabel">ImpulsiveBurn</span> coordinate system is time varying,
      and the <span class="guilabel">ImpulsiveBurn</span> parameters are requested
      after further manipulation of the participants using other commands
      (such as <span class="guilabel">Propagate</span>). In that case, it is possible
      that the participants are no longer at the epoch of the maneuver, and
      unexpected results can occur due to epoch mismatches.</p><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">B</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">B</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Element1</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">Element1</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Element2</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">Element2</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Element3</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">Element3</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p><span class="guilabel">CoordinateSystem</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">N</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">N</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">V</span></td><td>Y</td><td>Y</td><td><p>See <a class="link" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><span class="guilabel">ImpulsiveBurn</span>.<span class="guilabel">V</span></a></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2FC28"></a><h3>FiniteBurn</h3><p>To compute <span class="guilabel">FiniteBurn</span> parameters, GMAT
      requires that a <span class="guilabel">FiniteBurn</span> has been executed using
      a <span class="guilabel">BeginFiniteBurn</span> command like this:</p><pre class="programlisting"><code class="code">BeginFiniteBurn Maneuver myFiniteBurn(mySat)  </code></pre><p>In the case that a <span class="guilabel">FiniteBurn</span> has not been
      applied, GMAT will output zeros for all reportable
      <span class="guilabel">FiniteBurn</span> parameters to a report file. All finite
      burn parameters will report zeros whenever a finite burn is not turned
      on. The table below shows reportable finite burn parameters:</p><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">TotalAcceleration1</span></td><td>N</td><td>Y</td><td><p>First component of the total acceleration from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Km/s<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalAcceleration2</span></td><td>N</td><td>Y</td><td><p>Second component of the total acceleration from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Km/s<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalAcceleration3</span></td><td>N</td><td>Y</td><td><p>Third component of the total acceleration from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Km/s<sup>2</sup></p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalMassFlowRate</span></td><td>N</td><td>Y</td><td><p>Total mass flow rate from all thrusters. Zero is
              reported whenever thruster is not turned on </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg/s</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalThrust1</span></td><td>N</td><td>Y</td><td><p>First component of the total thrust from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Newtons</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalThrust2</span></td><td>N</td><td>Y</td><td><p>Second component of the total thrust from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Newtons</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TotalThrust3</span></td><td>N</td><td>Y</td><td><p>Third component of the total thrust from all
              thrusters in the three coordinate directions of a J2000 system.
              Zero is reported whenever thruster is not turned on
              </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Newtons</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2FD35"></a><h3>Solver</h3><p><span class="guilabel">Solver</span> parameters allow you to query a
      <span class="guilabel">Solver</span> for its convergence state to determine if
      the <span class="guilabel">Solver</span> converged. There are both string and
      numeric parameters which are described in further detail in the table
      below the following usage example using solver parameters before and
      after a <span class="guilabel">Target</span> sequence.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aPropagator

Create ImpulsiveBurn aBurn
Create DifferentialCorrector aDC
Create OrbitView EarthView
EarthView.Add = {Earth,aSat}
EarthView.ViewScaleFactor = 5

Create ReportFile aReport

BeginMissionSequence
Report aReport aDC.SolverStatus aDC.SolverState
Target aDC
   Vary aDC(aBurn.Element1 = 1.0, {Upper = 3})
   Maneuver aBurn(aSat)
   Propagate aPropagator(aSat,{aSat.Apoapsis})
   Achieve aDC(aSat.RMAG = 42164)
EndTarget
Report aReport aDC.SolverStatus aDC.SolverState</code></pre><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="12%"><col width="12%"><col width="51%"></colgroup><thead><tr><th>Parameter</th><th>Settable</th><th>Plottable</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">SolverStatus</span></td><td>N</td><td>N</td><td><p>The <span class="guilabel">SolverStatus</span> parameter
              contains the state of a <span class="guilabel">Solver</span>. If the
              <span class="guilabel">Solver</span> has not executed,
              <span class="guilabel">SolverStatus</span> is
              <span class="guilabel">Initialized</span>. If the
              <span class="guilabel">Solver</span> has executed and converged,
              <span class="guilabel">SolverStatus</span> is
              <span class="guilabel">Converged</span>. If the
              <span class="guilabel">Solver</span> is iterating,
              <span class="guilabel">SolverStatus</span> is
              <span class="guilabel">Running</span>. If the <span class="guilabel">Solver</span>
              has executed and reached the maximum number of iterations before
              convergence, <span class="guilabel">SolverStatus</span> is
              <span class="guilabel">ExceededIterations</span>. If the
              <span class="guilabel">Solver</span> has executed and failed to converge,
              but did not exceed the maximum iterations,
              <span class="guilabel">SolverStatus</span> is
              <span class="guilabel">DidNotConverge</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolverState</span></td><td>N</td><td>Y</td><td><p>The <span class="guilabel">SolverState</span> parameter
              contains the state of a <span class="guilabel">Solver</span>. If the
              solver has not executed, <span class="guilabel">SolverState</span> is 0.
              If the <span class="guilabel">Solver</span> has executed and converged,
              <span class="guilabel">SolverState</span> is <span class="guilabel">1</span>. If
              the <span class="guilabel">Solver</span> is iterating, <span class="guilabel">
              SolverState</span> is 0. If the <span class="guilabel">Solver</span>
              has executed and reached the maximum number of iterations before
              convergence, <span class="guilabel">SolverState</span> is
              <span class="guilabel">-1</span>. If the <span class="guilabel">Solver</span> has
              executed and failed to converge, but did not exceed the maximum
              iterations, <span class="guilabel">SolverState</span> is
              <span class="guilabel">-2</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Dependency</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>(None)</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2FDFD"></a><h3>Array, String, Variable</h3><p><span class="guilabel">Array</span>, <span class="guilabel">String</span>, and
      <span class="guilabel">Variable</span> resources are themselves parameters, and
      can be used as any other parameter would. All of these are writable
      parameters, though only <span class="guilabel">Variable</span> resources and
      individual elements of <span class="guilabel">Array</span> resources can be
      plotted.</p></div><div class="refsection"><a name="ElapsedTimeParameters"></a><h3>Elapsed Time Parameters</h3><p>Elapsed time parameters such as <span class="guilabel">ElapsedSecs</span>
      and <span class="guilabel">ElapsedDays</span> are computed based on a reference
      epoch that is dependent upon the context of use. In general, the
      reference epoch is determined by the command where the parameter is
      used. The example below shows how the reference epoch is computed based
      on context.</p><div class="informalexample"><pre class="programlisting"><code class="code">Create Spacecraft Sat
Create Propagator Prop

BeginMissionSequence

Sat.Epoch.TAIModJulian = 35000

%  The reference epoch for While is TAIModJuian = 35000.
%  The ref. epoch is held constant as the while loop executes
While Sat.ElapsedDays &lt;= 1  
   
   % The reference epoch for Propagate changes every pass.
   % It is set to the epoch at start of propagation
   Propagate Prop(Sat){Sat.ElapsedDays = .1}   

EndWhile
</code></pre></div></div></div><div class="refsection"><a name="N2FE20"></a><h2>Examples</h2><div class="informalexample"><p>Using parameters in the Mission Sequence:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aProp
Create ReportFile aReport
Create Variable i

BeginMissionSequence

% propagate for 100 steps
For i=1:100
Propagate aProp(aSat)
% write four parameters (one standalone, three coordinate-system-dependent) to a file
Report aReport aSat.TAIGregorian aSat.EarthFixed.X aSat.EarthFixed.Y aSat.EarthFixed.Z
EndFor</code></pre></div><div class="informalexample"><p>Using parameters as plot data:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aProp

Create XYPlot aPlot
aPlot.XVariable = aSat.TAIModJulian
aPlot.YVariables = {aSat.Earth.Altitude, aSat.Earth.ECC}

Create Variable i

BeginMissionSequence

% propagate for 100 steps
For i=1:100
    Propagate aProp(aSat)
EndFor</code></pre></div><div class="informalexample"><p>Using parameters as stopping conditions:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.SMA = 6678

Create ForceModel anFM
anFM.Drag.AtmosphereModel = MSISE90

Create Propagator aProp
aProp.FM = anFM

BeginMissionSequence

Propagate aProp(aSat) {aSat.Earth.Altitude = 100, aSat.ElapsedDays = 365}</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch24.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch24.html#N2D3E2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="CommandLine.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;24.&nbsp;System&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Command-Line Usage</td></tr></table></div></body></html>