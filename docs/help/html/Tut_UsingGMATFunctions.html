<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch09s04.html" title="Design the Trajectory"><link rel="next" href="ch10s02.html" title="Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators, Differential Corrector, Coordinate Systems and Graphics"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch09s04.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch10s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Tut_UsingGMATFunctions"></a>Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Tut_UsingGMATFunctions.html#N12DC0">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch10s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s02.html#N12E53">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12EC5">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12F8E">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1301D">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1322B">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1325D">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch10s02.html#N132D5">Create the Orbit Views</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1344F">Create single Report File</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1348A">Create a GMAT Function</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s03.html#N134F3">Create Commands to Initiate the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N1358F">Configure the Mission Tree to Run the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N13597">Configure the Make Objects Global Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135BE">Configure the Target Desired B-Plane Coord. From Inside Function Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135E5">Configure the Report Parameters Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s04.html#N1369D">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13783">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F1">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F9">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13820">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138AC">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138D3">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13909">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Advanced</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>75 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Complete <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><i>Simulating an Orbit</i></a>, <a class="xref" href="SimpleOrbitTransfer.html" title="Chapter&nbsp;6.&nbsp;Simple Orbit Transfer"><i>Simple Orbit Transfer</i></a>, <a class="xref" href="Mars_B_Plane_Targeting.html" title="Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting"><i>Mars B-Plane Targeting</i></a> and a basic understanding of B-Planes and
        their usage in targeting is required.</p></td></tr><tr><td><p><span class="term">Script and function Files</span></p></td><td><p><code class="filename">Tut_UsingGMATFunctions.script, TargeterInsideFunction.gmf</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N12DC0"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>One of the most challenging problems in space mission design is to
      design an interplanetary transfer trajectory that takes the spacecraft
      within a very close vicinity of the target planet. One possible approach
      that puts the spacecraft close to a target planet is by targeting the
      B-Plane of that planet. The B-Plane is a planar coordinate system that
      allows targeting during a gravity assist. It can be thought of as a
      target attached to the assisting body. In addition, it must be
      perpendicular to the incoming asymptote of the approach hyperbola. <a class="xref" href="Tut_UsingGMATFunctions.html#Tut_UsingGMATFunctions_ID_1" title="Figure&nbsp;10.1.&nbsp;Geometry of the B-Plane as seen from a viewpoint perpendicular to the B-Plane">Figure&nbsp;10.1, &ldquo;<span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane&rdquo;</a> and <a class="xref" href="Tut_UsingGMATFunctions.html#Tut_UsingGMATFunctions_ID_2" title="Figure&nbsp;10.2.&nbsp;The B-vector as seen from a viewpoint perpendicular to orbit plane">Figure&nbsp;10.2, &ldquo;<span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane&rdquo;</a> show the geometry of
      the B-Plane and B-vector as seen from a viewpoint perpendicular to orbit
      plane. To read more on B-Planes, please consult the GMATMathSpec
      document. A good example involving the use of B-Plane targeting is a
      mission to Mars. Sending a spacecraft to Mars can be achieved by
      performing a Trajectory Correction Maneuver (TCM) that targets Mars
      B-Plane. Once the spacecraft gets close to Mars, then an orbit insertion
      maneuver can be performed to capture into Mars orbit.</p></div><div class="figure"><a name="Tut_UsingGMATFunctions_ID_1"></a><p class="title"><b>Figure&nbsp;10.1.&nbsp;<span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_Plane_1A.png" align="middle" width="274" alt="Geometry of the B-Plane as seen from a viewpoint perpendicular to the B-Plane"></div></div></div></div><br class="figure-break"><div class="figure"><a name="Tut_UsingGMATFunctions_ID_2"></a><p class="title"><b>Figure&nbsp;10.2.&nbsp;<span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_Plane_2.png" align="middle" width="359" alt="The B-vector as seen from a viewpoint perpendicular to orbit plane"></div></div></div></div><br class="figure-break"><p>In this tutorial, we will use GMAT to model a mission to Mars with
    the emphasis of how to use GMAT functions. Starting from an out-going
    hyperbolic trajectory around Earth, we will perform a TCM to target Mars
    B-Plane. Once we are close to Mars, we will adjust the size of the
    maneuver to perform a Mars Orbit Insertion (MOI) to achieve a final
    elliptical orbit with an inclination of 90 degrees. Meeting these mission
    objectives requires us to create two separate targeting sequences. In
    order to focus on the configuration of the two targeters, we will make
    extensive use of the default configurations for spacecraft, propagators,
    and maneuvers.</p><p>The first target sequence employs maneuvers in the Earth-based
    Velocity (V), Normal (N) and Bi-normal (B) directions and includes four
    propagation sequences. The purpose of the maneuvers in VNB directions is
    to target BdotT and BdotR components of the B-vector. BdotT is targeted to
    0 km and BdotR is targeted to a non-zero value to generate a polar orbit
    that has inclination of 90 degrees. BdotR is targeted to -7000 km to avoid
    having the orbit intersect Mars, which has a radius of approximately 3396
    km. The entire first target sequence will be created inside a GMAT
    function. In the <span class="guilabel">Mission</span> tree, this function will be called through GMAT's
    <span class="guilabel">CallGmatFunction</span> command. Additionally, we'll go ahead and declare
    pertinent objects (e.g. spacecraft, force models, subscribers, impulsive burns etc.) as global in both the main script and inside the
    function through GMAT's <span class="guilabel">Global</span> command. </p><p>The second target sequence employs a single, Mars-based
    anti-velocity direction (-V) maneuver and includes one propagation
    sequence. This single anti-velocity direction maneuver will occur at
    periapsis. The purpose of the maneuver is to achieve MOI by targeting
    position vector magnitude of 12,000 km at apoapsis. Unlike the first target sequence, the second target sequence will not be created inside a function.</p><p>The purpose behind this tutorial is to demonstrate how GMAT functions are created, populated, called-upon and used as part of practical mission design. 
	In this tutorial, we'll deliberately put the entire first target sequence inside a GMAT function. Next in the Mission tree, we'll call and execute the function,
	then continue with the design of the second target sequence outside of the function. Key objects such as the spacecraft, force models, subscribers etc. will be 
	declared global in order to assure continuous flow of data is plotted and reported to all the subscribers.
	The basic steps of this tutorial are: </p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Modify the <code class="classname">DefaultSC</code> to define
        spacecraft&rsquo;s initial state. The initial state is an out-going
        hyperbolic trajectory that is with respect to Earth.</p></li><li class="step"><p>Create and configure a <code class="classname">Fuel Tank</code>
        resource.</p></li><li class="step"><p>Create two <code class="classname">ImpulsiveBurn</code> resources with
        default settings.</p></li><li class="step"><p>Create and configure three <code class="classname">Propagators:</code>
        NearEarth, DeepSpace and NearMars</p></li><li class="step"><p>Create and configure
        <code class="classname">DifferentialCorrector</code> resource.</p></li><li class="step"><p>Create and configure three
        <code class="classname">DefaultOrbitView</code> resources to visualize Earth,
        Sun and Mars centered trajectories.</p></li><li class="step"><p>Create and configure single
        <code class="classname">ReportFile</code> resource that will be used in reporting data.</p></li><li class="step"><p>Create and configure three
        <code class="classname">CoordinateSystems:</code> Earth, Sun and Mars
        centered.</p></li><li class="step"><p>Create and configure single
        <code class="classname">GmatFunction</code> resource that will be called and executed in the <span class="guilabel">Mission</span> tree.
        </p></li><li class="step"><p>Create first <code class="classname">Target</code> sequence inside the GMAT function. This sequence will be used to target
        BdotT and BdotR components of the B-vector.</p></li><li class="step"><p>Create second <code class="classname">Target</code> sequence to
        implement MOI by targeting position magnitude at apoapsis.</p></li><li class="step"><p>Run the mission and analyze the results.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch09s04.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch10s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Design the Trajectory&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</td></tr></table></div></body></html>