<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Running GMAT</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="GettingStarted.html" title="Chapter&nbsp;2.&nbsp;Getting Started"><link rel="prev" href="GettingStarted.html" title="Chapter&nbsp;2.&nbsp;Getting Started"><link rel="next" href="SampleMissions.html" title="Sample Missions"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Running GMAT</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="GettingStarted.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;2.&nbsp;Getting Started</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SampleMissions.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="RunningGmat"></a>Running GMAT</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N102DD"></a>Starting GMAT</h3></div></div></div><p>If you installed GMAT from a <code class="filename">.zip</code> file or by
    compiling the system, locate the GMAT <code class="filename">bin</code> directory
    double-click <code class="filename">GMAT.exe</code>.</p><p>To start GMAT from the command line, run
    <code class="filename">GMAT.exe</code>. Various command-line parameters are
    available; see <a class="xref" href="CommandLine.html" title="Command-Line Usage"><span class="refentrytitle">Command-Line Usage</span></a> for
    details.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>GMAT on macOS is not a notarized application. The
      <code class="filename">GMAT</code> folder must be installed either to the
      system-wide <code class="filename">/Applications</code> folder or to the user's
      <code class="filename">~/Applications</code> folder. The latter is useful for
      users without admin access to their Mac.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10300"></a>Exiting GMAT</h3></div></div></div><p>To end a GMAT session on Windows or Linux, in the menu bar, click
    <span class="guimenu">File</span>, then click <span class="guimenuitem">Exit</span>. On
    macOS, in the menu bar, click <span class="guilabel">GMAT</span>, then click
    <span class="guimenuitem">Quit GMAT</span>, or type <span class="keycap"><strong>Command</strong></span>+<span class="keycap"><strong>Q</strong></span>.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="GettingStarted.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="GettingStarted.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SampleMissions.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;2.&nbsp;Getting Started&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Sample Missions</td></tr></table></div></body></html>