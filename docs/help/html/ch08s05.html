<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Run the Mission with first and second Target Sequences</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Mars_B_Plane_Targeting.html" title="Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting"><link rel="prev" href="ch08s04.html" title="Run the Mission with first Target Sequence"><link rel="next" href="OptimalLunarFlyby.html" title="Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Run the Mission with first and second Target Sequences</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch08s04.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="OptimalLunarFlyby.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N12940"></a>Run the Mission with first and second Target Sequences</h2></div></div></div><p>Before running the mission, click <span class="guilabel">Save</span>
    (<span class="inlinemediaobject"><img src="../files/images/icons/SaveMission.png" align="middle" height="10"></span>). This will save the additional changes that we implemented in the <span class="guilabel">Mission</span> tree.  Now click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>). The first <span class="guilabel">Target</span> sequence will converge in one-iteration. 
      This is because earlier, we stored the solution as the initial conditions. The second <span class="guilabel">Target</span> sequence may converge after 10 to11 iterations.</p><p>As the mission runs, you will see GMAT solve the second <span class="guilabel">Target</span> sequence&rsquo;s targeting problem. 
	Each iteration and perturbation is shown in <span class="guilabel">MarsView</span> windows in light blue, and the final solution 
	is shown in red. After the mission completes, the <span class="guilabel">MarsView</span> 3D view should appear as in  the image shown below. 
	<span class="guilabel">EarthView</span> and <span class="guilabel">SolarSystemView</span> 3D views are same as before. You may want to run the mission several times to see the targeting in progress.</p><div class="figure"><a name="N12975"></a><p class="title"><b>Figure&nbsp;8.28.&nbsp;3D view of Mars Capture orbit after MOI maneuver (MarsView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Mars Capture MarsView_2.png" align="middle" height="774" alt="3D view of Mars Capture orbit after MOI maneuver (MarsView)"></td></tr></table></div></div></div></div><br class="figure-break"><p>If you were to continue developing this mission, you can store the final solution
	of the second <span class="guilabel">Target</span> sequence as the initial condition of <span class="guilabel">MOI</span> resource. This is so that
	when you make small changes, the subsequent runs will take less time. To do this, follow these steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, double-click
        <span class="guilabel">Mars Capture</span> to edit its
        properties.</p></li><li class="step"><p>Click <span class="guilabel">Apply Corrections</span>.</p></li><li class="step"><p>Now re-run the mission. If you inspect the results in the message window, you will see that now
        the second <span class="guilabel">Target</span> sequence also converges in one iteration. This is because you stored the solution as the 
        initial condition. Now whenever you re-run the mission, both first and second Target sequences will converge in just one iteration.</p></li><li class="step"><p>In the <span class="guilabel">Mission</span> tree, double-click
        <span class="guilabel">Vary MOI.V</span>, you will notice that the values in
        <span class="guilabel">Initial Value</span> box have been updated to the final solution of the second
        <span class="guilabel">Target</span> sequence.</p></li></ol></div><p>If you want to know MOI maneuver&rsquo;s delta-V vector values and how
    much fuel was expended during the maneuver, do the following steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
        <span class="guilabel">Apply MOI</span>, and click on <span class="guilabel">Command
        Summary</span>.</p></li><li class="step"><p>Scroll down and under <code class="literal">Maneuver Summary</code>
        heading, values for delta-V vector are:</p><p><code class="literal">Delta V Vector:</code></p><p><code class="literal">Element 1:   -1.6034665169868 km/s</code></p><p><code class="literal">Element 2:    0.0000000000000 km/s</code></p><p><code class="literal">Element 3:    0.0000000000000 km/s</code></p></li><li class="step"><p>Scroll down and under <code class="literal">Mass depletion from
        MainTank</code> heading, <code class="literal">Delta V</code> and
        <code class="literal">Mass Change</code> tells you MOI maneuver&rsquo;s magnitude and
        how much fuel was used for the maneuver:</p><p><code class="literal">Delta V:        1.6034665169868 km/s</code></p><p><code class="literal">Mass change:   -1076.0639629424 kg</code></p></li></ol></div><p>Just to make sure that the goal of second
    <span class="guilabel">Target</span> sequence was met successfully, let us access
    command summary for <span class="guilabel">Achieve RMAG</span> command by
    doing the following steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
        <span class="guilabel">Achieve RMAG</span>, and click on
        <span class="guilabel">Command Summary</span>.</p></li><li class="step"><p>Under <span class="guilabel">Coordinate System</span>, select
        <span class="guilabel">MarsInertial</span>.</p></li><li class="step"><p>Under <code class="literal">Keplerian State</code> and <code class="literal">and Spherical State</code> headings, see the
        values of <code class="literal">TA</code> and <code class="literal">RMAG</code>. You can see that the desired radius of the capture orbit at apoapsis was achieved successfully:</p><p><code class="literal">TA   =   180.00000241484 deg</code></p><p><code class="literal">RMAG =   12000.019889021 km</code></p></li></ol></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch08s04.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Mars_B_Plane_Targeting.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="OptimalLunarFlyby.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Run the Mission with first Target Sequence&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting</td></tr></table></div></body></html>