<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="Appendix_A_Determination_of_Measurement_Noise_Values.html" title="Appendix A &ndash; Determination of Measurement Noise Values"><link rel="next" href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html" title="Create and configure the spacecraft, spacecraft transponder, and related parameters"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Appendix_A_Determination_of_Measurement_Noise_Values.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Orbit_Estimation_using_DSN_Range_and_Doppler_Data"></a>Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html#N14D17">Objective and Overview</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D6F">Create a satellite and set its epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D8D">Create a Transponder object and attach it to our
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html">Create and configure the Ground Station and related
    parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14DDC">Create Ground Station Transmitter, Receiver, and Antenna
      objects</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E26">Create Ground Station</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E54">Create Ground Station Error Models</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html">Define the types of measurements that will be processed</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_Force_model_and_propagator.html">Create and configure Force model and propagator</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html">Create and configure BatchEstimator object</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html">Run the mission and analyze the results</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Message_Window_Output">Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Plots_of_Observation_Residuals">Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Batch_Estimator_Output_Report">Batch Estimator Output Report</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Matlab_Output_File">Matlab Output File</a></span></dt></dl></dd><dt><span class="section"><a href="ch14s08.html">References</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_A.html">Appendix A &ndash; GMAT Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_B.html">Appendix B &ndash; Zeroth Iteration Plots of Observation
    Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_C.html">Appendix C &ndash; First Iteration Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_D.html">Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Intermediate level</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>60 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Simulate DSN Range and Doppler Data Tutorial</p></td></tr><tr><td><p><span class="term">Script Files</span></p></td><td><p><code class="filename">Tut_Orbit_Estimation_using_DSN_Range_and_Doppler_Data.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N14D17"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>GMAT currently implements a number of different data types for
      orbit determination. Please refer to <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a>
      for details on all the measurment types currently supported by GMAT. The
      measurements being considered here are DSN two way range and DSN two way
      Doppler.</p></div><p>In this tutorial, we will use GMAT to read in simulated DSN range
    and Doppler measurement data for a sample spacecraft in orbit about the
    Sun and determine its orbit. The spacecraft is in an Earth &ldquo;drift away&rdquo;
    type orbit about 1 AU away from the Sun and almost 300 million km away
    from the Earth. This tutorial has many similarities with the Simulate DSN
    Range and Doppler Data Tutorial in that most of the same GMAT resources
    need to be created and configured. There are differences, however, in how
    GMAT uses the resources that we will point out as we go along.</p><p>The basic steps of this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Create and configure the spacecraft, spacecraft transponder, and
        related parameters</p></li><li class="step"><p>Create and configure the Ground Station and related
        parameters</p></li><li class="step"><p>Define the types of measurements to be processed</p></li><li class="step"><p>Create and configure Force model and propagator</p></li><li class="step"><p>Create and configure Batch Estimator object</p></li><li class="step"><p>Run the mission and analyze the results</p></li></ol></div><p>Note that this tutorial, unlike most of the mission design
    tutorials, will be entirely script based. This is because most of the
    resources and commands related to navigation are not implemented in the
    GUI and are only available via the script interface.</p><p>As you go through the tutorial below, it is recommended that you
    paste the script segments into GMAT as you go along. After each paste into
    GMAT, you should perform a syntax check by hitting the Save, Sync button
    (<span class="inlinemediaobject"><img src="../files/images/icons/Save_Sync.png" align="middle" height="11"></span>). To avoid syntax errors, where needed, don&rsquo;t
    forget to add the following command, as needed, to the last line of the
    script segment you are checking.</p><pre class="programlisting">BeginMissionSequence</pre><p>We note that in addition to the material presented here, you should
    also look at the individual Help resources for all the objects and
    commands we create and use here. For example,
    <span class="guilabel">Spacecraft</span>, <span class="guilabel">Transponder</span>,
    <span class="guilabel">Transmitter</span>, <span class="guilabel">GroundStation</span>,
    <span class="guilabel">ErrorModel</span>, <span class="guilabel">TrackingFileSet</span>,
    <span class="guilabel">RunEstimator</span>, etc all have their own Help
    pages.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Appendix_A_Determination_of_Measurement_Noise_Values.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix A &ndash; Determination of Measurement Noise Values&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and configure the spacecraft, spacecraft transponder, and
    related parameters</td></tr></table></div></body></html>