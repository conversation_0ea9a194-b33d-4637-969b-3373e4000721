<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;5.&nbsp;Simulating an Orbit</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="Tutorials.html" title="Tutorials"><link rel="next" href="ch05s02.html" title="Configure the Spacecraft"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;5.&nbsp;Simulating an Orbit</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Tutorials.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch05s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="SimulatingAnOrbit"></a>Chapter&nbsp;5.&nbsp;Simulating an Orbit</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="SimulatingAnOrbit.html#N10E53">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch05s02.html">Configure the Spacecraft</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s02.html#N10E97">Rename the Spacecraft</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EB4">Set the Spacecraft Epoch</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EE3">Set the Keplerian Orbital Elements</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s03.html">Configure the Propagator</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s03.html#N10F61">Rename the Propagator</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10F7E">Configure the Force Model</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10FF5">Configuring the Orbit View Plot</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s04.html">Configure the Propagate Command</a></span></dt><dt><span class="section"><a href="ch05s05.html">Run and Analyze the Results</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Beginner</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>30 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Tut_SimulatingAnOrbit.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N10E53"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The most fundamental capability of GMAT is to propagate, or
      simulate the orbital motion of, spacecraft. The ability to propagate
      spacecraft is used in nearly every practical aspect of space mission
      analysis, from simple orbital predictions (e.g. When will the
      International Space Station be over my house?) to complex analyses that
      determine the thruster firing sequence required to send a spacecraft to
      the Moon or Mars.</p></div><p>This tutorial will teach you how to use GMAT to propagate a
    spacecraft. You will learn how to configure
    <code class="classname">Spacecraft</code> and <code class="classname">Propagator</code>
    resources, and how to use the <code class="function">Propagate</code> command to
    propagate the spacecraft to orbit periapsis, which is the point of minimum
    distance between the spacecraft and Earth. The basic steps in this
    tutorial are:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Configure a <code class="classname">Spacecraft</code> and define its
        epoch and orbital elements.</p></li><li class="listitem"><p>Configure a <code class="classname">Propagator</code>.</p></li><li class="listitem"><p>Modify the default <code class="classname">OrbitView</code> plot to
        visualize the spacecraft trajectory.</p></li><li class="listitem"><p>Modify the <code class="function">Propagate</code> command to propagate
        the spacecraft to periapsis.</p></li><li class="listitem"><p>Run the mission and analyze the results.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Tutorials.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch05s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Tutorials&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Spacecraft</td></tr></table></div></body></html>