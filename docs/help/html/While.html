<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>While</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s02.html" title="Commands"><link rel="prev" href="Stop.html" title="Stop"><link rel="next" href="ch22s03.html" title="System"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">While</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Stop.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch22s03.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="While"></a><div class="titlepage"></div><a name="N2D04B" class="indexterm"></a><a name="N2D04E" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">While</span></h2><p>While &mdash; Execute a series of commands repeatedly while a condition is
    met</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">While</code> <em class="replaceable"><code>logical expression</code></em>
    [<em class="replaceable"><code>script statement</code></em>]
    &hellip;
<code class="literal">EndWhile</code></pre></div><div class="refsection"><a name="N2D06E"></a><h2>Description</h2><p>The <span class="guilabel">While</span> command is a control logic statement
    that executes a series of commands repeatedly as long as the value of the
    provided logical expression is true. The logical expression is evaluated
    before every iteration of the loop. If the expression is initially false,
    the loop is never executed. If the while loop is empty, it is skipped and 
    a warning message is posted to the user. The syntax of the expression is 
    described in the <a class="link" href="ScriptLanguage.html" title="Script Language">script language
    reference</a>.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="ScriptLanguage.html" title="Script Language"><span class="refentrytitle">Script Language</span></a>, <a class="xref" href="For.html" title="For"><span class="refentrytitle">For</span></a>, <a class="xref" href="If.html" title="If"><span class="refentrytitle">If</span></a></p></div><div class="refsection"><a name="N2D087"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_While_GUI.png" align="middle" height="318"></td></tr></table></div></div><p>The <span class="guilabel">While</span> command GUI panel features a table in
    which you can build a complex logical expression. The rows of the table
    correspond to individual relational expressions in a compound logical
    expression, and the columns correspond to individual elements of those
    expressions. The first line automatically contains a default
    statement:</p><pre class="programlisting"><code class="code">While DefaultSC.ElapsedDays &lt; 1.0</code></pre><p>The first column of the first row contains a placeholder for the
    <span class="guilabel">While</span> command name. This cannot be changed. The first
    column of each additional row contains the logical operator
    (<span class="guilabel">&amp;</span>, <span class="guilabel">|</span>) that joins the
    expression in that row with the one above it. To select a logical
    operator, double-click or right-click in the appropriate box in the table,
    and a selection window will appear. Click the correct operator and click
    <span class="guilabel">OK</span> to select it.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_IfWhile_LogicalOperators.png" align="middle" height="336"></td></tr></table></div></div><p>The <span class="guilabel">Left Hand Side</span> column contains the
    left-hand side of each individual relational expression. Double-click the
    cell to type a parameter name. To set this value from a parameter
    selection list instead, either click &ldquo;&hellip;&rdquo; to the left of the cell you want
    to set, or right-click the cell itself. A
    <span class="guilabel">ParameterSelectDialog</span> window will appear that allows
    you to choose a parameter.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_While_GUI_3.png" align="middle" height="416"></td></tr></table></div></div><p>The <span class="guilabel">Condition</span> column contains the conditional
    operator (<span class="guilabel">==</span>, <span class="guilabel">~=</span>,
    <span class="guilabel">&lt;</span>, etc.) that joins the left-hand and right-hand
    sides of the expression. To select a relational operator, double-click or
    right-click in the appropriate box in the table, and a selection window
    will appear. Click the correct operator and click <span class="guilabel">OK</span>
    to select it.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_IfWhile_RelationalOperators.png" align="middle" height="336"></td></tr></table></div></div><p>Finally, the <span class="guilabel">Right Hand Side</span> column contains
    the right-hand side of the expression. This value can be modified the same
    way as the <span class="guilabel">Left Hand Side</span> column.</p><p>When you are finished, click <span class="guilabel">Apply</span> to save your
    changes, or click <span class="guilabel">OK</span> to save your changes and close
    the window. The command will be validated when either button is
    clicked.</p></div><div class="refsection"><a name="N2D0ED"></a><h2>Examples</h2><div class="informalexample"><p>Propagate a spacecraft until it reaches a predefined altitude,
      reporting data at each periapsis crossing:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.SMA = 6800
aSat.ECC = 0

Create ForceModel aForceModel
aForceModel.Drag.AtmosphereModel = MSISE90

Create Propagator aProp
aProp.FM = aForceModel

Create ReportFile aReport

BeginMissionSequence

While aSat.Altitude &gt; 300
    Propagate aProp(aSat) {aSat.Periapsis}
    Report aReport aSat.TAIGregorian aSat.Altitude
EndWhile</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Stop.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch22s03.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Stop&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;System</td></tr></table></div></body></html>