<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Mission Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_UsingGMATFunctions.html" title="Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions"><link rel="prev" href="ch10s02.html" title="Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators, Differential Corrector, Coordinate Systems and Graphics"><link rel="next" href="ch10s04.html" title="Run the Mission with first Target Sequence"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Mission Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch10s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch10s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N134DA"></a>Configure the Mission Sequence</h2></div></div></div><p>Now we are ready to configure the <span class="guilabel">Mission Sequence</span>. We will first insert a <span class="guilabel">Global</span> command 
    and declare the same objects as global that were declared global inside the <span class="guilabel">TargeterInsideFunction</span> function.
	Next we'll insert <span class="guilabel">CallGmatFunction</span> command which will call and initiate our <span class="guilabel">TargeterInsideFunction</span> function that contains our first target sequence.
	The first target sequence will solve for the <span class="guilabel">TCM</span> maneuver values required to achieve BdotT and BdotR
    components of the B-vector. BdotT will be targeted to 0 km and BdotR is
    targeted to a non-zero value in order to generate a polar orbit that will
    have an inclination of 90 degrees.</p><p>The second target sequence employs a single, Mars-based
    anti-velocity direction (-V) maneuver and includes one propagation
    sequence. This single anti-velocity direction maneuver will occur at
    periapsis. The purpose of the maneuver is to achieve MOI by targeting
    position vector magnitude of 12,000 km at apoapsis. The basic steps of
    this tutorial are:</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N134F3"></a>Create Commands to Initiate the First Target Sequence</h3></div></div></div><p>Now create the commands necessary to perform the first
      <span class="guilabel">Target</span> sequence. <a class="xref" href="ch10s03.html#Tut_UsingGMATFunctions_1" title="Figure&nbsp;10.3.&nbsp;Mission Sequence for the First Target sequence">Figure&nbsp;10.3, &ldquo;Mission Sequence for the First Target sequence&rdquo;</a> illustrates the
      configuration of the <span class="guilabel">Mission</span> tree after you have
      completed the steps in this section. </p><div class="figure"><a name="Tut_UsingGMATFunctions_1"></a><p class="title"><b>Figure&nbsp;10.3.&nbsp;Mission Sequence for the First Target sequence</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_1.png" align="middle" height="73" alt="Mission Sequence for the First Target sequence"></td></tr></table></div></div></div></div><br class="figure-break"><p>Do following steps to set-up for the first <code class="function">Target</code> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>You&rsquo;ll see that there already exists a
          <span class="guilabel">Propagate1</span> command. We need to delete this
          command</p></li><li class="step"><p>Right-click on <span class="guilabel">Propagate1</span> command and
          click <span class="guilabel">Delete</span>.</p></li><li class="step"><p>Right-click on <span class="guilabel">Mission Sequence</span> folder,
          point to <span class="guilabel">Append</span>, and click
          <span class="guilabel">Global</span>. A new command called
          <span class="guilabel">Global1</span> will be created.</p></li><li class="step"><p>Right-click <span class="guilabel">Global1</span> and click
          <span class="guilabel">Rename</span>. In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Make Objects Global</span> and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click on <span class="guilabel">Mission Sequence</span> folder,
          point to <span class="guilabel">Append</span>, and click
          <span class="guilabel">CallGmatFunction</span>. A new command called
          <span class="guilabel">CallGmatFunction1</span> will be created.</p></li><li class="step"><p>Right-click <span class="guilabel">CallGmatFunction1</span> and click
          <span class="guilabel">Rename</span>. In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Target Desired B-Plane Coord. From Inside Function</span> and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click on <span class="guilabel">Mission Sequence</span> folder,
          point to <span class="guilabel">Append</span>, and click
          <span class="guilabel">Report</span>. A new command called
          <span class="guilabel">Report1</span> will be created.</p></li><li class="step"><p>Right-click <span class="guilabel">Report1</span> and click
          <span class="guilabel">Rename</span>. In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Report Parameters</span> and click <span class="guilabel">OK</span>.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1358F"></a>Configure the Mission Tree to Run the First Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure various
      parts of the first <span class="guilabel">Target</span> sequence to do what we
      want.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13597"></a>Configure the Make Objects Global Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Make Objects Global</span> to edit its properties.</p></li><li class="step"><p>Under <span class="guilabel">Please Select Objects to Make Global</span> check all the available object and make all available objects as global.
          Recall that same objects were declared as global inside <span class="guilabel">TargeterInsideFunction</span> function as well.
          </p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N135B0"></a><p class="title"><b>Figure&nbsp;10.4.&nbsp;<span class="guilabel">Make Objects Global</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_2.png" align="middle" height="500" alt="Make Objects Global Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N135BE"></a>Configure the Target Desired B-Plane Coord. From Inside Function Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Target Desired B-Plane Coord. From Inside Function</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Function</span>, select <span class="guilabel">TargeterInsideFunction</span> from drop down menu. In this particular example, since we're not passing any
		  input(s) or receiving any output(s) to and from the function, hence we won't be editing Input/Output menu.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N135D7"></a><p class="title"><b>Figure&nbsp;10.5.&nbsp;<span class="guilabel">Target Desired B-Plane Coord. From Inside Function</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_3.png" align="middle" height="245" alt="Target Desired B-Plane Coord. From Inside Function Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N135E5"></a>Configure the Report Parameters Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Report Parameters</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">ReportFile</span>, make sure <span class="guilabel">rf</span> is selected from the from drop down menu.</p></li><li class="step"><p>Under <span class="guilabel">Parameter List</span> click on <span class="guilabel">View</span>. This opens up a new <span class="guilabel">ParameterSelectDialog</span> panel. Make sure to
		  select the parameters that are shown in the below <span class="guilabel">Report Parameters</span> screenshot image.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N1360D"></a><p class="title"><b>Figure&nbsp;10.6.&nbsp;<span class="guilabel">Report Parameters</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_UsingGMATFunctions_4.png" align="middle" height="384" alt="Report Parameters Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch10s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_UsingGMATFunctions.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch10s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission with first Target Sequence</td></tr></table></div></body></html>