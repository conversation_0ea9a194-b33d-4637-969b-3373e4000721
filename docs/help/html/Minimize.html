<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Minimize</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch20s02.html" title="Commands"><link rel="prev" href="Achieve.html" title="Achieve"><link rel="next" href="NonlinearConstraint.html" title="NonlinearConstraint"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Minimize</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Achieve.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="NonlinearConstraint.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Minimize"></a><div class="titlepage"></div><a name="N26D3D" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Minimize</span></h2><p>Minimize &mdash; Define the cost function to minimize</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">Minimize</code> <em class="replaceable"><code>OptimizerName</code></em> (<em class="replaceable"><code>ObjectiveFunction</code></em>)    </pre></div><div class="refsection"><a name="N26D5B"></a><h2>Description</h2><p>The <span class="guilabel">Minimize</span> command is used within an
    <span class="guilabel">Optimize</span>/<span class="guilabel">EndOptimize</span>
    Optimization sequence to define the objective function that you want to
    minimize.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Vary.html" title="Vary"><span class="refentrytitle">Vary</span></a>, <a class="xref" href="NonlinearConstraint.html" title="NonlinearConstraint"><span class="refentrytitle">NonlinearConstraint</span></a>, <a class="xref" href="Optimize.html" title="Optimize"><span class="refentrytitle">Optimize</span></a></p></div><div class="refsection"><a name="N26D76"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">ObjectiveFunction</span></td><td><p> Specifies the objective function that the optimizer
            will try to minimize. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Spacecraft parameter, Array element, Variable, or
                    any other single element user defined parameter, excluding
                    numbers</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">DefaultSC.Earth.RMAG</code>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OptimizerName</span></td><td><p> Specifies which optimizer to use to minimize the
            cost function </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any <span class="guilabel">VF13ad</span> or
                    <span class="guilabel">fminconOptimizer</span> resource</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultSQP</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N26DE5"></a><h2>GUI</h2><p>You use a <span class="guilabel">Minimize</span> command, within an
    <span class="guilabel">Optimize</span>/<span class="guilabel">EndOptimize</span>
    Optimization sequence as shown below, to define a cost function that you
    wish to minimize.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Minimize_GUI_1.png" align="middle" height="94"></td></tr></table></div></div><p>Double click on <span class="guilabel">Minimize1</span> to bring up the
    <span class="guilabel">Minimize</span> command dialog box shown below..</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Minimize_GUI_2.png" align="middle" height="156"></td></tr></table></div></div><p>You must provide two inputs for the <span class="guilabel">Minimize</span>
    command dialog box above: </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Choice of optimizer.</p></li><li class="listitem"><p>Object (and associated variable) to be minimized. You can
          input an object directly or you can click the
          <span class="guilabel">Edit</span> button to the right of this field to
          select the type of object from three possible choices,
          <span class="guilabel">Spacecraft</span>, <span class="guilabel">Variable</span>, or
          <span class="guilabel">Array</span>.</p></li></ul></div></div><div class="refsection"><a name="N26E25"></a><h2>Remarks</h2><div class="refsection"><a name="N26E28"></a><h3>Number of Vary, NonlinearConstraint, and Minimize Commands Within
      an Optimization Sequence</h3><p>An Optimization sequence must contain one or more
      <span class="guilabel">Vary</span> commands. <span class="guilabel">Vary</span> commands
      must occur before any <span class="guilabel">Minimize</span> or
      <span class="guilabel">NonlinearConstraint</span> commands.</p><p>At most, a single <span class="guilabel">Minimize</span> command is allowed
      within an optimization sequence.</p><p>It is possible for an
      <span class="guilabel">Optimize</span>/<span class="guilabel">EndOptimize</span>
      optimization sequence to contain no <span class="guilabel">Minimize</span>
      commands. In this case, since every optimization sequence must contain
      (a) one or more <span class="guilabel">NonlinearConstraint</span> commands and/or
      (b) a single <span class="guilabel">Minimize</span> command, the optimization
      sequence must contain at least one
      <span class="guilabel">NonlinearConstraint</span> command.</p></div><div class="refsection"><a name="N26E52"></a><h3>Command Interactions</h3><p>The <span class="guilabel">Minimize</span> command is only used within an
      <span class="guilabel">Optimize</span>/<span class="guilabel">EndOptimize</span>
      Optimization sequence. See the <span class="guilabel">Optimize</span> command
      documentation for a complete worked example using the
      <span class="guilabel">Minimize</span> command.</p><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><tbody><tr><td><span class="guilabel">Vary command </span></td><td><p> Every Optimization sequence must contain at least
              one <span class="guilabel">Vary</span> command. <span class="guilabel">Vary</span>
              commands are used to define the control variables associated
              with an Optimization sequence. </p></td></tr><tr><td><span class="guilabel">NonlinearConstraint command</span></td><td><p> <span class="guilabel">NonlinearConstraint</span> commands
              are used to define the constraints (i.e., goals) associated with
              an Optimization sequence. Note that multiple
              <span class="guilabel">NonlinearConstraint</span> commands are allowed
              within an Optimization sequence. </p></td></tr><tr><td><span class="guilabel">Optimize command</span></td><td><p> A <span class="guilabel">Minimize</span> command can only
              occur within an <span class="guilabel">Optimize/EndOptimize</span>
              command sequence. </p></td></tr></tbody></table></div></div></div><div class="refsection"><a name="N26E99"></a><h2>Examples</h2><div class="informalexample"><pre class="programlisting"><code class="code">% Minimize the eccentricity of Sat, using SQP1
Minimize SQP1(Sat.ECC)

% Minimize the Variable DeltaV, using SQP1
Minimize SQP1(DeltaV)

% Minimize the first component of MyArray, using VF13ad1
Minimize VF13ad1(MyArray(1,1))</code>      </pre></div><p>As mentioned above, the <span class="guilabel">Minimize</span> command only
    occurs within an <span class="guilabel">Optimize</span> sequence. See the
    <span class="guilabel"><a class="xref" href="Optimize.html" title="Optimize"><span class="refentrytitle">Optimize</span></a></span> command help for complete
    examples showing the use of the <span class="guilabel">Minimize</span>
    command.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Achieve.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch20s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="NonlinearConstraint.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Achieve&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;NonlinearConstraint</td></tr></table></div></body></html>