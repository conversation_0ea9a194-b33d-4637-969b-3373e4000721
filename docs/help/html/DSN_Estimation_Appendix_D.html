<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data"><link rel="prev" href="DSN_Estimation_Appendix_C.html" title="Appendix C &ndash; First Iteration Plots of Observation Residuals"><link rel="next" href="FilterSmoother_GpsPosVec.html" title="Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="DSN_Estimation_Appendix_C.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="FilterSmoother_GpsPosVec.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="DSN_Estimation_Appendix_D"></a>Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</h2></div></div></div><p>In this tutorial, we performed estimation using DSN data. In this
    appendix, we take a look at how our simulation script described in the
    previous tutorial,
    <code class="filename">Tut_Simulate_DSN_Range_and_Doppler_Data_3_weeks.script</code>,
    and our estimation script described in this tutorial,
    <code class="filename">Tut_Orbit_Estimation_using_DSN_Range_and_Doppler_Data.script</code>,
    need to be changed in order to process GN instead of DSN data. Recall that
    both of these scripts can be found in the GMAT "samples" folder. We note
    that these tutorials used a deep space spacecraft state which is
    consistent with using using DSN data. For the discussion that follows, we
    will use this same spacecraft state even though typically the GN data type
    is used with a spacecraft closer to Earth.</p><p>In the discussion that follows, we will comment out the old commands
    using DSN data and replace it with new commands using GN data. We start
    first with the simulation script. In what follows, we will use the object
    names previously created even though the object names may no longer be
    accurate. For example, in the simulation script, we created an
    <span class="guilabel">Antenna</span> object, <span class="guilabel">DSNAntenna</span>. We
    will continue to use this object name although clearly a better name for
    this object would be <span class="guilabel">GNAntenna</span>. Let's consider the
    spacecraft and ground station electronics. For the GN, S-band is typically
    used. We change the value of the spacecraft transponder turn-around ratio
    and the ground station transmitter frequency to account for this.</p><pre class="programlisting">SatTransponder.TurnAroundRatio = '240/221';       % was '880/749';

DSNTransmitter.Frequency       = 2067.5;   %MHz.  % was 7200</pre><p>We now consider the<span class="guilabel"> ErrorModels</span> objects,
    attached to our 3 <span class="guilabel">GroundStation</span> objects, used to
    describe the DSN noise characteristics. Since we will now be using the GN,
    we must change the <span class="guilabel">ErrorModels</span> to describe GN noise
    characteristics. As shown below, the Range 1-sigma noise is set to 10
    meters and the RangeRate 1-sigma noise is set to 1 cm/s.</p><pre class="programlisting" width="80">DSNrange.Type             = 'Range'       % was 'DSN_SeqRange';
DSNrange.NoiseSigma       = 0.010;        % was 10.63;

DSNdoppler.Type           = 'RangeRate';  %was 'DSN_TCP';
DSNdoppler.NoiseSigma     = 0.00001;      %was 0.0282;</pre><p>As shown below, we need to tell GMAT to simulate Range and RangeRate
    data instead of DSN_SeqRange and DSN_TCP data.</p><pre class="programlisting" width="80">DSNsimData.AddTrackingConfig = {{CAN, Sat, CAN},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{CAN, Sat, CAN},'RangeRate'};  %was 'DSN_TCP'
DSNsimData.AddTrackingConfig = {{GDS, Sat, GDS},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{GDS, Sat, GDS},'RangeRate'};  %was 'DSN_TCP'
DSNsimData.AddTrackingConfig = {{MAD, Sat, MAD},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{MAD, Sat, MAD},'RangeRate'};  %was 'DSN_TCP'</pre><p>Our original simulation script used a ramp table to specify the
    ground station transmit frequencies. Since ramp tables are not used with
    the GN, as shown below, we want to comment out the command that reads in a
    ramp file. For script clarity, we also comment out a command that does not
    apply for GN data.</p><pre class="programlisting" width="80">%DSNsimData.RampTable  = ...
             {'../data/navdata/Simulate DSN Range and Doppler Data 3 weeks.rmp'};

%DSNsimData.SimRangeModuloConstant  = 3.3554432e+07;</pre><p>With all the changes above made to script,
    <code class="filename">Tut_Simulate_DSN_Range_and_Doppler_Data_3_weeks</code>, save
    the script to a new name, say, <code class="filename">Simulate_GN_data</code>.
    Then, run this new script. As we know from the simulation tutorial, a file
    named <code class="filename">Simulate DSN Range and Doppler Data 3 weeks.gmd</code>
    will be written to the local GMAT installation "output" folder
    location.</p><p>We now move on the examining the estimation script. Open up the
    estimation script from the tutorial and save it to a new name, say
    <code class="filename">Estimation_GN_data</code>. As was done for the simulation
    script, we change the value of the spacecraft transponder turn-around
    ratio and the ground station transmitter frequencies to account for the
    fact that we are using the GN.</p><pre class="programlisting" width="80">SatTransponder.TurnAroundRatio = '240/221';      % was '880/749';
DSNTransmitter.Frequency       = 2067.5;   %MHz.  % was 7200
</pre><p>As was done for the simulation script, we configure the
    <span class="guilabel">ErrorModels</span> objects to describe GN data as opposed to
    DSN data.</p><pre class="programlisting" width="80">DSNrange.Type             = 'Range'       % was 'DSN_SeqRange';
DSNrange.NoiseSigma       = 0.010;        % was 10.63;

DSNdoppler.Type           = 'RangeRate';  %was 'DSN_TCP';
DSNdoppler.NoiseSigma     = 0.00001;      %was 0.0282;</pre><p>Next, we need to tell GMAT to estimate using Range and RangeRate
    data instead of DSN_SeqRange and DSN_TCP data. We note that, if desired,
    the new lines below can be deleted and the script will still work. GMAT
    will just read in the GMD file and automatically detect which ground
    stations and data types are being used.</p><pre class="programlisting" width="80">DSNsimData.AddTrackingConfig = {{CAN, Sat, CAN},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{CAN, Sat, CAN},'RangeRate'};  %was 'DSN_TCP'
DSNsimData.AddTrackingConfig = {{GDS, Sat, GDS},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{GDS, Sat, GDS},'RangeRate'};  %was 'DSN_TCP'
DSNsimData.AddTrackingConfig = {{MAD, Sat, MAD},'Range'};      %was 'DSN_SeqRange'
DSNsimData.AddTrackingConfig = {{MAD, Sat, MAD},'RangeRate'};  %was 'DSN_TCP'</pre><p>Recall that our new GN simulation script is configured to output the
    GMD file to the GMAT "output" directory. Let's change our estimation
    script, which will read in this file, accordingly.</p><pre class="programlisting" width="85">% DSNsimData.FileName = {'../data/navdata/Simulate DSN Range and Doppler Data 3 weeks.gmd'};
DSNsimData.FileName   = {'../output/Simulate DSN Range and Doppler Data 3 weeks.gmd'};</pre><p>As was done for our simulation script, we comment out the line that
    reads in a ramp table.</p><pre class="programlisting" width="85">%DSNsimData.RampTable  = {'../data/navdata/Simulate DSN Range and Doppler Data 3 weeks.rmp'};</pre><p>The last step is to run our new <code class="filename">Estimation_GN_data
    script</code> and then analyze the results in a similar fashion as to
    what was done in the Estimation tutorial.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="DSN_Estimation_Appendix_C.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="FilterSmoother_GpsPosVec.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix C &ndash; First Iteration Plots of Observation Residuals&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</td></tr></table></div></body></html>