<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>OrbitView</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch19.html#N22D14" title="Resources"><link rel="prev" href="OpenFramesInterface.html" title="OpenFramesInterface"><link rel="next" href="ReportFile.html" title="ReportFile"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">OrbitView</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="OpenFramesInterface.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReportFile.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="OrbitView"></a><div class="titlepage"></div><a name="N23C1A" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">OrbitView</span></h2><p>OrbitView &mdash; A user-defined resource that plots 3-Dimensional
    trajectories</p></div><div class="refsection"><a name="N23C2B"></a><h2>Description</h2><p>The <span class="guilabel">OrbitView</span> resource allows you to plot
    trajectories of a spacecraft or a celestial body. GMAT also allows you to
    plot trajectories associated with multiple spacecrafts or celestial
    bodies. You can create multiple <span class="guilabel">OrbitView</span> resources
    by using either the GUI or script interface of GMAT.
    <span class="guilabel">OrbitView</span> plots also come with multiple options that
    allow you to customize the view of spacecraft&rsquo;s trajectories. See the
    <a class="xref" href="OrbitView.html#Fields_Section" title="Fields">Fields</a> section below
    for detailed discussion on available plotting and drawing options.</p><p>GMAT also provides the option of when to start and stop plotting
    spacecraft&rsquo;s trajectories to an <span class="guilabel">OrbitView</span> resource
    through the <span class="guilabel">Toggle On/Off</span> command. See the <a class="xref" href="OrbitView.html#Orbitview_Remarks" title="Remarks">Remarks</a> section below for
    detailed discussion of the interaction between an
    <span class="guilabel">OrbitView</span> resource and the
    <span class="guilabel">Toggle</span> command. GMAT&rsquo;s
    <span class="guilabel">Spacecraft</span>, <span class="guilabel">SolarSystem</span> and
    <span class="guilabel">OrbitView</span> resources also interact with each other
    throughout the entire mission duration. Discussion of the interaction
    between these resources is also mentioned in the <a class="xref" href="OrbitView.html#Orbitview_Remarks" title="Remarks">Remarks</a> section.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Toggle.html" title="Toggle"><span class="refentrytitle">Toggle</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a>, <a class="xref" href="SolarSystem.html" title="SolarSystem"><span class="refentrytitle">SolarSystem</span></a>, <a class="xref" href="CoordinateSystem.html" title="CoordinateSystem"><span class="refentrytitle">CoordinateSystem</span></a>, <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a></p></div><div class="refsection"><a name="Fields_Section"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="29%"><col width="71%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Add</span></td><td><p>This field allows you to add a
            <span class="guilabel">Spacecraft</span>, <span class="guilabel">Celestial
            body</span>, <span class="guilabel">Libration Point</span>, or
            <span class="guilabel">Barycenter</span> resource to a plot. When creating
            a plot, the <span class="guilabel">Earth</span> is added as a default body
            and may be removed at any time. You can add a
            <span class="guilabel">Spacecraft</span>, <span class="guilabel">Celestial
            body</span>, <span class="guilabel">Libration Point</span>, or
            <span class="guilabel">Barycenter</span> to a plot by using the name used
            to create the resource. The GUI's <span class="guilabel">Selected</span>
            field is the equivalent of the script's <span class="guilabel">Add</span>
            field. In the event of no <span class="guilabel">Add</span> command or no
            resources in the <span class="guilabel">Selected</span> field, GMAT should
            run without the <span class="guilabel">OrbitView</span> plot and a warning
            message will be displayed in the message window. The following
            warning message is sufficient: The OrbitView named
            "DefaultOrbitView" will be turned off. No SpacePoints were added
            to plot. This field cannot be modified in the Mission
            Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span>,
                    <span class="guilabel">CelestialBody</span>,
                    <span class="guilabel">LibrationPoint</span>,
                    <span class="guilabel">Barycenter</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">DefaultSC</span>,
                    <span class="guilabel">Earth</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Axes</span></td><td><p>Allows you to draw the Cartesian axis system
            associated with the coordinate system selected under the
            <span class="guilabel">CoordinateSystem</span> field of an
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>On</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EclipticPlane</span></td><td><p>Allows you to draw a grid representing the
            <span class="guilabel">Ecliptic Plane</span> in an
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CoordinateSystem</span></td><td><p>Allows you to select which <span class="guilabel">coordinate
            system</span> to use to draw the plot data. A
            <span class="guilabel">coordinate system</span> is defined as an
            <span class="guilabel">origin</span> and an <span class="guilabel">axis
            system</span>. The <span class="guilabel">CoordinateSystem</span> field
            allows you to determine the <span class="guilabel">origin</span> and
            <span class="guilabel">axis system</span> of an
            <span class="guilabel">OrbitView</span> plot. See the
            <span class="guilabel">CoordinateSystem</span> resource fields for
            information of defining different types of <span class="guilabel">coordinate
            systems</span>. This field cannot be modified in the Mission
            Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CoordinateSystem</span>
                    resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">EarthMJ2000Eq</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DataCollectFrequency</span></td><td><p>Allows you to define how data is collected for
            plotting. It is often inefficient to draw every ephemeris point
            associated with a trajectory. Often, drawing a smaller subset of
            the data still results in smooth trajectory plots, while executing
            more quickly. The <span class="guilabel">DataCollectFrequency</span> is an
            integer that represents how often to collect data and store for
            plotting. If <span class="guilabel">DataCollectFrequency</span> is set to
            10, then data is collected every 10 integration steps. This field
            cannot be modified in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &ge; 1</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DrawObject</span></td><td><p> The <span class="guilabel">DrawObject</span> field allows you
            the option of displaying <span class="guilabel">Spacecraft</span> or
            <span class="guilabel">Celestial</span> resources on the
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>[true true]</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EnableConstellations </span></td><td><p>Allows you the option of displaying star
            constellations on the <span class="guilabel">OrbitView</span> Plot. This
            field cannot be modified in the Mission Sequence.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>On</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EnableStars </span></td><td><p>This field gives you the option of displaying stars
            on the <span class="guilabel">OrbitView</span> Plot. When the
            <span class="guilabel">EnableStars</span> field is turned off, then
            <span class="guilabel">EnableConstellations</span> field is automatically
            diabled. This field cannot be modified in the Mission
            Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>On</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Grid </span></td><td><p>Allows you to draw a grid representing the longitude
            and latitude lines on the celestial bodies added to an
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Maximized</span></td><td><p>Allows you to maximize the
            <span class="guilabel">OrbitView</span> plot window. This field cannot be
            modified in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>false</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">NumPointsToRedraw</span></td><td><p> When <span class="guilabel">NumPointsToRedraw</span> field is
            set to zero, all ephemeris points are drawn. When
            <span class="guilabel">NumPointsToRedraw</span> is set to a positive
            integer, say 10 for example, only the last 10 collected data
            points are drawn. See <span class="guilabel">DataCollectFrequency</span>
            for explanation of how data is collected for an
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &ge; 1</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RelativeZOrder</span></td><td><p>Allows you to select which
            <span class="guilabel">OrbitView</span> window to display first on the
            screen. The <span class="guilabel"> OrbitViewPlot</span> with lowest
            <span class="guilabel">RelativeZOrder</span> value will be displayed last
            while <span class="guilabel">OrbitViewPlot</span> with highest
            <span class="guilabel">RelativeZOrder</span> value will be displayed first.
            This field cannot be modified in the Mission Sequence.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &ge; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShowPlot</span></td><td><p>Allows you to turn off a plot for a particular run,
            without deleting the plot, or removing it from the script. If you
            select true, then the plot will be shown. If you select false,
            then the plot will not be shown. This field cannot be modified in
            the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>True</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShowLabels</span></td><td><p>Allows you to turn on or off spacecraft and celestial
            body Object labels. If you select true, then spacecraft and
            celestial body object labels will show up in orbit view plot. If
            you select false, then spacecraft and celestial body labels will
            not be shown in the orbit plot. This field cannot be modified in
            the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>True</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Size</span></td><td><p>Allows you to control the display size of
            <span class="guilabel">OrbitViewPlot</span> window. First value in [0 0]
            matrix controls horizonal size and second value controls vertical
            size of <span class="guilabel">OrbitViewPlot</span> display window. This
            field cannot be modified in the Mission Sequence. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>[0 0]</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolverIterations</span></td><td><p>This field determines whether or not data associated
            with perturbed trajectories during a solver
            (<span class="guilabel">Targeter</span>, <span class="guilabel">Optimize</span>)
            sequence is plotted to <span class="guilabel">OrbitView</span>. When
            <span class="guilabel">SolverIterations</span> is set to
            <span class="guilabel">All</span>, all perturbations/iterations are plotted
            to an <span class="guilabel">OrbitView</span> plot. When
            <span class="guilabel">SolverIterations</span> is set to
            <span class="guilabel">Current</span>, only current solution is plotted to
            an <span class="guilabel">OrbitView</span>. When
            <span class="guilabel">SolverIterations</span> is set to
            <span class="guilabel">None</span>, this shows only final solution after
            the end of an iterative process and draws only final trajectory to
            an <span class="guilabel">OrbitView</span> plot. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">All</span>,
                    <span class="guilabel">Current</span>,
                    <span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Current</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">StarCount</span></td><td><p>Allows you to enter the number of stars that need to
            be displayed in an <span class="guilabel">OrbitView</span> plot. This field
            cannot be modified in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &ge; 1</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>7000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SunLine</span></td><td><p>Allows you to draw a line that starts at the center
            of central body and points towards the <span class="guilabel">Sun</span>.
            This field cannot be modified in the Mission Sequence. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UpdatePlotFrequency</span></td><td><p>This field lets you specify how often to update an
            <span class="guilabel">OrbitView</span> plot is updated with new data
            collected during the process of propagating spacecraft and running
            a mission. Data is collected for a plot according to the value
            defined by <span class="guilabel">DataCollectFrequency</span>. An
            <span class="guilabel">OrbitView</span> plot is updated with the new data,
            according to the value set in
            <span class="guilabel">UpdatePlotFrequency</span>. If
            <span class="guilabel">UpdatePlotFrequency</span> is set to 10 and
            <span class="guilabel">DataCollectFrequency</span> is set to 2, then the
            plot is updated with new data every 20 (10*2) integration steps.
            This field cannot be modified in the Mission Sequence. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &ge; 1</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>50</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UpperLeft</span></td><td><p>Allows you to pan the <span class="guilabel">OrbitView
            </span>plot window in any direction. First value in [0 0]
            matrix helps to pan the <span class="guilabel">OrbitView</span> window
            horizontally and second value helps to pan the window vertically.
            This field cannot be modified in the Mission Sequence. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>[0 0]</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseInitialView</span></td><td><p>This field lets you control the view of an
            <span class="guilabel">OrbitView</span> plot between multiple runs of a
            mission sequence. The first time a specific
            <span class="guilabel">OrbitView</span> plot is created, GMAT will
            automatically use the view as defined by the fields associated
            with <span class="guilabel">View Definition</span>, <span class="guilabel">View Up
            Direction</span>, and <span class="guilabel">View Option</span>.
            However, if you change the view using the mouse, GMAT will retain
            this view upon rerunning the mission as long as
            <span class="guilabel">UseInitialView</span> is set to false. If
            <span class="guilabel">UseInitialView</span> is set to true, the view for
            an <span class="guilabel">OrbitView</span> plot will be returned to the
            view defined by the initial settings. This field cannot be
            modified in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>On</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewDirection</span></td><td><p>Allows you to select the direction of view in an
            <span class="guilabel">OrbitView</span> plot. You can specify the view
            direction by choosing a resource to point at such as a
            <span class="guilabel">Spacecraft</span>, <span class="guilabel">Celestial
            body</span>, <span class="guilabel">Libration Point</span>, or
            <span class="guilabel">Barycenter</span>. Alternatively, you can also
            specify a vector of the form [x y z]. If the user specification of
            <span class="guilabel">ViewDirection</span>,
            <span class="guilabel">ViewPointReference</span>, and
            <span class="guilabel">ViewPointVector</span> results in a zero vector,
            GMAT uses [0 0 10000] for <span class="guilabel">ViewDirection</span>. This
            field cannot be modified in the Mission Sequence.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span>,
                    <span class="guilabel">CelestialBody</span>,
                    <span class="guilabel">LibrationPoint</span>,
                    <span class="guilabel">Barycenter</span>, or a 3-vector of
                    numerical values</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Earth</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km or N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewPointReference</span></td><td><p>This optional field allows you to change the
            reference point from which <span class="guilabel">ViewPointVector</span> is
            measured. <span class="guilabel">ViewPointReference</span> defaults to the
            origin of the coordinate system for the plot. A
            <span class="guilabel">ViewPointReference</span> can be any
            <span class="guilabel">Spacecraft</span>, <span class="guilabel">Celestial
            body</span>, <span class="guilabel">Libration Point</span>, or
            <span class="guilabel">Barycenter</span>. This field cannot be modified in
            the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span>,
                    <span class="guilabel">CelestialBody</span>,
                    <span class="guilabel">LibrationPoint</span>,
                    <span class="guilabel">Barycenter</span>, or a 3-vector of
                    numerical values</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Earth</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km or N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewPointVector</span></td><td><p> The product of <span class="guilabel">ViewScaleFactor</span>
            and <span class="guilabel">ViewPointVector</span> field determines the view
            point location with respect to
            <span class="guilabel">ViewPointReference</span>.
            <span class="guilabel">ViewPointVector</span> can be a vector, or any of
            the following resources: <span class="guilabel">Spacecraft</span>,
            <span class="guilabel">Celestial body</span>, <span class="guilabel">Libration
            Point</span>, or <span class="guilabel">Barycenter</span>. The location
            of the view point in three-dimensional space is defined as the
            vector addition of <span class="guilabel">ViewPointReference</span> and the
            vector defined by product of <span class="guilabel">ViewScaleFactor</span>
            and <span class="guilabel">ViewPointVector</span> in the coordinate system
            chosen by you. This field cannot be modified in the Mission
            Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span>,
                    <span class="guilabel">CelestialBody</span>,
                    <span class="guilabel">LibrationPoint</span>,
                    <span class="guilabel">Barycenter</span>, or a 3-vector of
                    numerical values</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>[30000 0 0]</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km or N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewScaleFactor</span></td><td><p>This field scales
            <span class="guilabel">ViewPointVector</span> before adding it to
            <span class="guilabel">ViewPointReference</span>. The
            <span class="guilabel">ViewScaleFactor</span> allows you to back away from
            an object to fit in the field of view. This field cannot be
            modified in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number &ge; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewUpAxis</span></td><td><p>This field lets you define which axis of the
            <span class="guilabel">ViewUpCoordinateSystem</span> field will appear as
            the up direction in an <span class="guilabel">OrbitView</span> plot. See
            the comments under <span class="guilabel">ViewUpCoordinateSystem</span> for
            more details of fields used to determine the up direction in an
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">X </span>, <span class="guilabel">-X</span> ,
                    <span class="guilabel">Y</span> , <span class="guilabel">-Y</span>
                    ,<span class="guilabel"> Z</span> , <span class="guilabel">-Z</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Z</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ViewUpCoordinateSystem</span></td><td><p> The <span class="guilabel">ViewUpCoordinateSystem</span> and
            <span class="guilabel">ViewUpAxis</span> fields are used to determine which
            direction appears as up in an <span class="guilabel">OrbitView</span> plot
            and together with the fields associated the the <span class="guilabel">View
            Direction</span>, uniquely define the view. The fields
            associated with the <span class="guilabel">View Definition</span> allows
            you to define the point of view in three-dimensional space, and
            the direction of the line of sight. However, this information
            alone is not enough to uniquely define the view. We also must
            provide how the view is oriented about the line of sight. This is
            accomplished by defining what direction should appear as the up
            direction in the plot and is configured using the
            <span class="guilabel">ViewUpCoordinateSystem</span> field and the
            <span class="guilabel">ViewUpAxis</span> field. The
            <span class="guilabel">ViewUpCoordinateSystem</span> allows you to select a
            coordinate system to define the up direction. Most of the time
            this system will be the same as the coordinate system chosen under
            the <span class="guilabel">CoordinateSystem</span> field. This field cannot
            be modified in the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CoordinateSystem</span>
                    resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">EarthMJ2000Eq</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">WireFrame</span></td><td><p> When the <span class="guilabel">WireFrame</span> field is set
            to <span class="guilabel">On</span>, celestial bodies are drawn using a
            wireframe model. When the <span class="guilabel">WireFrame</span> field is
            set to <span class="guilabel">Off</span>, then celestial bodies are drawn
            using a full map. This field cannot be modified in the Mission
            Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Off, On</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">XYPlane</span></td><td><p>Allows you to draw a grid representing the
            <span class="guilabel">XY-plane</span> of the coordinate system selected
            under the <span class="guilabel">CoordinateSystem</span> field of the
            <span class="guilabel">OrbitView</span> plot. This field cannot be modified
            in the Mission Sequence.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>On, Off</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>On</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2437D"></a><h2>GUI</h2><p>The figure below shows the default settings for the
    <span class="guilabel">OrbitView</span> resource:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView_GUI_4.png" align="middle" height="598"></td></tr></table></div></div><div class="refsection"><a name="N2438E"></a><h3>OrbitView Window Mouse Controls</h3><p>The list of controls in the table below helps you navigate through
      the <span class="guilabel">OrbitView</span> graphics window.
      "<span class="guilabel">Left</span>" and "<span class="guilabel">Right</span>" designate
      the mouse button which have to be pressed.</p><div class="informaltable"><table border="1"><colgroup><col width="20%"><col width="80%"></colgroup><thead><tr><th>Control</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Left Drag</span></td><td><p> Helps to change camera orientation. Camera
              orientation can be changed in
              <span class="guilabel">Up</span>/<span class="guilabel">Down</span>/<span class="guilabel">Left</span>/<span class="guilabel">Right</span>
              directions. </p></td></tr><tr><td><span class="guilabel">Right Drag</span></td><td><p> Helps to zoom in and out of the graphics window.
              Moving the cursor in <span class="guilabel">Up</span> direction leads to
              zoom out of the graphics window. Moving the cursor in
              <span class="guilabel">Down</span> direction helps to zoom into the
              graphics window. </p></td></tr><tr><td><span class="guilabel">Shift+Right Drag</span></td><td><p> Helps to adjust the <span class="guilabel">Field of
              View</span>. </p></td></tr></tbody></table></div></div></div><div class="refsection"><a name="Orbitview_Remarks"></a><h2>Remarks</h2><div class="refsection"><a name="N243DC"></a><h3>Behavior when using OrbitView Resource &amp; Toggle
      Command</h3><p>The <span class="guilabel">OrbitView</span> resource plots spacecraft&rsquo;s
      trajectory at each propagation step of the entire mission duration. If
      you want to report data to an <span class="guilabel">OrbitView</span> plot at
      specific points in your mission, then a <span class="guilabel">Toggle</span>
      <span class="guilabel">On</span>/<span class="guilabel">Off</span> command can be inserted
      into the mission sequence to control when <span class="guilabel">OrbitView</span>
      is to plot a given trajectory. When <span class="guilabel">Toggle Off</span>
      command is issued for an <span class="guilabel">OrbitView</span>, no trajectory
      is drawn until a <span class="guilabel">Toggle On</span> command is issued.
      Similarly, when a <span class="guilabel">Toggle On</span> command is used,
      trajectory is plotted at each integration step until a <span class="guilabel">Toggle
      Off</span> command is used.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth}

BeginMissionSequence

Toggle anOrbitView Off
Propagate aProp(aSat) {aSat.ElapsedDays = 2}
Toggle anOrbitView On
Propagate aProp(aSat) {aSat.ElapsedDays = 4}</code></pre></div><div class="refsection"><a name="N24405"></a><h3>Behavior when using OrbitView, Spacecraft and SolarSystem
      Resources</h3><p><span class="guilabel">Spacecraft</span> resource contains information
      about spacecraft&rsquo;s orbit. <span class="guilabel">Spacecraft</span> resource
      interacts with <span class="guilabel">OrbitView</span> throughout the entire
      mission duration. The trajectory data retrieved from the spacecraft is
      what gets plotted at each propagation step of the entire mission
      duration. Similarly, the sun and all other planets available under the
      <span class="guilabel">SolarSystem</span> resource may be plotted or referenced
      in the <span class="guilabel">OrbitView</span> resource as well.</p></div><div class="refsection"><a name="N24418"></a><h3>Behavior when reporting data in Iterative Processes</h3><p>GMAT allows you to specify how trajectories are plotted during
      iterative processes such as differential correction or optimization. The
      <span class="guilabel">SolverIterations</span> field of
      <span class="guilabel">OrbitView</span> resource supports 3 options which are
      described in the table below:</p><div class="informaltable"><table border="1"><colgroup><col width="20%"><col width="80%"></colgroup><thead><tr><th>SolverIterations options</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Current</span></td><td><p> Shows only current iteration/perturbation in an
              iterative process and plots current trajectory. </p></td></tr><tr><td><span class="guilabel">All</span></td><td><p> Shows all iterations/perturbations in an iterative
              process and plots all perturbed trajectories. </p></td></tr><tr><td><span class="guilabel">None</span></td><td><p> Shows only the final solution after the end of an
              iterative process and plots only that final trajectory.
              </p></td></tr></tbody></table></div></div><div class="refsection"><a name="N2444A"></a><h3>Behavior when plotting multiple spacecrafts</h3><p>GMAT allows you to plot trajectories of any number of spacecrafts
      when using the <span class="guilabel">OrbitView</span> resource. The initial
      epoch of all the spacecrafts must be same in order to plot the
      trajectories. If initial epoch of one of the spacecrafts does not match
      with initial epoch of other spacecrafts, then GMAT throws in an error
      alerting you that there is a coupled propagation error mismatch between
      the spacecrafts. GMAT also allows you to propagate trajectories of
      spacecrafts using any combination of the propagators that you may
      create.</p><p>Below is an example script snippet that shows how to plot
      trajectories of multiple spacecrafts that use different
      propagators:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat aSat2 aSat3
aSat2.INC = 45.0
aSat3.INC = 90.0
aSat3.SMA = 9000

Create Propagator aProp
Create Propagator bProp

Create OrbitView anOrbitView anOrbitView2

anOrbitView.Add = {aSat, aSat2, Earth}
anOrbitView2.Add = {aSat3, Earth}

BeginMissionSequence

Propagate aProp(aSat, aSat2) bProp(aSat3) {aSat.ElapsedSecs = 12000.0}</code></pre></div><div class="refsection"><a name="N24457"></a><h3>OrbitView View Definition Controls</h3><p>GMAT is capable of drawing orbit plots that allow you to visualize
      the motion of spacecraft and celestial bodies throughout the mission
      sequence. Here we discuss the options you can use in setting up and
      viewing Orbit plots. You can choose many properties including the
      coordinate system of the orbit view plot and the view location and
      direction from where visualizations can be seen. The script snippet
      below shows how to create <span class="guilabel">OrbitView</span> resource that
      includes key view definition controls fields as well. Detailed
      definitions of all fields for <span class="guilabel">OrbitView</span> resource
      can be found in <a class="xref" href="OrbitView.html#Fields_Section" title="Fields">Fields</a> section.</p><pre class="programlisting"><code class="code">Create OrbitView PlotName
PlotName.CoordinateSystenm      = CoordinateSystemName
PlotName.Add                    = [SpacecraftName, BodyName, ... 
                                  LibrationPoint, Barycenter]
PlotName.ViewPointReference     = [ObjectName, VectorName]
PlotName.ViewPointVector        = [ObjectName, VectorName]
PlotName.ViewDirection          = [ObjectName, VectorName]
PlotName.ViewScaleFactor        = [Real Number]
PlotName.ViewUpCoordinateSystem = CoordinateSystemName
PlotName.ViewUpAxis             = [X,-X,Y,-Y,Z,-Z];</code></pre><p>You can specify the view location and direction of
      <span class="guilabel">OrbitView</span> plot object by using the
      <span class="guilabel">ViewPointReference</span>,
      <span class="guilabel">ViewPointVector</span>,
      <span class="guilabel">ViewDirection</span>,
      <span class="guilabel">ViewUpCoordinateSystem</span> and
      <span class="guilabel">ViewUpAxis</span> fields. Figure below shows a graphical
      definition of <span class="guilabel">ViewPointReference</span>,
      <span class="guilabel">ViewPointVector</span>, and
      <span class="guilabel">ViewDirection</span> fields and how they determine the
      actual view location and view direction. You can supply
      <span class="guilabel">ViewPointReference</span>,
      <span class="guilabel">ViewPointVector</span> and
      <span class="guilabel">ViewDirection</span> fields by either giving a vector in
      the format [x y z] or by specifying an object name. If a vector is given
      for one of the quantities, then we simply use it in its appropriate
      place in the computations below. If an object is given, we must
      determine the vector associated with it. The rest of this section is
      devoted in determining <span class="guilabel">ViewPointReference</span>,
      <span class="guilabel">ViewPointVector</span> and
      <span class="guilabel">ViewDirection</span> fields if you specify an
      object.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView ViewDirection Diagram.png" align="middle" height="752"></td></tr></table></div></div><p><span class="guilabel">ViewPointReference</span> field defines the point
      from which <span class="guilabel">ViewPointVector</span> is measured. If an
      object is given for <span class="guilabel">ViewPointReference</span> field, i.e.
      when you have the following in the sample script:</p><pre class="programlisting"><code class="code">MyOrbitViewPlot.CoordinateSystenm    = MyCoordSys 
MyOrbitViewPlot.ViewPointReference   = ViewRefObject</code></pre><p>then we need to determine
      <span class="guilabel">r</span><sub>r</sub> as illustrated in above
      figure. If ViewRefObject is the same as the origin of MyCoordSys, then
      <span class="guilabel">r</span><sub>r</sub> = [0 0 0]. Otherwise
      <span class="guilabel">r</span><sub>r</sub> is the cartesian position
      of <span class="guilabel">ViewPointReference</span> in MyCoordSys.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView_Equation1.png" align="middle" height="135"></td></tr></table></div></div><p><span class="guilabel">ViewPointVector</span> field points from
      <span class="guilabel">ViewPointReference</span>
      (<span class="guilabel">r</span><sub>r</sub>) in the direction of the
      view point location. If an object is given for
      <span class="guilabel">ViewPointVector</span> field, i.e. you have the following
      in the sample script:</p><pre class="programlisting"><code class="code">MyOrbitViewPlot.CoordinateSystenm    = MyCoordSys 
MyOrbitViewPlot.ViewPointVector      = ViewPointObject</code></pre><p>then we need to determine
      <span class="guilabel">r</span><sub>v</sub> as illustrated in above
      figure by using the coordinate system conversion routine to calculate
      the following:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView_Equation2.png" align="middle" height="140"></td></tr></table></div></div><p>We now know everything to calculate the location of the view point
      in the desired coordinate system. From inspection of the above figure,
      we see that the relation is:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView_Equation4.png" align="middle" height="86"></td></tr></table></div></div><p>Now that we know the view point location, we need to determine the
      ViewDirection: <span class="guilabel">r</span><sub>d</sub> as
      illustrated in above figure. If a vector was specified for
      <span class="guilabel">ViewDirection</span> field, then no computations are
      required. However, if an object was given as shown in the following
      sample script:</p><pre class="programlisting"><code class="code">MyOrbitViewPlot.CoordinateSystenm    = MyCoordSys 
MyOrbitViewPlot.ViewDiection         = ViewDirectionObject</code></pre><p>then we calculate <span class="guilabel">r</span><sub>d</sub>
      from the following:</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_OrbitView_Equation3.png" align="middle" height="142"></td></tr></table></div></div><p>Note that ViewDirection vector
      <span class="guilabel">r</span><sub>d</sub> must not be zero vector
      [0 0 0].</p><p><span class="guilabel">ViewUpCoordinateSystem</span> and
      <span class="guilabel">ViewUpAxis</span> fields are used to determine which
      direction appears as up in an <span class="guilabel">OrbitView</span> plot. Most
      of the time, coordinate system chosen under
      <span class="guilabel">ViewUpCoordinateSystem</span> field will be the same as
      the coordinate system selected under the
      <span class="guilabel">CoordinateSystem</span> field.
      <span class="guilabel">ViewUpAxis</span> field allows you to define which axis of
      the <span class="guilabel">ViewUpCoordinateSystem</span> field will appear as the
      up direction in an orbit plot.</p><p>Below are some examples that show how to generate
      <span class="guilabel">OrbitView</span> plots using different View Definition
      Controls configurations:</p><p>Earth Inertial view with spacecraft: This example shows orbit view
      plot with Earth and a spacecraft. Since
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      Earth), hence ViewPointRef vector in above figure is [0 0 0] in
      EarthMJ2000Eq coordinate system. The
      <span class="guilabel">ViewPointVector</span> field is set to a vector ( i.e. set
      to [0 0 40000] ). This means that the view is from 40000 km above the
      Earth's equatorial plane on the z-axis of the EarthMJ2000Eq coordinate
      system. The view direction (specified in
      <span class="guilabel">ViewDirection</span> field) is towards the earth.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth}

anOrbitView.CoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewPointReference = Earth
anOrbitView.ViewPointVector = [ 0 0 40000 ]
anOrbitView.ViewDirection = Earth
anOrbitView.ViewScaleFactor = 1
anOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewUpAxis = Z

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 1}</code></pre><p>Earth Inertial view with spacecraft and Luna: This example shows
      orbit view plot with Earth, spacecraft and Moon. Note
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      Earth), hence ViewPointRef vector in above figure = [0 0 0] in
      EarthMJ2000Eq coordinate system. <span class="guilabel">ViewPointVector</span>
      field is still set to a vector ( i.e. set to [0 0 500000] ). This means
      that the view is from 500000 km above the Earth's equatorial plane on
      the z-axis of the EarthMJ2000Eq coordinate system.
      <span class="guilabel">ViewDirection</span> field defines the view direction
      which is set towards the earth.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth, Luna}

anOrbitView.CoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewPointReference = Earth
anOrbitView.ViewPointVector = [ 0 0 500000 ]
anOrbitView.ViewDirection = Earth
anOrbitView.ViewScaleFactor = 1
anOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewUpAxis = Z

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 5}</code></pre><p>View of spacecraft from Luna in Earth inertial frame: This example
      of an orbit view plot shows spacecraft as viewed from Luna orbiting
      around Earth in an inertial reference frame.
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      Earth), hence ViewPointRef vector is [0 0 0] in EarthMJ2000Eq coordinate
      system. This time <span class="guilabel">ViewPointVector</span> field is set to
      an object (i.e. Luna ). This means that the spacecraft will be seen from
      the vantage point of Luna. Note that <span class="guilabel">ViewDirection</span>
      field is set to spacecraft (aSat). This means that view direction as
      seen from Luna is towards the spacecraft. After you run this example,
      re-run this example but this time with
      <span class="guilabel">ViewScaleFactor</span> field set to 2 and see what
      happens. You'll notice that <span class="guilabel">ViewScaleFactor</span> simply
      scales <span class="guilabel">ViewPointVector</span> field.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth, Luna}

anOrbitView.CoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewPointReference = Earth
anOrbitView.ViewPointVector = Luna
anOrbitView.ViewDirection = aSat
anOrbitView.ViewScaleFactor = 1
anOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewUpAxis = Z

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 5}</code></pre><p>View towards Luna from Earth as spacecraft orbits around Luna in
      inertial frame: This example of an orbit view plot shows view of Luna
      from vantage point of Earth as a spacecraft orbits around Luna.
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      Luna), hence ViewPointRef vector in above figure is [0 0 0] in
      LunaMJ2000Eq coordinate system. <span class="guilabel">ViewPointVector</span>
      field is set to an object (i.e. Earth ). This means that the camera or
      vantage point is located at Earth. <span class="guilabel">ViewDirection</span>
      field is also set to an object (i.e. Luna). This means that view
      direction as seen from Earth is towards Luna.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

Create CoordinateSystem LunaMJ2000Eq
LunaMJ2000Eq.Origin = Luna
LunaMJ2000Eq.Axes = MJ2000Eq

aSat.CoordinateSystem = LunaMJ2000Eq
aSat.SMA = 7300
aSat.ECC = 0.4
aSat.INC = 90
aSat.RAAN = 270
aSat.AOP = 315
aSat.TA = 180

Create ForceModel aFM
aFM.CentralBody = Luna
aFM.PointMasses = {Luna}

Create Propagator aProp
aProp.FM = aFM

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Luna, Earth}
anOrbitView.CoordinateSystem = LunaMJ2000Eq
anOrbitView.ViewPointReference = Luna
anOrbitView.ViewPointVector = Earth
anOrbitView.ViewDirection = Luna
anOrbitView.ViewScaleFactor = 1;
anOrbitView.ViewUpCoordinateSystem = LunaMJ2000Eq;
anOrbitView.ViewUpAxis = Z;

BeginMissionSequence
Propagate aProp(aSat) {aSat.ElapsedDays = 5}</code></pre><p>View towards spacecraft1 from spacecraft2 in inertial frame: This
      example of an orbit view plot shows spacecraft1 (aSat1) being viewed
      from spacecraft2 (aSat2) as they move in inertial reference frame.
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      Earth), hence ViewPointRef vector in above figure is [0 0 0] in
      EarthMJ2000Eq coordinate system. <span class="guilabel">ViewPointVector</span>
      field is set to an object (i.e. aSat2 ) and
      <span class="guilabel">ViewDirection</span> field is also set to an object (i.e.
      aSat1). This means that aSat1 will be viewed from the vantage point of
      aSat2.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat aSat2

aSat2.X = 19500
aSat2.Z = 10000

Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, aSat2, Earth,}

anOrbitView.CoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewPointReference = Earth
anOrbitView.ViewPointVector = aSat2
anOrbitView.ViewDirection = aSat
anOrbitView.ViewScaleFactor = 1.0
anOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq
anOrbitView.ViewUpAxis = Z

BeginMissionSequence

Propagate aProp(aSat, aSat2){aSat.ElapsedSecs = 12000.0}</code></pre><p>Orbit view plot of Sun-Earth-Moon L1 Rotating System: This example
      of an orbit view plot shows the Earth and spacecraft in the
      Sun-Earth-Moon rotating coordinate system.
      <span class="guilabel">ViewPointReference</span> field is set to an object (i.e.
      ESL1), hence ViewPointRef vector in above figure is [0 0 0] in
      SunEarthMoonL1 rotating coordinate system.
      <span class="guilabel">ViewPointVector</span> field is set to a vector (i.e. [0 0
      30000] ). This means that the view is taken from 30000 km above the
      SunEarthMoonL1 coordinate system's XY plane on the z-axis of the
      SunEarthMoonL1 coordinate system. <span class="guilabel">ViewDirection</span>
      field is also set to an object (i.e. ESL1). This means that view
      direction as seen from 30000 km above the SunEarthMoonL1 coordinate
      system's XY plane is towards ESL1. Note that in this example,
      <span class="guilabel">ViewScaleFactor</span> is set to 25. This simply scales or
      amplifies <span class="guilabel">ViewPointVector</span> field 25 times its
      original value.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

GMAT aSat.DateFormat = UTCGregorian;
GMAT aSat.Epoch = '01 Apr 2013 00:00:00.000' 
GMAT aSat.CoordinateSystem = EarthMJ2000Eq
GMAT aSat.DisplayStateType = Cartesian
GMAT aSat.X = 1429457.8833484
GMAT aSat.Y = 147717.32846679
GMAT aSat.Z = -86529.655549364
GMAT aSat.VX = -0.037489820883615                     
GMAT aSat.VY = 0.32032521614858
GMAT aSat.VZ = 0.15762889268226

Create Barycenter EarthMoonBarycenter
GMAT EarthMoonBarycenter.BodyNames = {Earth, Luna}

Create LibrationPoint ESL1
GMAT ESL1.Primary = Sun
GMAT ESL1.Secondary = EarthMoonBarycenter
GMAT ESL1.Point = L1

Create ForceModel aFM
aFM.CentralBody = Earth
aFM.PointMasses = {Luna, Sun}

Create Propagator aProp
aProp.FM = aFM

Create CoordinateSystem SunEarthMoonL1
GMAT SunEarthMoonL1.Origin = ESL1
GMAT SunEarthMoonL1.Axes = ObjectReferenced
GMAT SunEarthMoonL1.XAxis = R
GMAT SunEarthMoonL1.ZAxis = N
GMAT SunEarthMoonL1.Primary = Sun
GMAT SunEarthMoonL1.Secondary = EarthMoonBarycenter

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth, Sun}
anOrbitView.CoordinateSystem = SunEarthMoonL1
anOrbitView.ViewPointReference = ESL1
anOrbitView.ViewPointVector = [ 0 0 30000 ]
anOrbitView.ViewDirection = ESL1
anOrbitView.ViewScaleFactor = 25
anOrbitView.ViewUpCoordinateSystem = SunEarthMoonL1
anOrbitView.ViewUpAxis = Z

BeginMissionSequence
Propagate aProp(aSat) {aSat.ElapsedDays = 15}</code></pre></div><div class="refsection"><a name="N2459A"></a><h3>Behavior when using View Definition panel of OrbitView
      Resource</h3><p>Currently in <span class="guilabel">OrbitView</span> resource&rsquo;s View
      Definition panel, fields like <span class="guilabel">ViewPointReference</span>,
      <span class="guilabel">ViewPointVector</span> and
      <span class="guilabel">ViewDirection</span> are initialized but not dynamically
      updated during a mission run. <span class="guilabel">OrbitView</span> resource&rsquo;s
      View Definition panel sets up geometry at initial epoch and then mouse
      controls geometry of the simulation from that point on.</p></div><div class="refsection"><a name="N245AE"></a><h3>Spacecraft Model Considerations in GMAT's OrbitView</h3><p>GMAT displays spacecraft models by reading model data from 3D
      Studio files describing the spacecraft shape and colors. These files
      have the file extension .3ds, and are generally called 3ds files. 3ds
      files contain data that defines the 3-dimensional coordinates of
      vertices outlining the spacecraft, a mapping of those vertices into
      triangles used to create the displayed surface of the spacecraft, and
      information about the colors and texture maps used to fill in the
      displayed triangles.</p><p>GMAT's implementation of the spacecraft model can display models
      consisting of up to 200,000 vertices that map up to 100,000 triangles.
      The GMAT model can use up 500 separate color or texture maps to fill in
      these triangles.</p></div><div class="refsection"><a name="N245B5"></a><h3>Behavior When Specifying Empty Brackets in OrbitView's Add
      Field</h3><p>When using <span class="guilabel">OrbitView.Add</span> field, if brackets
      are not populated with user-defined spacecrafts, then GMAT turns off
      <span class="guilabel">OrbitView</span> resource and no plot is generated. If you
      run the script with <span class="guilabel">Add</span> field having empty
      brackets, then GMAT throws in a warning message in the Message Window
      indicating that <span class="guilabel">OrbitView</span> resource will be turned
      off since no SpacePoints were added to the plot. Below is a sample
      script snippet that generates such a warning message:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat aSat2
Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {}

BeginMissionSequence
Propagate aProp(aSat, aSat2){aSat.ElapsedSecs = 12000.0}</code></pre></div></div><div class="refsection"><a name="N245C9"></a><h2>Examples</h2><div class="informalexample"><p>Propagate spacecraft for 1 day and plot the orbit at every
      integrator step:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aProp

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth}

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 1}</code></pre></div><div class="informalexample"><p>Plotting orbit during an iterative process. Notice
      <span class="guilabel">SolverIterations</span> field is selected as
      <span class="guilabel">All</span>. This means all iterations/perturbations will
      be plotted.</p><pre class="programlisting">Create Spacecraft aSat
Create Propagator aProp

Create ImpulsiveBurn TOI
Create DifferentialCorrector aDC

Create OrbitView anOrbitView
anOrbitView.Add = {aSat, Earth}
anOrbitView.SolverIterations = All

BeginMissionSequence

Propagate aProp(aSat) {aSat.Earth.Periapsis}

Target aDC
  Vary aDC(TOI.Element1 = 0.24, {Perturbation = 0.001, Lower = 0.0, ...
  Upper = 3.14159, MaxStep = 0.5})
  Maneuver TOI(aSat)
  Propagate aProp(aSat) {aSat.Earth.Apoapsis}
  Achieve aDC(aSat.Earth.RMAG = 42165)
EndTarget</pre></div><div class="informalexample"><p>Plotting spacecraft&rsquo;s trajectory around non-default central body.
      This example shows how to plot a spacecraft&rsquo;s trajectory around
      Luna:</p><pre class="programlisting">Create Spacecraft aSat
  
Create CoordinateSystem LunaMJ2000Eq
LunaMJ2000Eq.Origin = Luna
LunaMJ2000Eq.Axes = MJ2000Eq

aSat.CoordinateSystem = LunaMJ2000Eq
aSat.SMA = 7300
aSat.ECC = 0.4
aSat.INC = 90
aSat.RAAN = 270
aSat.AOP = 315
aSat.TA = 180

Create ForceModel aFM
aFM.CentralBody = Luna
aFM.PointMasses = {Luna}

Create Propagator aProp
aProp.FM = aFM

Create OrbitView anOrbitView

anOrbitView.Add = {aSat, Luna}
anOrbitView.CoordinateSystem = LunaMJ2000Eq
anOrbitView.ViewPointReference = Luna
anOrbitView.ViewDirection = Luna

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 1}</pre></div><div class="informalexample"><p>Plotting spacecraft&rsquo;s trajectory around non-default central body.
      This example shows how to plot a spacecraft&rsquo;s trajectory around
      Mars:</p><pre class="programlisting">Create Spacecraft aSat

Create CoordinateSystem MarsMJ2000Eq
MarsMJ2000Eq.Origin = Mars
MarsMJ2000Eq.Axes = MJ2000Eq

aSat.CoordinateSystem = MarsMJ2000Eq
aSat.SMA = 7300
aSat.ECC = 0.4
aSat.INC = 90
aSat.RAAN = 270
aSat.AOP = 315
aSat.TA = 180

Create ForceModel aFM
aFM.CentralBody = Mars
aFM.PointMasses = {Mars}

Create Propagator aProp
aProp.FM = aFM

Create OrbitView anOrbitView

anOrbitView.Add = {aSat, Mars}
anOrbitView.CoordinateSystem = MarsMJ2000Eq
anOrbitView.ViewPointReference = Mars
anOrbitView.ViewDirection = Mars

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 1}</pre></div><div class="informalexample"><p>Plotting spacecraft&rsquo;s trajectory around non-default central body.
      This example shows how to plot a spacecraft&rsquo;s trajectory around Sun.
      This is an interplanetary trajectory. Spacecraft is shown on an
      out-going hyperbolic trajectory in an EarthView and then an
      interplanetary trajectory is drawn around Sun in a SunView. Mars Orbit
      around Sun is also shown:</p><pre class="programlisting">Create Spacecraft aSat

aSat.CoordinateSystem = EarthMJ2000Eq
aSat.DateFormat = UTCGregorian
aSat.Epoch = '18 Nov 2013 20:26:24.315'

aSat.X = 3728.345810006184
aSat.Y = 4697.943961035268
aSat.Z = -2784.040094879185
aSat.VX = -9.502477543864449
aSat.VY = 5.935188001372066
aSat.VZ = -2.696272103530009

Create ForceModel aFM
aFM.CentralBody = Earth
aFM.PointMasses = {Earth}

Create ForceModel bFM
aFM.CentralBody = Sun
aFM.PointMasses = {Sun}

Create Propagator aProp
aProp.FM = aFM

Create Propagator bProp
aProp.FM = bFM

Create CoordinateSystem SunEcliptic
SunEcliptic.Origin = Sun
SunEcliptic.Axes = MJ2000Ec

Create OrbitView EarthView SunView

EarthView.Add = {aSat, Earth}
EarthView.CoordinateSystem = EarthMJ2000Eq
EarthView.ViewPointReference = Earth
EarthView.ViewDirection = Earth

SunView.Add = {aSat, Mars, Sun}
SunView.CoordinateSystem = SunEcliptic
SunView.ViewPointReference = Sun
SunView.ViewDirection = Sun
SunView.ViewPointVector = [ 0 0 500000000 ]

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 3}
Propagate bProp(aSat) {aSat.ElapsedDays = 225}</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="OpenFramesInterface.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch19.html#N22D14">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReportFile.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">OpenFramesInterface&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;ReportFile</td></tr></table></div></body></html>