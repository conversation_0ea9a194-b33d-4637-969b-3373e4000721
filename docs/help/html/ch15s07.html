<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Warm-start the filter</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="FilterSmoother_GpsPosVec.html" title="Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data"><link rel="prev" href="ch15s06.html" title="Review and quality check the smoother run"><link rel="next" href="ch15s08.html" title="A few words about filter tuning"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Warm-start the filter</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch15s06.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch15s08.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N15504"></a>Warm-start the filter</h2></div></div></div><p>We&rsquo;ve seen everything that it takes to set up, run, and quality
    check a GMAT filter/smoother OD run. There&rsquo;s one more aspect of filter
    operations that we should take a little time to understand and explore.
    Recall, or go back and look at, the filter position covariance plot we
    generated. That plot has a large initial covariance and then takes some
    amount of time, maybe 6 to 12 hours in our case, to reach a steady or
    &ldquo;converged&rdquo; state. This is because we have performed what is called a
    filter &ldquo;initialization&rdquo; or &ldquo;cold start&rdquo;. We started the filter from our
    own coarse initial guesses of the spacecraft state and uncertainty. As the
    filter runs forward, it improves the state estimate and uncertainty by
    processing the measurement data. At the end of the filter run, we now have
    a state estimate and uncertainty that are much better than those we
    started with.</p><p>In mission operations, we will need to run the filter on some
    regular basis, maybe daily, and the next time we start the filter, it
    would be best to start it from the filter&rsquo;s own ending state estimate and
    uncertainty. We might think we could do this manually by updating our
    script with the filter estimates of the spacecraft state and full (6x6,
    not just the diagonal elements) spacecraft orbit error covariance matrix.
    We&rsquo;d also need to update the spacecraft Cd and Cd uncertainty. This
    process would be laborious and would not even fully capture the end state
    of the filter because there&rsquo;s no way to manually provide GMAT the
    components of covariance that link Cd to the spacecraft position and
    velocity.</p><p>The good news is that there&rsquo;s a much easier way to start the filter
    from a previously estimated state. When we covered the output files
    generated by the filter, there was one file we didn&rsquo;t describe &ndash; the
    filter.csv file assigned on the filter OutputWarmStartFile
    parameter.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Go to your <span class="emphasis"><em>&lt;GMAT Installation&gt;</em></span>/output
        directory. Find and open filter.csv for reading in a text
        editor.</p></li></ol></div><p>The filter.csv file is a comma-separated file containing a record of
    the full filter state and covariance at every measurement and time update
    in the processing span. Scroll to the right in the file and look at the
    header row (if you prefer, you can open the file in Excel for greater
    convenience). You should see that each record in the file contains the
    full state of the filter (that is, the spacecraft position and velocity
    component estimates along with Cd estimates) and the full (7x7) covariance
    matrix associated with the state and Cd estimates of the record. Any
    record in this file contains all the information the filter needs to start
    up and continue processing forward from the record epoch. Starting the
    filter in this fashion from a previous estimated state and full covariance
    matrix is called a &ldquo;warm start&rdquo;, and the filter.csv file is called a warm
    start file.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The covariance stored in the warm start file is actually the
      square root of the full covariance matrix. The headers of the covariance
      columns indicate this (SqrtCovariance). The full covariance matrix P may
      be recovered from the square root covariance by the formula P =
      SqrtCovariance * SqrtCovariance<sup>T</sup>.</p></div><p>Let&rsquo;s go through a quick demonstration of how to perform a warm
    start.</p><div class="orderedlist"><ol class="orderedlist" start="2" type="1"><li class="listitem"><p>Update the filter ExtendedKalmanFilter configuration in the
        mission sequence as shown below. It may be convenient to just
        comment-out or remove the current filter instance and paste in this
        new code.</p></li></ol></div><pre class="programlisting">%
%   Estimator
%

Create ExtendedKalmanFilter EKF;

EKF.ShowProgress         = True;
EKF.Measurements         = {EstData};
EKF.Propagator           = Prop;
EKF.ShowAllResiduals     = On;
EKF.InputWarmStartFile   = 'filter.csv';
EKF.WarmStartEpochFormat = 'UTCGregorian';
EKF.WarmStartEpoch       = '10 Jun 2014 18:00:00.000';
EKF.ReportFile           = 'filter.txt';
EKF.MatlabFile           = 'filter.mat';
</pre><div class="orderedlist"><ol class="orderedlist" start="3" type="1"><li class="listitem"><p>At the bottom of the script window, click on the &ldquo;Save,Sync&rdquo;
        button.</p></li></ol></div><p>Here, we&rsquo;ve assigned the filter.csv warm start file we have as the
    InputWarmStartFile, meaning that GMAT will now read from that file instead
    of writing to it. We have also specified a WarmStartEpoch (and epoch
    format). This tells GMAT which record to use in the input warm start file
    as GMAT&rsquo;s starting point. In this case, we&rsquo;re choosing not to write out a
    new filter OutputWarmStartFile, but we could do that at the same time, as
    long as we choose a different file name from the input warm start file.
    Let&rsquo;s run the filter and see how a warm start works.</p><div class="orderedlist"><ol class="orderedlist" start="4" type="1"><li class="listitem"><p>Run the script by clicking on the &ldquo;Save,Sync,Run&rdquo; button,
        clicking on the blue &ldquo;Run&rdquo; error in the tool bar, or by hitting the F5
        key.</p></li><li class="listitem"><p>When the run completes, go to your <span class="emphasis"><em>&lt;GMAT
        Installation&gt;</em></span>/output directory. Find and open filter.txt
        for reading in a text editor.</p></li></ol></div><p>Look at the spacecraft initial conditions at the top of the filter
    output report file. You should now see that the initial spacecraft epoch
    state are at the warm start epoch we specified. If you wish, you can
    manually compare the initial state reported in the filter output report to
    the 10 Jun 2020 18:00 record in the filter.csv warm start file &ndash; they
    should match. Most importantly, the 7x7 initial covariance reported in the
    output file will also match the 7x7 covariance from the warm start file
    record (if you convert the square root covariance in the warm start file
    to a full covariance).</p><p>Next, scroll down in the output to the filter measurement residuals
    report. Notice that the residual report begins with the 10 Jun 2014 18:10
    measurement. When warm-starting the filter, it will automatically ignore
    any measurements prior to the selected warm start epoch and begin forward
    processing from the first measurement after the warm start epoch. This is
    because the warm start record at that epoch already includes the effect of
    processing all earlier measurement data. (As an aside, since the forward
    filter ignored measurements prior to 18:00, the backward filter and the
    smoother will also ignore the earlier measurement data, since their
    knowledge of what measurements to process comes solely from the forward
    filter run.)</p><p>If you scroll down further in the output file to the FILTER STATE
    INFORMATION report, you will see that the end time and final estimated
    state of the run matches what was obtained in the original full-span run.
    The purpose of a warm start is to allow the filter to continue running
    from the warm start epoch as though it had never stopped. Choosing any
    warm start epoch at all will lead to identically the same estimated state
    and covariance at the end of the run.</p><p>In this instance, we got the same end state because we were just
    reprocessing the same set of data as our cold-start run, with the only
    difference of choosing a different starting point. In normal mission
    operations, the warm start record is used to advance the filter estimated
    state continuously forward using the new data that is available each time
    the filter is run. An outline of this process for a routine OD scenario
    looks like the following:</p><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_FilterSmoother_GpsPosVec_Fig8.png" align="middle" width="928"></div></div><p>As the flowchart illustrates, ideally the filter is cold-started, or
    initialized, only once and then is run continuously in warm start mode,
    with each new day&rsquo;s run starting from an epoch chosen from the prior run&rsquo;s
    output warm start file (normally the last or most recent warm start
    record). In practice, it is occasionally necessary to re-initialize the
    filter. This may be required if a severe spacecraft thrusting event occurs
    which cannot be modeled in the filter, or more commonly, due to changes in
    the filter scenario due to the addition of new tracking stations or
    significant changes in current tracking stations.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch15s06.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="FilterSmoother_GpsPosVec.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch15s08.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Review and quality check the smoother run&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;A few words about filter tuning</td></tr></table></div></body></html>