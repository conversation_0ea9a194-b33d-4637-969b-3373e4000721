<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Contributors</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="WelcomeToGmat.html" title="Chapter&nbsp;1.&nbsp;Welcome to GMAT"><link rel="prev" href="ch01s05.html" title="Component Status"><link rel="next" href="GettingStarted.html" title="Chapter&nbsp;2.&nbsp;Getting Started"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Contributors</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch01s05.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;1.&nbsp;Welcome to GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="GettingStarted.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N10229"></a>Contributors</h2></div></div></div><p>The Navigation and Mission Design Branch at NASA&rsquo;s Goddard Space
    Flight Center performs project management activities and is involved in
    most phases of the development process including requirements, algorithms,
    design, and testing. The Ground Software Systems Branch performs design,
    implementation, and integration testing. External participants contribute
    to design, implementation, testing and documentation. We use a
    collaborative development model that enables innovation and actively
    involves the public and private sector having seen contributions from 12
    commercial firms. External participants for this release include:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Thinking Systems, Inc. (system architecture and all aspects of
        development)</p></li><li class="listitem"><p>Omitron Pearl River, Inc (testing, requirements, specifications,
        development)</p></li><li class="listitem"><p>Emergent Space Technologies, Inc. (graphics, OD)</p></li></ul></div><p>Past commercial and external contributors to GMAT include:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Air Force Research Lab (all aspects of development)</p></li><li class="listitem"><p>Omitron Inc. (algorithms and testing)</p></li><li class="listitem"><p>Boeing (algorithms and testing)</p></li><li class="listitem"><p>The Schafer Corporation (all aspects of development)</p></li><li class="listitem"><p>Honeywell Technology Solutions (testing)</p></li><li class="listitem"><p>Computer Sciences Corporation (requirements)</p></li><li class="listitem"><p>Korea Aerospace Research Institute</p></li><li class="listitem"><p>Chonbuk National University, South Korea</p></li><li class="listitem"><p>Korea Advanced Institute of Science and Technology</p></li><li class="listitem"><p>Yonsei University, South Korea</p></li></ul></div><p>The NASA Jet Propulsion Laboratory (JPL) has provided funding for
    integration of the SPICE toolkit into GMAT. Additionally, the European
    Space Agency&rsquo;s (ESA) Advanced Concepts team has developed optimizer
    plug-ins for the Non-Linear Programming (NLP) solvers SNOPT (Sparse
    Nonlinear OPTimizer) and IPOPT (Interior Point OPTimizer).</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch01s05.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="WelcomeToGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="GettingStarted.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Component Status&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;2.&nbsp;Getting Started</td></tr></table></div></body></html>