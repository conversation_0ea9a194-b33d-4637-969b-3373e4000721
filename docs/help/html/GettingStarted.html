<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;2.&nbsp;Getting Started</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="UsingGmat.html" title="Using GMAT"><link rel="prev" href="ch01s06.html" title="Contributors"><link rel="next" href="RunningGmat.html" title="Running GMAT"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;2.&nbsp;Getting Started</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch01s06.html">Prev</a>&nbsp;</td><th align="center" width="60%">Using GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="RunningGmat.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="GettingStarted"></a>Chapter&nbsp;2.&nbsp;Getting Started</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="GettingStarted.html#Installation">Installation</a></span></dt><dt><span class="section"><a href="RunningGmat.html">Running GMAT</a></span></dt><dd><dl><dt><span class="section"><a href="RunningGmat.html#N102DD">Starting GMAT</a></span></dt><dt><span class="section"><a href="RunningGmat.html#N10300">Exiting GMAT</a></span></dt></dl></dd><dt><span class="section"><a href="SampleMissions.html">Sample Missions</a></span></dt><dt><span class="section"><a href="GettingHelp.html">Getting Help</a></span></dt></dl></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Installation"></a>Installation</h2></div></div></div><a name="N10265" class="indexterm"></a><p>Application bundles are available on the GMAT SourceForge project
  page, located at <code class="uri">https://sourceforge.net/projects/gmat</code>.</p><p>The following packages are available for the major platforms:</p><div class="informaltable"><table border="1"><colgroup><col align="center" width="33%"><col width="33%"><col width="34%"></colgroup><thead><tr><th align="center">Operating System</th><th align="center">Binary bundle</th><th align="center">Source code</th></tr></thead><tbody><tr><td align="center">Windows</td><td align="center" valign="middle">&#10004;</td><td align="center" valign="middle">&#10004;</td></tr><tr><td align="center">macOS</td><td align="center" valign="middle">&#10004;</td><td align="center" valign="middle">&#10004;</td></tr><tr><td align="center">Linux</td><td align="center" valign="middle">&#10004;</td><td align="center" valign="middle">&#10004;</td></tr></tbody></table></div><div class="simplesect"><div class="titlepage"><div><div><h3 class="title"><a name="N102A3"></a>Binary Bundle</h3></div></div></div><p>A binary bundle is available on Windows as a
    <code class="filename">.zip</code> archive. To use it, unzip it anywhere in your
    file system, making sure to keep the folder structure intact. To run GMAT,
    run the <code class="filename">bin\GMAT.exe</code> executable in the extracted
    folder.</p><p>The MacOS binary bundle is available as a signed DMG file. To use
    it, install the image in either the global Applications folder (requires
    administrative access) or in your user account's Applications folder. To
    run the production quality GMAT console application, open a terminal,
    change directory to the GMAT <code class="filename">bin/</code> folder, and run the
    <code class="filename">GmatConsole</code> application. To run the Beta level GMAT
    GUI, run the <code class="filename">GMAT-R2025a_Beta.app</code> application in the
    <code class="filename">bin/</code> folder.</p><p>The Linux builds for GMAT are packaged in compressed tarballs.
    Download either the Red Hat or Ubuntu tarball, extract the contents in a
    convenient location (preserving the file system structure), and GMAT will
    be available for use. To run the production quality console application,
    open a terminal, change directories to your release
    <code class="filename">bin/</code> folder, and run the
    <code class="filename">GmatConsole</code> application. The beta quality GMAT GUI,
    <code class="filename">GMAT_Beta</code> can also be run from this folder. (Note
    that the GMAT Linux builds follow the Linux custom of placing support
    libraries in a lib folder parallel to the bin folder. You may need to load
    that folder by setting LD_LIBRARY_PATH as part of your launch
    process.)</p></div><div class="simplesect"><div class="titlepage"><div><div><h3 class="title"><a name="N102C7"></a>Source Code</h3></div></div></div><p>GMAT is available as a platform-independent source code bundle. See
    the <a class="link" href="http://gmatcentral.org" target="_top">GMAT Wiki</a> for
    compiling instructions.</p><p>The release snapshot of the GMAT code is available from the Git
    repository at SourceForge: </p><div class="literallayout"><p><code class="uri">https://git.code.sf.net/p/gmat/git</code></p></div><p>There are tags available in the repository for reach release.</p></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch01s06.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="UsingGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="RunningGmat.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Contributors&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Running GMAT</td></tr></table></div></body></html>