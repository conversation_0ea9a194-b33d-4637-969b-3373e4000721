<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;22.&nbsp;Programming</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="RefGuide.html" title="Reference Guide"><link rel="prev" href="InitialOrbitDetermination.html" title="Initial Orbit Determination"><link rel="next" href="Array.html" title="Array"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;22.&nbsp;Programming</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="InitialOrbitDetermination.html">Prev</a>&nbsp;</td><th align="center" width="60%">Reference Guide</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Array.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="N2BAB0"></a>Chapter&nbsp;22.&nbsp;Programming</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ch22.html#N2BAB5">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Array.html">Array</a></span><span class="refpurpose"> &mdash; A user-defined one- or two-dimensional array
    variable</span></dt><dt><span class="refentrytitle"><a href="GmatFunction.html">GMATFunction</a></span><span class="refpurpose"> &mdash; Declaration of a GMAT function</span></dt><dt><span class="refentrytitle"><a href="MatlabFunction.html">MatlabFunction</a></span><span class="refpurpose"> &mdash; Declaration of an external MATLAB function</span></dt><dt><span class="refentrytitle"><a href="String.html">String</a></span><span class="refpurpose"> &mdash; A user-defined string variable</span></dt><dt><span class="refentrytitle"><a href="Variable.html">Variable</a></span><span class="refpurpose"> &mdash; A user-defined numeric variable</span></dt></dl></dd><dt><span class="section"><a href="ch22s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Assignment.html">Assignment (<code class="literal">=</code>)</a></span><span class="refpurpose"> &mdash; Set a variable or resource field to a value, possibly using
    mathematical expressions</span></dt><dt><span class="refentrytitle"><a href="BeginMissionSequence.html">BeginMissionSequence</a></span><span class="refpurpose"> &mdash; Begin the mission sequence portion of a script</span></dt><dt><span class="refentrytitle"><a href="BeginScript.html">BeginScript</a></span><span class="refpurpose"> &mdash; Execute free-form script commands</span></dt><dt><span class="refentrytitle"><a href="Breakpoint.html">Breakpoint</a></span><span class="refpurpose"> &mdash; 
      Pause a run and let the user examine the state of objects.
    </span></dt><dt><span class="refentrytitle"><a href="CallGmatFunction.html">CallGmatFunction</a></span><span class="refpurpose"> &mdash; Call a GMAT function</span></dt><dt><span class="refentrytitle"><a href="CallMatlabFunction.html">CallMatlabFunction</a></span><span class="refpurpose"> &mdash; Call a MATLAB function</span></dt><dt><span class="refentrytitle"><a href="CallPythonFunction.html">CallPythonFunction</a></span><span class="refpurpose"> &mdash; Call a Python function</span></dt><dt><span class="refentrytitle"><a href="CommandEcho.html">CommandEcho</a></span><span class="refpurpose"> &mdash; Toggle the use of the <span class="guilabel">Echo</span>
    command</span></dt><dt><span class="refentrytitle"><a href="For.html">For</a></span><span class="refpurpose"> &mdash; Execute a series of commands a specified number of
    times</span></dt><dt><span class="refentrytitle"><a href="Global.html">Global</a></span><span class="refpurpose"> &mdash; Declare Objects as global</span></dt><dt><span class="refentrytitle"><a href="If.html">If</a></span><span class="refpurpose"> &mdash; Conditionally execute a series of commands</span></dt><dt><span class="refentrytitle"><a href="Stop.html">Stop</a></span><span class="refpurpose"> &mdash; Stop mission execution</span></dt><dt><span class="refentrytitle"><a href="While.html">While</a></span><span class="refpurpose"> &mdash; Execute a series of commands repeatedly while a condition is
    met</span></dt></dl></dd><dt><span class="section"><a href="ch22s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="IncludeMacro.html">#Include Macro</a></span><span class="refpurpose"> &mdash; Load or import a script snippet</span></dt><dt><span class="refentrytitle"><a href="MatlabInterface.html">MATLAB Interface</a></span><span class="refpurpose"> &mdash; Interface to MATLAB system</span></dt><dt><span class="refentrytitle"><a href="PythonInterface.html">Python Interface</a></span><span class="refpurpose"> &mdash; Interface to the Python programming language</span></dt></dl></dd></dl></div><p> This chapter contains documentation for Resources and Commands related to script programming and customization functionality. </p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N2BAB5"></a>Resources</h2></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="InitialOrbitDetermination.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="RefGuide.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Array.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Initial Orbit Determination&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Array</td></tr></table></div></body></html>