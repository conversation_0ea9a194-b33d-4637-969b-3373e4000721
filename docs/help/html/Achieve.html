<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Achieve</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch20s02.html" title="Commands"><link rel="prev" href="ch20s02.html" title="Commands"><link rel="next" href="Minimize.html" title="Minimize"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Achieve</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch20s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Minimize.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Achieve"></a><div class="titlepage"></div><a name="N26BAD" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Achieve</span></h2><p>Achieve &mdash; Specify a goal for a <span class="guilabel">Target</span>
    sequence</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">Achieve</code> <em class="replaceable"><code>SolverName</code></em> (<em class="replaceable"><code>Goal</code></em> = Arg1, [{<em class="replaceable"><code>Tolerance</code></em> = Arg2}])   </pre></div><div class="refsection"><a name="N26BD1"></a><h2>Description</h2><p>The <span class="guilabel">Achieve</span> command is used in conjunction with
    the <span class="guilabel">Target</span> command as part of the
    <span class="guilabel">Target</span> sequence. The purpose of the
    <span class="guilabel">Achieve</span> command is to define a goal for the targeter
    (currently, the differential corrector is the only targeter available
    within a <span class="guilabel">Target</span> sequence) to achieve. To configure
    the <span class="guilabel">Achieve</span> command, you specify the goal object, its
    corresponding desired value, and an optional tolerance so the differential
    corrector can find a solution. The <span class="guilabel">Achieve</span> command
    must be accompanied and preceded by a <span class="guilabel">Vary</span> command in
    order to assist in the targeting process.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="DifferentialCorrector.html" title="DifferentialCorrector"><span class="refentrytitle">DifferentialCorrector</span></a>, <a class="xref" href="Target.html" title="Target"><span class="refentrytitle">Target</span></a>, <a class="xref" href="Vary.html" title="Vary"><span class="refentrytitle">Vary</span></a></p></div><div class="refsection"><a name="N26BFB"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Arg1</span></td><td><p>Specifies the desired value for the
            <span class="guilabel">Goal</span> after the
            <span class="guilabel">DifferentialCorrector</span> has converged. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Array, ArrayElement, Variable, String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number, Array element, or Variable</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>42165</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Arg2</span></td><td><p> Convergence tolerance for how close
            <span class="guilabel">Goal</span> equals&nbsp;<span class="guilabel">Arg1</span> </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Real Number, Array element, Variable, or any
                    user-defined parameter &gt; 0</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number, Array element, Variable, or any
                    user-defined parameter &gt; 0</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.1</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Goal</span></td><td><p>Allows you to select any single element user defined
            parameter, except a number, as a targeter goal. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Object parameter, ArrayElement, Variable</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span> parameter,
                    <span class="guilabel">Array</span> element,
                    <span class="guilabel">Variable</span>, or any other single element
                    user defined parameter, excluding numbers</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">DefaultSC.Earth.RMAG</code>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolverName</span></td><td><p>Specifies the
            <span class="guilabel">DifferentialCorrector</span> being used in the
            <span class="guilabel">Target</span> sequence</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any user defined
                    <span class="guilabel">DifferentialCorrector</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultDC</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N26CCE"></a><h2>GUI</h2><p>You use an <span class="guilabel">Achieve</span> command, which is only valid
    within a <span class="guilabel">Target</span> sequence, to define your desired
    goal. More than one <span class="guilabel">Achieve</span> command may be used
    within a <span class="guilabel">Target</span> command sequence. The
    <span class="guilabel">Achieve</span> command dialog box, which allows you to
    specify the targeter, goal object, goal value, and convergence tolerance,
    is shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Achieve_GUI.png" align="middle" height="235"></td></tr></table></div></div></div><div class="refsection"><a name="N26CEB"></a><h2>Remarks</h2><div class="refsection"><a name="N26CEE"></a><h3>Command Interactions</h3><p>A <span class="guilabel">Target</span> sequence must contain at least one
      <span class="guilabel">Vary</span> and one <span class="guilabel">Achieve</span>
      command.</p><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><tbody><tr><td><span class="guilabel">Target command</span></td><td><p>An <span class="guilabel">Achieve</span> command only occurs
              within a <span class="guilabel">Target </span>sequence </p></td></tr><tr><td><span class="guilabel">Vary command </span></td><td><p>Associated with any <span class="guilabel">Achieve</span>
              command is at least one <span class="guilabel">Vary</span> command. The
              <span class="guilabel">Vary</span> command identifies the control
              variable used by the targeter. The goal specified by the
              <span class="guilabel">Achieve</span> command is obtained by varying the
              control variables. </p></td></tr></tbody></table></div></div></div><div class="refsection"><a name="N26D28"></a><h2>Examples</h2><p>As mentioned above, an <span class="guilabel">Achieve</span> command only
    occurs within a <span class="guilabel">Target</span> sequence. See the
    <span class="guilabel">Target</span> command help for examples showing the use of
    the <span class="guilabel">Achieve</span> command.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch20s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch20s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Minimize.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Commands&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Minimize</td></tr></table></div></body></html>