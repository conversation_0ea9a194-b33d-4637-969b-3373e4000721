<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Stop</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s02.html" title="Commands"><link rel="prev" href="If.html" title="If"><link rel="next" href="While.html" title="While"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Stop</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="If.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="While.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Stop"></a><div class="titlepage"></div><a name="N2D004" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Stop</span></h2><p>Stop &mdash; Stop mission execution</p></div><div class="refsection"><a name="N2D015"></a><h2>Description</h2><p>The <span class="guilabel">Stop</span> command stops execution of the current
    mission at the point that the command is encountered and returns control
    to the GMAT interface. The effect is similar to that of the
    <span class="guilabel">Stop</span> button on the GUI toolbar.</p></div><div class="refsection"><a name="N2D020"></a><h2>GUI</h2><p>The <span class="guilabel">Stop</span> command can be inserted into and
    deleted from Mission tree, but the command has no GUI panel of its
    own.</p></div><div class="refsection"><a name="N2D028"></a><h2>Remarks</h2><p>The <span class="guilabel">Stop</span> command stops execution of the current
    mission, not the GMAT application. All data displayed to the point, at
    which the script was stopped (e.g. <span class="guilabel">OrbitView</span> windows,
    <span class="guilabel">GroundTrackPlot</span> windows), remain available for
    manipulation. Using the <span class="guilabel">Stop</span> command within a loop or
    solver structure will stop execution at the first iteration during which
    the command is encountered.</p></div><div class="refsection"><a name="N2D039"></a><h2>Examples</h2><div class="informalexample"><p>Stopping the execution of a script between commands:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create ForceModel aForceModel
Create Propagator aProp
aProp.FM = aForceModel

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = 30};
Stop
Propagate aProp(aSat) {aSat.ElapsedDays = 30};</code></pre><p>Stopping the execution of a solver structure for further
      investigation:</p><pre class="programlisting"><code class="code">Create ChemicalTank aTank
Create ForceModel aForceModel
Create DifferentialCorrector aDC

Create Spacecraft aSat
aSat.Tanks = {aTank}

Create Propagator aProp
aProp.FM = aForceModel

Create ImpulsiveBurn anIB
anIB.DecrementMass = true
anIB.Tanks = {aTank}

BeginMissionSequence

Target aDC
Vary aDC(anIB.Element1 = 0.5)
Maneuver anIB(aSat)
Propagate aProp(aSat) {aSat.Periapsis}
If aSat.aTank.FuelMass &lt; 10
Stop
EndIf
Achieve aDC(aSat.Altitude = 1000)</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="If.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="While.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">If&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;While</td></tr></table></div></body></html>