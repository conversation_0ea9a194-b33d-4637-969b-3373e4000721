<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Spacecraft</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="SolarSystem.html" title="SolarSystem"><link rel="next" href="SpacecraftAttitude.html" title="Spacecraft Attitude"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Spacecraft</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="SolarSystem.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SpacecraftAttitude.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Spacecraft"></a><div class="titlepage"></div><a name="N1DA54" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Spacecraft</span></h2><p>Spacecraft &mdash; A spacecraft model</p></div><div class="refsection"><a name="N1DA65"></a><h2>Description</h2><p>A <span class="guilabel">Spacecraft</span> resource is GMAT's spacecraft
    model and includes data and models for the spacecraft's orbit, epoch,
    attitude, and physical parameters (such as mass and drag coefficient), as
    well as attached hardware, including tanks and thrusters. The
    <span class="guilabel">Spacecraft</span> model also contains the data that
    configures how the <span class="guilabel">Spacecraft</span> 3-D CAD model is used
    in an <span class="guilabel">OrbitView</span>. <span class="guilabel">Spacecraft </span>has
    certain fields that can be set in the Mission Sequence and some that
    cannot. See the field tables on the pages below for more
    information.</p><p>GMAT's documentation for <span class="guilabel">Spacecraft</span> is
    extensive and is broken down into the following sections:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><a class="xref" href="SpacecraftAttitude.html" title="Spacecraft Attitude"><span class="refentrytitle">Spacecraft Attitude</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties"><span class="refentrytitle">Spacecraft Ballistic/Mass Properties</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftEpoch.html" title="Spacecraft Epoch"><span class="refentrytitle">Spacecraft Epoch</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftHardware.html" title="Spacecraft Hardware"><span class="refentrytitle">Spacecraft Hardware</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftNavigation.html" title="Spacecraft Navigation"><span class="refentrytitle">Spacecraft Navigation</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="refentrytitle">Spacecraft Orbit State</span></a></p></li><li class="listitem"><p><a class="xref" href="SpacecraftVisualizationProperties.html" title="Spacecraft Visualization Properties"><span class="refentrytitle">Spacecraft Visualization Properties</span></a></p></li></ul></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="SolarSystem.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SpacecraftAttitude.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">SolarSystem&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Spacecraft Attitude</td></tr></table></div></body></html>