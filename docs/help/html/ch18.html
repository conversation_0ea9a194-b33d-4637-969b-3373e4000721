<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;18.&nbsp;Dynamics and Modeling</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="RefGuide.html" title="Reference Guide"><link rel="prev" href="ch17.html" title="Chapter&nbsp;17.&nbsp;API"><link rel="next" href="Barycenter.html" title="Barycenter"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;18.&nbsp;Dynamics and Modeling</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch17.html">Prev</a>&nbsp;</td><th align="center" width="60%">Reference Guide</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Barycenter.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="N158ED"></a>Chapter&nbsp;18.&nbsp;Dynamics and Modeling</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ch18.html#N158F2">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Barycenter.html">Barycenter</a></span><span class="refpurpose"> &mdash; The center of mass of selected celestial bodies</span></dt><dt><span class="refentrytitle"><a href="CelestialBody.html">CelestialBody</a></span><span class="refpurpose"> &mdash; Modeling of Moon, Planet, Asteroid, and Comet
    objects</span></dt><dt><span class="refentrytitle"><a href="FuelTank.html">ChemicalTank</a></span><span class="refpurpose"> &mdash; Model of a chemical fuel tank</span></dt><dt><span class="refentrytitle"><a href="Thruster.html">ChemicalThruster</a></span><span class="refpurpose"> &mdash; A chemical thruster model</span></dt><dt><span class="refentrytitle"><a href="ContactLocator.html">ContactLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">Spacecraft</span> and a <span class="guilabel">GroundStation</span>
    or <span class="guilabel">PlanetographicRegion</span>.</span></dt><dt><span class="refentrytitle"><a href="CoordinateSystem.html">CoordinateSystem</a></span><span class="refpurpose"> &mdash; An axis and origin pair</span></dt><dt><span class="refentrytitle"><a href="EclipseLocator.html">EclipseLocator</a></span><span class="refpurpose"> &mdash; A <span class="guilabel">Spacecraft</span> eclipse event
    locator</span></dt><dt><span class="refentrytitle"><a href="ElectricTank.html">ElectricTank</a></span><span class="refpurpose"> &mdash; A model of a tank containing fuel for an electric propulsion
    system</span></dt><dt><span class="refentrytitle"><a href="ElectricThruster.html">ElectricThruster</a></span><span class="refpurpose"> &mdash; An electric thruster model</span></dt><dt><span class="refentrytitle"><a href="FiniteBurn.html">FiniteBurn</a></span><span class="refpurpose"> &mdash; A finite burn</span></dt><dt><span class="refentrytitle"><a href="FieldOfView.html">FieldOfView</a></span><span class="refpurpose"> &mdash; Models the mask, or field-of-view, of a hardware
    <span class="guilabel">Resource</span>.</span></dt><dt><span class="refentrytitle"><a href="ForceModel.html">ForceModel</a></span><span class="refpurpose"> &mdash; Used to specify force modeling options such as gravity, drag,
    solar radiation pressure, and non-central bodies for
    propagation.</span></dt><dt><span class="refentrytitle"><a href="Formation.html">Formation</a></span><span class="refpurpose"> &mdash; A collection of spacecraft.</span></dt><dt><span class="refentrytitle"><a href="GroundStation.html">GroundStation</a></span><span class="refpurpose"> &mdash; A ground station model.</span></dt><dt><span class="refentrytitle"><a href="Imager.html">Imager</a></span><span class="refpurpose"> &mdash; An imager with a defined field of view.</span></dt><dt><span class="refentrytitle"><a href="ImpulsiveBurn.html">ImpulsiveBurn</a></span><span class="refpurpose"> &mdash; An impulsive maneuver</span></dt><dt><span class="refentrytitle"><a href="IntrusionLocator.html">IntrusionLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">CelestialBody</span> and an observer
    <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="LibrationPoint.html">LibrationPoint</a></span><span class="refpurpose"> &mdash; An equilibrium point in the circular, restricted 3-body
    problem</span></dt><dt><span class="refentrytitle"><a href="NuclearPowerSystem.html">NuclearPowerSystem</a></span><span class="refpurpose"> &mdash; A nuclear power system</span></dt><dt><span class="refentrytitle"><a href="PlanetographicRegion.html">PlanetographicRegion</a></span><span class="refpurpose"> &mdash; Define an area on a celestial body's surface as a target for
    an observing <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="Plate.html">Plate</a></span><span class="refpurpose"> &mdash; Used to specify the properties of a single spacecraft surface
    (body panel, solar array side, or other surface) for high-fidelity solar
    radiation pressure modeling, including specular, diffuse, and absorptive
    effects.</span></dt><dt><span class="refentrytitle"><a href="Propagator.html">Propagator</a></span><span class="refpurpose"> &mdash; A propagator models spacecraft motion</span></dt><dt><span class="refentrytitle"><a href="SolarPowerSystem.html">SolarPowerSystem</a></span><span class="refpurpose"> &mdash; A solar power system model</span></dt><dt><span class="refentrytitle"><a href="SolarSystem.html">SolarSystem</a></span><span class="refpurpose"> &mdash; High level solar system configuration options</span></dt><dt><span class="refentrytitle"><a href="Spacecraft.html">Spacecraft</a></span><span class="refpurpose"> &mdash; A spacecraft model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftAttitude.html">Spacecraft Attitude</a></span><span class="refpurpose"> &mdash; The spacecraft attitude model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></span><span class="refpurpose"> &mdash; The physical properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftEpoch.html">Spacecraft Epoch</a></span><span class="refpurpose"> &mdash; The spacecraft epoch</span></dt><dt><span class="refentrytitle"><a href="SpacecraftHardware.html">Spacecraft Hardware</a></span><span class="refpurpose"> &mdash; Add hardware to a spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftOrbitState.html">Spacecraft Orbit State</a></span><span class="refpurpose"> &mdash; The orbital initial conditions</span></dt><dt><span class="refentrytitle"><a href="SpacecraftVisualizationProperties.html">Spacecraft Visualization Properties</a></span><span class="refpurpose"> &mdash; The visual properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="ThrustHistoryFile.html">ThrustHistoryFile</a></span><span class="refpurpose"> &mdash; A time history of input thrust/acceleration vectors and mass
    flow rate</span></dt><dt><span class="refentrytitle"><a href="ThrustSegment.html">ThrustSegment</a></span><span class="refpurpose"> &mdash; One or more <span class="guilabel">ThrustSegments</span> define how
    data in a thrust history file are used.</span></dt></dl></dd><dt><span class="section"><a href="ch18s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="BeginFileThrust.html">BeginFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="BeginFiniteBurn.html">BeginFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers</span></dt><dt><span class="refentrytitle"><a href="EndFileThrust.html">EndFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="EndFiniteBurn.html">EndFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers in the mission
    sequence</span></dt><dt><span class="refentrytitle"><a href="FindEvents.html">FindEvents</a></span><span class="refpurpose"> &mdash; Execute an event location search</span></dt><dt><span class="refentrytitle"><a href="Maneuver.html">Maneuver</a></span><span class="refpurpose"> &mdash; Perform an impulsive (instantaneous) maneuver</span></dt><dt><span class="refentrytitle"><a href="Propagate.html">Propagate</a></span><span class="refpurpose"> &mdash; Propagates spacecraft to a requested stopping
    condition</span></dt></dl></dd></dl></div><p> This chapter contains documentation for Resources and Commands related to dyanmics and modeling. </p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N158F2"></a>Resources</h2></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch17.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="RefGuide.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Barycenter.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;17.&nbsp;API&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Barycenter</td></tr></table></div></body></html>