<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Load the Mission</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_EventLocation.html" title="Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts"><link rel="prev" href="Tut_EventLocation.html" title="Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts"><link rel="next" href="ch11s03.html" title="Configure GMAT for Event Location"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Load the Mission</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Tut_EventLocation.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch11s03.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13A47"></a>Load the Mission</h2></div></div></div><p>For this tutorial, we will start with a preexisting mission created
    during the Simple Orbit Transfer tutorial. You can either complete that
    tutorial prior to this one, or you can load the end result directly, as
    shown below.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Open GMAT.</p></li><li class="step"><p>Click <span class="guilabel">Open</span> in the toolbar and navigate to
        the GMAT <code class="filename">samples</code> directory.</p></li><li class="step"><p>Select <code class="filename">Tut_SimpleOrbitTransfer.script</code> and
        click <span class="guibutton">Open</span>.</p></li><li class="step"><p>Click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>) to run the mission.</p></li></ol></div><p>You should see the following result in the
    <span class="guilabel">DefaultOrbitView</span> window.</p><div class="figure"><a name="N13A75"></a><p class="title"><b>Figure&nbsp;11.1.&nbsp;<span class="guilabel">DefaultOrbitView</span> window</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_EventLocation_1.png" align="middle" height="314" alt="DefaultOrbitView window"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Tut_EventLocation.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_EventLocation.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch11s03.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;11.&nbsp;Finding Eclipses and Station Contacts&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure GMAT for Event Location</td></tr></table></div></body></html>