<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>GMAT R2020a Release Notes</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ReleaseNotes.html" title="Release Notes"><link rel="prev" href="ReleaseNotesR2022a.html" title="GMAT R2022a Release Notes"><link rel="next" href="ReleaseNotesR2018a.html" title="GMAT R2018a Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">GMAT R2020a Release Notes</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ReleaseNotesR2022a.html">Prev</a>&nbsp;</td><th align="center" width="60%">Release Notes</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReleaseNotesR2018a.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ReleaseNotesR2020a"></a>GMAT R2020a Release Notes</h2></div></div></div><p>The General Mission Analysis Tool (GMAT) version R2020a was released
  in May 2020. This is the first public release since May 2018 and is the
  13<sup>th</sup> release for the project. This is a major
  project release that includes numerous improvements in dynamics modeling
  and other areas and five major new subsystems and components: </p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>A new 3D graphics engine based on OpenFrames. OpenFrames is the
        graphics engine used in NASA's Copernicus software. GMAT and
        Copernicus now share graphics capability, improving development
        efficiency and providing greater capablity to both systems.</p></li><li class="listitem"><p>The first production-quality C++ release of the Collocation
        Stand-Alone Library and Toolkit (CSALT). CSALT solves the general
        optimal control problem and supports pseudospectral and Lobatto IIIa
        transcriptions. CSALT can be used independent of GMAT (though CSALT
        leverages some low-level GMAT math libraries).</p></li><li class="listitem"><p>A new optimal control subsystem that supports finite-thrust
        optimization. The optimal control plugin uses the CSALT optimization
        library, electric propulsion models, and GMAT's high-fidelity dynamics
        models to support high-fidelity finite thrust optmization. This plugin
        makes extensive use of analytic partial derivatives and employs
        optimal finite differencing when analytic partials are not
        provided.</p></li><li class="listitem"><p>A new (beta) API developed to provide users low-level access to
        GMAT functionality and to support interoperablity between NASA's GMAT,
        Copernicus, and MONTE software applications.</p></li><li class="listitem"><p>A new (alpha) Extended Kalman Filter Smoother with process
        noise.</p></li></ul></div><p>Below is a more detailed summary of key changes in this release.
  Please see the full <a class="link" href="http://bugs.gmatcentral.org/secure/ReleaseNote.jspa?version=11104&amp;styleName=Html&amp;projectId=10000&amp;Create=Create&amp;atl_token=B8F2-GAHA-O7AM-D5JZ%7C78ed3832b129ed9d51b5d382a7c04b0602d918d9%7Clin" target="_top">R2020a
  Release Notes</a> on JIRA for a complete list.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30C43"></a>Milestones and Accomplishments</h3></div></div></div><p>We are excited that GMAT continues to see significant adoption for
    operational mission support.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>On May 1, 2018, the Lunar Reconnaissance Orbiter (LRO) project
        held an Operational Readiness Review (ORR) to evaluate GMAT as a
        replacement for GTDS as the primary operational orbit determination
        (OD) tool for LRO. GMAT was approved for this purpose at this ORR and
        LRO began using GMAT for operational OD in June 2018.</p></li><li class="listitem"><p>In April 2018, the Transiting Exoplanet Survey Satellite (TESS)
        mission launched. TESS used GMAT as its primary tool for mission
        design and maneuver planning from proposal development through
        operations. For more information about TESS, including a discussion of
        how GMAT was used, see the <a class="link" href="https://ntrs.nasa.gov/search.jsp?R=20180006156" target="_top">published
        paper</a>.</p></li><li class="listitem"><p>The GMAT team is currently developing an Extended Kalman Filter
        Smoother (EKFS) orbit determination capability for future release. In
        late 2020, a low Earth NASA mission that uses the GPS point solution
        data type will help operationally test our new EKFS capability. A
        publicly available version of GMAT, with EKFS capabilities for all
        data types supported by GMAT, is planned for the R2021a
        release.</p></li><li class="listitem"><p>The GMAT team is currently integrating CSALT into GSFC's Core
        Flight System (cFS) to demonstrate the ability to perform optimal
        control onboard.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30C59"></a>New Features</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30C5C"></a>General Capabilities</h4></div></div></div><p>The following general capabilities were added in release
      R2020a:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Force models now support Solar Radiation Pressure (SRP)
          N-Plate force modeling (alpha/beta level). See the <a class="xref" href="ForceModel.html" title="ForceModel"><span class="refentrytitle">ForceModel</span></a> section
          for details.</p></li><li class="listitem"><p>Attitude propagation models now support kinematic attitude
          propagation using initial atittude and angular velocity. See the
          <a class="xref" href="SpacecraftAttitude.html" title="Spacecraft Attitude"><span class="refentrytitle">Spacecraft Attitude</span></a>
          section for details.</p></li><li class="listitem"><p>Hardware models (e.g., antennae, sun sensors) are extended to
          support fields of view including</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>The orientation of the hardware relative to the spacecraft
              body</p></li><li class="listitem"><p>Field-of-view masks for conical, rectangular and custom
              fields of view</p></li><li class="listitem"><p>Visibility model for points represented as unit
              vectors</p></li><li class="listitem"><p>Visualization of sensor cones in OpenFrames
              graphics</p></li></ul></div></li><li class="listitem"><p>Force models now include the ability to use continuous thrust
          files during numerical propagation. For details, see the new <a class="xref" href="ThrustSegment.html" title="ThrustSegment"><span class="refentrytitle">ThrustSegment</span></a> and <a class="xref" href="ThrustHistoryFile.html" title="ThrustHistoryFile"><span class="refentrytitle">ThrustHistoryFile</span></a> sections and
          the sample script
          <code class="filename">samples/Ex_R2020a_Propagate_ThrustHistoryFile.script</code>
          for an example demonstrating the use of finite-thrust modeling in
          propagation. The thrust history model supports: </p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: circle; "><li class="listitem"><p>Interpolation of finite thrust (low, medium, or high
                continuous thrust) or accelerations</p></li><li class="listitem"><p>Optional integration of mass flow rate in
                propagation</p></li><li class="listitem"><p>Choice of coordinate system for input
                thrust/acceleration</p></li><li class="listitem"><p>Scaling of selected inputs</p></li><li class="listitem"><p>Choice of interpolation methods</p></li></ul></div></li><li class="listitem"><p>The drag model now supports interpolation of a SPAD drag model
          for attitude dependent drag modeling. SPAD files are created by
          ray-tracing CAD models to compute an attitude-dependent area profile
          that is interpolated during numerical propagation. For more
          information on how to configure a force model and spacecraft to use
          a SPAD drag model, see the <a class="xref" href="ForceModel.html" title="ForceModel"><span class="refentrytitle">ForceModel</span></a> and <a class="xref" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties"><span class="refentrytitle">Spacecraft Ballistic/Mass Properties</span></a>
          sections, respectively, and the sample script
          <code class="filename">samples/Navigation/Ex_R2020a_Estimate_SPADDragScaleFactor.script</code></p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30CAD"></a>Orbit Determination (OD) Enhancements</h4></div></div></div><p>There are numerous OD enhancements including 5 new Resources
      <span class="guilabel">KalmanFilter</span>, <span class="guilabel">Smoother</span>,
      <span class="guilabel">ProcessNoiseModel</span>,
      <span class="guilabel">EstimatedParameter</span>, and <span class="guilabel"> Plate</span>
      and numerous improvements to existing functionality.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The OD subsystem has undergone significant refactoring to
            prepare for a future release of an extended Kalman Filter with
            smoother.</p></li><li class="listitem"><p>The ionosphere media correction model is faster via
            correction caching. GMAT re-uses computed ionosphere correction to
            light-time when multiple independent measurements (such as range,
            range-rate, and angles) occur from the same station at the same
            epoch.</p></li><li class="listitem"><p>The Batch Least Squares (BLS) now supports inner-loop sigma
            editing. After each BLS iteration, an iterative linearized
            approximation is used to predict measurements likely to be edited
            on the next BLS iteration and to correct the state accordingly.
            This can accelerate convergence and help to reduce the overall
            number of BLS iterations required. See the
            <span class="guilabel">UseInnerLoopEditing</span> option to the
            <span class="guilabel">BatchEstimator</span> resource.</p></li><li class="listitem"><p>The Batch Least Squares (BLS) now removes unobservable
            states from the normal matrix before attempting inversion. This is
            useful, for example, when the user configures estimation of a
            measurement bias but all observations of the bias are edited out.
            GMAT now removes the unobservable state and continues processing
            instead of terminating with a matrix inversion error.</p></li><li class="listitem"><p>Several new measurement types are supported for OD
            simulation and estimation. See the User Guide section on <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a> for more details on the new
            supported measurement types, including:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: circle; "><li class="listitem"><p>Azimuth, Elevation angles</p></li><li class="listitem"><p>XEast, YNorth angles</p></li><li class="listitem"><p>XSouth, YEast angles</p></li><li class="listitem"><p>Range_Skin (C-band, non-transponder range)</p></li></ul></div></li><li class="listitem"><p>The Batch Least Squares (BLS) measurement statistics report
            now includes summary statistics on user-edited data.</p></li><li class="listitem"><p>The estimation subsystem now supports <a class="xref" href="ThrustHistoryFile.html" title="ThrustHistoryFile"><span class="refentrytitle">ThrustHistoryFile</span></a> and <a class="xref" href="ThrustSegment.html" title="ThrustSegment"><span class="refentrytitle">ThrustSegment</span></a> resources to
            model finite burns in the estimation arc for BLS OD, and can
            optionally estimate a scale factor correction to the nominal
            finite burn profile.</p></li><li class="listitem"><p>The structure of the BLS MATLAB data file has changed. See
            the <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a>
            resource documentation in the Orbit Determination section of the
            User Guide for details.</p></li><li class="listitem"><p>New <a class="xref" href="ExtendedKalmanFilter.html" title="ExtendedKalmanFilter"><span class="refentrytitle">ExtendedKalmanFilter</span></a> and <a class="xref" href="Smoother.html" title="Smoother"><span class="refentrytitle">Smoother</span></a> resources are available in Testing
            mode. These resources are currently under development and should
            not be used in operational support.</p></li><li class="listitem"><p>The estimation subsystem now supports a (beta) N-Plate area
            model for solar radiation pressure (SRP). The user may configure a
            detailed multi-plate area model for SRP modeling for both
            prediction and estimation. The model includes specular and diffuse
            reflectivity, support for moving panels, like solar arrays, and
            estimation of one or more SRP correction scale factors. This model
            is currently under development and should not be used in
            operational support. See the <a class="xref" href="Plate.html" title="Plate"><span class="refentrytitle">Plate</span></a> resource for more details on activating
            and configuring this model.</p></li><li class="listitem"><p>The estimation subsystem now supports the ablity to solve
            for SPAD drag/SRP coefficients.</p></li></ul></div><p>GMAT&rsquo;s orbit determination (OD) capability has been significantly
      enhanced. As with all new releases, missions that use GMAT&rsquo;s OD
      capability should perform a baseline set of regression/performance tests
      prior to using the new version of GMAT OD for operational purposes. GMAT
      is distributed with several examples that demonstrate new orbit
      determination capability in this release, including:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>KalmanFilter and Smoother: See</p><p><code class="filename">samples/Navigation/Ex_R2020a_Alpha_FilterSmoother.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Angle measurement types: See <code class="filename"></code></p><p><code class="filename">samples/Navigation/Ex_R2020a_Estimate_AngleData.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Skin range measurement type:</p><p>See
            <code class="filename">samples/Navigation/Ex_R2020a_Estimate_RangeSkin.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>SPAD drag estimation: See <code class="filename"></code></p><p><code class="filename">samples/Navigation/Ex_R2020a_Estimate_SPADDragScaleFactor.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>SPAD SRP estimation: See</p><p><code class="filename">samples/Navigation/Ex_R2020a_Estimate_SPADSRPScaleFactor.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Finite thrust estimation: See <code class="filename"></code></p><p><code class="filename">samples/Navigation/Ex_R2020a_Estimate_ThrustScaleFactor.script</code></p><p><code class="filename"></code></p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30D4D"></a>3D Visualization Plugin: OpenFramesInterface</h4></div></div></div><p>GMAT now supports a new 3D graphics component called
      OpenFramesInterface (OFI), which enables high-performance interactive 3D
      visualizations with significant additional functionality compared to
      GMATs legacy 3D graphics (<span class="guilabel">OrbitView</span>) capability.
      GMAT R2020a includes OFI v1.0, the first tested and documented release
      of the plugin. The complete set of documentation for OFI is available at
      <code class="uri">https://gitlab.com/EmergentSpaceTechnologies/OpenFramesInterface/-/wikis/home</code>.
      Screen captures of selected components are included in the appendix of
      this section. A summary of key OFI interface capablity is shown
      below.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The new OFI graphics component supports all functionaliy in
          GMAT's legacy 3D graphics, as well as significant new
          functionality.</p></li><li class="listitem"><p>The system now supports user-defined views of any spacecraft
          or celestial body. Views can be body-fixed, coordinate system-fixed,
          or look from one body towards another. Additionally, the user can
          switch between views in a single graphics window.</p></li><li class="listitem"><p>Multithreaded visualizations now make use of multiple
          processor cores, resulting in a general computation speedup
          throughout GMAT when compared to OrbitView.</p></li><li class="listitem"><p>The 3D graphics now supports full control over simulation
          time. View scene at any time, animate at various time scales, and
          synchronize time between multiple OpenFramesInterface
          windows.</p></li><li class="listitem"><p>The system supports granular control over displayed objects,
          including optionally plotting axes, bodies, trajectories, or many
          other per-body graphics options.</p></li><li class="listitem"><p>Graphics option changes no longer require a mission rerun.
          This includes switching between user-defined views and
          enabling/disabling displayed objects.</p></li><li class="listitem"><p>Graphics now support high-fidelity rendering, including
          multisample antialiasing (MSAA), lighting, accurate star map with
          star colors and sizes (HYGv3 database)</p></li><li class="listitem"><p>The system now supports vectors to objects in 3D graphics:
          body-fixed direction or pointing towards another object.</p></li><li class="listitem"><p>The OFI interface now supports visualizing sensor fields of
          view, including conical, rectangular, and custom fields of
          view.</p></li><li class="listitem"><p>The system supports Virtual Reality visualization via OpenVR.
          Any scene can be visualized on OpenVR-compatible hardware, including
          the Oculus Rift or HTC Vive.</p></li></ul></div><p>There is an extensive set of samples scripts illustrating how to
      use the new graphics capability in GMAT located in
      <code class="filename">samples/NeedOpenFramesInterface</code>. A few scripts of
      particular interest are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Near real-time graphics: See</p><p><code class="filename">samples/NeedOpenFramesInterface/Ex_R2020a_OFI_RealTimeMOC.script</code></p><p></p></li><li class="listitem"><p>Visualization of sensor cones: See <code class="filename"></code></p><p><code class="filename">samples/NeedOpenFramesInterface/Ex_R2020a_OFI_FieldOfView.script</code>
            .</p><p></p></li><li class="listitem"><p>Solar eclipse visualization: See</p><p><code class="filename">samples/NeedOpenFramesInterface/Ex_R2020a_OFI_VR_2017SolarEclipse.script</code></p><p></p></li><li class="listitem"><p>Custom segment colors: See</p><p><code class="filename">samples/NeedOpenFramesInterface/Ex_R2020a_OFI_CustomSegmentColors.script</code></p><p></p></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The OFI is now the default 3D visualization component in GMAT.
        <span class="guilabel">OrbitView</span> will continue to be supported for
        backward compatibility purposes but will only be modified for critical
        bug fixes.</p></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30DA3"></a>Trajectory Optimization Plugins</h4></div></div></div><p>GMAT has a new subsystem for trajectory optimization, including
      new techniques and a new C++ and script interface, (The legacy parameter
      optimzation functionality is still supported.) The trajectory
      optimization capablity of GMAT is extended to support a stand-alone
      library for solving the optimal control problem using collocation. This
      library is called the Collocation Stand Alone Library and Toolkit
      (CSALT). CSALT solves the general optimal control problem and supports
      pseudospectral and Lobatto IIIa transcriptions with automatic mesh
      refinement. CSALT can be used independent of GMAT (though, uses some
      low-level GMAT math libraries.)</p><p>There are two new proprietary plugins -- CsaltInterface and
      EMTGModels -- for spacecraft trajectory optimization. Together with the
      CSALT library, those features combine to form the new GMAT optimal
      control capability. The CsaltInterface plugin contains GMAT's interface
      to the collocation transcriptions and optimization capabilities in
      CSALT. The EMTGModels plugin allows GMAT to use spacecraft power and
      engine models implemented in the Evolutionary Mission Trajectory
      Generator (EMTG). Together, those components provide extensive support
      for analytic partial derivatives of optimal control functions, including
      orbital dynamcs and thrust models. When analytic derivatives are not
      provided, the system uses optimal finite differencing.</p><p>The user guide for the optimal control components including CSALT
      and GMAT optimal control is written in restructured text (rst) and
      located in the GMAT distribution here:
      <code class="filename">gmat/docs/GMAT_OptimalControl_Specification.pdf</code>.
      Extensive training materials, beyond the reference material, is
      available for GMAT optimal control functionality and CSALT. Below is a
      brief summary of the new capability.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>CSALT solves the generic optimal control problem, supporting
          dynamics, Lagrange form of the cost function, path, and boundary
          constraints. CSALT does not assume the problem is low-thrust and can
          solve general problems including but not limited to 6-DOF
          optimization, classical optimal control problems, low-thrust,
          high-finite thrust, formation and constellation, and re-entry
          problems. CSALT is distributed with a limited set of models. In
          general, when using CSALT, the user must provide models of dynamics,
          cost, and constraint functions. (NOTE: the GMAT optimal control
          subsystem is used to solve low-thrust trajectory problems in
          GMAT.)</p></li><li class="listitem"><p>The GMAT plugins CsaltInterface and EMTGModels support
          low-thrust trajectory optimization.</p></li><li class="listitem"><p>The GMAT interface to CSALT is fully contained in the GMAT
          scripting language. GMAT script users do not need to write low-level
          C++ code. (However, those familiar with C++ can call CSALT directly
          via the C++ interface.)</p></li><li class="listitem"><p>The system can optimize a constrained spacecraft trajectory
          using one of several collocation transcriptions, with mesh
          refinement.</p></li><li class="listitem"><p>The system supports a trajectory composed of multiple
          &ldquo;phases,&rdquo; each of which may be defined by a different dynamics
          model. Phases may be &ldquo;linked&rdquo; through user-settable constraints
          (e.g., full-state/time linkage between the end of one phase and the
          beginning of another phase to enforce continuity).</p></li><li class="listitem"><p>Users have the ability to set optimization settings from
          within a GMAT script. Examples of settable parameters include
          feasibility/optimality tolerances, upper/lower bounds on states, and
          initial collocation mesh settings.</p></li><li class="listitem"><p>Users can choose from one of multiple commonly used merit
          functions (e.g., maximum final mass, minimum time of flight).</p></li><li class="listitem"><p>Thrust models now include constant thrust and specific impulse
          (Isp), fixed efficiency, polynomials, and smoothed throttle
          tables.</p></li><li class="listitem"><p>Users may include in their trajectory multiple commonly used
          spacecraft trajectory constraints, such as an integrated flyby or a
          celestial body rendezvous.</p></li><li class="listitem"><p>There is alpha-level support for scalar inline expressions in
          optimal control functions. I.e., a user may specify a constraint in
          a script based on GMAT variables if a built-in constraint does not
          satisfy the user&rsquo;s needs.</p></li><li class="listitem"><p>New sample scripts and examples of supporting files are
          included in the release in gmat/samples/OptimalControl (for sample
          scripts) and gmat/data/misc and gmat/data/emtg (for supporting
          files).</p></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The source code for GMAT optimal control and CSALT can be
        distributed, but is currently not included in the public release as
        they depend on the nonlinear programming solver software SNOPT. Source
        code for those components can be provided upon request but the user
        must obtain SNOPT from Standford Business Software to compile those
        components.</p></div><p>There is an extensive set of sample scripts illustrating optimal
      control functionality distributed with GMAT in the folder
      <code class="filename">samples/OptimalControl/</code>. (Note: For CSALT C++
      examples and tutorials see the user guide referenced above.) A few
      scripts of particular interest are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Low-thrust Interplanetary Transfer: See</p><p><code class="filename">samples/OptimalControl/Ex_R2020a_EarthToMarsSOI_C3Eq0_CSALTTutorial.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Launch Model: See</p><p><code class="filename">samples/OptimalControl/Ex_R2020a_PCLaunch_ConstrainedC3AndDLA_EarthLaunch_EarthOrigin.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Configuring Transcription to Lobatto IIIa: See</p><p><code class="filename">samples/OptimalControl/Ex_R2020a_Phase_Type_ImplicitRKOrder6.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Integrated Flyby Model: See <code class="filename"></code></p><p><code class="filename">samples/OptimalControl/Ex_R2020a_IntegratedFlyby_MarsFlyby.script</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Thrust Model Configuration: See</p><p><code class="filename">samples/OptimalControl/Ex_R2020a_EMTGSpacecraft_SCOpt_*.script</code></p><p><code class="filename"></code></p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30E05"></a>API</h4></div></div></div><p>GMAT now provides access to many of its internal components from
      Python and Java, and from MATLAB using the Java-based API. This new
      Beta-quality functionality enables interoperablity between NASA's GMAT,
      Copernicus, and MONTE software applications. The user guide for the API
      is written in restructured text (rst) and located in the GMAT
      distribution here:<code class="filename">docs/GMAT_API_UsersGuide.pdf</code>. A
      summary of key API features is shown below.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Users can now load GMAT scripts, modify parameters, and
          execute the loaded script via an API interface.</p></li><li class="listitem"><p>After running a script, users can now access the objects from
          the run and retrieve the results of the run.</p></li><li class="listitem"><p>Users can now create GMAT objects directly via the API.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>Created objects can be configured using a syntax similar
              to GMAT scripting.</p></li><li class="listitem"><p>Object-to-object connections are made by calling an
              initialization function.</p></li><li class="listitem"><p>Initialized objects provide data that matches the
              computations performed by GMAT during a run.</p></li></ul></div></li><li class="listitem"><p>Users can interact with GMAT objects from their platform&rsquo;s
          console interface - that is, from the MATLAB console, a Python
          console (Spyder, ipython, or the default Python console
          application).</p></li><li class="listitem"><p>The API can be accessed using Jupyter notebooks.</p></li><li class="listitem"><p>The GMAT API provides live help during use.</p></li><li class="listitem"><p>Examples of all of these features are included in the api
          folder for the release.</p></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The compiled API resources for Python were built using Python
        3.7 on Windows and Mac, and the platform-supplied Python 3.6 libraries
        on Linux.</p></div><p>There is an extensive set of sample scripts illustrating API
      functionality distributed with GMAT in the folder
      <code class="filename">gmat/api</code>. A few scripts of particular interest
      are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Call GMAT Force Models: See <code class="filename"></code></p><p><code class="filename">gmat/api/Ex_R2020a_CompleteForceModel.*</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Call GMAT Propagators:</p><p>See
            <code class="filename">gmat/api/Ex_R2020a_RangeMeasurement.*</code></p><p><code class="filename"></code></p></li><li class="listitem"><p>Call GMAT Tracking Data Models: See <code class="filename"></code></p><p><code class="filename">samples/OptimalControl/Ex_R2020a_RangeMeasurement.*</code></p><p><code class="filename"></code></p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30E53"></a>Improvements</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>There are several new built-in GMAT functions:
        <code class="function">Pause</code>, <code class="function">SystemTime</code>,
        <code class="function">ConvertTime</code>, <code class="function">Num2str</code>,
        <code class="function">Str2num</code>, <code class="function">RotationMatrix</code>, and
        <code class="function">Sign</code>.</p></li><li class="listitem"><p>There is a preliminary interface to use the GMAT python
        interface and python sockets to bring in raw telemetry data into
        GMAT.</p></li><li class="listitem"><p>A new graphical interface, DynamicDataDisplay, allows display of
        numeric and text data during system execution and can optionally color
        code the data based on user defined constraints.</p></li><li class="listitem"><p>There is a new, minimally tested interface to SNOPT that allows
        users to provide precompiled versions of SNOPT 7.5. Previous versions
        of GMAT had SNOPT compiled directly into the SNOPT plugin, which
        prohibits release.</p></li><li class="listitem"><p>Updates to the beta polyhedral gravity model allow polyhedral
        regions with non-uniform density.</p></li><li class="listitem"><p>The script editor now supports syntax highlighting.</p></li><li class="listitem"><p>A ground station can now be placed on any celestial body (not
        only Earth as in previous releases).</p></li><li class="listitem"><p>When compiling GMAT, dependency configuration is now unified on
        all platforms via a Python 3 script. Previous versions used bash and
        windows .bat files.</p></li><li class="listitem"><p>There are numerous improvements to the GMAT build system: easier
        to incorporate external plugins (e.g. OpenFramesInterface), and CMake
        automatically downloads some dependencies (currently Boost). This
        makes it easier for developers to compile GMAT.</p></li><li class="listitem"><p>The SPAD SRP model supports improved interpolation.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30E8B"></a>Compatibility Changes</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The <span class="guilabel">BatchEstimatorInv</span> resource has been
        renamed <span class="guilabel">BatchEstimator</span>.
        <span class="guilabel">BatchEstimatorInv</span> will continue to be recognized
        for R2020a but will be deprecated in the next major release.</p></li><li class="listitem"><p>The structure of the <span class="guilabel">BatchEstimator</span> MATLAB
        data file has changed. See the <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a> resource documentation for details.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30EA5"></a>Fixed &amp; Known Issues</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30EA8"></a>Fixed Issues</h4></div></div></div><p>Over 150 bugs were closed in this release. See the <a class="link" href="http://bugs.gmatcentral.org/issues/?filter=14801" target="_top">"Critical
      Issues Fixed in R2020a" report</a> for a list of critical bugs and
      resolutions in R2020a. See the <a class="link" href="http://bugs.gmatcentral.org/issues/?filter=14804" target="_top">"Minor
      Issues Fixed for R2020a"</a> report for minor issues addressed in
      R2020a. Some fixes in R2020a include:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The simulator now accounts for occultation of the central
            body of the orbit when other than the Earth. (GMT-6134).</p></li><li class="listitem"><p>An error affecting the ability to simulate data for
            durations longer than three weeks has been corrected.
            (GMT-6649)</p></li><li class="listitem"><p>Solve-for parameters may now be specified in any order on
            the <span class="guilabel">Spacecraft.SolveFors</span> field.
            (GMT-6658)</p></li><li class="listitem"><p>The <span class="guilabel">BatchEstimator</span>
            <span class="guilabel">ResetBestRMSIfDiverging</span> setting now works.
            (GMT-7036)</p></li><li class="listitem"><p>The MarsGRAM atmosphere model now works correctly for Mac
            and Linux users. (GMT-5044)</p></li><li class="listitem"><p>GMAT now properly interpolates UT1-UTC on the day of a leap
            second. (GMT-5954)</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30ED3"></a>Known Issues</h4></div></div></div><p>See the <a class="link" href="http://bugs.gmatcentral.org/issues/?filter=14802&amp;jql=affectedVersion%20in%20(R2019a%2CR2020a)" target="_top">"All
      Known Issues for R2020a" report</a> for a list of all known issues in
      R2020a.</p><p>There are several known issues in this release that we consider to
      be significant:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td>GMT-5417</td><td>Adaptive step size control behaves inconsistently when
                used in GMAT's navigation system. Fixed-step integration is
                currently required for simulation and estimation.</td></tr><tr><td>GMT-6202</td><td>Spikes of up to 1 mm/sec may be observed in some cases
                in <span class="guilabel">DSN_TCP</span> and
                <span class="guilabel">Doppler</span> ionospheric corrections. The
                IRI2007 model has some jumps in the electron density when
                moving through time. Spikes are caused when the start and end
                signal paths are located on different sides of these
                jumps.</td></tr><tr><td>GMT-7207</td><td>The GMAT python interface is only designed to work with
                a standard python installation from <code class="uri">python.org</code>.
                (For Mac and Windows, we recommend use of Python 3.7. For
                Linux, we recommend the use of Python 3.6 or 3.7). In the
                future, we would like to allow the user the choice to install
                either <code class="uri">python.org</code> python or Anaconda python. Note
                that preliminary testing shows that, for Windows, the user may
                be able to use the Anaconda python interface by (1) deleting
                system variable references to <code class="uri">python.org</code> python and
                (2) creating the System Variable <code class="varname">PYTHONHOME</code>
                and setting it to the folder where python.exe resides. Note
                that this is not fully tested and it is not known if this can
                cause other problems.</td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30F0F"></a>Appendix: Screen Captures of Selected New Graphics
      Capablity</h4></div></div></div><p>The images below illustrate new graphics capability in GMAT
      R2020a.</p><div class="figure"><a name="R2020a_OFI_SensorCones"></a><p class="title"><b>Figure&nbsp;121.&nbsp;Visualization of sensor cones.</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="60%"><tr><td align="center"><img src="../files/images/Resource_OFI_SensorCones.png" align="middle" height="357" alt="Visualization of sensor cones."></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="R2020a_OFI_MoonRise"></a><p class="title"><b>Figure&nbsp;122.&nbsp;Earth rise from the Moon.</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="70%"><tr><td align="center"><img src="../files/images/Resource_OFI_EarthRise.png" align="middle" height="370" alt="Earth rise from the Moon."></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="R2020a_OFI_Shadows"></a><p class="title"><b>Figure&nbsp;123.&nbsp;Rendering of complex bodies with shadows.</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="70%"><tr><td align="center"><img src="../files/images/Resource_OFI_CometShadows.png" align="middle" height="457" alt="Rendering of complex bodies with shadows."></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="R2020a_OFI_MOCGraphics"></a><p class="title"><b>Figure&nbsp;124.&nbsp;Near real-time graphics example with inertial and body views
        and dynamic data display.</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="50%"><tr><td align="center"><img src="../files/images/Resource_OFI_NearRealTimeMOCGraphics.png" align="middle" height="341" alt="Near real-time graphics example with inertial and body views and dynamic data display."></td></tr></table></div></div></div></div><br class="figure-break"></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ReleaseNotesR2022a.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ReleaseNotes.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReleaseNotesR2018a.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">GMAT R2022a Release Notes&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;GMAT R2018a Release Notes</td></tr></table></div></body></html>