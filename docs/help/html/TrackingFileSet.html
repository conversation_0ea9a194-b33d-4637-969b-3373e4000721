<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>TrackingFileSet</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="SpacecraftNavigation.html" title="Spacecraft Navigation"><link rel="next" href="Transmitter.html" title="Transmitter"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">TrackingFileSet</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="SpacecraftNavigation.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Transmitter.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="TrackingFileSet"></a><div class="titlepage"></div><a name="N2A1D0" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">TrackingFileSet</span></h2><p>TrackingFileSet &mdash; Manages the observation data contained in one or more external
    tracking data files.</p></div><div class="refsection"><a name="N2A1E1"></a><h2>Description</h2><p>A <span class="guilabel">TrackingFileSet</span> is required for both
    simulator and estimator runs. For a data simulation run, the user must
    specify the desired tracking strings for the simulated data (via
    <span class="guilabel">AddTrackingConfig</span>) and provide an output file name
    for the simulated tracking observations (via
    <span class="guilabel">FileName</span>). In simulation mode, the user may specify a
    range modulo constant, Doppler count interval, and other parameters,
    depending on the type of tracking data being simulated. See the remarks
    below for more details. For both the simulator and estimator, measurement
    and media corrections may optionally be applied.</p><p>When running the estimator, the <span class="guilabel">FileName</span>
    parameter specifies the path to a pregenerated external tracking data
    file. It is not necessary to explicitly specify tracking configurations
    when running the estimator; GMAT will examine the specified external
    tracking data file and attempt to determine the tracking configurations
    automatically. GMAT will throw an error message if it is unable to
    uniquely identify all objects found in the tracking data file.</p><p>When running the estimator, one or more
    <span class="guilabel">AcceptFilter</span>s and/or
    <span class="guilabel">RejectFilter</span>s may be employed to select from all
    available observations a smaller subset for use in the estimation
    process.</p><p>See Also: <a class="xref" href="Simulator.html" title="Simulator"><span class="refentrytitle">Simulator</span></a>, <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a>, <a class="xref" href="AcceptFilter.html" title="AcceptFilter"><span class="refentrytitle">AcceptFilter</span></a>, <a class="xref" href="RejectFilter.html" title="RejectFilter"><span class="refentrytitle">RejectFilter</span></a>, <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a></p></div><div class="refsection"><a name="N2A20C"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">AberrationCorrection</span></td><td><p>Apply an aberration correction to angle measurements.
            This correction applies only to the angle measurement
            types.</p><p>The Diurnal correction accounts for the
            velocity of the observer due to rotation of the central body only.
            The Annual correction accounts for the velocity of the observer
            due to central body orbital motion only. The AnnualAndDiurnal
            correction accounts for both effects.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>None, Annual, Diurnal, AnnualAndDiurnal</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AddTrackingConfig</span></td><td><p>One or more signal paths and measurement types for
            simulation or estimation. See the Remarks section below for
            details on the Tracking Strand specification. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>{{Tracking Strand}, MeasurementType1[,
                    MeasurementType2, ...]}</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DataFilters</span></td><td><p>Defines filters to be applied to the data. One or
            more filters of either type (<span class="guilabel">AcceptFilter</span>,
            <span class="guilabel">RejectFilter</span>) may be specified. Rules
            specified by data filters on a
            <span class="guilabel">TrackingFileSet</span> are applied to determine what
            data is admitted or rejected as input to the estimation
            process.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>User defined instances of
                    <span class="guilabel">AcceptFilter</span> and
                    <span class="guilabel">RejectFilter</span> resources</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FileName</span></td><td><p>For simulation, specifies an output file for the
            simulated measurement data. For estimation, specifies one or more
            preexisting tracking data input files in
            GMD-format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file path</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MaxCentralAngleOfRayPath</span></td><td><p>For space-to-space tracking only. Specifies an angle,
            with apex at the center of the central body, between the tracking
            and target satellite. If the participants in a space-to-space
            tracking measurement (like TDRS tracking) are within this angle of
            separation, the measurement will be accepted regardless of the
            computed height of ray path. If the participants are separated by
            an angle greater than this value, the
            <span class="guilabel">MinHeightOfRayPath</span> criteria is applied to
            determine acceptance. This parameter is typically used in
            conjunction with <span class="guilabel">MinHeightOfRayPath</span> to
            specify criteria for rejection of space-to-space tracking signals
            that may experience excessive atmospheric refraction.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 &lt;= MaxCentralAngleOfRayPath &lt;= 180</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">70 degrees, for Earth-centered
                    orbits</span></p><p>This constraint is ignored by default for orbits
                    around bodies other than the Earth, see Remarks
                    below.</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Degrees</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MinHeightOfRayPath</span></td><td><p>For space-to-space tracking only. Specifies a minimum
            altitude of the space-to-space tracking signal path above the
            surface of the Earth (assuming a spherical model using the
            equatorial radius) for which the measurement will be accepted.
            This parameter is only tested if the computed central angle of ray
            path exceeds <span class="guilabel">MaxCentralAngleOfRayPath</span>. Any
            measurements rejected due to this criteria are flagged with the
            HORP editing indicator in the estimator output
            report.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">500 km, for Earth-centered
                    orbits</span></p><p>This constraint is ignored by default for orbits
                    around bodies other than the Earth, see Remarks
                    below.</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kilometers</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RampTable</span></td><td><p>Specifies a transmit frequency ramp table to be used
            when computing measurements for both simulation and
            estimation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file path</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SimDopplerCountInterval</span></td><td><p>Specifies the Doppler count interval used for
            simulating Doppler and range-rate measurements. This parameter is
            not used when running estimation. In estimation, the Doppler count
            interval should already be specified in the GMD
            file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1.0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Seconds</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SimRangeModuloConstant</span></td><td><p>Specifies the value of the DSN range ambiguity
            interval for simulation. This parameter is not used when running
            estimation. In estimation, the range modulo, when needed, should
            already be specified in the GMD file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1.00E+18</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Range Units (RU)</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SimTDRSServiceAccessList</span></td><td><p>TDRS service access list for simulation. A service
            will be chosen randomly from the services provided in the list.
            This parameter is not used when running estimation. In estimation,
            the TDRS service ID should already be specified in the GMD
            file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String list</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>'SA1', 'SA2', or 'MA'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{SA1}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SimTDRSSmarId</span></td><td><p>S-Band Multiple Access Return (SMAR) downlink
            frequency ID for simulation. This parameter is not used when
            running estimation. In estimation, the SMAR ID should already be
            specified in the GMD file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer between 1 and 30 inclusive</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SimTDRSTrackerType</span></td><td><p>Tracker type indicator for simulating TDRS MA
            services. This parameter is not used when running estimation. In
            estimation, the TDRS tracker type should already be specified in
            the GMD file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 = STGT/legacy, 1 = TDRS-K.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">0</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TimeGapForPassBreak</span></td><td><p>Time gap in seconds between measurements to indicate
            the start of a new tracking pass. Consecutive measurements
            separated by greater than this time span are assumed to come from
            different tracking passes. This is used in conjunction with the
            ErrorModel PassBiases solve-fors option. See the <a class="link" href="ErrorModel.html#ErrorModel_Remarks_PassBiases" title="Pass-dependent Bias Estimation">remarks</a> section of
            the ErrorModel resource for more details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1e70</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Seconds</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseETminusTAI</span></td><td><p>Flag specifying if general relativistic time
            corrections should be made to the measurements. If this flag is
            set, GMAT will apply the adjustment from TAI to Ephemeris Time
            when solving the light time equations for the computed
            measurement. </p><p>We strongly recommend that this be set
            to True for any tracking file set that includes tracking strands
            with different transmit and receive nodes, regardless of the total
            light time or distance from the Sun of the measurement path. See
            Remarks below for more details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">False</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseLightTime</span></td><td><p>Flag specifying whether light time corrections should
            be applied to computed measurements. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">True</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseRelativityCorrection</span></td><td><p>Flag specifying if General Relativistic corrections
            should be made to the computed measurements. If this flag is set,
            GMAT will adjust the computed light time to include the effects
            due to the coordinate velocity of light and bending of the signal
            path. See Remarks below for more details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>True, False</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">False</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2A512"></a><h2>Remarks</h2><p>See <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a> for a detailed listing of
    all tracking data types and tracking data file formats supported by GMAT
    for orbit determination.</p><p>Setting <span class="guilabel">UseETminusTAI</span> to True corresponds to
    inclusion in the computed round-trip light time of the ET-TAI uplink and
    downlink terms in Eq. 11-7 of Moyer, <span class="emphasis"><em>Formulation of Observed and
    Computed Values of Deep Space Network Data Types for
    Navigation</em></span>, JPL Publication 00-7, October 2000. When choosing
    to formulate light-time in the Solar System Barycentric frame, as GMAT
    does, the ET-TAI correction mostly cancels out for measurement paths
    having the same transmit and receive station. However, if the transmit and
    receive stations differ, even for short light times, the ET-TAI correction
    may be significant and should be included for highest-accuracy measurement
    processing.</p><p>Setting <span class="guilabel">UseRelativityCorrection</span> to True
    corresponds to inclusion of the RLT uplink and downlink terms in Eq. 11-7
    of Moyer.</p><p>The <span class="guilabel">SimRangeModuloConstant</span> field is used only
    in the simulation of DSN range tracking data. The user may specify a value
    to be used for this field or may omit it, in which case the default value
    is used. This field is not applicable to estimation. In estimation, this
    value is provided in the input tracking data file.</p><p>The <span class="guilabel">SimDopplerCountInterval</span> is used in the
    simulation of DSN_TCP and RangeRate tracking data. The user may specify a
    value to be used for this field or may omit it, in which case default
    value of 1 second is used. This field is not applicable to estimation. In
    estimation, this value is provided in the input tracking data file.</p><div class="refsection"><a name="N2A531"></a><h3>Ray Path Editing Criteria</h3><p>The default values of the constraints pertaining to Height of Ray
      Path (HORP) measurement editing, <span class="guilabel">MinHeightOfRayPath</span>
      and <span class="guilabel">MaxCentralAngleOfRayPath</span>, apply for
      Earth-centered orbits only. By default, no HORP constraints are applied
      for space-to-space tracking involving spacecraft orbiting bodies other
      than the Earth. The user can apply HORP constraints for non-Earth
      centered orbits by specifying explicit values for the constraints in
      their script. To disable use of the HORP constraint for Earth orbits,
      set <span class="guilabel">MinHeightOfRayPath</span> to 0 km.</p><p>The ray path editing criteria described above are applied
      identically for both simulation and estimation. In the case of
      simulation, the criteria determine whether a simulated measurement will
      be written to the tracking data file or ignored. In the case of
      estimation, the criteria determine whether a measurement will be
      accepted or rejected from the state update in either the batch estimator
      or Kalman filter.</p></div><div class="refsection"><a name="N2A541"></a><h3>Tracking Strand Specification</h3><p>When simulating tracking data, at least one tracking strand must
      be specified using the <span class="guilabel">AddTrackingConfig</span> parameter.
      The tracking strand must be enclosed within curly braces. The format of
      the tracking strand depends on the measurement data type being
      generated. Use the following table as a guide. It is not necessary to
      specify tracking strands when estimating using real tracking data. In
      this case, GMAT will examine the input tracking data file and
      automatically determine the tracking strands present in the file.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>In both simulation and estimation, the error model on the
        receiving station determines the measurement properties (bias and
        noise) and the transmit station error model properties are
        ignored.</p></div><div class="informaltable"><table border="1"><colgroup><col width="36%"><col width="64%"></colgroup><thead><tr><th align="center">Measurement Type Name</th><th align="center">Tracking Strand Specification
              Format</th></tr></thead><tbody><tr><td>All angle types, Range_Skin, DSN_SeqRange, DSN_PNRange,
              DSN_TCP</td><td align="left">{XmitGroundStation, Spacecraft,
              RecvGroundStation}</td></tr><tr><td>Range, RangeRate (ground to space)</td><td align="left">{XmitGroundStation, Spacecraft,
              RecvGroundStation}</td></tr><tr><td>Range, RangeRate (space to space)</td><td align="left">{XmitSpacecraft, Spacecraft,
              RecvSpacecraft}</td></tr><tr><td>BRTS_Doppler, BRTS_Range</td><td align="left">{XmitGroundStation, ForwardTDRS,
              BRTSGroundStation, ReturnTDRS, RecvGroundStation}</td></tr><tr><td>GPS_PosVec</td><td>{Spacecraft.Receiver}</td></tr><tr><td>SN_Doppler, SN_Range</td><td>{XmitGroundStation, ForwardTDRS, SNUserSpacecraft,
              ReturnTDRS, RecvGroundStation}</td></tr><tr><td>SN_Doppler_Rtn</td><td>{SNUserSpacecraft, ReturnTDRS, RecvGroundStation}</td></tr><tr><td>SN_DOWD</td><td>{SNUserSpacecraft, ReturnTDRS1, RecvGroundStation1,
              ReturnTDRS2, RecvGroundStation2}</td></tr></tbody></table></div></div><div class="refsection"><a name="N2A588"></a><h3>Measurement Corrections</h3><p>The <span class="guilabel">TrackingFileSet</span> object is used to specify
      various corrections GMAT may apply when deriving a computed measurement.
      Some corrections are not applicable to some measurement types. The
      aberration correction is only applicable to angle measurement types, and
      no corrections are applicable to GPS_PosVec. Selecting an inapplicable
      correction will not result in an error, but no correction will be
      applied.</p></div></div><div class="refsection"><a name="N2A590"></a><h2>Examples</h2><div class="informalexample"><p>This example illustrates use of the
      <span class="guilabel">TrackingFileSet</span> object for simulation of DSN
      tracking data. Specification of the tracking configurations
      (<span class="guilabel">AddTrackingConfig</span>) is optional when running the
      estimator. If omitted, GMAT will attempt to automatically detect the
      tracking configurations present in the tracking data file.</p><p>In this example, the frequency ramp table file
      <code class="literal">dsn.ramp</code> must be a preexisting ramp table. GMAT will
      not simulate ramp table records. Alternatively, the user may omit
      specification of a ramp table when simulating data. If the ramp table is
      omitted, the simulator will use the frequency specified on the
      <span class="guilabel">Transmitter</span> object attached to each
      <span class="guilabel">GroundStation</span>.</p><pre class="programlisting">Create TrackingFileSet dsnObs;

%Create objects referenced by dnsObs
Create GroundStation GDS CAN MAD;
Create Spacecraft EstSat;
Create AcceptFilter af;
  
dsnObs.AddTrackingConfig       = {{GDS, EstSat, GDS}, 'DSN_TCP'};
dsnObs.AddTrackingConfig       = {{CAN, EstSat, CAN}, 'DSN_TCP'};
dsnObs.AddTrackingConfig       = {{MAD, EstSat, MAD}, 'DSN_TCP', 'DSN_SeqRange'};
dsnObs.FileName                = {'dsn.gmd'};
dsnObs.RampTable               = {'dsn.ramp'};
dsnObs.UseLightTime            = True;
dsnObs.UseRelativityCorrection = False;
dsnObs.UseETminusTAI           = False;
dsnObs.SimRangeModuloConstant  = 67108864;
dsnObs.SimDopplerCountInterval = 10.; 
dsnObs.DataFilters             = {af};

BeginMissionSequence;</pre></div><div class="informalexample"><p>This example illustrates use of the
      <span class="guilabel">TrackingFileSet</span> object for simulation of GPS_PosVec
      tracking data. This example assumes that <code class="literal">GpsReceiver</code>
      is a previously created instance of <span class="guilabel">Receiver</span> and
      has been attached to <code class="literal">SimSat</code> using the
      <span class="guilabel">AddHardware</span> method.</p><pre class="programlisting">Create TrackingFileSet PosVecObs;

PosVecObs.FileName          = {'posvec_obs.gmd'};
PosVecObs.AddTrackingConfig = {{SimSat.GpsReceiver}, 'GPS_PosVec'};
SimMeas.DataFilters         = {};

BeginMissionSequence;</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="SpacecraftNavigation.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Transmitter.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Spacecraft Navigation&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Transmitter</td></tr></table></div></body></html>