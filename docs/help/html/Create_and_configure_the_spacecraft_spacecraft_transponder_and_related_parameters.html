<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create and configure the spacecraft, spacecraft transponder, and related parameters</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data"><link rel="prev" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data"><link rel="next" href="Create_and_configure_the_Ground_Station_and_related_parameters.html" title="Create and configure the Ground Station and related parameters"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Create_and_configure_the_Ground_Station_and_related_parameters.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters"></a>Create and configure the spacecraft, spacecraft transponder, and
    related parameters</h2></div></div></div><p>For this tutorial, you&rsquo;ll need GMAT open, with a new empty script
    open. To create a new script, click <span class="guibutton">New Script</span>,
    (<span class="inlinemediaobject"><img src="../files/images/icons/NewScript.png" align="middle" height="11"></span>)</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1407E"></a>Create a satellite and set its epoch and Cartesian
      coordinates</h3></div></div></div><p>Since this is a Sun-orbiting spacecraft, we choose to represent
      the orbit in a Sun-centered coordinate frame which we define using the
      scripting below.</p><pre class="programlisting">%  Create the Sun-centered J2000 frame.
Create CoordinateSystem SunMJ2000Eq;
SunMJ2000Eq.Origin = Sun;
SunMJ2000Eq.Axes   = MJ2000Eq;  %Earth mean equator axes</pre><p>Next, we create a new spacecraft, <span class="guilabel">Sat</span>, and
      set its epoch and Cartesian coordinates.</p><pre class="programlisting">Create Spacecraft Sat;
Sat.DateFormat       = UTCGregorian;
Sat.CoordinateSystem = SunMJ2000Eq;
Sat.DisplayStateType = Cartesian;
Sat.Epoch            = 19 Aug 2015 00:00:00.000;
Sat.X                = -126544968
Sat.Y                =  61978514
Sat.Z                =  24133221
Sat.VX               = -13.789
Sat.VY               = -24.673
Sat.VZ               = -10.662

Sat.Id               = 11111;</pre><p>Note that, in addition to setting <span class="guilabel">Sat&rsquo;s</span>
      coordinates, we also assigned it an ID number. This is the number that
      will be written to the GMAT Measurement Data (GMD) file that we will
      discuss later.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N14091"></a>Create a Transponder object and attach it to our
      spacecraft</h3></div></div></div><p>To simulate navigation measurements for a given spacecraft, GMAT
      requires that a <span class="guilabel">Transponder</span> object, which receives
      the ground station uplink signal and re-transmits it, typically, to a
      ground station, be attached to the spacecraft. Below, we create the
      <span class="guilabel">Transponder</span> object and attach it to our
      spacecraft.</p><pre class="programlisting">Create Antenna HGA;

Create Transponder SatTransponder;
SatTransponder.PrimaryAntenna      = HGA;
SatTransponder.HardwareDelay       = 1e-06; %seconds
SatTransponder.TurnAroundRatio     = '880/749';

Sat.AddHardware                    = {SatTransponder, HGA};</pre><p>After we create the <span class="guilabel">Transponder</span> object, there
      are three fields, <span class="guilabel">PrimaryAntenna</span>,
      <span class="guilabel">HardwareDelay</span>, and
      <span class="guilabel">TurnAroundRatio</span> that must be set.</p><p>The <span class="guilabel">PrimaryAntenna</span> is the antenna that the
      spacecraft transponder, <span class="guilabel">SatTransponder</span>, uses to
      receive and retransmit RF signals. In the example above, we set this
      field to <span class="guilabel">HGA</span> which is an
      <span class="guilabel">Antenna</span> object we have created. Currently the
      <span class="guilabel">Antenna</span> resource has no function but in a future
      release, it may have a function. <span class="guilabel">HardwareDelay</span>, the
      transponder signal delay in seconds, is set to one micro-second. We set
      <span class="guilabel">TurnAroundRatio</span>, which is the ratio of the
      retransmitted to the input signal, to '880/749.' See the
      <span class="guilabel">FRC-21_RunSimulator </span>Help and <span class="emphasis"><em><a class="xref" href="Appendix_A_Determination_of_Measurement_Noise_Values.html" title="Appendix A &ndash; Determination of Measurement Noise Values">Appendix A &ndash; Determination of Measurement Noise Values</a></em></span> for a discussion on how GMAT uses
      this input field. As described in the Help, if our DSN data does not use
      a ramp table, this turn around ratio is used directly to calculate the
      Doppler measurements.</p><p>Note that in the last script command above, we attach our newly
      created <span class="guilabel">Transponder</span> and its related
      <span class="guilabel">Antenna</span> object to our spacecraft,
      <span class="guilabel">Sat</span>.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Create_and_configure_the_Ground_Station_and_related_parameters.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and configure the Ground Station and related
    parameters</td></tr></table></div></body></html>