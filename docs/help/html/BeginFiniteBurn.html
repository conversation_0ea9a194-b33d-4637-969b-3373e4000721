<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>BeginFiniteBurn</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18s02.html" title="Commands"><link rel="prev" href="BeginFileThrust.html" title="BeginFileThrust"><link rel="next" href="EndFileThrust.html" title="EndFileThrust"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">BeginFiniteBurn</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="BeginFileThrust.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="EndFileThrust.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="BeginFiniteBurn"></a><div class="titlepage"></div><a name="N221CF" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">BeginFiniteBurn</span></h2><p>BeginFiniteBurn &mdash; Model finite thrust maneuvers</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">BeginFiniteBurn</code> <em class="replaceable"><code>aFiniteBurn</code></em>(<em class="replaceable"><code>aSpacecraft</code></em>)</pre><pre class="synopsis"><code class="literal">EndFiniteBurn</code> <em class="replaceable"><code>aFiniteBurn</code></em>(<em class="replaceable"><code>aSpacecraft</code></em>)</pre></div><div class="refsection"><a name="N221F7"></a><h2>Description</h2><p>When you apply a <span class="guilabel">BeginFiniteBurn</span> command, you
    turn on the thruster configuration given in the specified
    <span class="guilabel">FiniteBurn</span> model. Similarly, when you apply an
    <span class="guilabel">EndFiniteBurn</span> command, you turn off the thruster
    configuration in the specified <span class="guilabel">FiniteBurn</span> model.
    After GMAT executes a <span class="guilabel">BeginFiniteBurn</span> command, all
    propagation for the spacecraft affected by the
    <span class="guilabel">FiniteBurn</span> object will include the configured finite
    thrust in the dynamics until an <span class="guilabel">EndFiniteBurn</span> line is
    executed for that configuration. In order to apply a non-zero finite burn
    , there must be a <span class="guilabel">Propagate</span> command between the
    <span class="guilabel">BeginFiniteBurn</span> and
    <span class="guilabel">EndFiniteBurn</span> commands.</p><p>To apply the <span class="guilabel">BeginFiniteBurn</span> and
    <span class="guilabel">EndFiniteBurn</span> commands, a
    <span class="guilabel">FiniteBurn</span> object must be configured. This object
    requires the configuration of <span class="guilabel">ChemicalTank</span> and
    <span class="guilabel">ChemicalThruster</span> models. See the Remarks section and the
    examples below for a more detailed explanation.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a>, <a class="xref" href="Thruster.html" title="ChemicalThruster"><span class="refentrytitle">ChemicalThruster</span></a>, <a class="xref" href="FuelTank.html" title="ChemicalTank"><span class="refentrytitle">ChemicalTank</span></a>, <a class="xref" href="FiniteBurn.html" title="FiniteBurn"><span class="refentrytitle">FiniteBurn</span></a></p></div><div class="refsection"><a name="N2223B"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="40%"><col width="60%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">BeginFiniteBurn - Burn</span></td><td><p>Specifies the <span class="guilabel">FiniteBurn</span> object
            activated by the <span class="guilabel">BeginFiniteBurn</span> command.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">FiniteBurn</span> resource</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultFB</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">BeginFiniteBurn -
            SpacecraftList</span></td><td><p>Specifies the <span class="guilabel">Spacecraft</span>
            (currently only a single <span class="guilabel">Spacecraft</span> can be in
            this list) acted upon by the <span class="guilabel">BeginFiniteBurn</span>
            command. The <span class="guilabel">Spacecraft</span> listed in
            <span class="guilabel">SpacecraftList</span> will have thrusters activated
            according to the configuration of the
            <span class="guilabel">FiniteBurn</span> object defined by the
            <span class="guilabel">Burn</span> field. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraf</span>t Objects</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultSC</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EndFiniteBurn - Burn</span></td><td><p>Specifies the <span class="guilabel">FiniteBurn</span> object
            de-activated by the <span class="guilabel">EndFiniteBurn</span> command.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">FiniteBurn</span> Object</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultFB</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EndFiniteBurn - SpacecraftList</span></td><td><p>Specifies the <span class="guilabel">Spacecraft</span>
            (currently only a single <span class="guilabel">Spacecraft</span> can be in
            this list) acted upon by the <span class="guilabel">EndFiniteBurn</span>
            command. <span class="guilabel">Spacecraft</span> listed in
            <span class="guilabel">SpacecraftList</span> will have thrusters
            de-activated according to the configuration of the
            <span class="guilabel">FiniteBurn</span> object defined by the
            <span class="guilabel">Burn</span> field. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>
                      <span class="guilabel">Spacecraft</span>
                    </p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spacecraft</span> resource</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultSC</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N22339"></a><h2>GUI</h2><p>The <span class="guilabel">BeginFiniteBurn</span> and
    <span class="guilabel">EndFiniteBurn</span> command dialog boxes allow you to
    implement a finite burn by specifying which finite burn model should be
    used and which spacecraft the finite burn should be applied to. The dialog
    boxes for <span class="guilabel">BeginFiniteBurn</span> and
    <span class="guilabel">EndFiniteBurn</span> are shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_FiniteBurn_GUI_1.png" align="middle" height="214"></td></tr></table></div></div><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_FiniteBurn_GUI_2.png" align="middle" height="214"></td></tr></table></div></div><p>Use the <span class="guilabel">Burn</span> menu to select the
    <span class="guilabel">FiniteBurn </span>model for the maneuver. Use the
    <span class="guilabel">Spacecraft</span> text box to select the spacecraft for the
    finite burn. You can either type the spacecraft name in the Spacecraft
    text box or click the <span class="guibutton">Edit</span> button and select the
    spacecraft using the <span class="guilabel">ParameterSelectDialog</span>
    box.</p><p>If you add a <span class="guilabel">BeginFiniteBurn</span> command or
    <span class="guilabel">EndFiniteBurn</span> command to the mission sequence,
    without first creating a <span class="guilabel">FiniteBurn</span> object, GMAT will
    create a default <span class="guilabel">FiniteBurn</span> object called
    <span class="guilabel">DefaultFB</span>. However, you will need to configure the
    required <span class="guilabel">ChemicalTank</span> and <span class="guilabel">ChemicalThruster</span>
    objects required for a <span class="guilabel">FiniteBurn</span> object before you
    can run the mission. See the Remarks section for detailed
    instructions.</p></div><div class="refsection"><a name="N22387"></a><h2>Remarks</h2><div class="refsection"><a name="N2238A"></a><h3>Configuring a Finite Burn</h3><p>To use the <span class="guilabel">BeginFiniteBurn</span> and
      <span class="guilabel">EndFiniteBurn</span> commands in your mission sequence,
      you must configure a <span class="guilabel">FiniteBurn</span> object along with
      <span class="guilabel">ChemicalTank</span> and <span class="guilabel">ChemicalThruster</span> objects
      as shown in the examples below and as described in these steps:</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Create and configure a <span class="guilabel">ChemicalTank</span>
          model.</p></li><li class="listitem"><p>Create a <span class="guilabel">ChemicalThruster</span> model:</p><div class="orderedlist"><ol class="orderedlist" type="a"><li class="listitem"><p>Set the parameters (direction, thrust, specific impulse,
              etc) for the thruster</p></li><li class="listitem"><p>Configure the <span class="guilabel">ChemicalThruster</span> to use the
              <span class="guilabel">ChemicalTank</span> created in Step 1.</p></li></ol></div></li><li class="listitem"><p>Add the <span class="guilabel">ChemicalTank</span> and
          <span class="guilabel">ChemicalThruster</span> created in the previous two steps to
          the <span class="guilabel">Spacecraft</span>.</p></li><li class="listitem"><p>Create a <span class="guilabel">FiniteBurn</span> model and configure
          it to use the <span class="guilabel">ChemicalThruster</span> created in Step
          2.</p></li></ol></div></div><div class="refsection"><a name="N223CD"></a><h3>Initial Thruster Status</h3><p>When you configure the<span class="guilabel"> Spacecraft</span>,
      <span class="guilabel">ChemicalTank</span>, <span class="guilabel">ChemicalThruster</span>, and
      <span class="guilabel">FiniteBurn</span> objects, GMAT initializes these objects
      with the thrusters turned off, so that no finite burns are active. You
      must use the <span class="guilabel">BeginFiniteBurn</span> command to turn on the
      thruster if you want to apply a finite burn during propagation.</p><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>Caution: If GMAT throws the error message &ldquo;Propagator Exception:
        MassFlow is not a known propagation parameter on DefaultSC&rdquo;, then you
        have not configured all of the required models to perform a finite
        burn. See detailed instructions above and examples to configure models
        required by the <span class="guilabel">EndFiniteBurn/BeginFiniteBurn</span>
        commands.</p></div></div><div class="refsection"><a name="N223E7"></a><h3>BeginFiniteBurn and EndFiniteBurn commands are NOT branch
      commands</h3><p>The <span class="guilabel">BeginFiniteBurn</span> and
      <span class="guilabel">EndFiniteBurn</span> commands are NOT branch commands,
      meaning, a <span class="guilabel">BeginFiniteBurn</span> command can exist
      without an <span class="guilabel">EndFiniteBurn</span> command (however, this may
      result in depleting all the fuel in the spacecraft model). For behavior
      when fuel mass is fully depleted during a finite burn see the
      <span class="guilabel">ChemicalTank</span> object.</p><p>Similarly, since the <span class="guilabel">BeginFiniteBurn</span> and
      <span class="guilabel">EndFiniteBurn</span> commands are used to turn on or off
      the thrusters, applying the same command multiple times in a script
      without its inverse is the same as applying it once. In other words, if
      you do this:</p><pre class="programlisting"><code class="code">BeginFiniteBurn aFiniteBurn(aSat)
BeginFiniteBurn aFiniteBurn(aSat)
BeginFiniteBurn aFiniteBurn(aSat)</code></pre><p>The effect is the same as only applying the
      <span class="guilabel">BeginFiniteBurn</span> command one time. The same holds
      true for the <span class="guilabel">EndFiniteBurn</span> command.</p></div></div><div class="refsection"><a name="N2240E"></a><h2>Examples</h2><div class="informalexample"><p>Perform a finite burn while the spacecraft is between true anomaly
      of 300 degrees and 60 degrees.</p><pre class="programlisting"><code class="code">%  Create objects
Create Spacecraft aSat
Create ChemicalThruster aThruster
Create ChemicalTank aTank
Create FiniteBurn aFiniteBurn
Create Propagator aPropagator

%  Configure the physical objects
aSat.Thrusters        = {aThruster}
aThruster.Tank        = {aTank}
aSat.Tanks            = {aTank}
aFiniteBurn.Thrusters = {aThruster}

BeginMissionSequence

%  Prop to TA = 300 then maneuver until TA = 60
Propagate aPropagator(aSat, {aSat.TA = 300})
BeginFiniteBurn aFiniteBurn(aSat)
Propagate aPropagator(aSat, {aSat.TA = 60})
EndFiniteBurn aFiniteBurn(aSat)</code>   </pre></div><div class="informalexample"><p>Perform a velocity direction maneuver firing the thruster for 2
      minutes.</p><pre class="programlisting"><code class="code">%  Create objects
Create Spacecraft aSat
Create ChemicalThruster aThruster
Create ChemicalTank aTank
Create FiniteBurn aFiniteBurn
Create Propagator aPropagator

%  Configure the physical objects
aThruster.CoordinateSystem = Local
aThruster.Origin = Earth
aThruster.Axes   = VNB
aThruster.ThrustDirection1 = 1
aThruster.ThrustDirection2 = 0
aThruster.ThrustDirection3 = 0

%  Configure the physical objects
aSat.Thrusters    = {aThruster}
aThruster.Tank    = {aTank}
aSat.Tanks        = {aTank}
aFiniteBurn.Thrusters = {aThruster}

BeginMissionSequence

%  Fire thruster for 2 minutes
BeginFiniteBurn aFiniteBurn(aSat)
Propagate aPropagator(aSat, {aSat.ElapsedSecs = 120})
EndFiniteBurn aFiniteBurn(aSat)</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="BeginFileThrust.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="EndFileThrust.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">BeginFileThrust&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;EndFileThrust</td></tr></table></div></body></html>