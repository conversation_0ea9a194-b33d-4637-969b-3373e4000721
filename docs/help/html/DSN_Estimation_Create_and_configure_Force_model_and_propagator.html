<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create and configure Force model and propagator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data"><link rel="prev" href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html" title="Define the types of measurements that will be processed"><link rel="next" href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html" title="Create and configure BatchEstimator object"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create and configure Force model and propagator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="DSN_Estimation_Create_and_configure_Force_model_and_propagator"></a>Create and configure Force model and propagator</h2></div></div></div><p>We now create and configure the force model and propagator that will
    be used for the simulation. For this deep space drift away orbit, we
    naturally choose the Sun as our central body. Since we are far away from
    all the planets, we use point mass gravity models and we include the
    effects of the Sun, Earth, Moon, and most of the other planets. In
    addition, we model Solar Radiation Pressure (SRP) effects and we include
    the effect of general relativity on the dynamics. The script segment
    accomplishing this is shown below.</p><pre class="programlisting">Create ForceModel Fm;
Create Propagator Prop;
Fm.CentralBody             = Sun;
Fm.PointMasses             = {Sun, Earth, Luna, Mars, Saturn, ...
                             Uranus, Mercury, Venus, Jupiter};
Fm.SRP                     = On;
Fm.RelativisticCorrection  = On;
Fm.ErrorControl            = None;
Prop.FM                    = Fm;
Prop.MinStep               = 0;    </pre><p>We set <code class="code">ErrorControl = None</code> because for the current
    release of GMAT, batch estimation requires fixed step numerical
    integration. The fixed step size is given by
    <code class="code">Prop.InitialStepSize</code> which has a default value of 60 seconds.
    For our deep space orbit, the dynamics are slowly changing and this step
    size is not too big. For more dynamic force models, a smaller step size
    may be needed.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Define the types of measurements that will be processed&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and configure BatchEstimator object</td></tr></table></div></body></html>