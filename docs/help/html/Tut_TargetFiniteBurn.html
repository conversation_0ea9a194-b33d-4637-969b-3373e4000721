<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch06s04.html" title="Run the Mission"><link rel="next" href="ch07s02.html" title="Create and Configure Spacecraft Hardware and Finite Burn"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch06s04.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch07s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Tut_TargetFiniteBurn"></a>Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Tut_TargetFiniteBurn.html#N11577">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch07s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s02.html#N115C7">Create a Thruster and a Fuel Tank</a></span></dt><dt><span class="section"><a href="ch07s02.html#N11659">Modify Thruster1 Thrust Coefficients</a></span></dt><dt><span class="section"><a href="ch07s02.html#N1169C">Attach ChemicalTank1 and Thruster1 to DefaultSC</a></span></dt><dt><span class="section"><a href="ch07s02.html#N116F7">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s03.html">Create the Differential Corrector and Target Control
    Variable</a></span></dt><dt><span class="section"><a href="ch07s04.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s04.html#N117B6">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch07s04.html#N117E4">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch07s04.html#N11880">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s05.html">Run the Mission</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s05.html#N119F2">Inspect Orbit View and Message Window</a></span></dt><dt><span class="section"><a href="ch07s05.html#N11A13">Explore the Command Summary Reports</a></span></dt></dl></dd></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Intermediate level</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>45 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Complete Simulating an Orbit and Simple Orbit Transfer</p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Tut_Target_Finite_Burn_to_Raise_Apogee.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N11577"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>One of the most common operational problems in space mission
      design is the design of a finite burn that achieves a given orbital
      goal. A finite burn model, as opposed to the idealized impulsive burn
      model used for preliminary design, is needed to accurately model actual
      spacecraft maneuvers.</p></div><p>In this tutorial, we will use GMAT to perform a finite burn for a
    spacecraft in low Earth orbit. &nbsp;The goal of this finite burn is to achieve
    a certain desired apogee radius. &nbsp;Since the most efficient orbital
    location to affect apoapsis is at periapsis, the first step in this
    tutorial is to propagate the spacecraft to perigee.</p><p>To calculate the duration of the perigee burn needed to achieve a
    desired apogee radius of 12000 km, we must create the appropriate
    targeting sequence. &nbsp;The main portion of the target sequence employs a
    <span class="guilabel">Begin/End FiniteBurn</span> command pair, for a velocity
    direction maneuver, followed by a command to propagate the spacecraft to
    orbit apogee.</p><p>The basic steps of this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Create and configure the <span class="guilabel">Spacecraft</span>
        hardware and <span class="guilabel">FiniteBurn</span> resources</p></li><li class="step"><p>Create the <span class="guilabel">DifferentialCorrector</span> and Target
        Control <span class="guilabel">Variable</span></p></li><li class="step"><p>Configure the Mission Sequence. To do this, we will</p><ol type="a" class="substeps"><li class="step"><p>Create <span class="guilabel">Begin/End FiniteBurn</span> commands
            with default settings.</p></li><li class="step"><p>Create a <span class="guilabel">Target</span> sequence to achieve a
            12000 km apogee radius.</p></li></ol></li><li class="step"><p>Run the mission and analyze the results.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch06s04.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch07s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Run the Mission&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and Configure Spacecraft Hardware and Finite Burn</td></tr></table></div></body></html>