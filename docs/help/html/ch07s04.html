<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Mission Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_TargetFiniteBurn.html" title="Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee"><link rel="prev" href="ch07s03.html" title="Create the Differential Corrector and Target Control Variable"><link rel="next" href="ch07s05.html" title="Run the Mission"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Mission Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch07s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch07s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N117A5"></a>Configure the Mission Sequence</h2></div></div></div><p>Now we will configure a <span class="guilabel">Target</span> sequence to
    solve for the finite burn duration required to raise apogee to
    <code class="literal">12000</code> km. We&rsquo;ll begin by creating the initial
    <span class="guilabel">Propagate</span> command, then the
    <span class="guilabel">Target</span> sequence itself.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N117B6"></a>Configure the Initial Propagate Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>Configure <span class="guilabel">Propagate1</span> to propagate to
          <span class="guilabel">DefaultSC.Earth.Periapsis</span>.</p></li><li class="step"><p>Rename <span class="guilabel">Propagate1</span> to <span class="guilabel">Prop To
          Perigee</span>.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig8"></a><p class="title"><b>Figure&nbsp;7.8.&nbsp;<span class="guilabel">Prop To Perigee </span>Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig8_better.png" align="middle" height="471" alt="Prop To Perigee Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N117E4"></a>Create the Target Sequence</h3></div></div></div><p>Now create the commands necessary to perform the
      <span class="guilabel">Target</span> sequence. <a class="xref" href="ch07s04.html#Tut_TargetFiniteBurn_Fig9_Final_Mission_Sequence" title="Figure&nbsp;7.9.&nbsp;Final Mission Sequence">Figure&nbsp;7.9, &ldquo;Final Mission Sequence&rdquo;</a> illustrates
      the configuration of the <span class="guilabel">Mission</span> tree after we have
      completed the steps in this section. We&rsquo;ll discuss the
      <span class="guilabel">Target</span> sequence after it has been created.</p><div class="figure"><a name="Tut_TargetFiniteBurn_Fig9_Final_Mission_Sequence"></a><p class="title"><b>Figure&nbsp;7.9.&nbsp;Final Mission Sequence</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig9_Final_Mission_Sequence.png" align="middle" height="166" alt="Final Mission Sequence"></td></tr></table></div></div></div></div><br class="figure-break"><p>To create the <span class="guilabel">Target</span> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
          <span class="guilabel">Prop To Perigee</span>, point to <span class="guilabel">Insert
          After</span>, and click <span class="guilabel">Target</span>. This will
          insert two separate commands: <span class="guilabel">Target1</span> and
          <span class="guilabel">EndTarget1</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target1</span> and click
          <span class="guilabel">Rename</span>. Type <span class="guilabel">Raise Apogee</span>
          and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Raise Apogee</span>, point to
          <span class="guilabel">Append</span>, and click <span class="guilabel">Vary</span>.
          Rename the newly created command as <span class="guilabel">Vary Burn
          Duration</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Vary Burn Duration</span>, point to
          <span class="guilabel">Insert After</span>, and click
          <span class="guilabel">BeginFiniteBurn</span>. Rename the newly created
          command as <span class="guilabel">Turn Thruster On</span>.</p></li><li class="step"><p>Complete the <span class="guilabel">Target</span> sequence by inserting
          the commands shown in <a class="xref" href="ch07s04.html#Additional_Target_Sequence_Commands" title="Table&nbsp;7.1.&nbsp;Additional Target Sequence Commands">Table&nbsp;7.1, &ldquo;Additional Target Sequence Commands&rdquo;</a>.</p></li></ol></div><div class="table"><a name="Additional_Target_Sequence_Commands"></a><p class="title"><b>Table&nbsp;7.1.&nbsp;Additional Target Sequence Commands</b></p><div class="table-contents"><table summary="Additional Target Sequence Commands" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Command</th><th>Name</th></tr></thead><tbody><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop BurnDuration</code></strong></td></tr><tr><td><span class="guilabel">EndFiniteBurn</span></td><td><strong class="userinput"><code>Turn Thruster Off</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop To Apogee</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve Apogee Radius =
              12000</code></strong></td></tr></tbody></table></div></div><br class="table-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N11880"></a>Configure the Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure the
      various parts of the <span class="guilabel">Target</span> sequence to do what we
      want.</p><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11888"></a>Configure the Raise Apogee Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Raise Apogee</span> to edit its
            properties.</p></li><li class="step"><p>In the <span class="guilabel">ExitMode</span> list, click
            <span class="guilabel">SaveAndContinue</span>. This instructs GMAT to save
            the final solution of the targeting problem after you run
            it.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N118A1"></a><p class="title"><b>Figure&nbsp;7.10.&nbsp;<span class="guilabel">Raise Apogee</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig10_Raise_Apogee_Command_Configuration.png" align="middle" height="227" alt="Raise Apogee Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N118AF"></a>Configure the Vary Burn Duration Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary Burn Duration</span> to edit
            its properties. We want this command to adjust (or
            &ldquo;<span class="guilabel">Vary</span>&rdquo;) the finite burn duration represented
            by the previously created control variable,
            <span class="guilabel">BurnDuration</span>. To accomplish this, click on
            the <span class="guilabel">Edit</span> button to bring up the
            <span class="guilabel">ParameterSelectDialog</span>. Use the
            <span class="guilabel">ObjectType</span> menu to select the
            <span class="guilabel">Variable</span> object type. The
            <span class="guilabel">ObjectList</span> menu will then display a list of
            user defined variables. Double-click on the variable,
            <span class="guilabel">BurnDuration</span>, so that
            <span class="guilabel">BurnDuration</span> appears in the
            <span class="guilabel">SelectedValues(s)</span> menu. Click the
            <span class="guilabel">OK</span> button to save the changes and return to
            the <span class="guilabel">Vary Burn Duration</span> command menu.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
            <code class="literal">200</code></p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
            <code class="literal">10000</code></p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
            <code class="literal">100</code>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig11_Vary_Burn_Duration_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.11.&nbsp;<span class="guilabel">Vary Burn Duration</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig11_Vary_Burn_Duration_Command_Configuration.png" align="middle" height="297" alt="Vary Burn Duration Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N1190B"></a>Configure the Turn Thruster On Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Turn Thruster On</span> to edit
            its properties. Notice that the command is already set to apply
            <span class="guilabel">FiniteBurn1</span> to the
            <span class="guilabel">DefaultSC</span> spacecraft, so we don&rsquo;t need to
            change anything here.</p></li><li class="step"><p>Click <span class="guilabel">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig12_Turn_Thruster_On_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.12.&nbsp;<span class="guilabel">Turn Thruster On</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig12_Turn_Thruster_On_Command_Configuration.png" align="middle" height="206" alt="Turn Thruster On Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11930"></a>Configure the Prop BurnDuration Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop BurnDuration</span> to edit
            its properties.</p></li><li class="step"><p>We will use the default <span class="guilabel">Parameter</span> value
            of <span class="guilabel">DefaultSC.ElapsedSecs</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace the default
            value with <span class="guilabel">Variable</span>,
            <span class="guilabel">BurnDuration</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig13_Prop_BurnDuration_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.13.&nbsp;<span class="guilabel">Prop BurnDuration</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig13_Prop_BurnDuration_Command_Configuration_better.png" align="middle" height="477" alt="Prop BurnDuration Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11964"></a>Configure the Turn Thruster Off Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Turn Thruster Off</span> to edit
            its properties. Notice that the command is already set to end
            <span class="guilabel">FiniteBurn1</span> as applied to the
            <span class="guilabel">DefaultSC</span> spacecraft, so we don&rsquo;t need to
            change anything here..</p></li><li class="step"><p>Click <span class="guilabel">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig14_Turn_Thruster_Off_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.14.&nbsp;<span class="guilabel">Turn Thruster Off</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig14_Turn_Thruster_Off_Command_Configuration.png" align="middle" height="207" alt="Turn Thruster Off Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N11989"></a>Configure the Prop To Apogee Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop to Apogee</span> to edit its
            properties.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
            <span class="guilabel">DefaultSC.ElapsedSecs</span> with
            <span class="guilabel">DefaultSC.Earth.Apoapsis</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig15_Prop_To_Apogee_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.15.&nbsp;<span class="guilabel">Prop To Apogee</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig15_Prop_To_Apogee_Command_Configuration_better.png" align="middle" height="479" alt="Prop To Apogee Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N119B4"></a>Configure the Achieve Apogee Radius = 12000 Command</h4></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve Apogee Radius =
            12000</span> to edit its properties.</p></li><li class="step"><p>Notice that <span class="guilabel">Goal</span> is set to
            <span class="guilabel">DefaultSC.Earth.RMAG</span>. This is what we need,
            so we make no changes here.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type
            <code class="literal">12000</code></p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes</p></li></ol></div><div class="figure"><a name="Tut_TargetFiniteBurn_Fig16_Achieve_Apogee_Radius_12000_Command_Configuration"></a><p class="title"><b>Figure&nbsp;7.16.&nbsp;<span class="guilabel">Achieve Apogee Radius = 12000</span> Command
          Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_TargetFiniteBurn_Fig16_Achieve_Apogee_Radius_12000_Command_Configuration.png" align="middle" height="217" alt="Achieve Apogee Radius = 12000 Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch07s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_TargetFiniteBurn.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch07s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create the Differential Corrector and Target Control
    Variable&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission</td></tr></table></div></body></html>