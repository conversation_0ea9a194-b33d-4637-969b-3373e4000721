<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Command-Line Usage</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch24.html#N2D3E2" title="System Level Components"><link rel="prev" href="CalculationParameters.html" title="Calculation Parameters"><link rel="next" href="KeyboardShortcuts.html" title="Keyboard Shortcuts"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Command-Line Usage</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="CalculationParameters.html">Prev</a>&nbsp;</td><th align="center" width="60%">System Level Components</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="KeyboardShortcuts.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="CommandLine"></a><div class="titlepage"></div><a name="N2FE39" class="indexterm"></a><a name="N2FE3C" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Command-Line Usage</span></h2><p>Command-Line Usage &mdash; Starting the <code class="filename">GMAT</code> application from the
    command line</p></div><div class="refsynopsisdiv"><h2>Synopsis</h2><div class="cmdsynopsis"><p><code class="command">GMAT</code>  [<em class="replaceable"><code>option</code></em>...] [<em class="replaceable"><code>script_file</code></em>]</p></div><div class="cmdsynopsis"><p><code class="command">GMATConsole</code>  [<em class="replaceable"><code>option</code></em>...] [<em class="replaceable"><code>script_file</code></em>]</p></div></div><div class="refsection"><a name="N2FE67"></a><h2>Description</h2><p>The <code class="literal">GMAT</code> command starts the GMAT graphical
    interface. If run with no arguments, GMAT starts with the default mission
    loaded. If <code class="literal"><em class="replaceable"><code>script_file</code></em></code> is
    specified, and is a valid path to a GMAT script, GMAT loads the script and
    remains open, but does not run it. The <code class="literal">GMATConsole</code>
    command starts the GMAT console interface. See below for options supported
    by each interface.</p></div><div class="refsection"><a name="N2FE76"></a><h2>Options</h2><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="option">-b</code>, </span><span class="term"><code class="option">--batch</code></span></dt><dd><p>Runs multiple scripts listed in specified file.</p></dd><dt><span class="term"><code class="option">-h</code>, </span><span class="term"><code class="option">--help</code></span></dt><dd><p>Start GMAT and display command-line usage information in the
          message window if using the GUI version, or in the terminal if using
          the console interface.</p></dd><dt><span class="term"><code class="option">-l &lt;filename&gt;</code>, </span><span class="term"><code class="option">--logfile &lt;filename&gt;</code></span></dt><dd><p>Specify the log file (ignored in Console interactive
          mode.</p></dd><dt><span class="term"><code class="option">-m</code>, </span><span class="term"><code class="option">--minimize</code></span></dt><dd><p>Start GMAT with a minimized interface.</p></dd><dt><span class="term"><code class="option">-ns</code>, </span><span class="term"><code class="option">--no_splash</code></span></dt><dd><p>Start GMAT without the splash screen showing.</p></dd><dt><span class="term"><code class="option">-r &lt;filename&gt;</code>, </span><span class="term"><code class="option">--run &lt;filename&gt;</code></span></dt><dd><p>Automatically run the specified script after loading.</p></dd><dt><span class="term"><code class="option">--save &lt;filename&gt;</code></span></dt><dd><p>Saves current script (interactive mode only).</p></dd><dt><span class="term"><code class="option">--start-server</code></span></dt><dd><p>Starts GMAT Server on start-up (ignored for Console).</p></dd><dt><span class="term"><code class="option">-s &lt;filename&gt;</code>, </span><span class="term"><code class="option">--startup_file &lt;filename&gt;</code></span></dt><dd><p>Specify the startup file (ignored in Console interactive
          mode).</p></dd><dt><span class="term"><code class="option">--summary</code></span></dt><dd><p>Writes command summary (interactive mode only).</p></dd><dt><span class="term"><code class="option">--verbose </code></span></dt><dd><p>Dump info messages to screen during run (default is
          on).</p></dd><dt><span class="term"><code class="option">-v</code>, </span><span class="term"><code class="option">--version</code></span></dt><dd><p>Start GMAT and display version information in the message
          window.</p></dd><dt><span class="term"><code class="option">-x</code>, </span><span class="term"><code class="option">--exit</code></span></dt><dd><p>Exit GMAT after running the specified script. If specified
          with only a script name (i.e. NO &ndash;run option), GMAT simply opens and
          closes.</p></dd></dl></div></div><div class="refsection"><a name="N2FEF2"></a><h2>Precedence Rules</h2><p>Some file locations, the log file for example, can be set in
    multiple locations. The precedence rules are as follows. Command line
    settings have the highest precedence, and those values are always used if
    set. The second precedence is taken by script level settings, for example,
    <code class="literal">GmatGlobal.LogFile = C:\myLog.txt</code>. Finally, if no other
    method is set, the value in the startup file is used.</p><p>There are additional precedence rules that apply when the startup
    file is configured to use <code class="literal">RUN_MODE = TESTING</code>. In that
    case, the log file name from the startup file has precedence, and the
    output path can be overwritten by settings avialalble in the GUI
    <code class="literal">Set File Paths</code> option in the <code class="literal">File</code>
    menu, or in the <code class="literal">Run Scripts</code> option avialable in the
    <code class="literal">Scripts</code> menu in the <code class="literal">Resource
    Tree</code>.</p></div><div class="refsection"><a name="N2FF0E"></a><h2>Examples</h2><div class="informalexample"><p>Start GMAT and run the script
      <code class="filename">MyScript.script</code>:</p><p><strong class="userinput"><code>GMAT MyScript.script</code></strong></p></div><div class="informalexample"><p>Run a script with the interface minimized, and exit
      afterwards:</p><p><strong class="userinput"><code>GMAT --minimize --exit
      MyScript.script</code></strong></p></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="CalculationParameters.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch24.html#N2D3E2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="KeyboardShortcuts.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Calculation Parameters&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Keyboard Shortcuts</td></tr></table></div></body></html>