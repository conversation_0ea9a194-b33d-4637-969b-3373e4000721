<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Reference Guide</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Preface.html" title="Documentation Overview"><link rel="prev" href="pr01s02.html" title="Tutorials"><link rel="next" href="UsingGmat.html" title="Using GMAT"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Reference Guide</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="pr01s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Documentation Overview</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="UsingGmat.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N10095"></a><a class="xref" href="RefGuide.html" title="Reference Guide">Reference Guide</a></h2></div></div></div><p>The <span class="emphasis"><em><a class="xref" href="RefGuide.html" title="Reference Guide">Reference Guide</a></em></span> contains individual topics that
    describe each of GMAT's resources and commands. When you need detailed
    information on syntax or application-specific examples for specific
    features, go here. It also includes system-level references that describe
    the script language syntax, parameter listings, external interfaces, and
    configuration files.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>This document uses two typographical conventions
        throughout:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Graphical user interface (GUI) elements and resource and
            command names are presented in <span class="bold"><strong>bold</strong></span>.</p></li><li class="listitem"><p>Filenames, script examples, and user input are presented in
            <code class="code">monospace</code>.</p></li></ul></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="pr01s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Preface.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="UsingGmat.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Tutorials&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Using GMAT</td></tr></table></div></body></html>