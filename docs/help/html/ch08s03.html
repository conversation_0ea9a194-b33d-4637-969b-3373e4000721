<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Mission Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Mars_B_Plane_Targeting.html" title="Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting"><link rel="prev" href="ch08s02.html" title="Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators, Differential Corrector, Coordinate Systems and Graphics"><link rel="next" href="ch08s04.html" title="Run the Mission with first Target Sequence"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Mission Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch08s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch08s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N1211B"></a>Configure the Mission Sequence</h2></div></div></div><p>Now we will configure first <span class="guilabel">Target</span> sequence to
    solve for the maneuver values required to achieve BdotT and BdotR
    components of the B-vector. BdotT will be targeted to 0 km and BdotR is
    targeted to a non-zero value in order to generate a polar orbit that will
    have an inclination of 90 degrees. To allow us to focus on the first
    <span class="guilabel">Target</span> sequence, we&rsquo;ll assume you have already
    learned how to propagate an orbit by having worked through <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit">Chapter&nbsp;5, <i>Simulating an Orbit</i></a> tutorial.</p><p>The second <span class="guilabel">Target</span> sequence will perform the MOI
    maneuver so that the spacecraft can orbit around Mars, but that sequence
    will be created later.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1212E"></a>Create the First Target Sequence</h3></div></div></div><p>Now create the commands necessary to perform the first
      <span class="guilabel">Target</span> sequence. <a class="xref" href="ch08s03.html#Tut_Mars_B_Plane_Targeting_B_MissionTree_1" title="Figure&nbsp;8.3.&nbsp;Mission Sequence for the First Target sequence">Figure&nbsp;8.3, &ldquo;Mission Sequence for the First Target sequence&rdquo;</a> illustrates the
      configuration of the <span class="guilabel">Mission</span> tree after you have
      completed the steps in this section. We&rsquo;ll discuss the first
      <span class="guilabel">Target</span> sequence after it has been created.</p><div class="figure"><a name="Tut_Mars_B_Plane_Targeting_B_MissionTree_1"></a><p class="title"><b>Figure&nbsp;8.3.&nbsp;Mission Sequence for the First Target sequence</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_MissionTree_1.png" align="middle" height="246" alt="Mission Sequence for the First Target sequence"></td></tr></table></div></div></div></div><br class="figure-break"><p>To create the first <code class="function">Target</code> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>You&rsquo;ll see that there already exists a
          <span class="guilabel">Propagate1</span> command. We need to delete this
          command</p></li><li class="step"><p>Right-click on <span class="guilabel">Propagate1</span> command and
          click <span class="guilabel">Delete</span>.</p></li><li class="step"><p>Right-click on <span class="guilabel">Mission Sequence</span> folder,
          point to <span class="guilabel">Append</span>, and click
          <span class="guilabel">Target</span>. This will insert two separate commands:
          <span class="guilabel">Target1</span> and
          <span class="guilabel">EndTarget1</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target1</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>Type <span class="guilabel">Target desired B-plane Coordinates</span>
          and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target desired B-plane
          Coordinates</span>, point to <span class="guilabel">Append</span>, and
          click <span class="guilabel">Propagate</span>. A new command called
          <span class="guilabel">Propagate1</span> will be created.</p></li><li class="step"><p>Right-click <span class="guilabel">Propagate1</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Prop 3
          Days</span> and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Complete the <span class="guilabel">Target</span> sequence by appending
          the commands in <a class="xref" href="ch08s03.html#Tut_Mars_B_Plane_Targeting_CommandTable" title="Table&nbsp;8.9.&nbsp;Additional First Target Sequence Commands">Table&nbsp;8.9, &ldquo;Additional First <span class="guilabel">Target</span> Sequence
              Commands&rdquo;</a>.</p><div class="table"><a name="Tut_Mars_B_Plane_Targeting_CommandTable"></a><p class="title"><b>Table&nbsp;8.9.&nbsp;Additional First <span class="guilabel">Target</span> Sequence
              Commands</b></p><div class="table-contents"><table summary="Additional First Target Sequence
              Commands" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Command</th><th>Name</th></tr></thead><tbody><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop 12 Days to TCM</code></strong></td></tr><tr><td><span class="guilabel">Vary</span></td><td><strong class="userinput"><code>Vary TCM.V</code></strong></td></tr><tr><td><span class="guilabel">Vary</span></td><td><strong class="userinput"><code>Vary TCM.N</code></strong></td></tr><tr><td><span class="guilabel">Vary</span></td><td><strong class="userinput"><code>Vary TCM.B</code></strong></td></tr><tr><td><span class="guilabel">Maneuver</span></td><td><strong class="userinput"><code>Apply TCM</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop 280 Days</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop to Mars
                    Periapsis</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve BdotT</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve BdotR</code></strong></td></tr></tbody></table></div></div><p><br class="table-break"></p></li></ol></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Let&rsquo;s discuss what the first <span class="guilabel">Target</span>
        sequence does. We know that a maneuver is required to perform the
        B-Plane targeting. We also know that the desired B-Plane coordinate
        values for BdotT and BdotR are 0 and -7000 km, resulting in a polar
        orbit with 90 degree inclination. However, we don&rsquo;t know the size (or
        &#916;V magnitude) and direction of the <span class="guilabel">TCM </span>maneuver
        that will precisely achieve the desired orbital conditions. We use the
        <span class="guilabel">Target</span> sequence to solve for those precise
        maneuver values. We must tell GMAT what controls are available (in
        this case, three controls associated with three components of the TCM
        maneuver) and what conditions must be satisfied (in this case, BdotT
        and BdotR values). You accomplish this by using the
        <span class="guilabel">Vary</span> and <span class="guilabel">Achieve</span> commands.
        Using the <span class="guilabel">Vary</span> command, you tell GMAT what to
        solve for&mdash;in this case, the &#916;V value and direction for
        <span class="guilabel">TCM</span>. You use the <span class="guilabel">Achieve</span>
        command to tell GMAT what conditions the solution must satisfy&mdash;in this
        case, BdotT and BdotR values that result in a 90 degree
        inclination.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1222A"></a>Configure the First Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure various
      parts of the first <span class="guilabel">Target</span> sequence to do what we
      want.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12232"></a>Configure the Target desired B-plane Coordinates Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Target desired B-plane
          Coordinates</span> to edit its properties.</p></li><li class="step"><p>In the <span class="guilabel">ExitMode</span> list, click
          <span class="guilabel">SaveAndContinue</span>. This instructs GMAT to save
          the final solution of the targeting problem after you run it.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N1224B"></a><p class="title"><b>Figure&nbsp;8.4.&nbsp;<span class="guilabel">Target desired B-plane</span> Coordinates Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_TaregetDesired_B_plane_Coordinates.png" align="middle" height="286" alt="Target desired B-plane Coordinates Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12259"></a>Configure the Prop 3 Days Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop 3 Days</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, make sure that
          <span class="guilabel">NearEarth</span> is selected</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.ElapsedDays</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace
          <span class="guilabel">12000.0</span> with <span class="guilabel">3</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N1228A"></a><p class="title"><b>Figure&nbsp;8.5.&nbsp;<span class="guilabel">Prop 3 Days</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Prop 3 Days_better.png" align="middle" height="471" alt="Prop 3 Days Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12298"></a>Configure the Prop 12 Days to TCM Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop 12 Days to TCM</span> to edit
          its properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">DeepSpace</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.ElapsedDays</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace
          <span class="guilabel">12000.0</span> with <span class="guilabel">12</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N122CC"></a><p class="title"><b>Figure&nbsp;8.6.&nbsp;<span class="guilabel">Prop 12 Days</span> to TCM Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Prop 12 Days to TCM_better.png" align="middle" height="471" alt="Prop 12 Days to TCM Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N122DA"></a>Configure the Vary TCM.V Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary TCM.V</span> to edit its
          properties. Notice that the variable in the
          <span class="guilabel">Variable</span> box is
          <span class="guilabel">TCM.Element1</span>, which by default is the velocity
          component of <span class="guilabel">TCM</span> in the local
          Velocity-Normal-Binormal (VNB) coordinate system. That&rsquo;s what we
          need, so we&rsquo;ll keep it.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
          <span class="guilabel">1e-005</span>.</p></li><li class="step"><p>In the <span class="guilabel">Perturbation</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>In the <span class="guilabel">Lower</span> box, type
          <span class="guilabel">-10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
          <span class="guilabel">10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
          <span class="guilabel">0.002</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12320"></a><p class="title"><b>Figure&nbsp;8.7.&nbsp;<span class="guilabel">Vary TCM.V</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Vary TCMV.png" align="middle" height="311" alt="Vary TCM.V Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1232E"></a>Configure the Vary TCM.N Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary TCM.N</span> to edit its
          properties. Notice that the variable in the
          <span class="guilabel">Variable</span> box is still
          <span class="guilabel">TCM.Element1</span>, which by default is the velocity
          component of TCM in the local VNB coordinate system. We need to
          insert <span class="guilabel">TCM.Element2</span> which is the normal
          component of TCM in the local VNB coordinate system. So let&rsquo;s do
          that.</p></li><li class="step"><p>Next to <span class="guilabel">Variable</span>, click the
          <span class="guilabel">Edit</span> button..</p></li><li class="step"><p>Under <span class="guilabel">Object</span> List, click
          <span class="guilabel">TCM</span>.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
          double-click <span class="guilabel">Element2</span> to move it to the
          <span class="guilabel">Selected Value(s)</span> list. See the image below for
          results.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog window</span>.</p></li><li class="step"><p>Notice that the variable in the <span class="guilabel">Variable</span>
          box is now <span class="guilabel">TCM.Element2</span>.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
          <span class="guilabel">1e-005</span>.</p></li><li class="step"><p>In the <span class="guilabel">Perturbation</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>In the <span class="guilabel">Lower</span> box, type
          <span class="guilabel">-10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
          <span class="guilabel">10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
          <span class="guilabel">0.002</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N123A4"></a><p class="title"><b>Figure&nbsp;8.8.&nbsp;<span class="guilabel">Vary TCM.N</span> Parameter Selection</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Vary TCM_N Parameter Select.png" align="middle" height="418" alt="Vary TCM.N Parameter Selection"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N123B2"></a><p class="title"><b>Figure&nbsp;8.9.&nbsp;<span class="guilabel">Vary TCM.N</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Vary TCM_N Command.png" align="middle" height="314" alt="Vary TCM.N Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N123C0"></a>Configure the Vary TCM.B Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary TCM.B</span> to edit its
          properties. Notice that the variable in the
          <span class="guilabel">Variable</span> box is still
          <span class="guilabel">TCM.Element1</span>, which by default is the velocity
          component of TCM. We need to insert
          <span class="guilabel">TCM.Element3</span> which is the bi-normal component
          of TCM in the local VNB coordinate system. So let&rsquo;s do that.</p></li><li class="step"><p>Next to <span class="guilabel">Variable</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>Under <span class="guilabel">Object</span> List, click
          <span class="guilabel">TCM</span>.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
          double-click <span class="guilabel">Element3</span> to move it to the
          <span class="guilabel">Selected Value(s)</span> list. See the image below for
          results.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog window</span>.</p></li><li class="step"><p>Notice that the variable in the <span class="guilabel">Variable</span>
          box is now <span class="guilabel">TCM.Element3</span>.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
          <span class="guilabel">1e-005</span>.</p></li><li class="step"><p>In the <span class="guilabel">Perturbation</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>In the <span class="guilabel">Lower</span> box, type
          <span class="guilabel">-10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
          <span class="guilabel">10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
          <span class="guilabel">0.002</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12436"></a><p class="title"><b>Figure&nbsp;8.10.&nbsp;<span class="guilabel">Vary TCM.B</span> Parameter Selection</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Vary TCM_B Parameter Select.png" align="middle" height="419" alt="Vary TCM.B Parameter Selection"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N12444"></a><p class="title"><b>Figure&nbsp;8.11.&nbsp;<span class="guilabel">Vary TCM.N</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Vary TCM_B Command.png" align="middle" height="311" alt="Vary TCM.N Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12452"></a>Configure the Apply TCM Command</h3></div></div></div><div class="procedure"><ul class="procedure"><li class="step"><p>Double-click <span class="guilabel">Apply TCM</span> to edit its
          properties. Notice that the command is already set to apply the
          <span class="guilabel">TCM</span> burn to the <span class="guilabel">MAVEN</span>
          spacecraft, so we don&rsquo;t need to change anything here.</p></li></ul></div><div class="figure"><a name="N12462"></a><p class="title"><b>Figure&nbsp;8.12.&nbsp;<span class="guilabel">Apply TCM</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Apply TCM Command.png" align="middle" height="231" alt="Apply TCM Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12470"></a>Configure the Prop 280 Days Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop 280 Days</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">DeepSpace</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.ElapsedDays</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace
          <span class="guilabel">12000.0</span> with <span class="guilabel">280</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N124A4"></a><p class="title"><b>Figure&nbsp;8.13.&nbsp;<span class="guilabel">Prop 280 Days</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Prop 280 Days Command_better.png" align="middle" height="473" alt="Prop 280 Days Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N124B2"></a>Configure the Prop to Mars Periapsis Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop to Mars Periapsis</span> to
          edit its properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">NearMars</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.Mars.Periapsis</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N124DA"></a><p class="title"><b>Figure&nbsp;8.14.&nbsp;<span class="guilabel">Prop to Mars Periapsis</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Prop to Mars Periapsis_better.png" align="middle" height="473" alt="Prop to Mars Periapsis Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N124E8"></a>Configure the Achieve BdotT Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve BdotT</span> to edit its
          properties.</p></li><li class="step"><p>Next to <span class="guilabel">Goal</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list, click
          <span class="guilabel">BdotT</span>.</p></li><li class="step"><p>Under <span class="guilabel">Coordinate System</span>, select
          <span class="guilabel">MarsInertial</span> and double-click on
          <span class="guilabel">BdotT</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type
          <span class="guilabel">0</span>.</p></li><li class="step"><p>In the <span class="guilabel">Tolerance</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12531"></a><p class="title"><b>Figure&nbsp;8.15.&nbsp;<span class="guilabel">Achieve BdotT</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Achieve BdotT Command.png" align="middle" height="232" alt="Achieve BdotT Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1253F"></a>Configure the Achieve BdotR Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve BdotR</span> to edit its
          properties.</p></li><li class="step"><p>Next to <span class="guilabel">Goal</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list, click
          <span class="guilabel">BdotR</span>.</p></li><li class="step"><p>Under <span class="guilabel">Coordinate System</span>, select
          <span class="guilabel">MarsInertial</span> and double-click on
          <span class="guilabel">BdotR</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type
          <span class="guilabel">-7000</span>.</p></li><li class="step"><p>In the <span class="guilabel">Tolerance</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12588"></a><p class="title"><b>Figure&nbsp;8.16.&nbsp;<span class="guilabel">Achieve BdotR</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Achieve BdotR Command.png" align="middle" height="229" alt="Achieve BdotR Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch08s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Mars_B_Plane_Targeting.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch08s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission with first Target Sequence</td></tr></table></div></body></html>