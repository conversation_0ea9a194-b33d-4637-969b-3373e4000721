<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Keyboard Shortcuts</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch24.html#N2D3E2" title="System Level Components"><link rel="prev" href="CommandLine.html" title="Command-Line Usage"><link rel="next" href="ScriptLanguage.html" title="Script Language"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Keyboard Shortcuts</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="CommandLine.html">Prev</a>&nbsp;</td><th align="center" width="60%">System Level Components</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ScriptLanguage.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="KeyboardShortcuts"></a><div class="titlepage"></div><a name="N2FF24" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Keyboard Shortcuts</span></h2><p>Keyboard Shortcuts &mdash; Keyboard shortcuts in the graphical user
    interface</p></div><div class="refsection"><a name="N2FF35"></a><h2>Description</h2><p>The GMAT graphical user interface (GUI) offers many keyboard
    shortcuts for easy access to common commands. See the tables below for
    details.</p></div><div class="refsection"><a name="N2FF3A"></a><h2>General shortcuts</h2><p>These keyboard shortcuts are available any time when using
    GMAT.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Key</th><th>Meaning</th></tr></thead><tbody><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>&lt;number&gt;</strong></span></td><td>Open recent script &lt;number&gt; (1&ndash;5).</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>N</strong></span></td><td>Create a new mission.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>N</strong></span></td><td>Create a new empty script.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>O</strong></span></td><td>Open the <span class="guilabel">Open</span> dialog box.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>S</strong></span></td><td>Save the current mission.</td></tr><tr><td><span class="keycap"><strong>F1</strong></span></td><td>Open the Help documentation.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>F1</strong></span></td><td>Open the <span class="guilabel">Welcome Page</span>.</td></tr><tr><td><span class="keycap"><strong>F5</strong></span></td><td>Run the current mission.</td></tr><tr><td><span class="keycap"><strong>F9</strong></span></td><td>Animate the current graphics window.</td></tr><tr><td><span class="keycap"><strong>F12</strong></span></td><td>Open the <span class="guilabel">Save As</span> dialog box.</td></tr></tbody></table></div></div><div class="refsection"><a name="N2FFAA"></a><h2>Tree view shortcuts</h2><p>These keyboard shortcuts are available when navigating the
    Resources, Mission, and Output trees.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Key</th><th>Meaning</th></tr></thead><tbody><tr><td><span class="keycap"><strong>Enter</strong></span></td><td>Open.</td></tr><tr><td><span class="keycap"><strong>Space</strong></span></td><td>Open.</td></tr><tr><td><span class="keycap"><strong>Delete</strong></span></td><td>Delete.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>C</strong></span></td><td>Clone (only available for resources).</td></tr><tr><td><span class="keycap"><strong>F2</strong></span></td><td>Rename.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Page Up</strong></span></td><td>View the next tab.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Page Down</strong></span></td><td>View the previous tab.</td></tr></tbody></table></div></div><div class="refsection"><a name="N2FFF3"></a><h2>Dialog box shortcuts</h2><p>These keyboard shortcuts are available when interacting with dialog
    boxes, such as the property windows for the
    <span class="guilabel">Spacecraft</span> resource or the
    <span class="guilabel">Propagate</span> command.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Key</th><th>Meaning</th></tr></thead><tbody><tr><td><span class="keycap"><strong>Tab</strong></span></td><td>Move to the next item.</td></tr><tr><td><span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>Tab</strong></span></td><td>Move to the previous item.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>C</strong></span></td><td>Copy.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>V</strong></span></td><td>Paste.</td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>W</strong></span></td><td>Close.</td></tr><tr><td><span class="keycap"><strong>F1</strong></span></td><td>Open feature-specific help.</td></tr><tr><td><span class="keycap"><strong>F7</strong></span></td><td>Show script.</td></tr></tbody></table></div></div><div class="refsection"><a name="KeyboardShortcuts_ScriptEditorShortcuts"></a><h2>Script editor shortcuts</h2><p>These keyboard shortcuts are available when using the script
    editor.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><tbody><tr><td><span class="keycap"><strong>Tab</strong></span></td><td><p>Insert a tab character.</p></td></tr><tr><td><span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>Tab</strong></span></td><td><p>Remove a tab character on the current
            line.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Tab</strong></span></td><td><p>Move to the next editor button.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>Tab</strong></span></td><td><p>Move to the previous editor button.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>A</strong></span></td><td><p>Select all.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>C</strong></span></td><td><p>Copy.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>F</strong></span></td><td><p>Open the <span class="guilabel">Find and Replace</span> dialog
            box.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>G</strong></span></td><td><p>Open the <span class="guilabel">Go To</span> dialog
            box.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>H</strong></span></td><td><p>Open the <span class="guilabel">Find and Replace</span> dialog
            box.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>I</strong></span></td><td><p>Indent more.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>I</strong></span></td><td><p>Indent less.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>R</strong></span></td><td><p>Comment the current line.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>S</strong></span></td><td><p>Save,Sync.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>T</strong></span></td><td><p>Uncomment the current line.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>V</strong></span></td><td><p>Paste. </p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>W</strong></span></td><td><p>Close.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>X</strong></span></td><td><p>Cut.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Y</strong></span></td><td><p>Redo.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Z</strong></span></td><td><p>Undo.</p></td></tr><tr><td><span class="keycap"><strong>F3</strong></span></td><td><p>Find next (after using <span class="guilabel">Find and
            Replace</span>)..</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>F5</strong></span></td><td><p>Save,Sync,Run.</p></td></tr><tr><td><span class="keycap"><strong>Ctrl</strong></span>+<span class="keycap"><strong>Shift</strong></span>+<span class="keycap"><strong>F12</strong></span></td><td><p>Save As.</p></td></tr></tbody></table></div><p>Additionally, the following mouse controls are
    available:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Hold down <span class="keycap"><strong>Ctrl</strong></span> while rotating the wheel
          button to increase or decrease the font size.</p></li></ul></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="CommandLine.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch24.html#N2D3E2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ScriptLanguage.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Command-Line Usage&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Script Language</td></tr></table></div></body></html>