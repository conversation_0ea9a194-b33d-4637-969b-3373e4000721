<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Propagate Command</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><link rel="prev" href="ch05s03.html" title="Configure the Propagator"><link rel="next" href="ch05s05.html" title="Run and Analyze the Results"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Propagate Command</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch05s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;5.&nbsp;Simulating an Orbit</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch05s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N1103F"></a>Configure the Propagate Command</h2></div></div></div><p>This is the last step before running the mission. Below you will
    configure a Propagate command to propagate (or simulate the motion of)
    <span class="guilabel">Sat</span> to orbit periapsis.</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Click the <span class="guilabel">Mission</span> tab to display the
        <span class="guilabel">Mission</span> tree.</p></li><li class="listitem"><p>Double-click <span class="guilabel">Propagate1</span>.</p></li><li class="listitem"><p>Under <span class="guilabel">Stopping Conditions</span>, click the
        (<span class="guibutton">...</span>) button to the left of
        <span class="guilabel">Sat.ElapsedSecs</span>. This will display the
        <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="listitem"><p>In the <span class="guilabel">Object List</span> box, click
        <span class="guilabel">Sat</span> if it is not already selected. This directs
        GMAT to associate the stopping condition with the spacecraft
        <span class="guilabel">Sat</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">Object Properties</span> list, double-click
        <span class="guilabel">Periapsis</span> to add it to the <span class="guilabel">Selected
        Values</span> list. This is shown in <a class="xref" href="ch05s04.html#Tut_PropASpacecraft_StopSetUp" title="Figure&nbsp;5.4.&nbsp;Propagate Command ParameterSelectDialog Configuration">Figure&nbsp;5.4, &ldquo;Propagate Command ParameterSelectDialog Configuration&rdquo;</a>.</p><div class="figure"><a name="Tut_PropASpacecraft_StopSetUp"></a><p class="title"><b>Figure&nbsp;5.4.&nbsp;Propagate Command ParameterSelectDialog Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_PropSpacecraft_PropagateCommandParameterSelectDialogConfiguration.png" align="middle" height="416" alt="Propagate Command ParameterSelectDialog Configuration"></div></div></div></div><br class="figure-break"><p></p></li><li class="listitem"><p>Click <span class="guilabel">OK</span>. Your screen should now match
        <a class="xref" href="ch05s04.html#Tut_PropASpacecraft_Propagate1Dialog" title="Figure&nbsp;5.5.&nbsp;Propagate Command Configuration">Figure&nbsp;5.5, &ldquo;Propagate Command Configuration&rdquo;</a>.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_PropASpacecraft_Propagate1Dialog"></a><p class="title"><b>Figure&nbsp;5.5.&nbsp;Propagate Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_PropSpacecraft_PropagateCommandConfiguration.png" align="middle" height="457" alt="Propagate Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch05s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="SimulatingAnOrbit.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch05s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure the Propagator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run and Analyze the Results</td></tr></table></div></body></html>