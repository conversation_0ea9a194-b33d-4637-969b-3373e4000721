<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;21.&nbsp;Orbit Determination</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="RefGuide.html" title="Reference Guide"><link rel="prev" href="Vary.html" title="Vary"><link rel="next" href="AcceptFilter.html" title="AcceptFilter"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;21.&nbsp;Orbit Determination</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Vary.html">Prev</a>&nbsp;</td><th align="center" width="60%">Reference Guide</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="AcceptFilter.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="N27B1B"></a>Chapter&nbsp;21.&nbsp;Orbit Determination</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ch21.html#N27B20">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="AcceptFilter.html">AcceptFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Antenna.html">Antenna</a></span><span class="refpurpose"> &mdash; Transmits or receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="BatchEstimator.html">BatchEstimator</a></span><span class="refpurpose"> &mdash; A batch least squares estimator</span></dt><dt><span class="refentrytitle"><a href="ErrorModel.html">ErrorModel</a></span><span class="refpurpose"> &mdash; Used to specify measurement noise for simulation and
    estimation, and to apply or estimate measurement biases.</span></dt><dt><span class="refentrytitle"><a href="EstimatedParameter.html">EstimatedParameter</a></span><span class="refpurpose"> &mdash; Used for modeling of dynamically estimated parameters in the
    Extended Kalman Filter.</span></dt><dt><span class="refentrytitle"><a href="ExtendedKalmanFilter.html">ExtendedKalmanFilter</a></span><span class="refpurpose"> &mdash; An extended Kalman filter orbit determination
    estimator</span></dt><dt><span class="refentrytitle"><a href="ProcessNoiseModel.html">ProcessNoiseModel</a></span><span class="refpurpose"> &mdash; Used to specify process noise for estimation when using the
    ExtendedKalmanFilter estimator.</span></dt><dt><span class="refentrytitle"><a href="Receiver.html">Receiver</a></span><span class="refpurpose"> &mdash; Hardware that receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="RejectFilter.html">RejectFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Simulator.html">Simulator</a></span><span class="refpurpose"> &mdash; Configures the generation of simulated tracking data
    measurements.</span></dt><dt><span class="refentrytitle"><a href="Smoother.html">Smoother</a></span><span class="refpurpose"> &mdash; A backwards filter yielding an improved estimate of states
    through weighted combination of forward and reverse sequential
    estimation.</span></dt><dt><span class="refentrytitle"><a href="SpacecraftNavigation.html">Spacecraft Navigation</a></span><span class="refpurpose"> &mdash; There are a number of <span class="guilabel">Spacecraft</span> fields
    that are used exclusively to support GMAT's navigation (orbit
    determination) capability.</span></dt><dt><span class="refentrytitle"><a href="TrackingFileSet.html">TrackingFileSet</a></span><span class="refpurpose"> &mdash; Manages the observation data contained in one or more external
    tracking data files.</span></dt><dt><span class="refentrytitle"><a href="Transmitter.html">Transmitter</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, attached to a
    <span class="guilabel">GroundStation</span> or <span class="guilabel">Spacecraft</span>
    resource, that transmits an RF signal.</span></dt><dt><span class="refentrytitle"><a href="Transponder.html">Transponder</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, typically attached to a
    spacecraft, that receives and automatically re-transmits an incoming
    signal.</span></dt></dl></dd><dt><span class="section"><a href="ch21s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="RunEstimator.html">RunEstimator</a></span><span class="refpurpose"> &mdash; Ingests navigation measurements and generates an estimated
    state vector</span></dt><dt><span class="refentrytitle"><a href="RunSimulator.html">RunSimulator</a></span><span class="refpurpose"> &mdash; Generates simulated navigation measurements</span></dt><dt><span class="refentrytitle"><a href="RunSmoother.html">RunSmoother</a></span><span class="refpurpose"> &mdash; Runs a sequential smoother estimator.</span></dt></dl></dd><dt><span class="section"><a href="ch21s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="TrackingDataTypes.html">Tracking Data Types for Orbit Determination</a></span><span class="refpurpose"> &mdash; This section describes tracking data types and file formats
    for orbit determination.</span></dt><dt><span class="refentrytitle"><a href="NavPropagatorConfiguration.html">Configuration of Propagators for Orbit
    Determination</a></span><span class="refpurpose"> &mdash; This section describes some special considerations for
    configuration of numerical and ephemeris propagators for GMAT's
    estimators.</span></dt><dt><span class="refentrytitle"><a href="InitialOrbitDetermination.html">Initial Orbit Determination</a></span><span class="refpurpose"> &mdash; A set of python functions to support early orbit
    operations</span></dt></dl></dd></dl></div><p> This chapter contains documentation for Resources and Commands related to orbit determination. </p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N27B20"></a>Resources</h2></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Vary.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="RefGuide.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="AcceptFilter.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Vary&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;AcceptFilter</td></tr></table></div></body></html>