<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;1.&nbsp;Welcome to GMAT</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="UsingGmat.html" title="Using GMAT"><link rel="prev" href="UsingGmat.html" title="Using GMAT"><link rel="next" href="ch01s02.html" title="Heritage"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;1.&nbsp;Welcome to GMAT</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="UsingGmat.html">Prev</a>&nbsp;</td><th align="center" width="60%">Using GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch01s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="WelcomeToGmat"></a>Chapter&nbsp;1.&nbsp;Welcome to GMAT</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="WelcomeToGmat.html#N100F3">Features Overview</a></span></dt><dd><dl><dt><span class="section"><a href="WelcomeToGmat.html#N100F8">Dynamics and Environment Modeling</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10117">Plotting, Reporting and Product Generation</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N1012A">Optimization and Targeting</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10140">Programming Infrastructure</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N10159">Orbit Determination Infrastructure</a></span></dt><dt><span class="section"><a href="WelcomeToGmat.html#N1017E">Interfaces</a></span></dt></dl></dd><dt><span class="section"><a href="ch01s02.html">Heritage</a></span></dt><dt><span class="section"><a href="ch01s03.html">Licensing</a></span></dt><dt><span class="section"><a href="ch01s04.html">Platform Support</a></span></dt><dt><span class="section"><a href="ch01s05.html">Component Status</a></span></dt><dt><span class="section"><a href="ch01s06.html">Contributors</a></span></dt></dl></div><p>The General Mission Analysis Tool (GMAT) is the world&rsquo;s only
  enterprise, multi-mission, open source software system for space mission
  design, optimization, and navigation. The system supports missions in flight
  regimes ranging from low Earth orbit to lunar, libration point, and deep
  space missions. GMAT is developed by a team of NASA, private industry,
  public, and private contributors and is used for real-world mission support,
  engineering studies, as a tool for education, and public engagement. See the
  release notes for a complete list of changes in this version.</p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N100F3"></a>Features Overview</h2></div></div></div><p>GMAT is a feature rich system containing high fidelity space system
    models, optimization and targeting, built in scripting and programming
    infrastructure, and customizable plots, reports and data products, to
    enable flexible analysis and solutions for custom and unique applications.
    GMAT can be driven from a fully featured, interactive GUI, from a custom
    script language or via API. Here are some of GMAT&rsquo;s key features broken
    down by feature group.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N100F8"></a>Dynamics and Environment Modeling</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>High fidelity dynamics models including harmonic gravity,
          drag, tides, attitude dependent drag and SRP, N-plate SRP, and
          relativistic corrections</p></li><li class="listitem"><p>High fidelity spacecraft modeling</p></li><li class="listitem"><p>Formations and constellations</p></li><li class="listitem"><p>Impulsive and finite maneuver modeling and optimization of low
          and high thrust systems</p></li><li class="listitem"><p>Propulsion system modeling including chemical and electric
          thrusters</p></li><li class="listitem"><p>Solar System modeling including high fidelity ephemerides,
          custom celestial bodies, libration points, and barycenters</p></li><li class="listitem"><p>Rich set of coordinate systems including J2000, ICRF, fixed,
          rotating, topocentric, and many others</p></li><li class="listitem"><p>Propagation using CCSDS, SPICE, STK, and Code 500 ephemeris
          files</p></li><li class="listitem"><p>Propagators that naturally synchronize epochs of multiple
          vehicles and avoid fixed step integration and interpolation</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10117"></a>Plotting, Reporting and Product Generation</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Interactive 3-D graphics</p></li><li class="listitem"><p>Customizable data plots and reports</p></li><li class="listitem"><p>Post computation animation</p></li><li class="listitem"><p>CCSDS, SPK, and Code-500 ephemeris generation</p></li><li class="listitem"><p>Eclipse and station contact location</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1012A"></a>Optimization and Targeting</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Boundary value targeters</p></li><li class="listitem"><p>Nonlinear, constrained optimization</p></li><li class="listitem"><p>High order collocation</p></li><li class="listitem"><p>Custom, scriptable cost functions</p></li><li class="listitem"><p>Custom, scriptable nonlinear equality and inequality
          constraint functions</p></li><li class="listitem"><p>Custom targeter controls and constraints</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10140"></a>Programming Infrastructure</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>User defined variables, arrays, and strings</p></li><li class="listitem"><p>User defined equations using MATLAB syntax. (i.e. overloaded
          array operation)</p></li><li class="listitem"><p>Control flow such as If, For, and While loops for custom
          applications</p></li><li class="listitem"><p>Matlab interface</p></li><li class="listitem"><p>Python interface</p></li><li class="listitem"><p>User-defined functions (sub-routines)</p></li><li class="listitem"><p>Built in parameters and calculations in multiple coordinate
          systems</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10159"></a>Orbit Determination Infrastructure</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Batch estimator</p></li><li class="listitem"><p>Extended Kalman Filter and Smoother</p></li><li class="listitem"><p>Extensive statistical results reporting</p></li><li class="listitem"><p>DSN, GN, TDRS, GPS point solution, and angle data types</p></li><li class="listitem"><p>Measurement data editing</p></li><li class="listitem"><p>Media corrections</p></li><li class="listitem"><p>Process noise modeling</p></li><li class="listitem"><p>Error modeling</p></li><li class="listitem"><p>Initial Orbit determination (IOD) routines</p></li><li class="listitem"><p>Covariance propagation using the
          <span class="guilabel">Propagate</span> command</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1017E"></a>Interfaces</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Fully featured, interactive GUI that makes simple analysis
          quick and easy</p></li><li class="listitem"><p>Custom scripting language that makes complex, custom analysis
          possible</p></li><li class="listitem"><p>Matlab interface for custom external simulations and
          calculations</p></li><li class="listitem"><p>Python interface for custom external simulations and
          calculations</p></li><li class="listitem"><p>File interface for the TCOPS Vector Hold File format, for
          loading of initial spacecraft data</p></li><li class="listitem"><p>Python, MATLAB, and JAVA APIs.</p></li><li class="listitem"><p>Command line interface for batch analysis</p></li></ul></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="UsingGmat.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="UsingGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch01s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Using GMAT&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Heritage</td></tr></table></div></body></html>