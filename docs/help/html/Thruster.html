<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>ChemicalThruster</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="FuelTank.html" title="ChemicalTank"><link rel="next" href="ContactLocator.html" title="ContactLocator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">ChemicalThruster</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="FuelTank.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ContactLocator.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Thruster"></a><div class="titlepage"></div><a name="N16A64" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">ChemicalThruster</span></h2><p>ChemicalThruster &mdash; A chemical thruster model</p></div><div class="refsection"><a name="N16A75"></a><h2>Description</h2><p>The <span class="guilabel">ChemicalThruster</span> resource is a model of a
    chemical thruster which uses polynomials to model the thrust and specific
    impulse as a function of tank pressure and temperature. The
    <span class="guilabel">ChemicalThruster</span> model also allows you to specify
    properties such as a duty cycle and scale factor and to connect a
    <span class="guilabel">ChemicalThruster</span> with a
    <span class="guilabel">ChemicalTank</span>. You can flexibly define the direction
    of the thrust by specifying the thrust components in coordinate systems
    such as (locally defined) <span class="guilabel">SpacecraftBody</span> or
    <span class="guilabel">LVLH</span>, or by choosing any configured
    <span class="guilabel">CoordinateSystem</span> resource.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="BeginFiniteBurn.html" title="BeginFiniteBurn"><span class="refentrytitle">BeginFiniteBurn</span></a>,<a class="xref" href="FuelTank.html" title="ChemicalTank"><span class="refentrytitle">ChemicalTank</span></a>,<a class="xref" href="FiniteBurn.html" title="FiniteBurn"><span class="refentrytitle">FiniteBurn</span></a></p></div><div class="refsection"><a name="N16A9C"></a><h2>Fields</h2><p>The constants <span class="guilabel">Ci</span> below are used in the
    following equation to calculate thrust (in Newtons),
    F<sub>T</sub>, as a function of pressure P (kPa) and
    temperature T (Celsius).</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_1.png" align="middle" height="65"></td></tr></table></div></div><p>The constants <span class="guilabel">Ki</span> below are used in the
    following equation to calculate ISP (in seconds), Isp, as a function of
    pressure P (kPa) and temperature T (Celsius).</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_2.png" align="middle" height="71"></td></tr></table></div></div><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">Axes</span></td><td><p>Allows the user to define a spacecraft centered set
            of axes for the <span class="guilabel">ChemicalThruster</span>. This field
            cannot be modified in the Mission Sequence</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">VNB</span>, <span class="guilabel">LVLH</span>,
                    <span class="guilabel">MJ2000Eq</span>,
                    <span class="guilabel">SpacecraftBody</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">VNB</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CoordinateSystem</span></td><td><p>Determines what coordinate system the orientation
            parameters, <span class="guilabel">ThrustDirection1</span>,
            <span class="guilabel">ThrustDirection2</span>, and
            <span class="guilabel">ThrustDirection3</span> refer to. This field cannot
            be modified in the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Local</span>,
                    <span class="guilabel">EarthMJ2000Eq</span>,
                    <span class="guilabel">EarthMJ2000Ec</span>,
                    <span class="guilabel">EarthFixed</span>, or any user defined
                    system</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Local</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C1</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>10</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C2</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C3</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C4</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C5</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>2</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C6</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C7</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C7</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C8</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C9</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C9</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C10</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/kPa<sup>C11</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C11</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C12</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C13</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C14</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C15</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">C16</span></td><td><p>Thrust coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DecrementMass</span></td><td><p>Flag which determines if the
            <span class="guilabel">FuelMass</span> is to be decremented as it used.
            This field cannot be modified in the Mission Sequence. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>false</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DutyCycle</span></td><td><p>Fraction of time that the thrusters are on during a
            maneuver. The thrust applied to the spacecraft is scaled by this
            amount. Note that this scale factor also affects mass flow rate.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 &lt;= Real &lt;= 1</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravitationalAccel </span></td><td><p>The gravitational acceleration. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>9.81</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m/s<sup>2</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSX</span></td><td><p>X-component of the origin of the hardware&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>meters</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSY</span></td><td><p>Y-component of the origin of the hardware&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate system.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>meters</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HWOriginInBCSZ</span></td><td><p>Z-component of the origin of the hardware&rsquo;s coordinate
            system expressed in the spacecraft&rsquo;s body coordinate
            system.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>meters</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K1</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>300</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K2</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K3</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K4</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K5</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>2</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K6</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C7</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K7</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K8</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C9</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K9</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K10</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s/kPa<sup>C11</sup></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K11</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K12</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K13</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K14</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K15</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">K16</span></td><td><p>ISP coefficient. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>1/kPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MixRatio</span></td><td><p>The mixture ratio employed to draw fuel from multiple
            tanks. For example, if there are two tanks and
            <span class="guilabel">MixRatio</span> is set to [2 1], then twice as much
            fuel will be drawn from tank one as from tank 2 in the
            <span class="guilabel">Tank</span> list. Note, if a MixRatio is not
            supplied, fuel is drawn from tanks in equal amounts, (the
            <span class="guilabel">MixRatio</span> is set to a vector of ones the same
            length as the <span class="guilabel">Tank</span> list). </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Array of real numbers with same length as number of
                    tanks in the <span class="guilabel">Tank</span> array</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>[1]</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Origin</span></td><td><p>This field, used in conjunction with the
            <span class="guilabel">Axes</span> field, allows the user to define a
            spacecraft centered set of axes for the
            <span class="guilabel">ChemicalThruster</span>. <span class="guilabel">Origin</span>
            has no affect when a <span class="guilabel">Local</span> coordinate system
            is used and the <span class="guilabel">Axes</span> are set to
            <span class="guilabel">MJ2000Eq</span> or
            <span class="guilabel">SpacecraftBody</span>. This field cannot be modified
            in the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Sun</span>,
                    <span class="guilabel">Mercury</span>, <span class="guilabel">Venus</span>,
                    <span class="guilabel">Earth</span>, <span class="guilabel">Luna</span>,
                    <span class="guilabel">Mars</span>,<span class="guilabel">Jupiter</span>,
                    <span class="guilabel">Saturn</span>, <span class="guilabel">Uranus</span>,
                    <span class="guilabel">Neptune</span>,
                    <span class="guilabel">Pluto</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Earth</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Tank</span></td><td><p>A list of<span class="guilabel"> ChemicalTank(s)</span> from
            which the thruster draws propellant from. A thruster requires the
            assignment of at least one tank. This field cannot be modified in
            the Mission Sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>User defined list of tank(s).</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection1</span></td><td><p>X component of the spacecraft thrust vector
            direction. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">1</code>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection2</span></td><td><p>Y component of the spacecraft thrust vector
            direction. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <code class="literal">0</code>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustDirection3</span></td><td><p>Z component of the spacecraft thrust vector
            direction. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThrustScaleFactor</span></td><td><p><span class="guilabel">ThrustScaleFactor</span> is a scale
            factor that is multiplied by the thrust vector, for a given
            thruster, before the thrust vector is added into the total
            acceleration. Note that the value of this scale factor does not
            affect the mass flow rate. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real Number</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1739D"></a><h2>Interactions</h2><div class="informaltable"><table border="1"><colgroup><col width="23%"><col width="77%"></colgroup><thead><tr><th align="left">Command or Resource</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">BeginFiniteBurn/EndFiniteBurn
            command</span></td><td><p>Use these commands, which require a
            <span class="guilabel">Spacecraft</span> and a
            <span class="guilabel">FiniteBurn</span> name as input, to implement a
            finite burn. </p></td></tr><tr><td><span class="guilabel">ChemicalTank resource</span></td><td><p>This resource contains the fuel used to power the
            <span class="guilabel">ChemicalThruster</span> specified by the<span class="guilabel">
            FiniteBurn</span> resource.</p></td></tr><tr><td><span class="guilabel">FiniteBurn resource</span></td><td><p>When using the
            <span class="guilabel">BeginFiniteBurn/EndFiniteBurn</span> commands, you
            must specify which <span class="guilabel">FiniteBurn</span> resource to
            implement. The <span class="guilabel">FiniteBurn</span> resource specifies
            which <span class="guilabel">ChemicalThruster(s)</span> to use for the
            finite burn. </p></td></tr><tr><td><span class="guilabel">Spacecraft resource</span></td><td><p>When using the
            <span class="guilabel">BeginFiniteBurn/EndFiniteBurn</span> commands, you
            must specify which <span class="guilabel">Spacecraft</span> to apply the
            finite burn to. </p></td></tr><tr><td><span class="guilabel">Propagate command</span></td><td><p>In order to implement a non-zero finite burn, a
            <span class="guilabel">Propagate</span> statement must occurr within the
            <span class="guilabel">BeginFiniteBurn</span> and
            <span class="guilabel">EndFiniteBurn</span> statements. </p></td></tr></tbody></table></div></div><div class="refsection"><a name="N173FD"></a><h2>GUI</h2><p>The <span class="guilabel">ChemicalThruster</span> dialog box allows you to
    specify properties of a <span class="guilabel">ChemicalThruster</span> including
    the <span class="guilabel">Coordinate System</span> of the thrust acceleration
    direction vector, the thrust magnitude and Isp coefficients, and choice of
    <span class="guilabel">ChemicalTank</span>. The layout of the
    <span class="guilabel">ChemicalThruster</span> dialog box is shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_3.png" align="middle" height="586"></td></tr></table></div></div><p>When configuring the <span class="guilabel">Coordinate System</span> field,
    you can choose between existing coordinate systems or use locally defined
    coordinate systems. The <span class="guilabel">Axes</span> field is only active if
    <span class="guilabel">Coordinate System</span> is set to
    <span class="guimenuitem">Local</span>. The <span class="guilabel">Origin</span> field is
    only active if <span class="guilabel">Coordinate System</span> is set to
    <span class="guimenuitem">Local</span> and <span class="guilabel">Axes</span> is set to
    either <span class="guimenuitem">VNB</span> or
    <span class="guimenuitem">LVLH</span>.</p><p>As shown below, if <span class="guilabel">Decrement Mass</span> is checked,
    then you can input the gravitational acceleration value used to calculate
    fuel use. The value of the gravitational acceleration input here only
    affects fuel use and does not affect the force model.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_4.png" align="middle" height="586"></td></tr></table></div></div><p>Selecting the <span class="guimenu">Edit Thruster Coef.</span> button brings
    up the following dialog box where you may input the coefficients for the
    <span class="guilabel">ChemicalThruster</span> polynomial.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_5.png" align="middle" height="424"></td></tr></table></div></div><p>Similarly, clicking the <span class="guilabel">Edit Impulse Coef.</span>
    button brings up the following dialog box where you may input the
    coefficients for the specific impulse (ISP) polynomial.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_6.png" align="middle" height="424"></td></tr></table></div></div></div><div class="refsection"><a name="N17467"></a><h2>Remarks</h2><div class="refsection"><a name="N1746A"></a><h3>Use of ChemicalThruster Resource in Conjunction With
      Maneuvers</h3><p>A <span class="guilabel">ChemicalThruster</span> resource is used only in
      association with finite maneuvers. To implement a finite maneuver, you
      must first create both a <span class="guilabel">ChemicalTank</span> and a
      <span class="guilabel">FiniteBurn</span> resource. You must also associate a
      <span class="guilabel">ChemicalTank</span> with the
      <span class="guilabel">ChemicalThruster</span> resource and you must associate a
      <span class="guilabel">ChemicalThruster</span> with the
      <span class="guilabel">FiniteBurn</span> resource. The finite maneuver is
      implemented using the
      <span class="guilabel">BeginFiniteBurn</span>/<span class="guilabel">EndFiniteBurn</span>
      commands. See the
      <span class="guilabel">BeginFiniteBurn</span>/<span class="guilabel">EndFiniteBurn</span>
      command documentation for worked examples on how the
      <span class="guilabel">ChemicalThruster</span> resource is used in conjunction
      with finite maneuvers.</p></div><div class="refsection"><a name="N17493"></a><h3>Thrust and ISP Calculation</h3><p>Unscaled thrust, F<sub>T</sub>, and Isp, as a function
      of Pressure, in kPa, and Temperature, in degrees Celsius, are calculated
      using the following polynomials.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_1.png" align="middle" height="65"></td></tr></table></div></div><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_2.png" align="middle" height="71"></td></tr></table></div></div><p>The thrust, T, output in Newtons, is scaled by the <span class="guimenu">Duty
      Cycle</span> and <span class="guimenu">Thrust Scale Factor</span>. The thrust
      acceleration direction vector (the direction of the actual acceleration
      not the thruster nozzle) is given by
      <span class="guimenu">ThrustDirection1-3</span> and is applied in the input
      <span class="guilabel">Coordinate System</span>. The Isp is output in
      seconds.</p><p>The mass flow rate and the thrust equations are shown below where
      F<sub>T</sub> and Isp are defined above,
      f<sub>d</sub> is the duty cycle, f<sub>s</sub>
      is the thrust scale factor, R<sub>iT</sub> is the rotation
      matrix from the thrust coordinate system to the inertial system, and
      T<sub>d</sub> is the unitized thrust direction.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Thruster_GUI_8.png" align="middle" height="113"></td></tr></table></div></div></div><div class="refsection"><a name="N174D5"></a><h3>Local Coordinate Systems</h3><p>Here, a Local coordinate system is defined as one that we
      configure "locally" using the <span class="guilabel">ChemicalThruster</span>
      resource interface as opposed to defining a coordinate system using the
      <span class="guilabel">Coordinate Systems</span> folder in the
      <span class="guilabel">Resources</span> Tree.</p><p>To configure a local coordinate system, you must specify the
      coordinate system of the input thrust acceleration direction vector,
      <span class="guimenu">ThrustDirection1-3</span>. If you choose a local coordinate
      system, the four choices available, as given by the
      <span class="guimenu">Axes</span> sub-field, are <span class="guimenuitem">VNB</span>,
      <span class="guimenuitem">LVLH</span>, <span class="guimenuitem">MJ2000Eq</span>,
      and <span class="guimenuitem">SpacecraftBody</span>.
      <span class="guimenuitem">VNB</span> or Velocity-Normal-Binormal is a
      non-inertial coordinate system based upon the motion of the spacecraft
      with respect to the <span class="guimenu">Origin</span> sub-field. For example, if
      the <span class="guimenu">Origin</span> is chosen as Earth, then the X-axis of
      this coordinate system is the along the velocity of the spacecraft with
      respect to the Earth, the Y-axis is along the instantaneous orbit normal
      (with respect to the Earth) of the spacecraft, and the Z-axis completes
      the right-handed set.</p><p>Similarly, Local Vertical Local Horizontal or
      <span class="guimenuitem">LVLH</span> is also a non-inertial coordinate system
      based upon the motion of the spacecraft with respect to the
      <span class="guimenu">Origin</span> sub-field. Again, if we choose Earth as the
      origin, then the X-axis of this coordinate system is the position of the
      spacecraft with respect to the Earth, the Z-axis is the instantaneous
      orbit normal (with respect to the Earth) of the spacecraft, and the
      Y-axis completes the right-handed set.</p><p><span class="guimenuitem">MJ2000Eq</span> is the J2000-based
      Earth-centered Earth mean equator inertial coordinate system. Note that
      the <span class="guimenu">Origin</span> sub-field is not needed to define this
      coordinate system.</p><p><span class="guimenuitem">SpacecraftBody</span> is the attitude system
      of the spacecraft. Since the thrust is applied in this system, GMAT uses
      the attitude of the spacecraft, a spacecraft attribute, to determine the
      inertial thrust direction. Note that the <span class="guilabel">Origin</span>
      sub-field is not needed to define this coordinate system.</p></div><div class="refsection"><a name="N17516"></a><h3>Caution When Setting the ChemicalTank Temperature and Reference
      Temperature</h3><p>Note that both the thrust and ISP polynomials have terms that
      involve the ratio, (Temperature / Reference Temperature). For GMAT, this
      temperature ratio is calculated in Celsius units, and thus, there is a
      discontinuity when the Reference Temperature is equal to zero. For this
      reason, GMAT requires that the absolute value of the input Reference
      Temperature is greater than 0.01.</p><p>Note also that the form of the Thrust and ISP polynomial has some
      behavior, when the Reference Temperature is near 0 degrees Centigrade,
      that you need to be aware of. Because of the previously mentioned
      discontinuity, the polynomials do not vary smoothly when the Reference
      Temperature is near zero. For example, consider the two Reference
      Temperatures, -0.011 and + 0.011 degrees Centigrade. These two
      temperatures are close to each other in value and one might expect that
      they have roughly similar thrust and ISP values. This may not be the
      case, depending upon your choice of thrust/ISP coefficients, since the
      temperature ratios associated with the two Reference Temperatures have
      the same magnitude but different signs. You may choose to set the input
      Reference Temperature equal to the input Temperature, thus eliminating
      any dependence of thrust and ISP with temperature when using the
      currently implemented <span class="guilabel">ChemicalTank</span> model based upon
      Boyle&rsquo;s Law where the fuel Temperature does not change as fuel is
      depleted.</p></div></div><div class="refsection"><a name="N17520"></a><h2>Examples</h2><div class="informalexample"><p>Create a default <span class="guilabel">ChemicalTank</span> and a
      <span class="guilabel">ChemicalThruster</span> that allows for fuel depletion,
      assign the <span class="guilabel">ChemicalThruster</span> the default
      <span class="guilabel">ChemicalTank</span>, and attach both the
      <span class="guilabel">ChemicalThruster</span> and
      <span class="guilabel">ChemicalTank</span> to a
      <span class="guilabel">Spacecraft</span>.</p><pre class="programlisting">%  Create the ChemicalTank Resource
Create ChemicalTank FuelTank1
FuelTank1.AllowNegativeFuelMass = false
FuelTank1.FuelMass = 756
FuelTank1.Pressure = 1500
FuelTank1.Temperature = 20
FuelTank1.RefTemperature = 20
FuelTank1.Volume = 0.75
FuelTank1.FuelDensity = 1260
FuelTank1.PressureModel = PressureRegulated

%  Create a ChemicalThruster, that allows fuel depletion, and assign it a ChemicalTank
Create ChemicalThruster Thruster1
Thruster1.CoordinateSystem = Local
Thruster1.Origin = Earth
Thruster1.Axes = VNB
Thruster1.ThrustDirection1 = 1
Thruster1.ThrustDirection2 = 0
Thruster1.ThrustDirection3 = 0
Thruster1.DutyCycle = 1
Thruster1.ThrustScaleFactor = 1
Thruster1.DecrementMass = true
Thruster1.Tank = {FuelTank1}
Thruster1.GravitationalAccel = 9.810000000000001
Thruster1.C1 = 10
Thruster1.C2 = 0
Thruster1.C3 = 0
Thruster1.C4 = 0
Thruster1.C5 = 0
Thruster1.C6 = 0
Thruster1.C7 = 0
Thruster1.C8 = 0
Thruster1.C9 = 0
Thruster1.C10 = 0
Thruster1.C11 = 0
Thruster1.C12 = 0
Thruster1.C13 = 0
Thruster1.C14 = 0
Thruster1.C15 = 0
Thruster1.C16 = 0
Thruster1.K1 = 300
Thruster1.K2 = 0
Thruster1.K3 = 0
Thruster1.K4 = 0
Thruster1.K5 = 0
Thruster1.K6 = 0
Thruster1.K7 = 0
Thruster1.K8 = 0
Thruster1.K9 = 0
Thruster1.K10 = 0
Thruster1.K11 = 0
Thruster1.K12 = 0
Thruster1.K13 = 0
Thruster1.K14 = 0
Thruster1.K15 = 0
Thruster1.K16 = 0

%  Add the ChemicalThruster and the ChemicalTank to a Spacecraft
Create Spacecraft DefaultSC
DefaultSC.Tanks = {FuelTank1}
DefaultSC.Thrusters = {Thruster1}

BeginMissionSequence</pre></div><div class="informalexample"><p>Create two <span class="guilabel">ChemicalTank</span>s (called
      <span class="guilabel">aTank1</span> and <span class="guilabel">aTank2</span>) and a
      <span class="guilabel">ChemicalThruster</span>, attach both the
      <span class="guilabel">ChemicalThruster</span> and
      <span class="guilabel">ChemicalTanks</span> to a <span class="guilabel">Spacecraft</span>,
      and configure the thruster to draw four times as much fuel from
      <span class="guilabel">aTank1</span> than <span class="guilabel">aTank2</span>.</p><pre class="programlisting">%  Create the ChemicalTank Resource
Create Spacecraft aSat
aSat.Tanks = {aTank1,aTank2}
aSat.Thrusters = {aThruster}

% Create two tanks
Create ChemicalTank aTank1 aTank2

%  Configure thruster to draw four times as much fuel 
%  from aTank1 than aTank2
Create ChemicalThruster aThruster
aThruster.Tank = {aTank1,aTank2}
aThruster.MixRatio = [4 1]

BeginMissionSequence</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="FuelTank.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ContactLocator.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">ChemicalTank&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;ContactLocator</td></tr></table></div></body></html>