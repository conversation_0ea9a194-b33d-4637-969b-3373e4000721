<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch07s05.html" title="Run the Mission"><link rel="next" href="ch08s02.html" title="Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators, Differential Corrector, Coordinate Systems and Graphics"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch07s05.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch08s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Mars_B_Plane_Targeting"></a>Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Mars_B_Plane_Targeting.html#N11AA6">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch08s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s02.html#N11B1F">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11B91">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11C5A">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11CE9">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11EF7">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11F29">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11FA1">Create the Orbit Views</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s03.html#N1212E">Create the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1222A">Configure the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12232">Configure the Target desired B-plane Coordinates Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12259">Configure the Prop 3 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12298">Configure the Prop 12 Days to TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N122DA">Configure the Vary TCM.V Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1232E">Configure the Vary TCM.N Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N123C0">Configure the Vary TCM.B Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12452">Configure the Apply TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12470">Configure the Prop 280 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124B2">Configure the Prop to Mars Periapsis Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124E8">Configure the Achieve BdotT Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1253F">Configure the Achieve BdotR Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s04.html#N1268C">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N1276C">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127DA">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127E2">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12809">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12895">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128BC">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128F2">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Advanced</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>75 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Complete <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><i>Simulating an Orbit</i></a>, <a class="xref" href="SimpleOrbitTransfer.html" title="Chapter&nbsp;6.&nbsp;Simple Orbit Transfer"><i>Simple Orbit Transfer</i></a> and a basic understanding of B-Planes and
        their usage in targeting is required.</p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Tut_Mars_B_Plane_Targeting.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N11AA6"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>One of the most challenging problems in space mission design is to
      design an interplanetary transfer trajectory that takes the spacecraft
      within a very close vicinity of the target planet. One possible approach
      that puts the spacecraft close to a target planet is by targeting the
      B-Plane of that planet. The B-Plane is a planar coordinate system that
      allows targeting during a gravity assist. It can be thought of as a
      target attached to the assisting body. In addition, it must be
      perpendicular to the incoming asymptote of the approach hyperbola. <a class="xref" href="Mars_B_Plane_Targeting.html#Tut_Mars_B_Plane_Targeting_B_Plane_1" title="Figure&nbsp;8.1.&nbsp;Geometry of the B-Plane as seen from a viewpoint perpendicular to the B-Plane">Figure&nbsp;8.1, &ldquo;<span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane&rdquo;</a> and <a class="xref" href="Mars_B_Plane_Targeting.html#Tut_Mars_B_Plane_Targeting_B_Plane_2" title="Figure&nbsp;8.2.&nbsp;The B-vector as seen from a viewpoint perpendicular to orbit plane">Figure&nbsp;8.2, &ldquo;<span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane&rdquo;</a> show the geometry of
      the B-Plane and B-vector as seen from a viewpoint perpendicular to orbit
      plane. To read more on B-Planes, please consult the GMATMathSpec
      document. A good example involving the use of B-Plane targeting is a
      mission to Mars. Sending a spacecraft to Mars can be achieved by
      performing a Trajectory Correction Maneuver (TCM) that targets Mars
      B-Plane. Once the spacecraft gets close to Mars, then an orbit insertion
      maneuver can be performed to capture into Mars orbit.</p></div><div class="figure"><a name="Tut_Mars_B_Plane_Targeting_B_Plane_1"></a><p class="title"><b>Figure&nbsp;8.1.&nbsp;<span class="guilabel">Geometry of the B-Plane</span> as seen from a
      viewpoint perpendicular to the B-Plane</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_Plane_1A.png" align="middle" width="274" alt="Geometry of the B-Plane as seen from a viewpoint perpendicular to the B-Plane"></div></div></div></div><br class="figure-break"><div class="figure"><a name="Tut_Mars_B_Plane_Targeting_B_Plane_2"></a><p class="title"><b>Figure&nbsp;8.2.&nbsp;<span class="guilabel">The B-vector</span> as seen from a viewpoint
      perpendicular to orbit plane</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_Plane_2.png" align="middle" width="359" alt="The B-vector as seen from a viewpoint perpendicular to orbit plane"></div></div></div></div><br class="figure-break"><p>In this tutorial, we will use GMAT to model a mission to Mars.
    Starting from an out-going hyperbolic trajectory around Earth, we will
    perform a TCM to target Mars B-Plane. Once we are close to Mars, we will
    adjust the size of the maneuver to perform a Mars Orbit Insertion (MOI) to
    achieve a final elliptical orbit with an inclination of 90 degrees.
    Meeting these mission objectives requires us to create two separate
    targeting sequences. In order to focus on the configuration of the two
    targeters, we will make extensive use of the default configurations for
    spacecraft, propagators, and maneuvers.</p><p>The first target sequence employs maneuvers in the Earth-based
    Velocity (V), Normal (N) and Bi-normal (B) directions and includes four
    propagation sequences. The purpose of the maneuvers in VNB directions is
    to target BdotT and BdotR components of the B-vector. BdotT is targeted to
    0 km and BdotR is targeted to a non-zero value to generate a polar orbit
    that has inclination of 90 degrees. BdotR is targeted to -7000 km to avoid
    having the orbit intersect Mars, which has a radius of approximately 3396
    km.</p><p>The second target sequence employs a single, Mars-based
    anti-velocity direction (-V) maneuver and includes one propagation
    sequence. This single anti-velocity direction maneuver will occur at
    periapsis. The purpose of the maneuver is to achieve MOI by targeting
    position vector magnitude of 12,000 km at apoapsis. The basic steps of
    this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Modify the <code class="classname">DefaultSC</code> to define
        spacecraft&rsquo;s initial state. The initial state is an out-going
        hyperbolic trajectory that is with respect to Earth.</p></li><li class="step"><p>Create and configure a <code class="classname">Fuel Tank</code>
        resource.</p></li><li class="step"><p>Create two <code class="classname">ImpulsiveBurn</code> resources with
        default settings.</p></li><li class="step"><p>Create and configure three <code class="classname">Propagators:</code>
        NearEarth, DeepSpace and NearMars</p></li><li class="step"><p>Create and configure
        <code class="classname">DifferentialCorrector</code> resource.</p></li><li class="step"><p>Create and configure three
        <code class="classname">OrbitView</code> resources to visualize Earth,
        Sun and Mars centered trajectories.</p></li><li class="step"><p>Create and configure three
        <code class="classname">CoordinateSystems:</code> Earth, Sun and Mars
        centered.</p></li><li class="step"><p>Create first <code class="classname">Target</code> sequence to target
        BdotT and BdotR components of the B-vector.</p></li><li class="step"><p>Create second <code class="classname">Target</code> sequence to
        implement MOI by targeting position magnitude at apoapsis.</p></li><li class="step"><p>Run the mission and analyze the results.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch07s05.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch08s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Run the Mission&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</td></tr></table></div></body></html>