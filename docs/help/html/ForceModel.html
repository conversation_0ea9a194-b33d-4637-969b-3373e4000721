<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>ForceModel</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="FieldOfView.html" title="FieldOfView"><link rel="next" href="Formation.html" title="Formation"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">ForceModel</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="FieldOfView.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Formation.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="ForceModel"></a><div class="titlepage"></div><a name="N19CA7" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">ForceModel</span></h2><p>ForceModel &mdash; Used to specify force modeling options such as gravity, drag,
    solar radiation pressure, and non-central bodies for
    propagation.</p></div><div class="refsection"><a name="N19CB8"></a><h2>Description</h2><p>A <span class="guilabel">ForceModel</span> is a model of the environmental
    forces and dynamics that affect the motion of a spacecraft. GMAT supports
    numerous forces, including perturbations from point mass and spherical
    harmonic gravity models, atmospheric drag, solar radiation pressure, tide
    models, and relativistic corrections. A <span class="guilabel">ForceModel</span> is
    configured and attached to the <span class="guilabel">Propagator</span> object (see
    the <span class="guilabel">Propagator</span> object for differences between script
    and GUI configuration when configuring a <span class="guilabel">Propagator</span>).
    The <span class="guilabel">Propagator</span>, along with the
    <span class="guilabel">Propagate</span> command, uses a
    <span class="guilabel">ForceModel</span> to numerically solve the orbital equations
    of motion (forwards or backwards in time) using the forces configured in
    the <span class="guilabel">ForceModel</span> object, and may include thrust terms
    and mass flow in the case of powered flight. See the discussion below for
    detailed information on how to configure force models for your
    application. This resource cannot be modified in the Mission
    Sequence.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a>, <a class="xref" href="FiniteBurn.html" title="FiniteBurn"><span class="refentrytitle">FiniteBurn</span></a></p></div><div class="refsection"><a name="ForceModel_Fields"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="37%"><col width="63%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">CentralBody</span></td><td><p>The central body of propagation.
            <span class="guilabel">CentralBody</span> must be a celestial body and
            cannot be a <span class="guilabel">LibrationPoint</span>,
            <span class="guilabel">Barycenter</span>, <span class="guilabel">Spacecraft</span>,
            or other special point.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource reference</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CelestialBody</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Earth</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag</span></td><td><p>Deprecated. This field has been replaced with
            <span class="guilabel">Drag.AtmosphereModel</span>.</p></td></tr><tr><td><span class="guilabel">Drag.AtmosphereModel</span></td><td><p>Specifies the atmosphere model used in the drag
            force. This field is only active if there is a
            <span class="guilabel">PrimaryBody</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>If <span class="guilabel">PrimaryBody</span> is
                    <span class="guilabel">Earth</span>: <span class="guilabel">None</span>,
                    <span class="guilabel">JacchiaRoberts</span>,
                    <span class="guilabel">MSISE86</span> (with plugin),
                    <span class="guilabel">MSISE90</span>,
                    <span class="guilabel">NRLMSISE00</span> (with plugin)</p><p>If <span class="guilabel">PrimaryBody</span> is
                    <span class="guilabel">Mars</span>: <span class="guilabel">None</span>,
                    <span class="guilabel">MarsGRAM2005</span> (with plugin)</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.CSSISpaceWeatherFile</span></td><td><p>The file name of the CSSI space weather file with
            optional path information. See <a class="xref" href="ForceModel.html#ForceModel_Remarks" title="Remarks">Remarks</a> for details on file
            format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing name of the CSSI file with
                    optional path information.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="literal">'SpaceWeather-All-v1.2.txt'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.DensityModel</span></td><td><p>Enabled when
            <span class="guilabel">Drag.AtmosphereModel</span> is
            <span class="guilabel">MarsGRAM2005</span>. Specifies the Mars-GRAM density
            model to use. <span class="guilabel">Mean</span> is mean density with any
            optional wave model perturbations enabled by the input file.
            <span class="guilabel">High</span> is <span class="guilabel">Mean</span> density
            plus 1 standard deviation. <span class="guilabel">Low</span> is
            <span class="guilabel">Mean</span> density minus 1 standard
            deviation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">High</span>, <span class="guilabel">Low</span>,
                    <span class="guilabel">Mean</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Mean</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.DragModel</span></td><td><p>User choice of Spacecraft area model for drag
            computation.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spherical, SPADFile</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Spherical</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.F107</span></td><td><p>The instantaneous value of solar flux at wavelength
            of 10.7 cm. This field is only active if there is a
            <span class="guilabel">PrimaryBody</span> and the current weather source is
            set to constant values (ConstantFluxAndGeoMag). Realistic values
            for this setting are 50 &lt;= <span class="guilabel">Drag.F107</span> &lt;=
            400.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Drag.F107</span>&gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>150</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>10^-22 W/m^2/Hz</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.F107A</span></td><td><p>The 81-day running average value of solar flux at
            wavelength of 10.7 cm. This field is only active in the script if
            there is a <span class="guilabel">PrimaryBody</span> and the current
            weather source is set to constant values (ConstantFluxAndGeoMag).
            Realistic values for this setting are 50 &lt;=
            <span class="guilabel">Drag.F107A</span> &lt;= 400.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Drag.F107A</span>&gt;=0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>150</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>10^-22 W/m^2/Hz</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.HistoricWeatherSource</span></td><td><p>Defines the source for historical flux and
            geomagnetic indices used in Earth density
            modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">ConstantFluxAndGeoMag</span>,
                    <span class="guilabel">CSSISpaceWeatherFile</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">ConstantFluxAndGeoMag</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.InputFile</span></td><td><p>Enabled when
            <span class="guilabel">Drag.AtmosphereModel</span> is
            <span class="guilabel">MarsGRAM2005</span>. Path to the Mars-GRAM input
            namelist file that configures the model. See the <a class="link" href="ForceModel.html#ForceModel_Remarks_ConfiguringDragModels_MarsGRAM2005" title="MarsGRAM2005">MarsGRAM2005
            section</a> for details on the individual settings in this file
            and how they are used by GMAT. Relative paths are relative to the
            GMAT <code class="filename">bin</code> directory.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid path to a Mars-GRAM input namelist file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="literal">'../data/atmosphere/MarsGRAM2005/inputstd0.txt'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.MagneticIndex</span></td><td><p>The geomagnetic index (Kp) used in density
            calculations. Kp is a planetary 3-hour-average, geomagnetic index
            that measures magnetic effects of solar radiation. This field is
            only active if there is a <span class="guilabel">PrimaryBody</span> and the
            current weather source is set to constant values
            (ConstantFluxAndGeoMag). </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0 &lt;= Real Number &lt;= 9</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>3</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.PredictedWeatherSource</span></td><td><p>Defines the source for predicted flux and geomagnetic
            indices used in Earth density modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">SchattenFile, ConstantFluxAndGeoMag,
                    CSSISpaceWeatherFile</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">ConstantFluxAndGeoMag</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.SchattenErrorModel</span></td><td><p>The error model used from the Schatten file. Schatten
            predicts include mean, +2 sigma, and -2 sigma models. See <a class="xref" href="ForceModel.html#ForceModel_Remarks" title="Remarks">Remarks</a> for
            details on the file format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Nominal</span>,
                    <span class="guilabel">PlusTwoSigma</span>,
                    <span class="guilabel">MinusTwoSigma</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Nominal</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.SchattenFile</span></td><td><p>The file name of the Schatten file with optional path
            information. See <a class="xref" href="ForceModel.html#ForceModel_Remarks" title="Remarks">Remarks</a> for details on file
            format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing name of the Schatten file with
                    optional path information.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="literal">'SchattenPredict.txt'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag.SchattenTimingModel</span></td><td><p>The timing model used from the Schatten file.
            Schatten predicts include a nominal solar cycle model, an early
            model, and a late model. See <a class="xref" href="ForceModel.html#ForceModel_Remarks" title="Remarks">Remarks</a> for details on the file
            format.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">NominalCycle</span>,
                    <span class="guilabel">EarlyCycle</span>,
                    <span class="guilabel">LateCycle</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">NominalCycle</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ErrorControl </span></td><td><p>Controls how error in the current integration step is
            estimated. The error in the current step is computed by the
            selection of <span class="guilabel">ErrorControl</span> and compared to the
            value set in the <span class="guilabel">Accuracy</span> field to determine
            if the step has an acceptable error or needs to be improved. All
            error measurements are relative error, however, the reference for
            the relative error changes depending upon the selection of
            <span class="guilabel">ErrorControl</span>. <span class="guilabel">RSSStep</span> is
            the Root Sum Square (RSS) relative error measured with respect to
            the current step. <span class="guilabel">RSSState</span> is the (RSS)
            relative error measured with respect to the current state.
            <span class="guilabel">LargestStep</span> is the state vector component
            with the largest relative error measured with respect to the
            current step. <span class="guilabel">LargestState</span> is the state
            vector component with the largest relative error measured with
            respect to the current state. Setting
            <span class="guilabel">ErrorControl</span> to <span class="guilabel">None</span>
            turns off error control and the integrator takes constant steps at
            the value defined by the smaller of
            <span class="guilabel">InitialStepSize</span> and
            <span class="guilabel">MaxStep</span> on the numerical
            integrator.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">None</span>,
                    <span class="guilabel">RSSStep</span>,
                    <span class="guilabel">RSSState</span>,
                    <span class="guilabel">LargestState</span>,
                    <span class="guilabel">LargestStep</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">RSSStep</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">External </span></td><td><p>This specifies a python script from which to get
            dynamics data from when using an external force model. The python
            script may be placed in the GMAT installation userfunctions/python
            folder, or in any folder specified by
            <span class="guilabel">PYTHON_MODULE_PATH</span> in the GMAT startup file.
            The name should not include the *.py suffix. This parameter, if
            not empty, specifies that an external force model is to be used.
            See <a class="xref" href="ForceModel.html#ForceModel_Remarks_ExternalForceModel" title="External Force Model">External Force Model</a> below for more
            details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing name of the python script
                    containing an external force model.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">External.DerivativesFunction</span></td><td><p>This specifies the name of the function within the
            Python script, <span class="guilabel">External</span>, for GMAT to call if
            an external force model is being used. See <a class="xref" href="ForceModel.html#ForceModel_Remarks_ExternalForceModel" title="External Force Model">External Force Model</a> below for more
            details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>String containing name of the python
                    function.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>GetDerivatives</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">External.ExcludeOtherForces</span></td><td><p>When true, GMAT will only use the external python
            force model for integration, excluding any GMAT forces defined in
            the <span class="guilabel">ForceModel</span>. If false, the force will be
            applied in addition to any other forces defined within the
            <span class="guilabel">ForceModel</span>. See <a class="xref" href="ForceModel.html#ForceModel_Remarks_ExternalForceModel" title="External Force Model">External Force Model</a> below for more
            details.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>false</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.Degree
            </span></td><td><p>The degree of the harmonic gravity field. This field
            is only active if there is a <span class="guilabel">PrimaryBody</span>.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0&lt;=<span class="guilabel">Degree</span>&lt;=Max Degree On
                    File</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>4 (When loading a custom file in the GUI, GMAT sets
                    <span class="guilabel">Degree</span> to the max value on the
                    file)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.Order</span></td><td><p>The order of the harmonic gravity field. This field
            is only active if there is a <span class="guilabel">PrimaryBody</span>.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0&lt;=<span class="guilabel">Order</span>&lt;=Max Degree On
                    File AND <span class="guilabel">Degree</span> &lt;=
                    <span class="guilabel">Order</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>4 (When loading a custom file in the GUI, GMAT sets
                    <span class="guilabel">Order</span> to the max value on the
                    file)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.PotentialFile</span></td><td><p>The gravity potential file. This field is only active
            if there is a <span class="guilabel">PrimaryBody</span>. See discussion
            below for detailed explanation of supported file types and how to
            configure gravity files.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>path and name of .cof OR .grv file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>JGM2.cof</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.StmLimit
            </span></td><td><p>The upper bound on the degree and order to be used
            when calculating the State Transition Matrix (STM). The STM will
            not use a degree or order greater than that specified by either
            the <span class="guilabel">Degree</span> and <span class="guilabel">Order</span>
            fields or the <span class="guilabel">StmLimit</span>. This field has no
            effect on the degree or order used to calculate the state, only
            the STM. This field is only active if there is a
            <span class="guilabel">PrimaryBody</span>. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Int &gt;= 0</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>100</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.TideFile</span></td><td><p>The tide file. This field is only active if there is
            a <span class="guilabel">PrimaryBody</span>. See discussion below for
            detailed explanation of supported file types and how to configure
            tide files.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>path and name of .tide file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">GravityField.&#8203;<em class="replaceable"><code>PrimaryBodyName</code></em>.TideModel</span></td><td><p>Flag for type of tide model. This field is always
            active but only used in the dynamics when there is a harmonic
            gravity model for the body.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">None</span>,
                    <span class="guilabel">Solid</span>,
                    <span class="guilabel">SolidAndPole</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Model</span></td><td><p>A GUI list of "configured' gravity files defined in
            the file gmat_startup_file.txt. <span class="guilabel">Model</span> allows
            you to quickly choose between gravity files distributed with GMAT.
            For example, if <span class="guilabel">PrimaryBody</span> is Earth, you can
            select among Earth gravity models provided with GMAT such as
            <span class="guilabel">JGM-2</span> and <span class="guilabel">EGM-96</span>. If you
            select <span class="guilabel">Other</span>, you can provide the path and
            filename for a custom gravity file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">JGM2</span>,
                    <span class="guilabel">JGM3</span>, <span class="guilabel">EGM96</span>,
                    <span class="guilabel">MARS50C</span>,
                    <span class="guilabel">MGNP180U</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set,get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">JGM2</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PointMasses</span></td><td><p>A list of celestial bodies to be treated as point
            masses in the force model. A body cannot be both a
            <span class="guilabel">PrimaryBody</span> and in the
            <span class="guilabel">PointMasses</span> list. An empty list "{}" removes
            all points masses from the list.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>array of <span class="guilabel">CelestialBodies</span> not
                    selected as a <span class="guilabel">PrimaryBody</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Empty List</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PrimaryBodies</span></td><td><p>A body modeled with a "complex" force model. A
            primary body can have an atmosphere and harmonic gravity model.
            You may assign multiple primary bodies if you wish to model
            harmonic gravity for multiple bodies. Please see the important
            <a class="link" href="ForceModel.html#ForceModel_Remarks_MultibodyHarmonicGravity" title="Using Multiple Harmonic Gravity Models">remarks</a>
            below regarding the use of multiple harmonic gravity models. The
            force model <span class="guilabel">CentralBody</span> should be one of the
            primary bodies, and primary bodies cannot be included in the
            <span class="guilabel">PointMasses</span> field. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource reference</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">CelestialBody</span> not included in
                    <span class="guilabel">PointMasses</span>.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Earth</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RelativisticCorrection</span></td><td><p>Sets relativistic correction on or
            off.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">On</span>,
                    <span class="guilabel">Off</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Off</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRP</span></td><td><p>Sets SRP force on or off. See the Remarks section for
            a detailed explanation of SRP configuration. The SRP model used is
            set in the SRP.Model field.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">On</span>,
                    <span class="guilabel">Off</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Off</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRP.Flux</span></td><td><p>The value of SRP flux at 1 AU. This field is only
            active in the script if <span class="guilabel">SRP</span> is on.
            </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>1200 &lt;<span class="guilabel">SRP.Flux</span> &lt;
                    1450</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1367</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>W/m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRP.Flux_Pressure</span></td><td><p>The solar flux at 1 AU divided by the speed of light.
            This field is only active in the script if SRP is on. See the
            Remarks section for a detailed explanation of SRP
            configuration.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>4.33e-6 &lt; <span class="guilabel">SRP.Flux_Pressure</span>
                    &lt; 4.84e-6</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>4.55982118135874e-006</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>W *s/m^3</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRP.SRPModel </span></td><td><p>User choice of Spacecraft area model for SRP
            computation. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Spherical, SPADFile,
                    NPlate</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Spherical</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRP.Nominal_Sun </span></td><td><p>The value of one Astronomical Unit in km used in
            scaling SRP.Flux, which is flux at 1 AU, to the flux at spacecraft
            distance from sun. This field is only active in the script if SRP
            is on. See the Remarks section for a detailed explanation of SRP
            configuration.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>135e6&lt;<span class="guilabel">Nominal_Sun</span>&lt;165e6</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>149597870.691</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="Propagator_GUI"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_ForceModel.png" align="middle" height="529"></td></tr></table></div></div><p>Settings for the<span class="guilabel"> ForceModel</span> object.</p></div><div class="refsection"><a name="ForceModel_Remarks"></a><h2>Remarks</h2><div class="refsection"><a name="N1A43C"></a><h3>Overview of Primary Body/Central Body and Field
      Interactions</h3><p>In GMAT, a primary body is a celestial body that is modeled with a
      complex force model which may include a spherical harmonic gravity
      model, tides, or drag. A body cannot appear in both the
      <span class="guilabel">PrimaryBodies</span> and <span class="guilabel">PointMasses</span>
      fields. GMAT allows multiple primary bodies in the force model, but
      restricts the model to a single primary body for orbit
      determination.</p><p>GMAT currently requires that the primary body is either the same
      as the <span class="guilabel">CentralBody</span> or set to
      <span class="guilabel">None</span> in the GUI. If you change the
      <span class="guilabel">CentralBody</span> in the GUI, GMAT changes the primary
      body to <span class="guilabel">None</span>, and you can then select between
      <span class="guilabel">None</span> and the central body. When you select a
      primary body in the GUI, the <span class="guilabel">Gravity</span> and
      <span class="guilabel">Drag</span> fields activate and allow you to select models
      for those forces consistent with the body selected in the
      <span class="guilabel">PrimaryBodies</span> field. For example, if you select
      Earth as the primary body, you can only select Earth drag models in the
      <span class="guilabel">Drag.AtmosphereModel</span> field. See the field list
      above for available models.</p></div><div class="refsection"><a name="N1A464"></a><h3>Configuring Gravitational Models</h3><p>GMAT supports point mass gravity, spherical harmonic, and tide
      modeling for all celestial bodies. On a <span class="guilabel">Propagator</span>,
      all celestial bodies are classified into two mutually exclusive
      categories: <span class="guilabel">PrimaryBodies</span>, and <span class="guilabel">Point
      Masses</span>. To model a body as a point mass, add it to the
      <span class="guilabel">PointMasses</span> list. When a primary body is selected
      on the GUI, the <span class="guilabel">CentralBody</span> and primary body must
      be the same.</p><p>Bodies modeled as <span class="guilabel">PointMasses</span> use the
      gravitational parameter defined on the body (i.e. Earth.Mu) in the
      equations of motion. Bodies defined as
      <span class="guilabel">PrimaryBodies</span> use the constants defined on the
      potential file in the equations of motion. GMAT supports two gravity
      file formats: the .cof format, and the STK .grv format. You can provide
      a custom potential file for your application as long as it is one of the
      supported formats. Potential files defined in the startup file are
      available in the <span class="guilabel">Model</span> list in the GUI. For
      example, the following lines in the startup file configure GMAT so that
      EGM96 is an available option for <span class="guilabel">Model</span> in the GUI
      when the primary body is Earth:</p><pre class="programlisting">EARTH_POT_PATH         = DATA_PATH/gravity/earth/
EGM96_FILE             = EARTH_POT_PATH/EGM96.cof </pre><p>Below is an example script snippet for configuring a custom
      gravity model.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.CentralBody = Earth
aForceModel.PrimaryBodies = {Earth}
aForceModel.GravityField.Earth.Degree = 21
aForceModel.GravityField.Earth.Order  = 21
aForceModel.GravityField.Earth.PotentialFile = 'c:\MyData\File.cof'</pre></div><div class="refsection"><a name="ForceModel_Remarks_MultibodyHarmonicGravity"></a><h3>Using Multiple Harmonic Gravity Models</h3><p>GMAT permits modeling non-spherical harmonic gravity models for
      multiple bodies simultaneously. This is an advanced capability that
      should only be used with caution, as it is easy to unknowingly
      misconfigure it and obtain inaccurate results. The user should note that
      there are few instances in the solar system where this capability is
      needed and should first consider if its use is appropriate or necessary.
      A critical aspect of this feature is the correct choice of the central
      body of the force model, which should generally be the body whose sphere
      of influence contains the trajectory. If choosing to use this feature,
      the user should choose the <span class="guilabel">ForceModel</span>
      <span class="guilabel">CentralBody</span> carefully and may wish to examine the
      results under different assumptions of the central body.</p><p>In the case of trajectory design and optimization, the common best
      practice is not to use multiple harmonic gravity models, but instead to
      segment the trajectory by sphere of influence, using force models
      employing only one primary body in each sphere of influence. The
      <span class="bold"><strong>Ex_LunarTransfer</strong></span> and <span class="bold"><strong>Ex_MarsBPlane</strong></span> sample scripts are good examples of
      this.</p><p>The current release of GMAT allows multiple harmonic gravity
      models for trajectory design and mission planning work, but restricts
      the use of harmonic gravity models to a single central body setting when
      performing orbit determination related tasks.</p><p>The script excerpt below shows an example of how to configure
      harmonic gravity modeling for both the Earth and Moon (Luna)
      simultaneously.</p><pre class="programlisting">Create ForceModel FM

FM.CentralBody                      = Earth;
FM.PrimaryBodies                    = {Earth, Luna};
FM.PointMasses                      = {Sun, Venus, Jupiter, Saturn};
FM.GravityField.Earth.Degree        = 12;
FM.GravityField.Earth.Order         = 12;
FM.GravityField.Earth.TideModel     = 'None';
FM.GravityField.Earth.PotentialFile = 'EGM96.cof';
FM.GravityField.Luna.Degree         = 50;
FM.GravityField.Luna.Order          = 50;
FM.GravityField.Luna.PotentialFile  = 'grgm900c.cof';
FM.GravityField.Luna.TideModel      = 'None';
FM.SRP                              = On
FM.ErrorControl                     = 'None'</pre></div><div class="refsection"><a name="N1A4A8"></a><h3>Force Model Settings and Choice of CentralBody can Greatly Affect
      Accuracy of Results</h3><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p><span class="guilabel">Sometimes, even when you might not think so, you must
        include gravitational forces from other bodies in the solar system in
        the force model.</span> When GMAT propagates a
        <span class="guilabel">Spacecraft</span>, the <span class="guilabel">CentralBody</span>
        parameter is used as the basis for the numerical integration. In
        particular, GMAT uses the &ldquo;relative&rdquo; equations of motion of the
        satellite with respect to the <span class="guilabel">CentralBody</span>. This
        equation involves terms from the Sun and other planets in the solar
        system. (See, for example, A. E. Roy, Orbital Motion 2e, Section 6.2,
        on the Equations of Relative Motion). If the GMAT user
        <span class="guilabel">Spacecraft</span> is in a multi-body orbit regime, or
        the user is switching the <span class="guilabel">CentralBody</span>, or is
        using a non-recommended (as explained below)
        <span class="guilabel">CentralBody</span>, <em class="citetitle"><span class="emphasis"><em>the user must
        include gravitational forces from other bodies in the solar
        system</em></span> in the <span class="guilabel">ForceModel</span></em>.
        Note that best practice is also to use the
        <span class="guilabel">CentralBody</span> associated with the dominant
        gravitational force on your <span class="guilabel">Spacecraft</span>. For
        example, if your <span class="guilabel">Spacecraft</span> is in low lunar
        orbit, the moon&rsquo;s gravity will be the dominant gravitational force.
        Thus, you should use the Moon as the <span class="guilabel">CentralBody</span>
        and not the Earth. One can also consider switching central bodies
        during your propagation as your <span class="guilabel">Spacecraft</span>
        approaches different spheres of influence.</p></div></div><div class="refsection"><a name="N1A4D8"></a><h3>Overview of Tide Model Field Interactions</h3><p>By default, the tide data source is set to
      <span class="guilabel">None</span> and the tide model selector is disabled if no
      tide model is selected. To use a tide model, first the tide data source
      must be changed to either <span class="guilabel">Inherited</span> or
      <span class="guilabel">Tide File</span>, at which point the Tide Model selector
      becomes enabled to select from the tide models supported by the tide
      data source. See the field list above for available models. The
      <span class="guilabel">Inherited</span> option indicates that the data for the
      tide model is provided either by the gravity potential file or the data
      is built into GMAT. The tide data contained in a gravity potential file
      has precedence over any built-in values. The <span class="guilabel">Tide
      File</span> option enables the file selector to choose a file
      containing the Love numbers to be used as the data source for the tide
      model. The tide data contained in a tide file has precedence over all
      other tide data sources.</p></div><div class="refsection"><a name="N1A4EC"></a><h3>Configuring Tide Models</h3><p>GMAT supports solid tide modeling for all central bodies, and both
      solid and pole tide modeling for the Earth. Tide models can only be used
      if a <span class="guilabel">PrimaryBody</span> is set. GMAT contains built-in
      values for both solid and pole tides for the Earth. External files can
      also be used to provide the Love numbers to be used in the tide model,
      either from a gravity file that supports tides, or a separate tide
      file.</p><p>If a gravity file with Love numbers is provided, those Love
      numbers will be used for the solid tide model calculations. If a tide
      file is provided, the Love numbers in the tide file will be used. If
      both a gravity file with Love numbers and a tide file are provided, the
      Love numbers from both files will be used, with the Love numbers in the
      tide file having precedence over the gravity file. Only if no tide file
      is provided and the gravity potential file has no love numbers are
      GMAT's default Love numbers used for the Earth. GMAT's built-in values
      are the only data source for pole tides.</p><p>Below is an example script snippet for configuring a custom
      gravity model including Lunar solid tides.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.CentralBody = Luna
aForceModel.PrimaryBodies = {Luna}
aForceModel.GravityField.Luna.Degree = 21
aForceModel.GravityField.Luna.Order  = 21
aForceModel.GravityField.Luna.PotentialFile = 'c:\MyData\File.cof'
aForceModel.GravityField.Luna.TideFile = 'c:\MyData\File.tide'
aForceModel.GravityField.Luna.TideModel = 'Solid'</pre><p>Tide files use the .tide file extension. You can provide a custom
      tide file for your application as long as it is in the supported format.
      Tide files contain the Love numbers to be used to model the solid tides.
      Tide files can include the k2, k3, and k+ coefficients. The format used
      by the tide file is 'k {degree} {order} {value}' or 'kplus {order}
      {value}' for k+.</p><p>Below is a sample tide file using the built-in values that GMAT
      uses for the Earth's Love numbers</p><pre class="programlisting">k 2 0 0.30190
k 2 1 0.29830
k 2 2 0.30102
k 3 0 0.093
k 3 1 0.093
k 3 2 0.093
k 3 3 0.094
kplus 0 -0.00087
kplus 1 -0.00079
kplus 2 -0.00057</pre></div><div class="refsection"><a name="N1A500"></a><h3>Zero Tide and Tide Free Models</h3><p>The selection of a tide model is closely linked to the
      gravitational potential model that is used. Some gravitational potential
      models incorporate some tidal effects into the gravitational potential
      model. Two common ways gravitational models handle modeling tidal forces
      are by being tide-free and zero-tide. Tide free gravitational models
      contain no effects of tidal forces in the gravitational potential, while
      zero tide gravitational models contain the permanent (time-independent)
      effect of tides on the potential. For STK .grv files, the
      "IncludesPermTides" keyword is recognized to identify if the
      gravitational potential model includes permanent tide effects, however
      the coefficients in the "TideFreeValues" and "ZeroTideValues" keyword
      blocks are currently ignored.</p><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>Caution: If a zero tide gravitational model is used with the
        <span class="guilabel">Solid</span> or <span class="guilabel">SolidAndPole</span> tide
        options, the effect of permanent tides is double counted and may yield
        inaccurate results. For further a more in-depth discussion, please
        consult the <span class="emphasis"><em>IERS Conventions (2010)</em></span>. GMAT does
        not convert between a zero tide and tide free potential, therefore the
        user must pay attention to which potential they intend on using,
        particularly when modeling solid tides.</p></div></div><div class="refsection"><a name="N1A511"></a><h3>Configuring Drag Models</h3><p>GMAT supports many density models for Earth including
      <span class="guilabel">Jacchia-Roberts</span> and various MSISE models. Density
      models for non-Earth bodies -- the Mars-GRAM model for example -- are
      included using custom plug-in components and are currently only
      supported in the script interface. With any density model, the user also
      has the choice of using either a spherical spacecraft area model or a
      SPAD file for the spacecraft area computation. Spacecraft area modeling
      is selected using the <span class="guilabel">Drag.DragModel</span> parameter.
      More details of SPAD area modeling can be found in <a class="xref" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties"><span class="refentrytitle">Spacecraft Ballistic/Mass Properties</span></a></p><p>To configure Earth density models, select Earth as the primary
      body, In the GUI, this activates the <span class="guilabel">AtmosphereModel
      </span>list. You can configure the solar flux values using the
      <span class="guilabel">Setup</span> button next to the
      <span class="guilabel">AtmosphereModel</span> list after you have selected an
      atmosphere model. Below is an example script snippet for configuring the
      <span class="guilabel">NRLMSISE00</span> density model.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.PrimaryBodies = {Earth}
aForceModel.Drag.AtmosphereModel = NRLMSISE00
</pre><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>Caution: GMAT uses the original single precision FORTAN code
        developed by the scientists who created the MSISE models. At low
        altitudes, the single precision density can cause numeric issues in
        the double precision integrator step size control and integration can
        be unacceptably slow. You can avoid the performance issue by using
        either fixed step integration or by using a relatively high
        <span class="guilabel">Accuracy</span> value such as 1e-8. You may need to
        experiment with the <span class="guilabel">Accuracy</span> setting to a value
        acceptable for your application.</p></div><p>Note that when you select <span class="guilabel">None</span> for
      <span class="guilabel">Drag.AtmosphereModel</span>, the fields associated with
      density configuration, such as <span class="guilabel">Drag.F107</span>,
      <span class="guilabel">Drag.F107A</span>, and
      <span class="guilabel">Drag.MagneticIndex</span> and others are inactive and must
      be removed from your script file to avoid parsing errors. When working
      in the GUI, this is performed automatically.</p><p>The table below describes the limits on altitude for drag models
      supported by GMAT.</p><div class="informaltable"><table border="1"><colgroup><col width="28%"><col width="29%"><col width="43%"></colgroup><thead><tr><th>Model</th><th>Theoretical Altitude (h) Limits</th><th>Comments</th></tr></thead><tbody><tr><td><span class="guilabel">MSISE86</span></td><td><p>90 &lt; h &lt; 1000</p></td><td><p>GMAT will not allow propagation below 90 km
              altitude.</p></td></tr><tr><td><span class="guilabel">MSISE90</span></td><td><p>0 &lt; h &lt;1000</p></td><td><p>GMAT will allow propagation below 0 km altitude but
              results are non-physical.</p></td></tr><tr><td><span class="guilabel">NRLMSISE00</span></td><td>0 &lt; h &lt;1000</td><td><p> GMAT will allow propagation below 0 km altitude
              but results are non-physical. </p></td></tr><tr><td><span class="guilabel">JacchiaRoberts</span></td><td><p>h &gt; 100</p></td><td><p>GMAT will not allow propagation below 100 km
              altitude. </p></td></tr></tbody></table></div><div class="refsection"><a name="ForceModel_Remarks_ConfiguringDragModels_MarsGRAM2005"></a><h4>MarsGRAM2005</h4><p>When <span class="guilabel">PrimaryBody</span> is
        <span class="guilabel">Mars</span>, you can choose Mars-GRAM 2005 as your
        atmosphere model. This model is only available when the
        <code class="filename">libMarsGRAM</code> plugin is available and enabled in
        the GMAT startup file.</p><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>As of version R2015a, you can only have one unique Mars-GRAM
          force model configuration in a given script. If you include multiple
          propagators with Mars-GRAM force models with different Mars-GRAM
          configurations, the different configurations are not honored, and
          all of the propagators will use the same configuration for
          Mars-GRAM.</p></div><p>When using the <span class="guilabel">MarsGRAM2005</span> atmosphere
        model, three new fields are available in the script language (but not
        the GUI):</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p><span class="guilabel">Drag.InputFile</span></p></li><li class="listitem"><p><span class="guilabel">Drag.DensityModel</span></p></li></ul></div><p>See the <a class="link" href="ForceModel.html#ForceModel_Fields" title="Fields">Fields section</a>
        for details on these fields.</p><p>In addition, the space weather fields are treated as
        follows:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p><span class="guilabel">Drag.F107</span>: value of 10.7 cm solar
              flux at 1 AU, as documented in the <a class="link" href="ForceModel.html#ForceModel_Fields" title="Fields">Fields section</a></p></li><li class="listitem"><p><span class="guilabel">Drag.F107A</span>: not used</p></li><li class="listitem"><p><span class="guilabel">Drag.MagneticIndex</span>: not used</p></li></ul></div><p>The Mars-GRAM 2005 input file is a text file in FORTRAN NAMELIST
        format. Most variables in this file are passed directly to the
        Mars-GRAM model and are used as intended. However, some are replaced
        internally by GMAT-supplied values. The following table lists those
        input variables that are handled specially.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Input Variable</th><th>GMAT usage</th></tr></thead><tbody><tr><td>(Unlisted)</td><td>Passed through to Mars-GRAM 2005 model</td></tr><tr><td><code class="literal">DATADIR</code></td><td>Always
                <code class="literal">'../data/atmosphere/MarsGRAM2005/binFiles'</code></td></tr><tr><td><code class="literal">GCMDIR</code></td><td>Always
                <code class="literal">'../data/atmosphere/MarsGRAM2005/binFiles'</code></td></tr><tr><td><code class="literal">IERT</code></td><td>Always 1 (Earth-receive time)</td></tr><tr><td><code class="literal">IUTC</code></td><td>Always 0 (TT time)</td></tr><tr><td><code class="literal">MONTH</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">MDAY</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">MYEAR</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">NPOS</code></td><td>Always 1</td></tr><tr><td><code class="literal">IHR</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">IMIN</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">ISEC</code></td><td>Replaced by current propagation epoch</td></tr><tr><td><code class="literal">LonEW</code></td><td>Always 1 (positive East)</td></tr><tr><td><code class="literal">F107</code></td><td>Replaced by value of
                <span class="guilabel">Drag.F107</span></td></tr><tr><td><code class="literal">FLAT</code></td><td>Replaced by current propagation state</td></tr><tr><td><code class="literal">FLON</code></td><td>Replaced by current propagation state</td></tr><tr><td><code class="literal">FHGT</code></td><td>Replaced by current propagation state</td></tr><tr><td><code class="literal">MOLAhgts</code></td><td>Always 0 (reference ellipsoid)</td></tr><tr><td><code class="literal">iup</code></td><td>Always 0 (no output)</td></tr><tr><td><code class="literal">ipclat</code></td><td>Always 0 (planetographic input)</td></tr><tr><td><code class="literal">requa</code></td><td>Replaced by value of
                <span class="guilabel">Mars.EquatorialRadius</span></td></tr><tr><td><code class="literal">rpole</code></td><td>Replaced by GMAT's value of Mars polar radius
                (calculated from <span class="guilabel">Mars.EquatorialRadius</span>
                and <span class="guilabel">Mars.Flattening</span>)</td></tr></tbody></table></div><p>The input file is read by the Mars-GRAM 2005 model code, which
        has limited error checking. If the input file or data files are
        incorrect or missing, GMAT may exhibit unintended behavior. Note that
        local winds returned by the Mars-GRAM 2005 model are not included in
        GMAT's drag model.</p></div></div><div class="refsection"><a name="N1A669"></a><h3>Space Weather Data for Earth Atmospheric Density Models</h3><p>GMAT supports several space weather input types for drag modeling
      including constant flux and geomagnetic index values, a historical data
      file, and a predicted data file. You can separately configure the data
      used for historical data and predicted data. For historical data you can
      choose between constant values and a CSSI space weather file. For
      predicted data you can choose between constant values, a CSSI space
      weather file, and a Schatten predict file. Each of those sources is
      discussed in detail below.</p><p>The precedence for data source is determined by the current
      propagation epoch (i.e. the epoch when density is evaluated), and the
      epochs contained on the data files. The following rules are
      applied:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>If both historical data and predicted data sources are set to
          <span class="guilabel">ConstantFluxAndGeoMag</span>, then constant values are
          always used.</p></li><li class="listitem"><p>If you have selected a CSSI file as the historical data source
          and the current propagation epoch falls before the last row of data
          in the CSSI file's observed data block, then the CSSI data is used.
          If the current propagation epoch is later than the last record in
          the CSSI observed data block, the specified predicted data source is
          used. If the propagation epoch is before the first observed data
          record the first record in the file is used.</p></li><li class="listitem"><p>If you have selected the Schatten file as the predicted data
          source and a CSSI file as the historic source, GMAT will switch to
          the Schatten file at the end of the observed data block in the CSSI
          data file.</p></li><li class="listitem"><p>If you have selected the Schatten file as the predicted data
          source and <span class="guilabel">ConstantFluxAndGeoMag</span> as the
          historic source, GMAT will switch to the Schatten data file at the
          start of the Schatten data records.</p></li><li class="listitem"><p>The Schatten file option may not be used as a historic space
          weather source.</p></li></ul></div></div><div class="refsection"><a name="N1A686"></a><h3>Constant Values</h3><p>GMAT supports constant flux and geomagnetic index values for all
      Earth density models. You configure GMAT to use those values for
      historical and predicted data as shown below using NRLMSISE00 for the
      example.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.Drag.AtmosphereModel = NRLMSISE00
aForceModel.Drag.HistoricWeatherSource = 'ConstantFluxAndGeoMag'
aForceModel.Drag.PredictedWeatherSource = 'ConstantFluxAndGeoMag'
aForceModel.Drag.F107 = 150
aForceModel.Drag.F107A = 150
aForceModel.Drag.MagneticIndex = 3
</pre></div><div class="refsection"><a name="N1A68D"></a><h3>CSSI Space Weather Data</h3><p>You can provide a Center for Space Standards and Innovation (CSSI)
      file for historical and predicted space weather data. The CCSI file
      format is described in detail at the <a class="link" href="https://celestrak.com/SpaceData/" target="_top">Celestrak</a> website
      and the files are available for download at that site and <a class="link" href="ftp://ftp.agi.com/pub/DynamicEarthData/" target="_top">here</a>. You
      can configure GMAT to use the CSSI file for historical and predicted
      data as shown below.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.Drag.AtmosphereModel = NRLMSISE00
aForceModel.Drag.HistoricWeatherSource = 'CSSISpaceWeatherFile'
aForceModel.Drag.PredictedWeatherSource = 'CSSISpaceWeatherFile'
aForceModel.Drag.CSSISpaceWeatherFile = 'SpaceWeather-All-v1.2.txt'
</pre><p>You can provide a full or relative path to the file, or put the
      file in GMAT&rsquo;s data file folders documented in the startup file
      help.</p></div><div class="refsection"><a name="N1A69E"></a><h3>Schatten Space Weather Data</h3><p>You configure GMAT to use Schatten predicted data as shown below.
      Schatten data files may only be used as predicted space weather
      data.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.Drag.AtmosphereModel = NRLMSISE00
aForceModel.Drag.PredictedWeatherSource = 'SchattenFile'
aForceModel.Drag.SchattenFile = 'SchattenPredict.txt'
aForceModel.Drag.SchattenErrorModel = 'Nominal'
aForceModel.Drag.SchattenTimingModel = 'NominalCycle'
</pre><p>The Schatten file is distributed by the Flight Dynamics Facility
      (FDF) at Goddard Space Flight Center and made accessible at the
      Community Coordinated Modeling Center (CCMC) website. You can access
      current and past Schatten data files at the
      https://iswa.ccmc.gsfc.nasa.gov/iswa_data_tree/model/solar/FDF/SchattenSolarFluxPrediction/
      URL. Note that when downloading Schatten files from the CCMC, you must
      manually add the BEGIN_DATA and END_DATA tags as shown in the example
      below.</p><p>You can provide a full or relative path to the file, or put the
      file in GMAT&rsquo;s data file folders documented in the startup file help.
      Additionally you can choose between <span class="guilabel">Nominal</span>,
      <span class="guilabel">PlusTwoSigma</span>, and
      <span class="guilabel">MinusTwoSigma</span> for the
      <span class="guilabel">SchattenErrorModel</span>, and between
      <span class="guilabel">NominalCycle</span>, <span class="guilabel">EarlyCycle</span>, and
      <span class="guilabel">LateCycle</span> for the
      <span class="guilabel">SchattenTimingModel</span>. Note that GMAT reads the raw
      file containing all permutation of mean, +2 sigma, and -2 sigma, and
      nominal, early and late solar cycles. The files from the FDF must be
      modified to include keywords that indicate when data starts and ends as
      shown below:</p><pre class="programlisting">           NOMINAL TIMING      EARLY TIMING        LATE TIMING      
 mo. yr.  mean +2sig -2sig ap mean +2sig -2sig ap mean +2sig -2sig ap
BEGIN_DATA 
  2 2011    92  107   76    9  105  125   85   10   77   87   66    8
  3 2011    93  110   77    9  106  128   86   10   79   89   67    8
  4 2011    95  112   78    9  108  129   87   10   80   92   69    8
END_DATA
</pre><p>Data must be formatted according to FORMAT(I3,I5,I6,11I5), and no
      comments or blank lines can occur between the BEGIN_DATA and END_DATA
      keywords.</p></div><div class="refsection"><a name="N1A6C5"></a><h3>Configuring SRP Models</h3><p>GMAT supports a spherical SRP model, and a SPAD file for high
      fidelity SRP modeling. Both models use a dual cone model for central
      body shadowing of the spacecraft. See the <a class="xref" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties"><span class="refentrytitle">Spacecraft Ballistic/Mass Properties</span></a>
      documentation for configuring a SPAD file for a spacecraft. The script
      snippet below shows how to configure two
      <span class="guilabel">ForceModels</span>, one that uses
      <span class="guilabel">Spherical</span> area modeling (the default), and one that
      uses a <span class="guilabel">SPADFile</span>.</p><pre class="programlisting">% A spherical SRP model
Create ForceModel aForceModel_1

aForceModel_1.PrimaryBodies = {Earth}
aForceModel_1.SRP = On
aForceModel_1.SRP.SRPModel = Spherical

% A SPAD SRP model
Create ForceModel aForceModel_2

aForceModel_2.PrimaryBodies = {Earth}
aForceModel_2.SRP = On
aForceModel_2.SRP.SRPModel = SPADFile
</pre><p>You can define the solar flux using two approaches which are
      currently only supported in the script interface. One approach is to
      define the flux value using the <span class="guilabel">SRP.Flux</span> field and
      the value of an astronomical unit (in km) using the
      <span class="guilabel">Nominal_Sun</span> field as shown in the following
      example.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.PrimaryBodies = {Earth}
aForceModel.SRP = On
aForceModel.SRP.Flux = 1367
aForceModel.SRP.Nominal_Sun = 149597870.691</pre><p>An alternative approach is to define the flux pressure at 1
      astronomical unit using the <span class="guilabel">Flux_Pressure</span> field as
      shown below..</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.PrimaryBodies = {Earth}
aForceModel.SRP = On
aForceModel.SRP.Flux_Pressure = 4.53443218374393e-006
aForceModel.SRP.Nominal_Sun = 149597870.691</pre><p>If you mix flux settings, as shown in the example below, GMAT will
      use the last approach in the script. Here, GMAT will use the
      <span class="guilabel">Flux_Pressure</span> setting.</p><pre class="programlisting">Create ForceModel aForceModel

aForceModel.PrimaryBodies = {Earth}
aForceModel.SRP = On
aForceModel.SRP.Flux = 1370
aForceModel.SRP.Nominal_Sun = 149597870
aForceModel.SRP.Flux_Pressure = 4.53443218374393e-006</pre><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>Caution: GMAT&rsquo;s default option for configuring solar flux for an
        SRP model is to use <span class="guilabel">SRP.Flux</span> and<span class="guilabel">
        Nominal_Sun</span> fields. If you initially configured the
        <span class="guilabel">Flux_Pressure</span> field, when you save your mission
        via the save button in the toolbar, GMAT will write out
        <span class="guilabel">SRP.Flux</span> and <span class="guilabel">Nominal_Sun</span>
        values consistent with your setting of
        <span class="guilabel">Flux_Pressure</span>.</p></div></div><div class="refsection"><a name="N1A706"></a><h3>Variational Equations and the STM</h3><p>GMAT can optionally propagate the orbit State Transition Matrix
      (STM). For more information on how to configure GMAT to compute the STM,
      see the Propagate command documentation.</p><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>Caution: GMAT allows you to propagate the State Transition
        Matrix (STM) along with the orbital state. However, not all
        variational terms are implemented for STM propagation. The following
        are implemented: point mass perturbation, spherical harmonics (with
        tide models), drag, and solar radiation pressure. The following are
        NOT implemented: relativistic terms and finite burns. Additionally,
        the SRP variational term does not include the partial derivative of
        the percent shadow with respect to orbital state. This approximation
        is acceptable for orbits with short penumbra durations but is
        inaccurate for orbits that spend relatively long periods of time in
        penumbra.</p></div></div><div class="refsection"><a name="ForceModel_Remarks_ExternalForceModel"></a><h3>External Force Model</h3><p>This feature expands the capability of GMAT
      <span class="guilabel">ForceModel</span>s by allowing the use of external,
      python-defined force models in GMAT integrators. Python scripts may be
      added to the Python userfunctions folder, or any other folder specified
      in the GMAT startup file with <span class="guilabel">PYTHON_MODULE_PATH</span>.
      Additionally, the GMAT <a class="link" href="PythonInterface.html#System_PythonInterface_InterfaceSetup">python interface</a>
      must be configured. To use an external force model, create a python
      script containing a function that GMAT will call during the orbit
      integration. By default, this function should be called
      GetDerivatives(), but the function name can be changed using the
      <span class="guilabel">ForceModel.External.DerivativesFunction</span> parameter.
      This function must take in four parameters in the order described below.
      The python names of these parameters can be changed by the user within
      the python function definition:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>state: list, The propagation state vector. The size and
          contents of this vector can vary and depend on the user
          <span class="guilabel">ForceModel</span> and <span class="guilabel">Propagator</span>
          configuration. Contains only floating point values.</p></li><li class="listitem"><p>epoch: float, The current epoch in
          <span class="guilabel">A1ModJulian</span> format.</p></li><li class="listitem"><p>description: list, A list of strings that describe each state
          vector item. The size and order of this list matches that of the
          state vector.</p></li><li class="listitem"><p>order: int (1,2), The order of the derivatives to be
          calculated by the ExternalForceModel. If order is 1, the values
          returned should be first order derivatives of the state vector. If
          order is 2, the values returned should be the second order
          derivatives. Most GMAT integrators use only 1st order derivatives.
          Currently, the only integrator that uses 2nd order derivatives is
          RungeKutta68.</p></li></ul></div><p>The derivatives function must return a list of floats that matches
      the size of the state vector. Use the table below to help you determine
      what GMAT expects to receive from the derivatives function. Be sure the
      list returned from the derivatives function is in the same order as
      specified in the state parameter list description passed to the
      function. Any matrices (like the <span class="bold"><strong>A</strong></span>
      matrix) to be returned must first be reshaped into linear arrays and
      appended to the state derivatives in the proper order.</p><div class="informaltable"><table border="1"><colgroup><col width="15%"><col width="22%"><col width="30%"><col width="33%"></colgroup><thead><tr><th align="center">State Component</th><th align="center">What GMAT Provides</th><th align="center">What to Return from the Derivatives
              Function</th><th align="center">Comments</th></tr></thead><tbody><tr><td>CartesianState</td><td>Position and velocity in the central body inertial (J2000
              axes) frame</td><td>For a first-order integrator, return [vx, vy, vz, ax, ay,
              az], where [ax, ay, az] are the acceleration components of the
              external force in inertial coordinates. For a second-order
              integrator, return [ax, ay, az, 0, 0, 0].</td><td>Velocity components of the returned list are ignored;
              GMAT will automatically substitute the velocity from the state
              vector. You may return zero for the velocity components. Most
              GMAT integrators are first-order integrators. RungeKutta68 is
              the only second-order integrator currently implemented.</td></tr><tr><td>TotalMass</td><td>Current spacecraft mass (kg)</td><td>The mass flow rate, dm/dt</td><td>Units are kg/sec.</td></tr><tr><td>STM (State Transition Matrix)</td><td>State transition matrix <m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                    <m:msub>
                      <m:mi>&#934;</m:mi>

                      <m:mn>i</m:mn>
                    </m:msub>
                  </m:math> from <span class="bold"><strong>t<sub>i-1</sub></strong></span> to <span class="bold"><strong>t<sub>i</sub></strong></span>, such that
              <m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                    <m:msub>
                      <m:mi>&#934;</m:mi>

                      <m:mn>i</m:mn>
                    </m:msub>

                    <m:mo>=</m:mo>

                    <m:mi>&#934;</m:mi>

                    <m:mo>(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>i-1</m:mn>
                    </m:msub>

                    <m:mo>,</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>i</m:mn>
                    </m:msub>

                    <m:mo>).</m:mo>
                  </m:math></td><td>The <span class="bold"><strong>A</strong></span> matrix</td><td>See the GMAT Math Specification, Section 4.1.3 and Eq.
              4.14 for a definition of the A matrix.</td></tr><tr><td>Covariance</td><td>A 6x6 placeholder matrix.</td><td>A 6x6 placeholder matrix. These values can be anything
              (zeros are recommended), and must be returned by the user in the
              appropriate locations of the partials vector, but they have no
              effect on the covariance propagation.</td><td>The external force model currently has no effect on
              covariance propagation. If using an external force model and
              propagating the covariance, an additional 6x6 matrix labeled
              "Covariance" will be included in the state. This matrix should
              be ignored. However, the user must pad out the returned partial
              derivatives vector with 36 place-holder elements.</td></tr></tbody></table></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>When returning 1st order derivatives, GMAT will ignore the first
        three values. These values correspond to Cartesian velocity of the
        first <span class="guilabel">Spacecraft</span> in the state vector. These
        values are taken from the current state vector internally by GMAT. It
        is recommended users return [0.0, 0.0, 0.0] for these values.</p></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Since the external force model necessitates using an external
        python script, a user can connect this script to GMATs python API.
        This allows for a variety of features including giving the user access
        to various spacecraft parameters from inside the derivatives function.
        A note of caution, spacecraft parameters accessed through the API from
        within an external force model function, may not always be up to date.
        Internal spacecraft objects are not updated every step as various
        integrators perform sub steps in which state data may be changed. The
        GMAT spacecraft object will only be updated after completing a full
        step. Any data that is contained in the state vector parameter should
        only be accessed from the state vector parameter passed in directly to
        the external force model derivatives function. However, any data not
        passed directly into the derivatives function is safe to access from
        the API.</p></div></div></div><div class="refsection"><a name="Propagator_Examples"></a><h2>Examples</h2><div class="informalexample"><p>A <span class="guilabel">ForceModel</span> for point mass
      propagation.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create ForceModel aForceModel

aForceModel.CentralBody = Earth
aForceModel.PointMasses = {Earth}

Create Propagator aProp

aProp.FM = aForceModel

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = .2}</code></pre></div><div class="informalexample"><p>A <span class="guilabel">ForceModel</span> for high fidelity low Earth
      orbit propagation.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create ForceModel aForceModel

aForceModel.CentralBody = Earth
aForceModel.PrimaryBodies = {Earth}
aForceModel.PointMasses = {Sun, Luna}
aForceModel.SRP = On
aForceModel.RelativisticCorrection = On
aForceModel.ErrorControl = RSSStep
aForceModel.GravityField.Earth.Degree = 20
aForceModel.GravityField.Earth.Order = 20
aForceModel.GravityField.Earth.PotentialFile = 'EGM96.cof'
aForceModel.GravityField.Earth.TideModel = 'None'
aForceModel.Drag.AtmosphereModel = MSISE90
aForceModel.Drag.F107 = 150
aForceModel.Drag.F107A = 150
aForceModel.Drag.MagneticIndex = 3
aForceModel.SRP.Flux = 1359.388569998901
aForceModel.SRP.SRPModel = Spherical;
aForceModel.SRP.Nominal_Sun = 149597870.691

Create Propagator aProp

aProp.FM = aForceModel

BeginMissionSequence

Propagate aProp(aSat){aSat.ElapsedDays = .2}</code></pre></div><div class="informalexample"><p>A <span class="guilabel">ForceModel</span> that uses a SPAD SRP
      File.</p><pre class="programlisting"><code class="code">Create Spacecraft aSpacecraft

aSpacecraft.DryMass   = 2000
aSpacecraft.SPADSRPFile = '..\data\vehicle\spad\SphericalModel.spo'
aSpacecraft.SPADSRPScaleFactor = 1

Create ForceModel aFM

aFM.SRP = On
aFM.SRP.SRPModel = SPADFile

Create Propagator aProp

aProp.FM = aFM

BeginMissionSequence

Propagate aProp(aSpacecraft) {aSpacecraft.ElapsedDays = 0.2}</code></pre></div><div class="informalexample"><p>A <span class="guilabel">ForceModel</span> for high fidelity lunar orbit
      propagation.</p><pre class="programlisting"><code class="code">Create Spacecraft moonSat

moonSat.DateFormat = UTCGregorian
moonSat.Epoch.UTCGregorian = 01 Jun 2004 12:00:00.000
moonSat.CoordinateSystem = MoonMJ2000Eq
moonSat.DisplayStateType = Cartesian
moonSat.X = -1486.792117191545200
moonSat.Y = 0.0
moonSat.Z = 1486.792117191543000
moonSat.VX = -0.142927729144255
moonSat.VY = -1.631407624437537
moonSat.VZ = 0.142927729144255

Create CoordinateSystem MoonMJ2000Eq

MoonMJ2000Eq.Origin = Luna
MoonMJ2000Eq.Axes   = MJ2000Eq

Create ForceModel MoonLP165P

MoonLP165P.CentralBody = Luna
MoonLP165P.PrimaryBodies = {Luna}
MoonLP165P.SRP = On
MoonLP165P.SRP.Flux = 1367
MoonLP165P.SRP.Nominal_Sun = 149597870.691
MoonLP165P.Gravity.Luna.PotentialFile = ../data/gravity/luna/LP165P.cof
MoonLP165P.Gravity.Luna.Degree = 20
MoonLP165P.Gravity.Luna.Order = 20

Create Propagator RKV89

RKV89.FM = MoonLP165P

BeginMissionSequence

Propagate RKV89(moonSat) {moonSat.ElapsedSecs = 300}</code></pre></div><div class="informalexample"><p>A <span class="guilabel">ForceModel</span> using an
      <span class="guilabel">ExternalForceModel</span>.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat

Create ForceModel ExternalForceModel
ExternalForceModel.External = SimpleExternalForceModel;
ExternalForceModel.External.ExcludeOtherForces = true;

Create Propagator aProp
aProp.FM = ExternalForceModel

BeginMissionSequence

Propagate aProp(aSat) {aSat.ElapsedDays = .2}</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="FieldOfView.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Formation.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">FieldOfView&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Formation</td></tr></table></div></body></html>