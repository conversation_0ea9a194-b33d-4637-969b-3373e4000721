<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>GMAT R2011a Release Notes</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ReleaseNotes.html" title="Release Notes"><link rel="prev" href="ReleaseNotesR2012a.html" title="GMAT R2012a Release Notes"><link rel="next" href="BookIndex.html" title="Index"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">GMAT R2011a Release Notes</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ReleaseNotesR2012a.html">Prev</a>&nbsp;</td><th align="center" width="60%">Release Notes</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="BookIndex.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ReleaseNotesR2011a"></a>GMAT R2011a Release Notes</h2></div></div></div><p>The General Mission Analysis Tool (GMAT) version R2011a was released
  April 29, 2011 on the following platforms:</p><div class="informaltable"><table border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td>Windows (XP, Vista, 7)</td><td>Beta</td></tr><tr><td>Mac OS X (10.6)</td><td>Alpha</td></tr><tr><td>Linux</td><td>Alpha</td></tr></tbody></table></div><p>This is the first release since September 2008, and is the 4th public
  release for the project. In this release:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>100,000 lines of code were added</p></li><li class="listitem"><p>798 bugs were opened and 733 were closed</p></li><li class="listitem"><p>Code was contributed by 9 developers from 4 organizations</p></li><li class="listitem"><p>6216 system tests were written and run nightly</p></li></ul></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31B65"></a>New Features</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31B68"></a>OrbitView</h4></div></div></div><p>GMAT's old OpenGLPlot 3D graphics view was completely revamped and
      renamed OrbitView. The new OrbitView plot supports all of the features
      of OpenGLPlot, but adds several new ones:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Perspective view instead of orthogonal</p></li><li class="listitem"><p>Stars and constellations (with names)</p></li><li class="listitem"><p>A new default Earth texture</p></li><li class="listitem"><p>Accurate lighting</p></li><li class="listitem"><p>Support for user-supplied spacecraft models in 3ds and POV
          formats.</p></li></ul></div><p>All existing scripts will use the new OrbitView object
      automatically, with no script changes needed. Here's a sample of what
      can be done with the new graphics:</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/orbitview-1.png" height="266"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31B89"></a>User-Defined Celestial Bodies</h4></div></div></div><p>Users can now define their own celestial bodies (Planets, Moons,
      Asteroids, and Comets) through the GMAT interface, by right-clicking on
      the Sun resource (for Planets, Asteroids, and Comets) or any other Solar
      System resource (for Moons). User-defined celestial bodies can be
      customized in many ways:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Mu (for propagation), radius and flattening (for calculating
          altitude)</p></li><li class="listitem"><p>User-supplied texture file, for use with OrbitView</p></li><li class="listitem"><p>Ephemeris from two-body propagation of an initial Keplerian
          state or from a SPICE kernel</p></li><li class="listitem"><p>Orientation and spin state</p></li></ul></div><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/udbodies.png"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31BA4"></a>Ephemeris Output</h4></div></div></div><p>GMAT can now output spacecraft ephemeris files in CCSDS-OEM and
      SPK formats by using the EphemerisFile resource. For each ephemeris, you
      can customize:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Coordinate system</p></li><li class="listitem"><p>Interpolation order</p></li><li class="listitem"><p>Step size</p></li><li class="listitem"><p>Epoch range</p></li></ul></div><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/ephemerisfile.png"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31BBF"></a>SPICE Integration for Spacecraft</h4></div></div></div><p>Spacecraft in GMAT can now be propagated using data from a SPICE
      kernel rather than by numerical integration. This can be activated on
      the SPICE tab of the Spacecraft resource, or through the script. The
      following SPICE kernels are supported:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>SPK/BSP (orbit)</p></li><li class="listitem"><p>CK (attitude)</p></li><li class="listitem"><p>FK (frame)</p></li><li class="listitem"><p>SCLK (spacecraft clock)</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31BD2"></a>Plugins</h4></div></div></div><p>New features can now be added to GMAT through plugins, rather than
      being compiled into the GMAT executable itself. The following plugins
      are included in this release, with their release status
      indicated:</p><div class="informaltable"><table border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td>libMatlabPlugin</td><td>Beta</td></tr><tr><td>libFminconOptimizer (Windows only)</td><td>Beta</td></tr><tr><td>libGmatEstimation</td><td>Alpha (preview)</td></tr></tbody></table></div><p>Plugins can be enabled or disabled through the startup file
      (<code class="filename">gmat_startup_file.txt</code>), located in the GMAT bin
      directory. All plugins are disabled by default.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31BEF"></a>GUI/Script Synchronization</h4></div></div></div><p>For those that work with both the script and the graphical
      interface, GMAT now makes it explicitly clear if the two are
      synchronized, and which script is active (if you have several loaded).
      The possible states are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Synchronized (the interface and the script have the same
          data)</p></li><li class="listitem"><p>GUI or Script Modified (one of them has been modified with
          respect to the other)</p></li><li class="listitem"><p>Unsynchronized (different changes exist in each place)</p></li></ul></div><p>The only state in which manual intervention is necessary is
      Unsynchronized, which must be merged manually (or one set of changes
      must be discarded). The following status indicators are available on
      Windows and Linux (on Mac, they appear as single characters on the GMAT
      toolbar).</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/sync.png"></div></div><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/guimod.png"></div></div><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/scriptmod.png"></div></div><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/unsync.png"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C1E"></a>Estimation [Alpha]</h4></div></div></div><p>GMAT R2011a includes significant new state estimation capabilities
      in the libGmatEstimation plugin. The included features are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Measurement models</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: circle; "><li class="listitem"><p>Geometric</p></li><li class="listitem"><p>TDRSS range</p></li><li class="listitem"><p>USN two-way range</p></li></ul></div></li><li class="listitem"><p>Estimators</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: circle; "><li class="listitem"><p>Batch</p></li><li class="listitem"><p>Extended Kalman</p></li></ul></div></li><li class="listitem"><p>Resources</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: circle; "><li class="listitem"><p>GroundStation</p></li><li class="listitem"><p>Antenna</p></li><li class="listitem"><p>Transmitter</p></li><li class="listitem"><p>Receiver</p></li><li class="listitem"><p>Transponder</p></li></ul></div></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>This functionality is alpha status, and is included with this
        release as a preview only. It has not been rigorously tested.</p></div><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/estimation.png"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C5D"></a>User Documentation</h4></div></div></div><p>GMAT&rsquo;s user documentation has been completely revamped. In place
      of the old wiki, our formal documentation is now implemented in DocBook,
      with HTML, PDF, and Windows Help formats shipped with GMAT. Our
      documentation resources for this release are:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Help (shipped with GMAT, accessed through the Help &gt;
          Contents menu item)</p></li><li class="listitem"><p>Online Help (updated frequently, <a class="link" href="http://gmat.sourceforge.net/docs/" target="_top">http://gmat.sourceforge.net/docs/</a>)</p></li><li class="listitem"><p>Video Tutorials (<a class="link" href="http://gmat.sourceforge.net/docs/videos.html" target="_top">http://gmat.sourceforge.net/docs/videos.html</a>)</p></li><li class="listitem"><p>Help Forum (<a class="link" href="http://gmat.ed-pages.com/forum/" target="_top">http://gmat.ed-pages.com/forum/</a>)</p></li><li class="listitem"><p>Wiki (for informal and user-contributed documentation,
          samples, and tips: <a class="link" href="http://gmat.ed-pages.com/wiki/tiki-index.php" target="_top">http://gmat.ed-pages.com/wiki/tiki-index.php</a>)</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C83"></a>Screenshot (<span class="inlinemediaobject"><img src="../files/images/relnotes/r2011a/Screenshot.png"></span>)</h4></div></div></div><p>GMAT can now export a screenshot of the OrbitView panel to the
      output folder in PNG format.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31C8D"></a>Improvements</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C90"></a>Automatic MATLAB Detection</h4></div></div></div><p>MATLAB connectivity is now automatically established through the
      libMatlabInterface plugin, if enabled in your gmat_startup_file.txt. We
      are no longer shipping separate executables with and without MATLAB
      integration. Most recent MATLAB versions are supported, though
      configuration is necessary.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C95"></a>Dynamics Model Numerics</h4></div></div></div><p>All included dynamics models have been thoroughly tested against
      truth software (AGI STK, and A.I. Solutions FreeFlyer, primarily), and
      all known numeric issues have been corrected.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31C9A"></a>Script Editor [Windows]</h4></div></div></div><p>GMAT&rsquo;s integrated script editor on Windows is much improved in
      this release, and now features:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Syntax highlighting for GMAT keywords</p></li><li class="listitem"><p>Line numbering</p></li><li class="listitem"><p>Find &amp; Replace</p></li><li class="listitem"><p>Active script indicator and GUI synchronization buttons</p></li></ul></div><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/editor.png"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31CB5"></a>Regression Testing</h4></div></div></div><p>The GMAT project developed a completely new testing system that
      allows us to do nightly, automated tests across the entire system, and
      on multiple platforms. The new system has the following features:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Focused on GMAT script testing</p></li><li class="listitem"><p>Written in MATLAB language</p></li><li class="listitem"><p>Includes 6216 tests with coverage of most of GMAT&rsquo;s functional
          requirements</p></li><li class="listitem"><p>Allows automatic regression testing on nightly builds</p></li><li class="listitem"><p>Compatible with all supported platforms</p></li></ul></div><p>The project is also regularly testing the GMAT graphical interface
      on Windows using the SmartBear TestComplete tool. This testing occurs
      approximately twice a week, and is focused on entering and running
      complete missions through the interface and checking that the results
      match those generated in script mode.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31CCD"></a>Visual Improvements</h4></div></div></div><p>This release features numerous visual improvements,
      including:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>A new application icon and splash screen (shown below)</p></li><li class="listitem"><p>Many new, professionally-created icons</p></li><li class="listitem"><p>A welcome page for new users</p></li></ul></div><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2011a/GMATSplashScreen.png"></div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31CE5"></a>Compatibility Changes</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31CE8"></a>Platform Support</h4></div></div></div><p>GMAT supports the following platforms:</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Windows XP</p></li><li class="listitem"><p>Windows Vista</p></li><li class="listitem"><p>Windows 7</p></li><li class="listitem"><p>Mac OS X Snow Leopard (10.6)</p></li><li class="listitem"><p>Linux (Intel 64-bit)</p></li></ul></div><p>With the exception of the Linux version, GMAT is a 32-bit
      application, but will run on 64-bit platforms in 32-bit mode. The MATLAB
      interface was tested with 32-bit MATLAB 2010b on Windows, and is
      expected to support 32-bit MATLAB versions from R2006b through
      R2011a.</p><p><span class="bold"><strong>Mac</strong></span>: MATLAB 2010a was tested, but
      version coverage is expected to be identical to Windows.</p><p><span class="bold"><strong>Linux</strong></span>: MATLAB 2009b 64-bit was
      tested, and 64-bit MATLAB is required. Otherwise, version coverage is
      expected to be identical to Windows.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31D0A"></a>Script Syntax Changes</h4></div></div></div><p>The <code class="literal">BeginMissionSequence</code> command will soon be
      required for all scripts. In this release a warning is generated if this
      statement is missing.</p><p>The following syntax elements are deprecated, and will be removed
      in a future release:</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="33%"><col width="34%"></colgroup><thead><tr><th align="center">Resource</th><th align="center">Field</th><th align="center">Replacement</th></tr></thead><tbody><tr><td><code class="literal">DifferentialCorrector</code></td><td><code class="literal">TargeterTextFile</code></td><td><code class="literal">ReportFile</code></td></tr><tr><td><code class="literal">DifferentialCorrector</code></td><td><code class="literal">UseCentralDifferences</code></td><td><code class="literal">DerivativeMethod =
              "CentralDifference"</code></td></tr><tr><td><code class="literal">EphemerisFile</code></td><td><code class="literal">FileName</code></td><td><code class="literal">Filename</code></td></tr><tr><td><code class="literal">FiniteBurn</code></td><td><code class="literal">Axes</code></td><td><code class="literal"></code></td></tr><tr><td><code class="literal">FiniteBurn</code></td><td><code class="literal">BurnScaleFactor</code></td><td><code class="literal"></code></td></tr><tr><td><code class="literal">FiniteBurn</code></td><td><code class="literal">CoordinateSystem</code></td><td><code class="literal"></code></td></tr><tr><td><code class="literal">FiniteBurn</code></td><td><code class="literal">Origin</code></td><td><code class="literal"></code></td></tr><tr><td><code class="literal">FiniteBurn</code></td><td><code class="literal">Tanks</code></td><td><code class="literal"></code></td></tr><tr><td><p><code class="literal">FiniteBurn</code></p><p><code class="literal">ImpulsiveBurn</code></p></td><td><code class="literal">CoordinateSystem = "Inertial"</code></td><td><code class="literal">CoordinateSystem = "MJ2000Eq"</code></td></tr><tr><td><p><code class="literal">FiniteBurn</code></p><p><code class="literal">ImpulsiveBurn</code></p></td><td><code class="literal">VectorFormat</code></td><td><code class="literal"></code></td></tr><tr><td><p><code class="literal">FiniteBurn</code></p><p><code class="literal">ImpulsiveBurn</code></p></td><td><p><code class="literal">V</code></p><p><code class="literal">N</code></p><p><code class="literal">B</code></p></td><td><p><code class="literal">Element1</code></p><p><code class="literal">Element2</code></p><p><code class="literal">Element3</code></p></td></tr><tr><td><code class="literal">FuelTank</code></td><td><code class="literal">PressureRegulated</code></td><td><code class="literal">PressureModel =
              PressureRegulated</code></td></tr><tr><td><code class="literal">OpenGLPlot</code></td><td><code class="literal"></code></td><td><code class="literal">OrbitView</code></td></tr><tr><td><code class="literal">OrbitView</code></td><td><code class="literal">EarthSunLines</code></td><td><code class="literal">SunLine</code></td></tr><tr><td><code class="literal">OrbitView</code></td><td><p><code class="literal">ViewDirection =
              Vector</code></p><p><code class="literal">ViewDirection = [0 0
              1]</code></p></td><td><code class="literal">ViewDirection = [0 0 1]</code></td></tr><tr><td><code class="literal">OrbitView</code></td><td><code class="literal">ViewPointRef</code></td><td><code class="literal">ViewPointReference</code></td></tr><tr><td><code class="literal">OrbitView</code></td><td><p><code class="literal">ViewPointRef =
              Vector</code></p><p><code class="literal">ViewPointRefVector = [0 0
              1]</code></p></td><td><code class="literal">ViewPointReference = [0 0 1]</code></td></tr><tr><td><code class="literal">OrbitView</code></td><td><p><code class="literal">ViewPointVector =
              Vector</code></p><p><code class="literal">ViewPointVectorVector = [0
              0 1]</code></p></td><td><code class="literal">ViewPointVector = [0 0 1]</code></td></tr><tr><td><code class="literal">SolarSystem</code></td><td><code class="literal">Ephemeris</code></td><td><code class="literal">EphemerisSource</code></td></tr><tr><td><code class="literal">Spacecraft</code></td><td><code class="literal">StateType</code></td><td><code class="literal">DisplayStateType</code></td></tr><tr><td><code class="literal">Thruster</code></td><td><p><code class="literal">X_Direction</code></p><p><code class="literal">Y_Direction</code></p><p><code class="literal">Z_Direction</code></p><p><code class="literal">Element1</code></p><p><code class="literal">Element2</code></p><p><code class="literal">Element3</code></p></td><td><p><code class="literal">ThrustDirection1</code></p><p><code class="literal">ThrustDirection2</code></p><p><code class="literal">ThrustDirection3</code></p></td></tr><tr><td><code class="literal">XYPlot</code></td><td><code class="literal">Add</code></td><td><code class="literal">YVariable</code></td></tr><tr><td><code class="literal">XYPlot</code></td><td><code class="literal">Grid</code></td><td><code class="literal">ShowGrid</code></td></tr><tr><td><code class="literal">XYPlot</code></td><td><code class="literal">IndVar</code></td><td><code class="literal">XVariable</code></td></tr></tbody></table></div><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="33%"><col width="34%"></colgroup><thead><tr><th align="center">Command</th><th align="center">Old Syntax</th><th align="center">New Syntax</th></tr></thead><tbody><tr><td><code class="literal">Propagate</code></td><td><code class="literal">Propagate -DefaultProp(sc)</code></td><td><code class="literal">Propagate BackProp
              DefaultProp(sc)</code></td></tr></tbody></table></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31E62"></a>Fixed Issues</h3></div></div></div><p>733 bugs were closed in this release, including 368 marked &ldquo;major&rdquo;
    or &ldquo;critical&rdquo;. See the <a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/buglist.cgi?query_format=advanced&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;product=GMAT&amp;long_desc_type=substring&amp;long_desc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=RESOLVED&amp;bug_status=VERIFIED&amp;bug_status=CLOSED&amp;bug_severity=critical-GMAT&amp;bug_severity=blocker&amp;bug_severity=critical&amp;bug_severity=major&amp;emailassigned_to1=1&amp;emailtype1=substring&amp;email1=&amp;emailassigned_to2=1&amp;emailreporter2=1&amp;emailcc2=1&amp;emailtype2=substring&amp;email2=&amp;bugidtype=include&amp;bug_id=&amp;chfieldfrom=2008-09-30&amp;chfieldto=Now&amp;chfield=bug_status&amp;chfieldvalue=&amp;cmdtype=doit&amp;order=Reuse+same+sort+as+last+time&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="_top">full
    report for details.</a></p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31E6A"></a>Known Issues</h3></div></div></div><p>There remain 268 open bugs in the project&rsquo;s <a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/buglist.cgi?query_format=advanced&amp;short_desc_type=allwordssubstr&amp;short_desc=&amp;product=GMAT&amp;long_desc_type=substring&amp;long_desc=&amp;bug_file_loc_type=allwordssubstr&amp;bug_file_loc=&amp;keywords_type=allwords&amp;keywords=&amp;bug_status=UNCONFIRMED&amp;bug_status=NEW&amp;bug_status=ASSIGNED&amp;bug_status=REOPENED&amp;bug_status=NEEDS+CLARIFICATION&amp;bug_status=NEEDS+TEST&amp;emailassigned_to1=1&amp;emailtype1=substring&amp;email1=&amp;emailassigned_to2=1&amp;emailreporter2=1&amp;emailcc2=1&amp;emailtype2=substring&amp;email2=&amp;bugidtype=include&amp;bug_id=&amp;chfieldfrom=&amp;chfieldto=Now&amp;chfieldvalue=&amp;cmdtype=doit&amp;order=Reuse+same+sort+as+last+time&amp;field0-0-0=noop&amp;type0-0-0=noop&amp;value0-0-0=" target="_top">Bugzilla
    database</a>, 42 of which are marked &ldquo;major&rdquo; or &ldquo;critical&rdquo;. These are
    tabulated below.</p><div class="table"><a name="N31E73"></a><p class="title"><b>Table&nbsp;24.&nbsp;Multiple platforms</b></p><div class="table-contents"><table summary="Multiple platforms" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=407" target="_top">407</a></td><td>Multi-Matlab run bug</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=636" target="_top">636</a></td><td>MATLAB Callbacks on Linux and Mac</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=648" target="_top">648</a></td><td>DOCUMENT BEHAVIOR - Final orbital state does not match for
            the two report methods</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=776" target="_top">776</a></td><td>Batch vs Individual Runs different</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1604" target="_top">1604</a></td><td>Keplerian Conversion Errors for Hyperbolic Orbits</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1668" target="_top">1668</a></td><td>Decimal marker not flexible enough for international
            builds</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1684" target="_top">1684</a></td><td>MMS script in GMAT takes 300 times longer than similar run
            in FreeFlyer</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1731" target="_top">1731</a></td><td>Major Performance issue in GMAT Functions</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1734" target="_top">1734</a></td><td>Spacecraft allows conversion for singular conic
            section.</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1992" target="_top">1992</a></td><td>Determinant of "large" disallowed due to poor algorithm
            performance</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2058" target="_top">2058</a></td><td>Can't set SRP Flux and Nominal Sun via GUI</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2088" target="_top">2088</a></td><td>EOP file reader uses Julian Day</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2147" target="_top">2147</a></td><td>Empty parentheses "( )" are not caught in math
            validation</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2313" target="_top">2313</a></td><td>Finite Burn/Thruster Tests Have errors &gt; 1000 km but may
            be due to script differences</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2322" target="_top">2322</a></td><td>DOCUMENT: MATLAB interface requires manual configuration by
            user</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2344" target="_top">2344</a></td><td>when a propagator object is deleted, its associated force
            model is not deleted</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2349" target="_top">2349</a></td><td>Performance Issue in Force Modelling</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2410" target="_top">2410</a></td><td>Ephemeris propagator has large numeric error</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2416" target="_top">2416</a></td><td>STM Parameters are wrong when using Coordinate System other
            than EarthMJ2000Eq</td></tr></tbody></table></div></div><br class="table-break"><div class="table"><a name="N31EFE"></a><p class="title"><b>Table&nbsp;25.&nbsp;Windows</b></p><div class="table-contents"><table summary="Windows" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=970" target="_top">970</a></td><td>Matlab connection issue</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1012" target="_top">1012</a></td><td>Quirky Numerical Issues 2 in Batch mode</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1128" target="_top">1128</a></td><td>GMAT incompatible with MATLAB R14 and earlier</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1417" target="_top">1417</a></td><td>Some lines prefixed by "function" are ingored</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1436" target="_top">1436</a></td><td>Potential performance issue using many propagate
            commands</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1528" target="_top">1528</a></td><td>GMAT Function scripts unusable depending on file
            ownership/permissions</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1580" target="_top">1580</a></td><td>Spacecraft Attitude Coordinate System Conversion not
            implemented</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1592" target="_top">1592</a></td><td>Atmosphere Model Setup File Features Not
            Implemented</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2056" target="_top">2056</a></td><td>Reproducibility of script run not guaranteed</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2065" target="_top">2065</a></td><td>Difficult to read low number in Spacecraft Attitude
            GUI</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2066" target="_top">2066</a></td><td>SC Attitude GUI won't accept 0.0:90.0:0.0 as a 3-2-1 Euler
            Angle input</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2067" target="_top">2067</a></td><td>Apply Button Sometimes Not Functional in SC Attitude
            GUI</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2374" target="_top">2374</a></td><td>Crash when GMAT tries to write to a folder without write
            permissions</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2381" target="_top">2381</a></td><td>TestComplete does not match user inputs to
            DefaultSC</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2382" target="_top">2382</a></td><td>Point Mass Issue when using Script vs. User Input</td></tr></tbody></table></div></div><br class="table-break"><div class="table"><a name="N31F6D"></a><p class="title"><b>Table&nbsp;26.&nbsp;Mac OS X</b></p><div class="table-contents"><table summary="Mac OS X" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1216" target="_top">1216</a></td><td>MATLAB-&gt;GMAT not working</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2081" target="_top">2081</a></td><td>Texture Maps not showing on Mac for OrbitView</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2092" target="_top">2092</a></td><td>GMAT crashes when MATLAB engine does not open</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2291" target="_top">2291</a></td><td>LSK file text ctrl remains visible when source set to DE405
            or 2Body</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2311" target="_top">2311</a></td><td>Resource Tree - text messed up for objects in
            folders</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=2383" target="_top">2383</a></td><td>Crash running RoutineTests with plots ON</td></tr></tbody></table></div></div><br class="table-break"><div class="table"><a name="N31F9D"></a><p class="title"><b>Table&nbsp;27.&nbsp;Linux</b></p><div class="table-contents"><table summary="Linux" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1851" target="_top">1851</a></td><td>On Linux, STC Editor crashes GMAT on Close</td></tr><tr><td><a class="link" href="http://pows003.gsfc.nasa.gov/bugzilla/show_bug.cgi?id=1877" target="_top">1877</a></td><td>On Linux, Ctrl-C crashes GMAT if no MDIChildren are
            open</td></tr></tbody></table></div></div><br class="table-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ReleaseNotesR2012a.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ReleaseNotes.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="BookIndex.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">GMAT R2012a Release Notes&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Index</td></tr></table></div></body></html>