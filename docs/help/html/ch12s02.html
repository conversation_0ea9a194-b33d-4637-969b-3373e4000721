<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create and Configure Spacecraft Hardware and Finite Burn</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion"><link rel="prev" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion"><link rel="next" href="ch12s03.html" title="Configure the Mission Sequence"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create and Configure Spacecraft Hardware and Finite Burn</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Tut_ElectricPropulsion.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;12.&nbsp;Electric Propulsion</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch12s03.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13DBD"></a>Create and Configure Spacecraft Hardware and Finite Burn</h2></div></div></div><p>For this tutorial, you&rsquo;ll need GMAT open with the default mission
    loaded. To load the default mission, click <span class="guibutton">New
    Mission</span> (<span class="inlinemediaobject"><img src="../files/images/icons/NewMission.png" align="middle" height="10"></span>) or start a new GMAT session. We will use the
    default configurations for the spacecraft (<span class="guilabel">DefaultSC</span>)
    and the propagator (<span class="guilabel">DefaultProp</span>).
    <span class="guilabel">DefaultSC</span> is configured by default to a near-circular
    orbit, and <span class="guilabel">DefaultProp</span> is configured to use Earth as
    the central body with a nonspherical gravity model of degree and order 4.
    You may want to open the dialog boxes for these objects and inspect them
    more closely as we will leave them at their default settings.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13DD9"></a>Create a Thruster, Fuel Tank, and Solar Power System</h3></div></div></div><p>To model thrust and fuel use associated with a finite burn, we
      must create an <span class="guilabel">ElectricThruster</span>, an
      <span class="guilabel">ElectricTank</span>, a power system, and then attach the
      newly created <span class="guilabel">ElectricTank</span> to the
      <span class="guilabel">ElectricThruster</span>, and attach all hardware to the
      spacecraft. We'll start by creating the hardware objects.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Resources</span> tree, right-click on the
          <span class="guilabel">Hardware</span> folder, point to
          <span class="guilabel">Add</span>, and click
          <span class="guilabel">ElectricThruster</span>. &nbsp;A Resource named
          <span class="guilabel">ElectricThruster1</span> will be created.</p></li><li class="step"><p>In the <span class="guilabel">Resources</span> tree, right-click on the
          <span class="guilabel">Hardware</span> folder, point to
          <span class="guilabel">Add</span>, and click
          <span class="guilabel">ElectricTank</span>. &nbsp;A Resource named
          <span class="guilabel">ElectricTank1</span> will be created.</p></li><li class="step"><p>In the <span class="guilabel">Resources</span> tree, right-click on the
          <span class="guilabel">Hardware</span> folder, point to
          <span class="guilabel">Add</span>, and click
          <span class="guilabel">SolarPowerSystem</span>. &nbsp;A Resource named
          <span class="guilabel">SolarPowerSystem1</span> will be created.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13E21"></a>Configure the Hardware</h3></div></div></div><p>Now we'll configure the hardware models for this exercise.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click<span class="guilabel"> ElectricThruster1</span> to edit
          its properties.</p></li><li class="step"><p>In the <span class="guilabel">Mass Change</span> group box, check
          <span class="guilabel">Decrement Mass</span>.</p></li><li class="step"><p>In the <span class="guilabel">Mass Change</span> group box, select
          <span class="guilabel">ElectricTank1</span> for the
          <span class="guilabel">Tank.</span>&nbsp;</p></li><li class="step"><p>In the <span class="guilabel">Thrust Config</span> group box, select
          <span class="guilabel">ConstantThrustAndIsp</span> for
          <span class="guilabel">ThrustModel</span> and set
          <span class="guilabel">ConstantThrust</span> to 5.0 N.&nbsp;</p></li></ol></div><p><a class="xref" href="ch12s02.html#Tut_ElectricPropulsion_Fig2_Thruster1_Configuration" title="Figure&nbsp;12.1.&nbsp;ElectricThruster1 Configuration">Figure&nbsp;12.1, &ldquo;<span class="guilabel">ElectricThruster1</span> Configuration&rdquo;</a> below
      shows the <span class="guilabel">ElectricThruster1</span> configuration that we
      will use.</p><div class="figure"><a name="Tut_ElectricPropulsion_Fig2_Thruster1_Configuration"></a><p class="title"><b>Figure&nbsp;12.1.&nbsp;<span class="guilabel">ElectricThruster1</span> Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_Thruster.png" align="middle" height="916" alt="ElectricThruster1 Configuration"></td></tr></table></div></div></div></div><br class="figure-break"><p>We will use the default tank settings. <a class="xref" href="ch12s02.html#Tut_ElectricPropulsion_Fig1_FuelTank1_Configuration" title="Figure&nbsp;12.2.&nbsp;ElectricTank1 Configuration">Figure&nbsp;12.2, &ldquo;<span class="guilabel">ElectricTank1</span> Configuration&rdquo;</a> shows
      the finished <span class="guilabel">ElectricTank1</span> configuration.</p><div class="figure"><a name="Tut_ElectricPropulsion_Fig1_FuelTank1_Configuration"></a><p class="title"><b>Figure&nbsp;12.2.&nbsp;<span class="guilabel">ElectricTank1</span> Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_Tank.png" align="middle" height="615" alt="ElectricTank1 Configuration"></td></tr></table></div></div></div></div><br class="figure-break"><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click<span class="guilabel"> SolarPowerSystem1</span> to edit
          its properties.</p></li><li class="step"><p>In the <span class="guilabel">General</span> group box, click the
          <span class="guilabel">Select</span> button next to
          <span class="guilabel">ShadowBodies</span>.</p></li><li class="step"><p>Remove <span class="guilabel">Earth</span> from the
          <span class="guilabel">ShadowBodies</span> list.&nbsp;</p></li></ol></div><p><a class="xref" href="ch12s02.html#Tut_ElectricPropulsion_PowerSystemConfig" title="Figure&nbsp;12.3.&nbsp;SolarPowerSystem1 Configuration">Figure&nbsp;12.3, &ldquo;<span class="guilabel">SolarPowerSystem1</span> Configuration&rdquo;</a> shows
      the finished <span class="guilabel">SolarPowerSystem1</span>
      configuration.</p><div class="figure"><a name="Tut_ElectricPropulsion_PowerSystemConfig"></a><p class="title"><b>Figure&nbsp;12.3.&nbsp;<span class="guilabel">SolarPowerSystem1</span> Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_PowerSystem.png" align="middle" height="427" alt="SolarPowerSystem1 Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13EB0"></a>Attach Hardware to the Spacecraft</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Resources</span> tree, double-click
          <span class="guilabel">DefaultSC</span> to edit its properties.</p></li><li class="step"><p>Select the <span class="guilabel">Tanks</span> tab. In the
          <span class="guilabel">Available Tanks</span> column, select
          <span class="guilabel">ElectricTank1</span>. Then click the right arrow
          button to add <span class="guilabel">ElectricTank1</span> to the
          <span class="guilabel">SelectedTanks</span> list. Click
          <span class="guilabel">Apply</span>.</p></li><li class="step"><p>Select the <span class="guilabel">Actuators</span> tab. In the
          <span class="guilabel">Available Thrusters</span> column, select
          <span class="guilabel">ElectricThruster1</span>. Then click the right arrow
          button to add <span class="guilabel">ElectricThruster1</span> to the
          <span class="guilabel">SelectedThrusters</span> list. Click
          <span class="guilabel">APPLY</span>.</p></li><li class="step"><p>Select the <span class="guilabel">PowerSystem</span> tab. In the
          <span class="guilabel">PowerSystem</span> tab, select
          <span class="guilabel">SolarPowerSystem1</span>. Click
          <span class="guilabel">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_ElectricPropulsion_Fig4_Attach_FuelTank1_to_DefaultSC"></a><p class="title"><b>Figure&nbsp;12.4.&nbsp;Attach <span class="guilabel">ElectricTank1</span> to
        <span class="guilabel">DefaultSC</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_AddTanksToSat.png" align="middle" height="735" alt="Attach ElectricTank1 to DefaultSC"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="Tut_ElectricPropulsion_Fig5_Attach_Thruster1_to_DefaultSC"></a><p class="title"><b>Figure&nbsp;12.5.&nbsp;Attach <span class="guilabel">ElectricThruster1</span> to
        <span class="guilabel">DefaultSC</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_AddThrusterToSat.png" align="middle" height="735" alt="Attach ElectricThruster1 to DefaultSC"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="Tut_ElectricPropulsion_AddPowerSystemToSat"></a><p class="title"><b>Figure&nbsp;12.6.&nbsp;Attach <span class="guilabel">SolarPowerSystem1</span> to
        <span class="guilabel">DefaultSC</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_AddPowerSystemToSat.png" align="middle" height="735" alt="Attach SolarPowerSystem1 to DefaultSC"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13F2C"></a>Create the Finite Burn Maneuver</h3></div></div></div><p>We&rsquo;ll need a single <span class="guilabel">FiniteBurn</span> Resource for
      this tutorial.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Resources</span> tree, right-click the
          <span class="guilabel">Burns</span> folder and add a
          <span class="guilabel">FiniteBurn</span>. A Resource named<span class="guilabel">
          FiniteBurn1</span> will be created.</p></li><li class="step"><p>Double-click <span class="guilabel">FiniteBurn1</span> to edit its
          properties.</p></li><li class="step"><p>Select the <span class="guilabel">ElectricThruster1</span> from the
          left side and use the <span class="bold"><strong>-&gt;</strong></span> button
          to move it to the right, setting it as a thruster associated with
          <span class="guilabel">FiniteBurn1</span>. Click
          <span class="guilabel">OK</span>.</p></li></ol></div><div class="figure"><a name="Tut_ElectricPropulsion_Fig6_Creation_of_FiniteBurn_Resource_FiniteBurn1"></a><p class="title"><b>Figure&nbsp;12.7.&nbsp;Creation of <span class="guilabel">FiniteBurn</span> Resource
        <span class="guilabel">FiniteBurn1</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_FiniteBurn.png" align="middle" height="362" alt="Creation of FiniteBurn Resource FiniteBurn1"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Tut_ElectricPropulsion.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_ElectricPropulsion.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch12s03.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;12.&nbsp;Electric Propulsion&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Mission Sequence</td></tr></table></div></body></html>