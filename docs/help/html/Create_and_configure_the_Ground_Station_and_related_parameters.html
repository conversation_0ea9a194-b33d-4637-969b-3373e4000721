<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Create and configure the Ground Station and related parameters</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data"><link rel="prev" href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html" title="Create and configure the spacecraft, spacecraft transponder, and related parameters"><link rel="next" href="Define_the_types_of_measurements_to_be_simulated.html" title="Define the types of measurements to be simulated"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Create and configure the Ground Station and related
    parameters</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Define_the_types_of_measurements_to_be_simulated.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="Create_and_configure_the_Ground_Station_and_related_parameters"></a>Create and configure the Ground Station and related
    parameters</h2></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N140DA"></a>Create Ground Station Transmitter, Receiver, and Antenna
      objects</h3></div></div></div><p>Before we create the <span class="guilabel">GroundStation</span> object
      itself, as shown below, we first create the
      <span class="guilabel">Transmitter</span>, <span class="guilabel">Receiver</span>, and
      <span class="guilabel">Antenna</span> objects that must be associated with any
      <span class="guilabel">GroundStation</span>.</p><pre class="programlisting">%  Ground Station electronics. 
Create Transmitter DSNTransmitter;
Create Receiver DSNReceiver;
Create Antenna DSNAntenna;

DSNTransmitter.PrimaryAntenna     = DSNAntenna;
DSNReceiver.PrimaryAntenna        = DSNAntenna;
DSNTransmitter.Frequency          = 7200;   %MHz</pre><p>In the script segment above, we first created
      <span class="guilabel">Transmitter</span>, <span class="guilabel">Receiver</span>, and
      <span class="guilabel">Antenna</span> objects. The GMAT script line
      <code class="code">DSNTransmitter.PrimaryAntenna = DSNAntenna</code>, sets the main
      antenna that the <span class="guilabel">Transmitter</span> object will be using.
      Likewise, the <code class="code">DSNReceiver.PrimaryAntenna = DSNAntenna</code>
      script line sets the main antenna that the <span class="guilabel">Receiver</span>
      object will be using. As previously mentioned, the
      <span class="guilabel">Antenna</span> object currently has no function, but we
      include it here both because GMAT requires it and for completeness since
      the <span class="guilabel">Antenna</span> resource may have a function in a
      future GMAT release. Finally, we set the transmitter frequency in the
      last GMAT script line above. See the <span class="guilabel">RunSimulator</span>
      Help for a complete description of how this input frequency is used. As
      described in the Help, since in this example we will not be using a ramp
      table, this input frequency will be used to calculate the simulated
      value of the range and Doppler observations. In addition, this input
      frequency will also be output to the range data file created by the
      <span class="guilabel">RunSimulator</span> command.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N14113"></a>Create Ground Station</h3></div></div></div><p>Below, we create and configure a
      <span class="guilabel">GroundStation</span> object.</p><pre class="programlisting">%   Create ground station and associated error models
Create GroundStation CAN;
CAN.CentralBody           = Earth;
CAN.StateType             = Cartesian;
CAN.HorizonReference      = Ellipsoid;
CAN.Location1             = -4461.083514
CAN.Location2             = 2682.281745
CAN.Location3             = -3674.570392

CAN.Id                    = 22222;

CAN.MinimumElevationAngle = 7.0;

CAN.IonosphereModel       = 'IRI2007';
CAN.TroposphereModel      = 'HopfieldSaastamoinen';

CAN.AddHardware           = {DSNTransmitter, DSNAntenna, ...
                                DSNReceiver};</pre><p>The script segment above is broken into five sections. In the
      first section, we create our <span class="guilabel">GroundStation</span> object
      and we set our Earth-Centered Fixed Cartesian coordinates. In the second
      section, we set the ID of the ground station that will output to the GMD
      file created by the <span class="guilabel">RunSimulator</span> command. In the
      third section, we set the minimum elevation angle to 7 degrees. Below
      this ground station to spacecraft elevation angle, no simulated data
      will be created. In the fourth section, we specify which troposphere and
      ionosphere model we wish to use to model RF signal atmospheric
      refraction effects. Finally, in the fifth section, we attached three
      pieces of previously created required hardware to our ground station, a
      transmitter, a receiver, and an antenna.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N14125"></a>Create Ground Station Error Models</h3></div></div></div><p>It is well known that all measurement types have random noise
      and/or biases associated with them. For GMAT, these affects are modelled
      using ground station error models. Since we have already created the
      <span class="guilabel">GroundStation</span> object and its related hardware, we
      now create the ground station error models. Since we wish to simulate
      both range and Doppler data, we need to create two error models as shown
      below, one for range measurements and one for Doppler
      measurements.</p><pre class="programlisting">%   Create Ground station error models
Create ErrorModel DSNrange;
DSNrange.Type                  = 'DSN_SeqRange';
DSNrange.NoiseSigma            = 10.63;
DSNrange.Bias                  = 0.0;

Create ErrorModel DSNdoppler;
DSNdoppler.Type                = 'DSN_TCP';
DSNdoppler.NoiseSigma          = 0.0282;
DSNdoppler.Bias                = 0.0;

CAN.ErrorModels                = {DSNrange, DSNdoppler};</pre><p>The script segment above is broken into three sections. The first
      section defines an <span class="guilabel">ErrorModel</span> named
      <span class="guilabel">DSNrange</span>. The error model Type is DSN_SeqRange
      which indicates that it is an error model for DSN sequential range
      measurements. The 1 sigma standard deviation of the Gaussian white noise
      is set to 10.63 Range Units (RU) and the measurement bias is set to 0
      RU.</p><p>The second section above defines an
      <span class="guilabel">ErrorModel</span> named <span class="guilabel">DSNdoppler</span>.
      The error model Type is DSN_TCP which indicates that it is an error
      model for DSN total count phase-derived Doppler measurements. The 1
      sigma standard deviation of the Gaussian white noise is set to 0.0282 Hz
      and the measurement bias is set to 0 Hz.</p><p>The third section above attaches the two
      <span class="guilabel">ErrorModel</span> resources we have just created to the
      <span class="guilabel">CAN</span> <span class="guilabel">GroundStation</span>. Note that
      in GMAT, the measurement noise or bias is defined on a per ground
      station basis. Thus, any range measurement error involving the CAN
      GroundStation is defined by the <span class="guilabel">DSNRange</span>
      <span class="guilabel">ErrorModel</span> and any Doppler measurement error
      involving the <span class="guilabel">CAN</span>
      <span class="guilabel">GroundStation</span> is defined by the
      <span class="guilabel">DSNdoppler</span> <span class="guilabel">ErrorModel</span>. Note
      that since GMAT currently only models two way measurements where the
      transmitting and receiving ground stations are the same, we do not have
      to consider the case where the transmitting and receiving ground
      stations are different. Suppose we were to add an additional
      <span class="guilabel">GroundStation</span> to this simulation. The measurement
      error for observations involving this new
      <span class="guilabel">GroundStation</span> would be defined by the
      <span class="guilabel">ErrorModel</span> resources attached to it.</p><p>See <a class="xref" href="Appendix_A_Determination_of_Measurement_Noise_Values.html" title="Appendix A &ndash; Determination of Measurement Noise Values">Appendix A &ndash; Determination of Measurement Noise Values</a> for a discussion of how we determined the
      values for NoiseSigma for the two <span class="guilabel">ErrorModel</span>
      resources we created.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Define_the_types_of_measurements_to_be_simulated.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create and configure the spacecraft, spacecraft transponder, and
    related parameters&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Define the types of measurements to be simulated</td></tr></table></div></body></html>