<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Appendix C. Check covariance matrix conditioning</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="FilterSmoother_GpsPosVec.html" title="Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data"><link rel="prev" href="ch15s11.html" title="Appendix B. Run the script from the command-line"><link rel="next" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Appendix C. Check covariance matrix conditioning</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch15s11.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N15616"></a>Appendix C. Check covariance matrix conditioning</h2></div></div></div><p>Filters can be susceptible to effects of numerical noise arising
    from finite machine precision. This tends to manifest itself in numerical
    instability of the covariance matrix. The covariance of the backward
    filter step of the Fraser-Potter smoother is particularly vulnerable to
    this condition if large data gaps occur near the start of backward filter
    processing (the end of the forward filter processing span). Users can
    examine the condition number of the forward filter, backward filter, and
    smoother using the <span class="guilabel">plot_cond_cov.py</span> tool.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>In the python script directory, type the following:</p></li></ol></div><pre class="programlisting">python plot_cond_cov.py <span class="emphasis"><em>&lt;full path to filter.mat&gt; &lt;full path to smoother.mat&gt;</em></span>
</pre><p>This command will produce a plot of the base-10 logarithm of the
    covariance condition numbers over the processing span. Users with access
    to MATLAB or python can use the filter and smoother MATLAB files for
    additional analysis of the covariance, such as testing that the covariance
    eigenvalues are positive and real or checking the symmetry of the
    covariance matrix.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch15s11.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="FilterSmoother_GpsPosVec.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix B. Run the script from the command-line&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</td></tr></table></div></body></html>