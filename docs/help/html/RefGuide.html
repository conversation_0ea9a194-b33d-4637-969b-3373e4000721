<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Reference Guide</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="prev" href="ch16s07.html" title="References"><link rel="next" href="ch17.html" title="Chapter&nbsp;17.&nbsp;API"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Reference Guide</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch16s07.html">Prev</a>&nbsp;</td><th align="center" width="60%">&nbsp;</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch17.html">Next</a></td></tr></table><hr></div><div class="part"><div class="titlepage"><div><div><h1 class="title"><a name="RefGuide"></a>Reference Guide</h1></div></div></div><div class="partintro"><div></div><p>The <span class="emphasis"><em><a class="xref" href="RefGuide.html" title="Reference Guide">Reference Guide</a></em></span> contains individual topics that
    describe each of GMAT's resources and commands. When you need detailed
    information on syntax or application-specific examples for specific
    features, go here. It also includes system-level references that describe
    the script language syntax, parameter listings, external interfaces, and
    configuration files.</p><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="chapter"><a href="ch17.html">17. API</a></span></dt><dd><dl><dt><span class="section"><a href="ch17.html#N158E4">User Guide</a></span></dt></dl></dd><dt><span class="chapter"><a href="ch18.html">18. Dynamics and Modeling</a></span></dt><dd><dl><dt><span class="section"><a href="ch18.html#N158F2">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Barycenter.html">Barycenter</a></span><span class="refpurpose"> &mdash; The center of mass of selected celestial bodies</span></dt><dt><span class="refentrytitle"><a href="CelestialBody.html">CelestialBody</a></span><span class="refpurpose"> &mdash; Modeling of Moon, Planet, Asteroid, and Comet
    objects</span></dt><dt><span class="refentrytitle"><a href="FuelTank.html">ChemicalTank</a></span><span class="refpurpose"> &mdash; Model of a chemical fuel tank</span></dt><dt><span class="refentrytitle"><a href="Thruster.html">ChemicalThruster</a></span><span class="refpurpose"> &mdash; A chemical thruster model</span></dt><dt><span class="refentrytitle"><a href="ContactLocator.html">ContactLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">Spacecraft</span> and a <span class="guilabel">GroundStation</span>
    or <span class="guilabel">PlanetographicRegion</span>.</span></dt><dt><span class="refentrytitle"><a href="CoordinateSystem.html">CoordinateSystem</a></span><span class="refpurpose"> &mdash; An axis and origin pair</span></dt><dt><span class="refentrytitle"><a href="EclipseLocator.html">EclipseLocator</a></span><span class="refpurpose"> &mdash; A <span class="guilabel">Spacecraft</span> eclipse event
    locator</span></dt><dt><span class="refentrytitle"><a href="ElectricTank.html">ElectricTank</a></span><span class="refpurpose"> &mdash; A model of a tank containing fuel for an electric propulsion
    system</span></dt><dt><span class="refentrytitle"><a href="ElectricThruster.html">ElectricThruster</a></span><span class="refpurpose"> &mdash; An electric thruster model</span></dt><dt><span class="refentrytitle"><a href="FiniteBurn.html">FiniteBurn</a></span><span class="refpurpose"> &mdash; A finite burn</span></dt><dt><span class="refentrytitle"><a href="FieldOfView.html">FieldOfView</a></span><span class="refpurpose"> &mdash; Models the mask, or field-of-view, of a hardware
    <span class="guilabel">Resource</span>.</span></dt><dt><span class="refentrytitle"><a href="ForceModel.html">ForceModel</a></span><span class="refpurpose"> &mdash; Used to specify force modeling options such as gravity, drag,
    solar radiation pressure, and non-central bodies for
    propagation.</span></dt><dt><span class="refentrytitle"><a href="Formation.html">Formation</a></span><span class="refpurpose"> &mdash; A collection of spacecraft.</span></dt><dt><span class="refentrytitle"><a href="GroundStation.html">GroundStation</a></span><span class="refpurpose"> &mdash; A ground station model.</span></dt><dt><span class="refentrytitle"><a href="Imager.html">Imager</a></span><span class="refpurpose"> &mdash; An imager with a defined field of view.</span></dt><dt><span class="refentrytitle"><a href="ImpulsiveBurn.html">ImpulsiveBurn</a></span><span class="refpurpose"> &mdash; An impulsive maneuver</span></dt><dt><span class="refentrytitle"><a href="IntrusionLocator.html">IntrusionLocator</a></span><span class="refpurpose"> &mdash; A line-of-sight event locator between a target
    <span class="guilabel">CelestialBody</span> and an observer
    <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="LibrationPoint.html">LibrationPoint</a></span><span class="refpurpose"> &mdash; An equilibrium point in the circular, restricted 3-body
    problem</span></dt><dt><span class="refentrytitle"><a href="NuclearPowerSystem.html">NuclearPowerSystem</a></span><span class="refpurpose"> &mdash; A nuclear power system</span></dt><dt><span class="refentrytitle"><a href="PlanetographicRegion.html">PlanetographicRegion</a></span><span class="refpurpose"> &mdash; Define an area on a celestial body's surface as a target for
    an observing <span class="guilabel">Spacecraft</span></span></dt><dt><span class="refentrytitle"><a href="Plate.html">Plate</a></span><span class="refpurpose"> &mdash; Used to specify the properties of a single spacecraft surface
    (body panel, solar array side, or other surface) for high-fidelity solar
    radiation pressure modeling, including specular, diffuse, and absorptive
    effects.</span></dt><dt><span class="refentrytitle"><a href="Propagator.html">Propagator</a></span><span class="refpurpose"> &mdash; A propagator models spacecraft motion</span></dt><dt><span class="refentrytitle"><a href="SolarPowerSystem.html">SolarPowerSystem</a></span><span class="refpurpose"> &mdash; A solar power system model</span></dt><dt><span class="refentrytitle"><a href="SolarSystem.html">SolarSystem</a></span><span class="refpurpose"> &mdash; High level solar system configuration options</span></dt><dt><span class="refentrytitle"><a href="Spacecraft.html">Spacecraft</a></span><span class="refpurpose"> &mdash; A spacecraft model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftAttitude.html">Spacecraft Attitude</a></span><span class="refpurpose"> &mdash; The spacecraft attitude model</span></dt><dt><span class="refentrytitle"><a href="SpacecraftBallisticMass.html">Spacecraft Ballistic/Mass Properties</a></span><span class="refpurpose"> &mdash; The physical properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftEpoch.html">Spacecraft Epoch</a></span><span class="refpurpose"> &mdash; The spacecraft epoch</span></dt><dt><span class="refentrytitle"><a href="SpacecraftHardware.html">Spacecraft Hardware</a></span><span class="refpurpose"> &mdash; Add hardware to a spacecraft</span></dt><dt><span class="refentrytitle"><a href="SpacecraftOrbitState.html">Spacecraft Orbit State</a></span><span class="refpurpose"> &mdash; The orbital initial conditions</span></dt><dt><span class="refentrytitle"><a href="SpacecraftVisualizationProperties.html">Spacecraft Visualization Properties</a></span><span class="refpurpose"> &mdash; The visual properties of the spacecraft</span></dt><dt><span class="refentrytitle"><a href="ThrustHistoryFile.html">ThrustHistoryFile</a></span><span class="refpurpose"> &mdash; A time history of input thrust/acceleration vectors and mass
    flow rate</span></dt><dt><span class="refentrytitle"><a href="ThrustSegment.html">ThrustSegment</a></span><span class="refpurpose"> &mdash; One or more <span class="guilabel">ThrustSegments</span> define how
    data in a thrust history file are used.</span></dt></dl></dd><dt><span class="section"><a href="ch18s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="BeginFileThrust.html">BeginFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="BeginFiniteBurn.html">BeginFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers</span></dt><dt><span class="refentrytitle"><a href="EndFileThrust.html">EndFileThrust</a></span><span class="refpurpose"> &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</span></dt><dt><span class="refentrytitle"><a href="EndFiniteBurn.html">EndFiniteBurn</a></span><span class="refpurpose"> &mdash; Model finite thrust maneuvers in the mission
    sequence</span></dt><dt><span class="refentrytitle"><a href="FindEvents.html">FindEvents</a></span><span class="refpurpose"> &mdash; Execute an event location search</span></dt><dt><span class="refentrytitle"><a href="Maneuver.html">Maneuver</a></span><span class="refpurpose"> &mdash; Perform an impulsive (instantaneous) maneuver</span></dt><dt><span class="refentrytitle"><a href="Propagate.html">Propagate</a></span><span class="refpurpose"> &mdash; Propagates spacecraft to a requested stopping
    condition</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch19.html">19. Input/Output</a></span></dt><dd><dl><dt><span class="section"><a href="ch19.html#N22D14">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DynamicDataDisplay.html">DynamicDataDisplay</a></span><span class="refpurpose"> &mdash; A user-defined resource used in tandem with the
    <span class="guilabel">UpdateDynamicData</span> command to print current values of
    parameters to a table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="EphemerisFile.html">EphemerisFile</a></span><span class="refpurpose"> &mdash; Generate spacecraft&rsquo;s ephemeris data</span></dt><dt><span class="refentrytitle"><a href="FileInterface.html">FileInterface</a></span><span class="refpurpose"> &mdash; An interface to a data file</span></dt><dt><span class="refentrytitle"><a href="GroundTrackPlot.html">GroundTrackPlot</a></span><span class="refpurpose"> &mdash; A user-defined resource that draws longitude and latitude
    time-history of a spacecraft</span></dt><dt><span class="refentrytitle"><a href="OpenFramesInterface.html">OpenFramesInterface</a></span><span class="refpurpose"> &mdash; A user-defined resource that provides high-performance 3D interactive visualizations of GMAT missions</span></dt><dt><span class="refentrytitle"><a href="OrbitView.html">OrbitView</a></span><span class="refpurpose"> &mdash; A user-defined resource that plots 3-Dimensional
    trajectories</span></dt><dt><span class="refentrytitle"><a href="ReportFile.html">ReportFile</a></span><span class="refpurpose"> &mdash; Report data to a text file</span></dt><dt><span class="refentrytitle"><a href="XYPlot.html">XYPlot</a></span><span class="refpurpose"> &mdash; Plots data onto the X and Y axes of a graph</span></dt></dl></dd><dt><span class="section"><a href="ch19s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="ClearPlot.html">ClearPlot</a></span><span class="refpurpose"> &mdash; Allows you to clear all data from an XYPlot</span></dt><dt><span class="refentrytitle"><a href="GetEphemStates_Function.html">GetEphemStates()</a></span><span class="refpurpose"> &mdash; Function used to output initial and final spacecraft states
    from an ephemeris file</span></dt><dt><span class="refentrytitle"><a href="MarkPoint.html">MarkPoint</a></span><span class="refpurpose"> &mdash; Allows you to add a special mark point character on an
    XYPlot</span></dt><dt><span class="refentrytitle"><a href="PenUpPenDown.html">PenUpPenDown</a></span><span class="refpurpose"> &mdash; Allows you to stop or begin drawing data on a
    plot</span></dt><dt><span class="refentrytitle"><a href="Report.html">Report</a></span><span class="refpurpose"> &mdash; Allows you to write data to a text file</span></dt><dt><span class="refentrytitle"><a href="Set.html">Set</a></span><span class="refpurpose"> &mdash; Configure a resource from a data interface</span></dt><dt><span class="refentrytitle"><a href="Toggle.html">Toggle</a></span><span class="refpurpose"> &mdash; Allows you to turn data output off or on</span></dt><dt><span class="refentrytitle"><a href="UpdateDynamicData.html">UpdateDynamicData</a></span><span class="refpurpose"> &mdash; A command used in tandem with a
	<span class="guilabel">DynamicDataDisplay</span> to update the data being shown in
	the display table on the GUI.</span></dt><dt><span class="refentrytitle"><a href="Write.html">Write</a></span><span class="refpurpose"> &mdash; Writes data to one or more of the following three
    destinations: the message window, the log file, or a
    <span class="guilabel">ReportFile</span> resource.</span></dt></dl></dd><dt><span class="section"><a href="ch19s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Color.html">Color</a></span><span class="refpurpose"> &mdash; Color support in GMAT resources and commands</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch20.html">20. Targeting/Parameter Optimization</a></span></dt><dd><dl><dt><span class="section"><a href="ch20.html#N25D9B">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DifferentialCorrector.html">DifferentialCorrector</a></span><span class="refpurpose"> &mdash; A numerical solver</span></dt><dt><span class="refentrytitle"><a href="FminconOptimizer.html">FminconOptimizer</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    fmincon</span></dt><dt><span class="refentrytitle"><a href="SNOPTOptimizer.html">SNOPT</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    SNOPT</span></dt><dt><span class="refentrytitle"><a href="VF13ad.html">VF13ad</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    VF13ad</span></dt><dt><span class="refentrytitle"><a href="Yukon.html">Yukon</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    Yukon</span></dt></dl></dd><dt><span class="section"><a href="ch20s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Achieve.html">Achieve</a></span><span class="refpurpose"> &mdash; Specify a goal for a <span class="guilabel">Target</span>
    sequence</span></dt><dt><span class="refentrytitle"><a href="Minimize.html">Minimize</a></span><span class="refpurpose"> &mdash; Define the cost function to minimize</span></dt><dt><span class="refentrytitle"><a href="NonlinearConstraint.html">NonlinearConstraint</a></span><span class="refpurpose"> &mdash; Specify a constraint used during optimization, with an optional tolerance used when checking physical constraint values</span></dt><dt><span class="refentrytitle"><a href="Optimize.html">Optimize</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Target.html">Target</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Vary.html">Vary</a></span><span class="refpurpose"> &mdash; Specifies variables used by a solver</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch21.html">21. Orbit Determination</a></span></dt><dd><dl><dt><span class="section"><a href="ch21.html#N27B20">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="AcceptFilter.html">AcceptFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Antenna.html">Antenna</a></span><span class="refpurpose"> &mdash; Transmits or receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="BatchEstimator.html">BatchEstimator</a></span><span class="refpurpose"> &mdash; A batch least squares estimator</span></dt><dt><span class="refentrytitle"><a href="ErrorModel.html">ErrorModel</a></span><span class="refpurpose"> &mdash; Used to specify measurement noise for simulation and
    estimation, and to apply or estimate measurement biases.</span></dt><dt><span class="refentrytitle"><a href="EstimatedParameter.html">EstimatedParameter</a></span><span class="refpurpose"> &mdash; Used for modeling of dynamically estimated parameters in the
    Extended Kalman Filter.</span></dt><dt><span class="refentrytitle"><a href="ExtendedKalmanFilter.html">ExtendedKalmanFilter</a></span><span class="refpurpose"> &mdash; An extended Kalman filter orbit determination
    estimator</span></dt><dt><span class="refentrytitle"><a href="ProcessNoiseModel.html">ProcessNoiseModel</a></span><span class="refpurpose"> &mdash; Used to specify process noise for estimation when using the
    ExtendedKalmanFilter estimator.</span></dt><dt><span class="refentrytitle"><a href="Receiver.html">Receiver</a></span><span class="refpurpose"> &mdash; Hardware that receives an RF signal.</span></dt><dt><span class="refentrytitle"><a href="RejectFilter.html">RejectFilter</a></span><span class="refpurpose"> &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</span></dt><dt><span class="refentrytitle"><a href="Simulator.html">Simulator</a></span><span class="refpurpose"> &mdash; Configures the generation of simulated tracking data
    measurements.</span></dt><dt><span class="refentrytitle"><a href="Smoother.html">Smoother</a></span><span class="refpurpose"> &mdash; A backwards filter yielding an improved estimate of states
    through weighted combination of forward and reverse sequential
    estimation.</span></dt><dt><span class="refentrytitle"><a href="SpacecraftNavigation.html">Spacecraft Navigation</a></span><span class="refpurpose"> &mdash; There are a number of <span class="guilabel">Spacecraft</span> fields
    that are used exclusively to support GMAT's navigation (orbit
    determination) capability.</span></dt><dt><span class="refentrytitle"><a href="TrackingFileSet.html">TrackingFileSet</a></span><span class="refpurpose"> &mdash; Manages the observation data contained in one or more external
    tracking data files.</span></dt><dt><span class="refentrytitle"><a href="Transmitter.html">Transmitter</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, attached to a
    <span class="guilabel">GroundStation</span> or <span class="guilabel">Spacecraft</span>
    resource, that transmits an RF signal.</span></dt><dt><span class="refentrytitle"><a href="Transponder.html">Transponder</a></span><span class="refpurpose"> &mdash; Defines the electronics hardware, typically attached to a
    spacecraft, that receives and automatically re-transmits an incoming
    signal.</span></dt></dl></dd><dt><span class="section"><a href="ch21s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="RunEstimator.html">RunEstimator</a></span><span class="refpurpose"> &mdash; Ingests navigation measurements and generates an estimated
    state vector</span></dt><dt><span class="refentrytitle"><a href="RunSimulator.html">RunSimulator</a></span><span class="refpurpose"> &mdash; Generates simulated navigation measurements</span></dt><dt><span class="refentrytitle"><a href="RunSmoother.html">RunSmoother</a></span><span class="refpurpose"> &mdash; Runs a sequential smoother estimator.</span></dt></dl></dd><dt><span class="section"><a href="ch21s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="TrackingDataTypes.html">Tracking Data Types for Orbit Determination</a></span><span class="refpurpose"> &mdash; This section describes tracking data types and file formats
    for orbit determination.</span></dt><dt><span class="refentrytitle"><a href="NavPropagatorConfiguration.html">Configuration of Propagators for Orbit
    Determination</a></span><span class="refpurpose"> &mdash; This section describes some special considerations for
    configuration of numerical and ephemeris propagators for GMAT's
    estimators.</span></dt><dt><span class="refentrytitle"><a href="InitialOrbitDetermination.html">Initial Orbit Determination</a></span><span class="refpurpose"> &mdash; A set of python functions to support early orbit
    operations</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch22.html">22. Programming</a></span></dt><dd><dl><dt><span class="section"><a href="ch22.html#N2BAB5">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Array.html">Array</a></span><span class="refpurpose"> &mdash; A user-defined one- or two-dimensional array
    variable</span></dt><dt><span class="refentrytitle"><a href="GmatFunction.html">GMATFunction</a></span><span class="refpurpose"> &mdash; Declaration of a GMAT function</span></dt><dt><span class="refentrytitle"><a href="MatlabFunction.html">MatlabFunction</a></span><span class="refpurpose"> &mdash; Declaration of an external MATLAB function</span></dt><dt><span class="refentrytitle"><a href="String.html">String</a></span><span class="refpurpose"> &mdash; A user-defined string variable</span></dt><dt><span class="refentrytitle"><a href="Variable.html">Variable</a></span><span class="refpurpose"> &mdash; A user-defined numeric variable</span></dt></dl></dd><dt><span class="section"><a href="ch22s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Assignment.html">Assignment (<code class="literal">=</code>)</a></span><span class="refpurpose"> &mdash; Set a variable or resource field to a value, possibly using
    mathematical expressions</span></dt><dt><span class="refentrytitle"><a href="BeginMissionSequence.html">BeginMissionSequence</a></span><span class="refpurpose"> &mdash; Begin the mission sequence portion of a script</span></dt><dt><span class="refentrytitle"><a href="BeginScript.html">BeginScript</a></span><span class="refpurpose"> &mdash; Execute free-form script commands</span></dt><dt><span class="refentrytitle"><a href="Breakpoint.html">Breakpoint</a></span><span class="refpurpose"> &mdash; 
      Pause a run and let the user examine the state of objects.
    </span></dt><dt><span class="refentrytitle"><a href="CallGmatFunction.html">CallGmatFunction</a></span><span class="refpurpose"> &mdash; Call a GMAT function</span></dt><dt><span class="refentrytitle"><a href="CallMatlabFunction.html">CallMatlabFunction</a></span><span class="refpurpose"> &mdash; Call a MATLAB function</span></dt><dt><span class="refentrytitle"><a href="CallPythonFunction.html">CallPythonFunction</a></span><span class="refpurpose"> &mdash; Call a Python function</span></dt><dt><span class="refentrytitle"><a href="CommandEcho.html">CommandEcho</a></span><span class="refpurpose"> &mdash; Toggle the use of the <span class="guilabel">Echo</span>
    command</span></dt><dt><span class="refentrytitle"><a href="For.html">For</a></span><span class="refpurpose"> &mdash; Execute a series of commands a specified number of
    times</span></dt><dt><span class="refentrytitle"><a href="Global.html">Global</a></span><span class="refpurpose"> &mdash; Declare Objects as global</span></dt><dt><span class="refentrytitle"><a href="If.html">If</a></span><span class="refpurpose"> &mdash; Conditionally execute a series of commands</span></dt><dt><span class="refentrytitle"><a href="Stop.html">Stop</a></span><span class="refpurpose"> &mdash; Stop mission execution</span></dt><dt><span class="refentrytitle"><a href="While.html">While</a></span><span class="refpurpose"> &mdash; Execute a series of commands repeatedly while a condition is
    met</span></dt></dl></dd><dt><span class="section"><a href="ch22s03.html">System</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="IncludeMacro.html">#Include Macro</a></span><span class="refpurpose"> &mdash; Load or import a script snippet</span></dt><dt><span class="refentrytitle"><a href="MatlabInterface.html">MATLAB Interface</a></span><span class="refpurpose"> &mdash; Interface to MATLAB system</span></dt><dt><span class="refentrytitle"><a href="PythonInterface.html">Python Interface</a></span><span class="refpurpose"> &mdash; Interface to the Python programming language</span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="ch23.html">23. Optimal Control</a></span></dt><dd><dl><dt><span class="section"><a href="ch23.html#N2D3D4">User Guide</a></span></dt></dl></dd><dt><span class="chapter"><a href="ch24.html">24. System</a></span></dt><dd><dl><dt><span class="section"><a href="ch24.html#N2D3E2">System Level Components</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="CalculationParameters.html">Calculation Parameters</a></span><span class="refpurpose"> &mdash; Resource properties available for use by commands and
    output</span></dt><dt><span class="refentrytitle"><a href="CommandLine.html">Command-Line Usage</a></span><span class="refpurpose"> &mdash; Starting the <code class="filename">GMAT</code> application from the
    command line</span></dt><dt><span class="refentrytitle"><a href="KeyboardShortcuts.html">Keyboard Shortcuts</a></span><span class="refpurpose"> &mdash; Keyboard shortcuts in the graphical user
    interface</span></dt><dt><span class="refentrytitle"><a href="ScriptLanguage.html">Script Language</a></span><span class="refpurpose"> &mdash; The GMAT script language</span></dt><dt><span class="refentrytitle"><a href="StartupFile.html">Startup File</a></span><span class="refpurpose"> &mdash; The <code class="filename">gmat_startup_file.txt</code> configuration
    file</span></dt></dl></dd></dl></dd></dl></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch16s07.html">Prev</a>&nbsp;</td><td align="center" width="20%">&nbsp;</td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch17.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">References&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;17.&nbsp;API</td></tr></table></div></body></html>