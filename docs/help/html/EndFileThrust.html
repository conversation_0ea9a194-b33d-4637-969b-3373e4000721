<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>EndFileThrust</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18s02.html" title="Commands"><link rel="prev" href="BeginFiniteBurn.html" title="BeginFiniteBurn"><link rel="next" href="EndFiniteBurn.html" title="EndFiniteBurn"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">EndFileThrust</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="BeginFiniteBurn.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="EndFiniteBurn.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="EndFileThrust"></a><div class="titlepage"></div><a name="N22422" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">EndFileThrust</span></h2><p>EndFileThrust &mdash; Apply a piece-wise continuous thrust/acceleration and mass
    flow rate profile</p></div><div class="refsection"><a name="N22433"></a><h2>Description</h2><p>To apply a piece-wise continuous thrust/acceleration and mass flow
    rate profile to a <span class="guilabel">Spacecraft</span> object, you use a pair
    of commands, the <span class="guilabel">BeginFileThrust</span> and the
    <span class="guilabel">EndFileThrust</span> command. The use of both of these
    commands is described in the <a class="xref" href="BeginFileThrust.html" title="BeginFileThrust"><span class="refentrytitle">BeginFileThrust</span></a> command
    help.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="BeginFiniteBurn.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="EndFiniteBurn.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">BeginFiniteBurn&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;EndFiniteBurn</td></tr></table></div></body></html>