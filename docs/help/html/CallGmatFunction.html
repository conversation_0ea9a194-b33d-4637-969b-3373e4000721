<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>CallGmatFunction</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s02.html" title="Commands"><link rel="prev" href="Breakpoint.html" title="Breakpoint"><link rel="next" href="CallMatlabFunction.html" title="CallMatlabFunction"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">CallGmatFunction</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Breakpoint.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="CallMatlabFunction.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="CallGmatFunction"></a><div class="titlepage"></div><a name="N2C7B2" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">CallGmatFunction</span></h2><p>CallGmatFunction &mdash; Call a GMAT function</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><em class="replaceable"><code>GmatFunction</code></em><code class="literal">()</code>
<em class="replaceable"><code>GmatFunction</code></em><code class="literal">(</code><em class="replaceable"><code>input_argument</code></em>[<code class="literal">,</code> <em class="replaceable"><code>input_argument</code></em>]...<code class="literal">)</code>
<code class="literal">[</code><em class="replaceable"><code>output_argument</code></em>[<code class="literal">,</code> <em class="replaceable"><code>output_argument</code></em>]...<code class="literal">]</code> <code class="literal">=</code> <em class="replaceable"><code>GmatFunction</code></em>
<code class="literal">[</code><em class="replaceable"><code>output_argument</code></em>[<code class="literal">,</code> <em class="replaceable"><code>output_argument</code></em>]...<code class="literal">]</code> <code class="literal">=</code> <em class="replaceable"><code>...
    GmatFunction</code></em><code class="literal">(</code><em class="replaceable"><code>input_argument</code></em>[<code class="literal">,</code> <em class="replaceable"><code>input_argument</code></em>]...<code class="literal">)</code></pre></div><div class="refsection"><a name="N2C810"></a><h2>Description</h2><p>GMAT provides a special command that allows you to call a GMAT
    function which is written via GMAT's <span class="guilabel">GmatFunction</span>
    resource. In the GUI, the GMAT function is called through the
    <span class="guilabel">CallGmatFunction</span> command.</p><p>In the syntax description, <span class="guilabel">GmatFunction</span> is a
    <span class="guilabel">GmatFunction</span> resource that must be declared during
    initialization. Arguments can be passed into the function as inputs and
    returned from the function as outputs. See <a class="xref" href="CallGmatFunction.html#CallGmatFunction_Remarks" title="Remarks">Remarks</a> for details.
    Furthermore, data that is passed into the function as input or received
    from the function as output can also be declared as global by using GMAT's
    <span class="guilabel">Global</span> command. See the <span class="guilabel"><a class="xref" href="Global.html" title="Global"><span class="refentrytitle">Global</span></a></span> reference for more details.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="GmatFunction.html" title="GMATFunction"><span class="refentrytitle">GMATFunction</span></a>, <a class="xref" href="Global.html" title="Global"><span class="refentrytitle">Global</span></a></p></div><div class="refsection"><a name="N2C838"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_CallGmatFunction_GUI_1.png" align="middle" height="252"></td></tr></table></div></div><p>The <span class="guilabel">CallGmatFunction</span> GUI provides two input
    boxes for input and output arguments and a list to select a GMAt function
    to call.</p><p>The <span class="guilabel">Output</span> box lists all configured output
    argument parameters. These must be selected by clicking
    <span class="guilabel">Edit</span>, which displays a
    <span class="guilabel">ParameterSelectioDialog</span> window. See the <a class="xref" href="CalculationParameters.html" title="Calculation Parameters"><span class="refentrytitle">Calculation Parameters</span></a> reference for details on how to select a
    parameter.</p><p>The <span class="guilabel">Input</span> box is identical in behavior to
    <span class="guilabel">Output</span>, but lists all configured input arguments to
    the function. Arguments must be selected by clicking
    <span class="guilabel">Edit</span>. The <span class="guilabel">Function</span> list displays
    all functions that have been declared as <span class="guilabel">GmatFunction</span>
    resources in the <span class="guilabel">Resources</span> tree. Select a function
    from the list to call it.</p><p>When the changes are accepted, GMAT does not perform any validation
    of input or output arguments. This validation is performed when the
    mission is actually run.</p></div><div class="refsection"><a name="CallGmatFunction_Remarks"></a><h2>Remarks</h2><p>GMAT objects can be passed into the GMAT function as input and can
    also be returned from the function as output. If a given GMAT object is
    not declared as global in both the main script and inside the GMAT
    function, then all objects that are passed into or received as output from
    the function are considered to be local to that function and the main
    script.</p><p>Below is a list of allowed arguments that can be passed as input to
    the function and received as output from the function. Also see
    <span class="guilabel">GmatFunction</span> resource's <a class="xref" href="GmatFunction.html#GmatFunction_Remarks" title="Remarks">Remarks</a> and <a class="xref" href="GmatFunction.html#GmatFunction_Examples" title="Examples">Examples</a> sections for
    more details and distinct examples that show how to pass objects as inputs
    to the function, perform an operation inside the function, then receive
    objects as outputs from the function. Note, a GMAT function file must
    contain one and only one function definition.</p><p>The input arguments (<em class="replaceable"><code>input_argument</code></em>
    values in the syntax description) can be any of the following types:
    </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Any resource objects (e.g. <span class="guilabel">Spacecraft</span>,
          <span class="guilabel">Propagator</span>, <span class="guilabel">DC</span>,
          <span class="guilabel">Optimizers</span>, <span class="guilabel">Impulsive or
          FiniteBurns</span>)</p></li><li class="listitem"><p>resource parameter of real number type (e.g.
          <span class="guilabel"><em class="replaceable"><code>Spacecraft</code></em>.X</span>)</p></li><li class="listitem"><p>resource parameter of string type (e.g.
          <span class="guilabel"><em class="replaceable"><code>Spacecraft</code></em>.UTCGregorian</span>)</p></li><li class="listitem"><p><span class="guilabel">Array</span>, <span class="guilabel">String</span>, or
          <span class="guilabel">Variable</span> resource</p></li></ul></div><p>The output arguments can be any of the following types:
    </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Resource object like <span class="guilabel">Spacecraft</span></p></li><li class="listitem"><p>resource parameter of real number type (e.g.
          <span class="guilabel"><em class="replaceable"><code>Spacecraft</code></em>.X</span>)</p></li><li class="listitem"><p>resource parameter of string type (e.g.
          <span class="guilabel"><em class="replaceable"><code>Spacecraft</code></em>.UTCGregorian</span>)</p></li><li class="listitem"><p><span class="guilabel">Array</span>, <span class="guilabel">String</span>, or
          <span class="guilabel">Variable</span> resource</p></li></ul></div></div><div class="refsection"><a name="CallGmatFunction_Examples"></a><h2>Examples</h2><div class="informalexample"><p>Call two different functions. One function performs a simple cross
      product and the second function performs a dot product.</p><pre class="programlisting"><code class="code">Create ReportFile rf
rf.WriteHeaders = false

Create GmatFunction cross_product
cross_product.FunctionPath = ...
'C:\Users\<USER>\Desktop\cross_product.gmf'

Create GmatFunction dot_product
dot_product.FunctionPath = ...
'C:\Users\<USER>\Desktop\dot_product.gmf'      

Create Array v1[3,1] v2[3,1] v3[3,1] ...
v4[3,1] v5[3,1]

Create Variable v6
Create String tempstring


BeginMissionSequence

v1(1,1) = 1
v1(2,1) = 2
v1(3,1) = 3
v2(1,1) = 4
v2(2,1) = 5
v2(3,1) = 6
v4(1,1) = 1
v4(2,1) = 2
v4(3,1) = 3
v5(1,1) = 4
v5(2,1) = -5
v5(3,1) = 6

% Call function. Pass local arrays as input:
% Receive local array as output
[v3] = cross_product(v1, v2)

Report rf v3

% Call function. Pass local arrays as input:
% Receive local variable as output
GMAT [v6] = dot_product(v4, v5)

tempstring = '---------'
Report rf tempstring
Report rf v6


%%%%%% cross_product Function begins below:

function [cross] = cross_product(vec1,vec2)

Create Array cross[3,1]

BeginMissionSequence

cross(1,1) = vec1(2,1)*vec2(3,1) - vec1(3,1)*vec2(2,1)
cross(2,1) = -(vec1(1,1)*vec2(3,1) - vec1(3,1)*vec2(1,1))
cross(3,1) = vec1(1,1)*vec2(2,1) - vec1(2,1)*vec2(1,1)


%%%%%% dot_product Function begins below:

function [c] = dot_product(a1,b1)

Create Variable c

BeginMissionSequence

c = a1(1,1)*b1(1,1) + a1(2,1)*b1(2,1) + a1(3,1)*b1(3,1)</code></pre></div><div class="informalexample"><p>Call GMAT function and pass local spacecraft as input, perform
      simple operation inside the function, then send out updated, local
      spacecraft to the main script. Finally report spacecraft old and updated
      position vector to the local report file subscriber:</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.DateFormat = UTCGregorian;
aSat.Epoch = '01 Jan 2000 11:59:28.000'
aSat.CoordinateSystem = EarthMJ2000Eq
aSat.DisplayStateType = Cartesian
aSat.X = 7100
aSat.Y = 0
aSat.Z = 1300

Create ReportFile rf
rf.WriteHeaders = false

Create GmatFunction Spacecraft_In_Out
Spacecraft_In_Out.FunctionPath = ...
'C:\Users\<USER>\Desktop\Spacecraft_In_Out.gmf'


BeginMissionSequence

% Report initial S/C Position to local 'rf':
Report rf aSat.X aSat.Y aSat.Z

% Call function. Pass local S/C as input:
% Receive updated local S/C:
[aSat] = Spacecraft_In_Out(aSat)

% Report updated S/C Position to local 'rf':
Report rf aSat.X aSat.Y aSat.Z



%%%%%%%%%% Function begins below:

function [aSat] = Spacecraft_In_Out(aSat)

BeginMissionSequence

% Update the S/C Position vector:
% Send updated S/C back to main script:
aSat.X = aSat.X + 1000
aSat.Y = aSat.Y + 2000
aSat.Z = aSat.Z + 3000</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Breakpoint.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="CallMatlabFunction.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Breakpoint&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;CallMatlabFunction</td></tr></table></div></body></html>