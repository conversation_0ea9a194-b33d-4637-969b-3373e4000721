<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Resources Tree</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="TourOfGmat.html" title="Chapter&nbsp;3.&nbsp;Tour of GMAT"><link rel="prev" href="TourOfGmat.html" title="Chapter&nbsp;3.&nbsp;Tour of GMAT"><link rel="next" href="MissionTree.html" title="Mission Tree"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Resources Tree</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="TourOfGmat.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;3.&nbsp;Tour of GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="MissionTree.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ResourceTree"></a>Resources Tree</h2></div></div></div><a name="N1058A" class="indexterm"></a><p>The Resources tree displays GMAT resources and organizes them into
  logical groups and represents any objects that might be used or called in
  the Mission tree. This tree allows a user to add, edit, rename, or delete
  most available resources. The Resources tree can be edited either in the
  GMAT GUI or by loading or syncing a script file. All objects created in a
  GMAT script using a <span class="guilabel">Create</span> command are found in the
  Resources tree in the GMAT desktop. The default Resource tree is displayed
  below (<a class="xref" href="ResourceTree.html#ResourcesTree_Fig_Default" title="Figure&nbsp;3.4.&nbsp;Default Resources tree">Figure&nbsp;3.4</a>).</p><div class="figure"><a name="ResourcesTree_Fig_Default"></a><p class="title"><b>Figure&nbsp;3.4.&nbsp;Default Resources tree</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Using_ResourcesTree_ResourcesTree.png" align="middle" height="437" alt="Default Resources tree"></td></tr></table></div></div></div></div><br class="figure-break"><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N105A3"></a>Organization</h3></div></div></div><p>The Resources tree displays created resources organized into folders
    by object category. The <span class="guilabel">SolarSystem</span> and
    <span class="guilabel">Solvers</span> folders contain more specific folders which
    can be found by clicking the expand (<span class="guilabel">+</span>) icon.
    Conversely, folders can be collapsed by clicking the minimize
    (<span class="guilabel">-</span>) icon.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N105B4"></a>Folder Menus</h3></div></div></div><p>Resources can be added by right clicking the folder of the resource
    and clicking the resource type from the available menu. Most folders have
    only one available resource type; for example if the
    <span class="guilabel">Spacecraft</span> folder is right-clicked, the user can only
    click &ldquo;<span class="guimenuitem">Add Spacecraft</span>&rdquo; (<a class="xref" href="ResourceTree.html#ResourcesTree_Fig_AddSpacecraft" title="Figure&nbsp;3.5.&nbsp;Folder menu for Spacecraft">Figure&nbsp;3.5</a>).
    Other folders have multiple objects that can be added and the user must
    first select the &ldquo;<span class="guimenuitem">Add</span>&rdquo; menu before selecting
    the object; for example to add a <span class="guilabel">ChemicalTank</span>, right
    click the &ldquo;<span class="guilabel">Hardware</span>&rdquo; folder, select
    &ldquo;<span class="guimenuitem">Add</span>&rdquo;, then the list of available resource
    types is displayed and the user can click &ldquo;<span class="guimenuitem">Fuel
    Tank</span>&rdquo; (<a class="xref" href="ResourceTree.html#ResourcesTree_Fig_AddFuelTank" title="Figure&nbsp;3.6.&nbsp;Folder menu for Hardware">Figure&nbsp;3.6</a>). User-defined solar system resources are added
    by right-clicking either <span class="guilabel">Sun</span> or a default
    <span class="guilabel">CelestialBody</span> resource. By right-clicking
    <span class="guilabel">Sun</span> the user can add a <span class="guilabel">Planet</span>,
    <span class="guilabel">Comet</span>, or <span class="guilabel">Asteroid</span> to the solar
    system. By right-clicking a <span class="guilabel">Planet</span> the user can add a
    <span class="guilabel">Moon</span> to that <span class="guilabel">Planet</span>.</p><div class="figure"><a name="ResourcesTree_Fig_AddSpacecraft"></a><p class="title"><b>Figure&nbsp;3.5.&nbsp;Folder menu for <span class="guilabel">Spacecraft</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_ResourceTree_GUI_2.png" align="middle" height="66" alt="Folder menu for Spacecraft"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="ResourcesTree_Fig_AddFuelTank"></a><p class="title"><b>Figure&nbsp;3.6.&nbsp;Folder menu for <span class="guilabel">Hardware</span></b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_ResourceTree_GUI_3.png" align="middle" height="203" alt="Folder menu for Hardware"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1060F"></a>Resource Menus</h3></div></div></div><p>Resources can be edited by right-clicking on the resources and
    selecting one of the options from the menu (<a class="xref" href="ResourceTree.html#ResourcesTree_Fig_ResourceMenu" title="Figure&nbsp;3.7.&nbsp;Resource menu">Figure&nbsp;3.7</a>).</p><div class="figure"><a name="ResourcesTree_Fig_ResourceMenu"></a><p class="title"><b>Figure&nbsp;3.7.&nbsp;Resource menu</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Using_ResourcesTree_ResourceMenu.png" align="middle" height="175" alt="Resource menu"></td></tr></table></div></div></div></div><br class="figure-break"><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N10625"></a><span class="guimenuitem">Open/Close</span></h4></div></div></div><p>To open a resource, you can either right-click the resource and
      select &ldquo;<span class="guimenuitem">Open</span>&rdquo;, or you can double click the
      resource. Conversely, the resource can be closed either by options in
      the resource properties window or selecting
      &ldquo;<span class="guimenuitem">Close</span>&rdquo; from the resource menu. When a
      resource is opened and the name is right-clicked in the Resource tree,
      the only options in the object menu are
      &ldquo;<span class="guimenuitem">Open</span>&rdquo; and
      &ldquo;<span class="guimenuitem">Close</span>&rdquo;.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N10637"></a><span class="guimenuitem">Rename</span></h4></div></div></div><p>Once a resource has been created, the user can rename it to any
      valid name. Valid names must begin with a letter and may be followed by
      any combination of letters digits and underscores. Invalid names
      include:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Folder names (eg, <span class="guilabel">Spacecraft</span>)</p></li><li class="listitem"><p>Command names (eg, <span class="guilabel">Propagate</span>)</p></li><li class="listitem"><p>Names already in use (eg, naming two variables
            &ldquo;<code class="literal">var</code>&rdquo;)</p></li><li class="listitem"><p>Keywords (eg, &ldquo;<code class="literal">GMAT</code>&rdquo; or
            &ldquo;<code class="literal">function</code>&rdquo;)</p></li><li class="listitem"><p>Names with spaces</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N1065C"></a><span class="guimenuitem">Delete</span></h4></div></div></div><p>Resources can be deleted by right clicking the object and
      selecting &ldquo;<span class="guilabel">Delete</span>&rdquo;. Resources cannot be deleted if
      they are used by another resource or command and an error with be
      thrown. For example, a <span class="guilabel">Spacecraft</span> resource cannot
      be deleted if one of its properties (eg.
      <span class="guilabel">DefaultSC</span>.<span class="guilabel">A1ModJulian</span>) is
      being used by the <span class="guilabel">Report</span> command. Some default
      objects cannot be deleted. In such cases, the
      <span class="guilabel">Delete</span> menu item will not be shown. They
      include:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Default coordinate systems</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p><span class="guilabel">EarthMJ2000Eq</span></p></li><li class="listitem"><p><span class="guilabel">EarthMJ2000Ec</span></p></li><li class="listitem"><p><span class="guilabel">EarthFixed</span></p></li><li class="listitem"><p><span class="guilabel">EarthICRF</span></p></li></ul></div></li><li class="listitem"><p>Default planetary bodies</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p><span class="guilabel">Sun</span></p></li><li class="listitem"><p><span class="guilabel">Mercury</span></p></li><li class="listitem"><p><span class="guilabel">Venus</span></p></li><li class="listitem"><p><span class="guilabel">Earth</span></p></li><li class="listitem"><p><span class="guilabel">Luna</span></p></li><li class="listitem"><p><span class="guilabel">Mars</span></p></li><li class="listitem"><p><span class="guilabel">Jupiter</span></p></li><li class="listitem"><p><span class="guilabel">Saturn</span></p></li><li class="listitem"><p><span class="guilabel">Uranus</span></p></li><li class="listitem"><p><span class="guilabel">Neptune</span></p></li><li class="listitem"><p><span class="guilabel">Pluto</span></p></li></ul></div></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N106B9"></a><span class="guimenuitem">Clone</span></h4></div></div></div><p>Objects can be cloned by selecting the
      &ldquo;<span class="guimenuitem">Clone</span>&rdquo; option in the menu. A cloned object
      will be an exact copy of the original object with a different name. Some
      objects cannot be cloned. In such cases, the
      <span class="guimenuitem">Clone</span> menu item will not be available. The
      only objects that cannot be cloned are:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Default coordinate systems (listed above)</p></li><li class="listitem"><p>Default planetary bodies (listed above)</p></li><li class="listitem"><p><span class="guilabel">Propagator</span> resource objects</p></li></ul></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="TourOfGmat.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="TourOfGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="MissionTree.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;3.&nbsp;Tour of GMAT&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Mission Tree</td></tr></table></div></body></html>