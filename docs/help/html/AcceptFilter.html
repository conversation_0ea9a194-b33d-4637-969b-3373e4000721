<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>AcceptFilter</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="ch21.html" title="Chapter&nbsp;21.&nbsp;Orbit Determination"><link rel="next" href="Antenna.html" title="Antenna"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">AcceptFilter</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch21.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Antenna.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="AcceptFilter"></a><div class="titlepage"></div><a name="N27B27" class="indexterm"></a><a name="N27B2A" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">AcceptFilter</span></h2><p>AcceptFilter &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</p></div><div class="refsection"><a name="N27B3D"></a><h2>Description</h2><p>The <span class="guilabel">AcceptFilter</span> object is used to create
    criteria for the inclusion of subsets of the available data in the
    estimation process based on observation frequency, tracker, measurement
    type, record number, or time. Instances of
    <span class="guilabel">AcceptFilter</span> are specified for use on the
    <span class="guilabel">DataFilters</span> field of a
    <span class="guibutton">TrackingFileSet</span> or
    <span class="guilabel">BatchEstimator</span> object.</p><p>GMAT implements two levels of data editing for estimation.
    First-level editing criteria are specified on the
    <span class="guilabel">DataFilters</span> field of the
    <span class="guilabel">TrackingFileSet</span> instance. At this level, the user may
    choose what data is admitted into the overall pool of observations
    provided to the estimator. Any data excluded at the tracking file set
    level will be immediately discarded and not available to the estimation
    process.</p><p>Second-level data editing is specified on the
    <span class="guilabel">DataFilters</span> field of the
    <span class="guilabel">BatchEstimator</span> instance. At this level, the user may
    choose what data is used in the estimation state update. Residuals will be
    computed for any observations admitted through first-level editing, but
    any data excluded at the estimator level will be flagged as user edited,
    and will not affect the computation of the state correction. This allows
    the user to evaluate the quality of untrusted data against a solution
    computed using a trusted set of measurements.</p><p>A single <span class="guilabel">AcceptFilter</span> may employ multiple
    selection criteria (for example simultaneously thinning different stations
    or data types by differing intervals). Multiple criteria on a single
    filter are considered in an AND sense. When multiple criteria are
    specified on a single filter, an observation must meet all specified
    criteria to be accepted.</p><p>Multiple <span class="guilabel">AcceptFilters</span> with different selection
    criteria may be specified on a single <span class="guilabel">TrackingFileSet</span>
    or <span class="guilabel">BatchEstimator</span>. When multiple filters are
    specified, these act in an OR sense. Data meeting criteria for any of the
    specified filters will be accepted.</p><p>See Also <a class="xref" href="RejectFilter.html" title="RejectFilter"><span class="refentrytitle">RejectFilter</span></a>, <a class="xref" href="TrackingFileSet.html" title="TrackingFileSet"><span class="refentrytitle">TrackingFileSet</span></a>, <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a></p></div><div class="refsection"><a name="N27B7B"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">DataTypes</span></td><td><p>List of data types</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>A set of any supported GMAT measurement types, or
                    'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">{All}</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EpochFormat</span></td><td><p>Allows user to select format of the epoch</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>UTCGregorian, UTCModJulian, TAIGregorian,
                    TAIModJulian, TTGregorian, TTModJulian A1Gregorian,
                    A1ModJulian, TDBGregorian, TDBModJulian</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">TAIModJulian</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FileNames</span></td><td><p>List of file names (a subset of the relevant
            <span class="guilabel">TrackingFileSet's</span>
            <span class="guilabel">FileName</span> field) containing the tracking data.
            If this field equals From_AddTrackingConfig, then two things
            happen; (1) All of the files in the relevant
            <span class="guilabel">TrackingFileSet</span> are used as a starting point,
            and (2) Of the data in all of the files, only the data defined by
            the <span class="guilabel">AddTrackingConfig</span> field of the relevant
            <span class="guilabel">TrackingFileSet</span> are used. This field is only
            applicable when the <span class="guilabel">AcceptFilter</span> is used on a
            <span class="guilabel">TrackingFileSet</span>.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>StringArray</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>valid file name, 'All', or
                    'From_AddTrackingConfig'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">{All}</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FinalEpoch</span></td><td><p>Final epoch of desired data to process</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid epoch</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">latest day defined in GMAT</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">InitialEpoch</span></td><td><p>Initial epoch of desired data to process</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid epoch</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">earliest day defined in GMAT</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ObservedObjects</span></td><td><p>List of user-created tracked objects (e.g., name of
            the <span class="guilabel">Spacecraft</span> resource being tracked)</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Object Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>User defined observed object or 'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">{All}</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RecordNumbers</span></td><td><p>A list of one or more single record numbers or spans
            of record numbers to accept. Observation record numbers are
            reported in the GMAT estimator output file. This field is only
            applicable when the <span class="guilabel">AcceptFilter</span> is used on
            the estimator level.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integers or spans of integers (see examples)</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">{All}</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThinMode</span></td><td><p>'Frequency' for record count frequency mode and
            'Time' for time interval mode. This field is only applicable when
            the <span class="guilabel">AcceptFilter</span> is used on a
            <span class="guilabel">TrackingFileSet</span>.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>'Frequency' or 'Time'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Frequency</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ThinningFrequency</span></td><td><p>If <span class="guilabel">ThinMode</span> is Frequency, the
            integer 'n' is used to specify that every nth data point should be
            accepted. For example, 3 specifies that every third data point,
            meeting all the accept criteria, should be accepted and 1
            specifies that every data point, meeting all the accept criteria,
            should be accepted. If <span class="guilabel">ThinMode</span> is Time, the
            integer 'n' is a number of seconds between accepted observations,
            using the first available observation as the anchor epoch. For
            example, a value of 300 means that observations will be accepted
            every 300 seconds, starting from the first available observation.
            This field is only applicable when the
            <span class="guilabel">AcceptFilter</span> is used on a
            <span class="guilabel">TrackingFileSet</span>.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Positive Integer</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">1</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Depends on <span class="guilabel">ThinMode</span>
                    value</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Trackers</span></td><td><p>List of user-created trackers (e.g., name of the
            <span class="guilabel">GroundStation</span> resource being used) </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Object Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid user-created Tracker object (e.g.,
                    <span class="guilabel">GroundStation</span>) or 'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">{All}</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N27DA6"></a><h2>Remarks</h2><p>Some fields of <span class="guilabel">AcceptFilter</span> are not applicable
    at either the first-level (tracking file set) or second-level (estimator)
    editing stages. The <span class="guilabel">RecordNumbers</span> field has no
    functionality when applied to an accept filter at the tracking file set
    level. The <span class="guilabel">FileNames</span>,
    <span class="guilabel">ThinningFrequency</span>, and <span class="guilabel">ThinMode</span>
    fields have no functionality when applied to an accept filter at the
    estimator level.</p><p>Use of combinations of instances of
    <span class="guilabel">AcceptFilter</span> and <span class="guilabel">RejectFilter</span> at
    both levels is permitted.</p></div><div class="refsection"><a name="N27DC2"></a><h2>Examples</h2><div class="refsection"><a name="N27DC5"></a><h3>First-level (TrackingFileSet) Data Editing</h3><p>The following examples illustrate use of an
      <span class="guilabel">AcceptFilter</span> for first-level data editing. At this
      level, the <span class="guilabel">AcceptFilter</span> instance should be assigned
      to the <span class="guilabel">DataFilters</span> field of a
      <span class="guilabel">TrackingFileSet</span>. In these examples, only data
      meeting the criteria specified by the accept filter will be admitted
      through. All other data is immediately discarded.</p><div class="informalexample"><p>This example shows how to create an
        <span class="guilabel">AcceptFilter</span> to sample the data at a frequency of
        1:10 (thinning the data to one tenth of its volume).</p><pre class="programlisting">Create AcceptFilter af;
  
af.ThinningFrequency = 10;

Create TrackingFileSet estData;

estData.DataFilters = {af};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The next example will accept all data from station
        <span class="guilabel">GDS</span> and accept every 5th observation from station
        <span class="guilabel">CAN</span>. Only data from stations
        <span class="guilabel">GDS</span> and <span class="guilabel">CAN</span> will be
        accepted.</p><pre class="programlisting">Create AcceptFilter af1;
Create AcceptFilter af2;
 
Create GroundStation GDS CAN;

af1.Trackers          = {'GDS'}; 
af2.Trackers          = {'CAN'};
af2.ThinningFrequency = 5;
 
Create TrackingFileSet estData;
 
estData.DataFilters = {af1, af2};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The last example illustrates thinning data by time interval,
        using a 300-second thinning interval.</p><pre class="programlisting">Create AcceptFilter saf;
 
af.ThinMode          = 'Time'; 
af.ThinningFrequency = 300;
 
Create TrackingFileSet estData;
 
estData.DataFilters = {af};

BeginMissionSequence;</pre></div></div><div class="refsection"><a name="N27DF4"></a><h3>Second-level (estimator) Data Editing</h3><p>The following examples illustrate use of an
      <span class="guilabel">AcceptFilter</span> for second-level data editing. At this
      level, the <span class="guilabel">AcceptFilter</span> instance should be assigned
      to the <span class="guilabel">DataFilters</span> field of a
      <span class="guilabel">BatchEstimator</span>. In these examples, only data
      meeting the criteria specified by the accept filter will be used in the
      estimation state update. Residuals will be computed for all available
      data (all data admitted at the first level), but data not accepted at
      the estimator level will be flagged as user edited.</p><div class="informalexample"><p>This example shows how to create an
        <span class="guilabel">AcceptFilter</span> to accept specific data records by
        record number.</p><pre class="programlisting">Create AcceptFilter af;
  
af.RecordNumbers = {10, 11, 20-150, 155-300};

Create BatchEstimator bls;

bls.DataFilters = {af};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The next example will accept only range data from station
        <span class="guilabel">MAD</span> over the time span 10 Jun 2012 02:56 to
        13:59.</p><pre class="programlisting">Create AcceptFilter af;
Create GroundStation MAD;

af.Trackers     = {'MAD'};
af.DataTypes    = {'Range'};
af.EpochFormat  = UTCGregorian;
af.InitialEpoch = '10 Jun 2012 02:56:00.000';
af.FinalEpoch   = '10 Jun 2012 13:59:00.000';

Create BatchEstimator bls;

bls.DataFilters = {af};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The last example illustrates accepting all data from station
        <span class="guilabel">MAD</span> and only range data from station
        <span class="guilabel">CAN</span>.</p><pre class="programlisting">Create AcceptFilter af1 af2;
Create GroundStation MAD CAN;
 
af1.Trackers         = {'MAD'}; 
af2.Trackers         = {'CAN'};
af2.DataTypes        = {'Range'};
 
Create BatchEstimator bls;
 
bls.DataFilters = {af1, af2};

BeginMissionSequence;</pre></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch21.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Antenna.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;21.&nbsp;Orbit Determination&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Antenna</td></tr></table></div></body></html>