<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Appendix B. Run the script from the command-line</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="FilterSmoother_GpsPosVec.html" title="Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data"><link rel="prev" href="ch15s10.html" title="Appendix A. Generate an ephemeris while running the filter and smoother"><link rel="next" href="ch15s12.html" title="Appendix C. Check covariance matrix conditioning"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Appendix B. Run the script from the command-line</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch15s10.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;15.&nbsp;Filter and Smoother Orbit Determination using GPS_PosVec Data</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch15s12.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N155E8"></a>Appendix B. Run the script from the command-line</h2></div></div></div><p>In this tutorial, we&rsquo;ve demonstrated configuring and running a GMAT
    filter/smoother script from the GMAT application GUI. In mission
    operations, it is not desirable to have to execute such point-and-click
    operations regularly, and automated execution is preferred. Here we
    demonstrate how to run our filter script from the command-line instead of
    using the GUI.</p><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Open a command-line prompt on your platform and navigate to the
        directory containing the GMAT executable.</p></li><li class="listitem"><p>In that directory, type the following:</p><p>a. If you are running the Mac or Linux version, type <span class="bold"><strong>GmatConsole</strong></span> instead of <span class="bold"><strong>gmat.exe</strong></span>.</p></li></ol></div><pre class="programlisting">gmat.exe --exit --run <span class="emphasis"><em>&lt;full path to filter.script&gt;</em></span>
</pre><p>In a Windows/DOS environment, this will cause GMAT to start the GUI,
    load and run the script, and then exit the GUI. The GmatConsole command
    (not currently supported on Windows), runs the script without the GUI at
    all. On the Windows platform, you can suppress the GUI by adding the
    <code class="literal">--minimize</code> option to the command-line. There are a
    number of other command-line options. You can type <code class="literal">gmat.exe
    --help</code> or look in the User&rsquo;s Guide under <a class="xref" href="CommandLine.html" title="Command-Line Usage"><span class="refentrytitle">Command-Line Usage</span></a>.</p><p>When the script completes, you can examine the GMAT output directory
    to confirm that all the same files generated when we ran the script inside
    the GUI were generated from the command-line run.</p><p>You can of course run GMAT in this fashion from any directory by
    specifying the full path to the GMAT executable. Note however, that in all
    instances it is necessary to specify the full path to the script, even if
    the script is in the directory from which you are calling GMAT. GMAT
    treats all relative paths as relative to its own <span class="bold"><strong>bin/</strong></span> directory.</p></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch15s10.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="FilterSmoother_GpsPosVec.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch15s12.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix A. Generate an ephemeris while running the filter and
    smoother&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Appendix C. Check covariance matrix conditioning</td></tr></table></div></body></html>