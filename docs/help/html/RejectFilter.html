<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>RejectFilter</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="Receiver.html" title="Receiver"><link rel="next" href="Simulator.html" title="Simulator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">RejectFilter</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Receiver.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Simulator.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="RejectFilter"></a><div class="titlepage"></div><a name="N2972B" class="indexterm"></a><a name="N2972E" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">RejectFilter</span></h2><p>RejectFilter &mdash; Allows selection of data subsets for processing by the batch
    least squares estimator.</p></div><div class="refsection"><a name="N29741"></a><h2>Description</h2><p>The <span class="guilabel">RejectFilter</span> object is used to create
    criteria for the exclusion of subsets of the available data in the
    estimation process based on tracker, observed object, measurement type, or
    time. Instances of <span class="guilabel">RejectFilter</span> are specified for use
    on the <span class="guilabel">DataFilters</span> field of a
    <span class="guilabel">TrackingFileSet</span> or
    <span class="guilabel">BatchEstimator</span> object.</p><p>GMAT implements two levels of data editing for estimation.
    First-level editing criteria are specified on the
    <span class="guilabel">DataFilters</span> field of the
    <span class="guilabel">TrackingFileSet</span> instance. At this level, the user may
    choose what data is admitted into the overall pool of observations
    provided to the estimator. Any data excluded at the tracking file set
    level will be immediately discarded and not available to the estimation
    process.</p><p>Second-level data editing is specified on the
    <span class="guilabel">DataFilters</span> field of the
    <span class="guilabel">BatchEstimator</span> instance. At this level, the user may
    choose what data is used in the estimation state update. Residuals will be
    computed for any observations admitted through first-level editing, but
    any data excluded at the estimator level will be flagged as user edited,
    and will not affect the computation of the state correction. This allows
    the user to evaluate the quality of untrusted data against a solution
    computed using a trusted set of measurements.</p><p>A single reject filter may employ multiple selection criteria (for
    example simultaneous thinning by time and tracker). Multiple criteria on a
    single filter are considered in an AND sense. When multiple criteria are
    specified in a single filter, an observation must meet all specified
    criteria to be rejected. Multiple filters with different selection
    criteria may be specified on a single <span class="guilabel">TrackingFileSet</span>
    or <span class="guilabel">BatchEstimator</span>. When multiple filters are
    specified, these act in an OR sense. Data meeting criteria for any of the
    specified filters will be rejected.</p><p>See Also <a class="xref" href="AcceptFilter.html" title="AcceptFilter"><span class="refentrytitle">AcceptFilter</span></a>, <a class="xref" href="TrackingFileSet.html" title="TrackingFileSet"><span class="refentrytitle">TrackingFileSet</span></a>, <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a></p></div><div class="refsection"><a name="N29777"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">DataTypes</span></td><td><p>List of data types</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>A set of any supported GMAT measurement types, or
                    'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{All}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">EpochFormat</span></td><td><p>Allows user to select format of the
            epoch</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>UTCGregorian, UTCModJulian, TAIGregorian,
                    TAIModJulian, TTGregorian, TTModJulian A1Gregorian,
                    A1ModJulian, TDBGregorian, TDBModJulian</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">TAIModJulian</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FileNames</span></td><td><p>List of file names (a subset of the relevant
            <span class="guilabel">TrackingFileSet's</span>
            <span class="guilabel">FileName</span> field) containing the tracking data,
            to be excluded from processing. This field is only applicable when
            the <span class="guilabel">RejectFilter</span> is used on a
            <span class="guilabel">TrackingFileSet</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>StringArray</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>valid file name or 'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{All}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FinalEpoch</span></td><td><p>Final epoch of desired data to
            process.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid epoch</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">latest day defined in
                    GMAT</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">InitialEpoch</span></td><td><p>Initial epoch of desired data to
            process.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid epoch</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">earliest day defined in
                    GMAT</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ObservedObjects</span></td><td><p>List of user-created tracked objects (e.g., name of
            the <span class="guilabel">Spacecraft</span> resource being
            tracked).</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Object Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>User defined observed object or 'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{All}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RecordNumbers</span></td><td><p>A list of one or more single record numbers or spans
            of record numbers to reject. Observation record numbers are
            reported in the GMAT estimator output file. This field is only
            applicable when the <span class="guilabel">RejectFilter</span> is used on
            the estimator level.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integers or spans of integers (see examples)</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Trackers</span></td><td><p>List of user-created trackers (e.g., name of the
            <span class="guilabel">GroundStation</span> resource being
            used).</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Object Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any valid user-created Tracker object (e.g.,
                    <span class="guilabel">GroundStation</span>) or 'All'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">{All}</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2990C"></a><h2>Remarks</h2><p>Some fields of <span class="guilabel">RejectFilter</span> are not applicable
    at either the first-level (tracking file set) or second-level (estimator)
    editing stages. The <span class="guilabel">RecordNumbers</span> field has no
    functionality when applied to a reject filter at the tracking file set
    level. The <span class="guilabel">FileNames</span> field has no functionality when
    applied to a reject filter at the estimator level.</p><p>Use of combinations of instances <span class="guilabel">AcceptFilter</span>
    and <span class="guilabel">RejectFilter</span> at both levels is permitted.</p></div><div class="refsection"><a name="N29922"></a><h2>Examples</h2><div class="refsection"><a name="N29925"></a><h3>First-level (TrackingFileSet) Data Editing</h3><p>The following examples illustrate use of a
      <span class="guilabel">RejectFilter</span> for first-level data editing. At this
      level, the <span class="guilabel">RejectFilter</span> instance should be assigned
      to the <span class="guilabel">DataFilters</span> field of a
      <span class="guilabel">TrackingFileSet</span>. In these examples, data meeting
      the criteria specified by the reject filter will be immediately
      discarded. All other data is admitted.</p><div class="informalexample"><p>This example shows how to create a
        <span class="guilabel">RejectFilter</span> to reject all observations from
        station <span class="guilabel">GDS</span>.</p><pre class="programlisting">Create GroundStation GDS;
Create RejectFilter rf;

rf.Trackers = {'GDS'};
 
Create TrackingFileSet estData;
 
estData.DataFilters = {rf};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The next example will reject all DSN Doppler (i.e., DSN_TCP)
        tracking measurements from station <span class="guilabel">GDS</span>, and all
        tracking of any type from station <span class="guilabel">CAN</span>. All other
        tracking measurements will be accepted.</p><pre class="programlisting">Create GroundStation GDS CAN;

Create RejectFilter rf1;
Create RejectFilter rf2;
 
rf1.Trackers  = {'GDS'}; 
rf1.DataTypes = {'DSN_TCP'};
rf2.Trackers  = {'CAN'};
 
Create TrackingFileSet estData;
 
estData.DataFilters = {rf1, rf2};

BeginMissionSequence;</pre></div></div><div class="refsection"><a name="N2994C"></a><h3>Second-level (estimator) Data Editing</h3><p>The following examples illustrate use of a
      <span class="guilabel">RejectFilter</span> for second-level data editing. At this
      level, the <span class="guilabel">RejectFilter</span> instance should be assigned
      to the <span class="guilabel">DataFilters</span> field of a
      <span class="guilabel">BatchEstimator</span>. In these examples, data meeting the
      criteria specified by the reject filter will excluded from the
      estimation state update. Residuals will be computed for all available
      data (all data admitted at the first level), but data rejected at the
      estimator level will be flagged as user edited.</p><div class="informalexample"><p>This example shows how to create a
        <span class="guilabel">RejectFilter</span> to reject specific observations by
        record number.</p><pre class="programlisting">Create RejectFilter rf;

rf.RecordNumbers = {13, 25, 75-87};
 
Create BatchEstimator bls;
 
bls.DataFilters = {rf};

BeginMissionSequence;</pre></div><div class="informalexample"><p>The next example shows how to simultaneously employ multiple
        reject filters. In this example:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>MAD range data over the span 10 Jun 2012 02:56 to 13:59 is
            rejected</p></li><li class="listitem"><p>All CAN DSN_TCP data is rejected</p></li><li class="listitem"><p>All RangeRate data (from any station) is rejected</p></li></ul></div><pre class="programlisting">Create RejectFilter rf1 rf2 rf3;
Create GroundStation MAD CAN;

rf1.Trackers     = {'MAD'};
rf1.DataTypes    = {'Range'};
rf1.EpochFormat  = UTCGregorian;
rf1.InitialEpoch = '10 Jun 2012 02:56:00.000';
rf1.FinalEpoch   = '10 Jun 2012 13:59:00.000';

rf2.Trackers     = {'CAN'};
rf2.DataTypes    = {'DSN_TCP'};

rf3.DataTypes    = {'RangeRate'};

Create BatchEstimator bls;

bls.DataFilters = {rf1, rf2, rf3};

BeginMissionSequence;</pre></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Receiver.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Simulator.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Receiver&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Simulator</td></tr></table></div></body></html>