<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>IntrusionLocator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="ImpulsiveBurn.html" title="ImpulsiveBurn"><link rel="next" href="LibrationPoint.html" title="LibrationPoint"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">IntrusionLocator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ImpulsiveBurn.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="LibrationPoint.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="IntrusionLocator"></a><div class="titlepage"></div><a name="N1B6BC" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">IntrusionLocator</span></h2><p>IntrusionLocator &mdash; A line-of-sight event locator between a target
    <span class="guilabel">CelestialBody</span> and an observer
    <span class="guilabel">Spacecraft</span></p></div><div class="refsection"><a name="N1B6D2"></a><h2>Description</h2><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p><span class="guilabel">IntrusionLocator</span> is a SPICE-based subsystem
      that uses a parallel configuration for the solar system and celestial
      bodies from other GMAT components. For precision applications, care must
      be taken to ensure that both configurations are consistent. See <a class="link" href="IntrusionLocator.html#IntrusionLocator_DataConfiguration" title="Data configuration">Remarks</a> for
      details.</p></div><p>An <span class="guilabel">IntrusionLocator</span> is an event locator used to
    find line-of-sight events between a <span class="guilabel">Spacecraft</span> and a
    <span class="guilabel">CelestialBody</span> based on the field of view of an
    <span class="guilabel">Imager</span> attached to the Spacecraft. By default, a
    <span class="guilabel">IntrusionLocator</span> generates a text event report
    listing the beginning and ending times of each line-of-sight event, with
    intermediate times generated at the selected step size. Each piece of data
    also contains the coordinates of the target within the user selected
    coordinate system, the angular diameter, and the illumination of the
    target. Intrusion location can be performed over the entire propagation
    interval or over a subinterval, and can optionally adjust for light-time
    delay and stellar aberration. Intrusion location can be configured to
    search for times of occultation of other
    <span class="guilabel">CelestialBody</span> resources that may block line of sight,
    and can limit intrusion events to a specified minimum illumination for the
    targets.</p><p>Intrusion location can be performed between one <span class="guilabel">Spacecraft
    Imager</span> (<span class="guilabel">Observer</span>) and any number of
    <span class="guilabel">CelestialBody</span> resources
    (<span class="guilabel">Targets</span>). Each target-observer pair is searched
    individually, and results in a separate segment of the resulting report.
    All pairs must use the same interval and search options; to customize the
    options per pair, use multiple <span class="guilabel">IntrusionLocator</span>
    resources.</p><p>Third-body occultation searches can be included by listing a
    <span class="guilabel">CelestialBody</span> resource in the
    <span class="guilabel">CentralBody</span> field. Any configured
    <span class="guilabel">CelestialBody</span> can be used as an occulting body,
    including user-defined bodies.</p><p>By default, the <span class="guilabel">IntrusionLocator</span> searches the
    entire interval of propagation of the
    <span class="guilabel">IntrudingBodies</span>, after applying endpoint light-time
    adjustments; see <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchInterval" title="Search interval">Remarks</a> for details. To
    search a custom interval, set <span class="guilabel">UseEntireInterval</span> to
    <code class="literal">False</code> and set <span class="guilabel">InitialEpoch</span> and
    <span class="guilabel">FinalEpoch</span> accordingly. Note that these epochs are
    assumed to be at the observer, and so must be valid when translated to the
    target via light-time delay and stellar aberration, if configured. If they
    fall outside the propagation interval of the
    <span class="guilabel">IntrudingBodies</span>, GMAT will display an error.</p><p>The intrusion locator can optionally adjust for both light-time
    delay and stellar aberration, using a signal moving in the receive
    direction (<span class="guilabel">Target to Observer</span>). The light-time
    direction affects the valid search interval by limiting searches near the
    end of the interval. See <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchInterval" title="Search interval">Remarks</a> for details.
    Stellar aberration is only applied for the line-of-sight portion of the
    search; it has no effect during occultation searches.</p><p>The event search is performed at a fixed step through the interval.
    You can control the step size (in seconds) by setting the
    <span class="guilabel">StepSize</span> field. An appropriate choice for step size
    is no greater than half the period of the line-of-sight function&mdash;that is,
    half the orbit period for an elliptical orbit. See <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchAlgorithm" title="Search algorithm">Remarks</a> for
    details.</p><p>GMAT uses the SPICE library for the fundamental event location
    algorithm. As such, all celestial body data is loaded from SPICE kernels
    for this subsystem, rather than GMAT's own
    <span class="guilabel">CelestialBody</span> shape and orientation configuration.
    See <a class="link" href="IntrusionLocator.html#IntrusionLocator_Remarks" title="Remarks">Remarks</a> for
    details.</p><p>Unless otherwise mentioned, <span class="guilabel">IntrusionLocator</span>
    fields cannot be set in the mission sequence.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="CelestialBody.html" title="CelestialBody"><span class="refentrytitle">CelestialBody</span></a>, <a class="xref" href="Imager.html" title="Imager"><span class="refentrytitle">Imager</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a></p></div><div class="refsection"><a name="N1B756"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">CentralBody</span></td><td><p>Name of a celestial body that will be checked for
            intervening between the <span class="guilabel">Imager</span> and the
            targets. When this occurs, the target does not appear as intruding
            in the sensor intrusion report.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>A <span class="guilabel">CelestialBody</span> resource (e.g.
                    <span class="guilabel">Planet</span>,
                    <span class="guilabel">Asteroid</span>, <span class="guilabel">Moon</span>,
                    etc.)</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any existing <span class="guilabel">CelestialBody</span>
                    resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Earth</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Filename</span></td><td><p>Name and path of the intrusion report file. This
            field can be set in the mission sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file path</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">'<em class="replaceable"><code>IntrusionLocator</code></em>.txt'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">FinalEpoch</span></td><td><p>Last epoch to search for intrusions, in the format
            specified by <span class="guilabel">InputEpochFormat</span>. The epoch must
            map to a valid epoch in the <span class="guilabel">Spacecraft</span> and
            target <span class="guilabel">Intruding Body</span> ephemeris interval,
            including any light time. This field can be set in the mission
            sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid epoch in available spacecraft ephemeris</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">'21545.138'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>ModifiedJulian epoch formats: days</p><p>Gregorian epoch formats: N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">InitialEpoch</span></td><td><p>First epoch to search for intrusions, in the format
            specified by <span class="guilabel">InputEpochFormat</span>. The epoch must
            map to a valid epoch in the <span class="guilabel">Spacecraft</span> and
            target <span class="guilabel">Intruding Body</span> ephemeris interval,
            including any light time. This field can be set in the mission
            sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid epoch in available spacecraft ephemeris</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">'21545'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>ModifiedJulian epoch formats: days</p><p>Gregorian epoch formats: N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IntrudingBodies</span></td><td><p>List of <span class="guilabel">CelestialBody</span> resources
            that are used as targets for the observers listed in the field
            <span class="guilabel">Sensors</span>. Events where these bodies are seen
            in the field of view of the <span class="guilabel">Sensors</span> will be
            tracked in the output file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>A <span class="guilabel">CelestialBody</span> resource list
                    (e.g. <span class="guilabel">Planet</span>,
                    <span class="guilabel">Asteroid</span>, <span class="guilabel">Moon</span>,
                    etc.)</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any existing <span class="guilabel">CelestialBody</span>
                    resources</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MinimumPhase</span></td><td><p>Value to set the minimum amount of illumination a
            target must have to be considered intruding when in the field of
            view of the <span class="guilabel">Sensors</span>. Note that for targets
            that are stars, they always have an illumination value of
            1.0.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>0.0 &lt; <span class="guilabel">MinimumPhase</span> &lt;
                    1.0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">'0.0'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ReportCoordinates</span></td><td><p>String to set what coordinate format the intruding
            bodies are reported in during an intrusion event. The
            SpacecraftBody coordinates use the body frame from the
            <span class="guilabel">Spacecraft</span> field. FixedGrid uses the
            coordinate frame provided from the
            <span class="guilabel">SpiceGridFrameFile</span> field.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>SpacecraftBody, FixedGrid</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">Spacecraft</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">RunMode</span></td><td><p>Mode of event location execution.
            <code class="literal">'Automatic'</code> triggers event location to occur
            automatically at the end of the run. <code class="literal">'Manual'</code>
            limits execution only to the <span class="guilabel">FindEvents</span>
            command. <code class="literal">'Disabled'</code> turns off event location
            entirely.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><code class="literal">Automatic</code>,
                    <code class="literal">Manual</code>,
                    <code class="literal">Disabled</code></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">'Automatic'</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Sensors</span></td><td><p>List of <span class="guilabel">Imager</span> resources to be
            used in checking for intrusions.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>One or more <span class="guilabel">Imager</span>
                    resources</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any existing <span class="guilabel">Imager</span> resource
                    attached to the <span class="guilabel">Spacecraft</span> entered in
                    the <span class="guilabel">Spacecraft</span> field</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Spacecraft</span></td><td><p>Name of a <span class="guilabel">Spacecraft</span> that has
            the desired <span class="guilabel">Imager</span> resources attached to it
            to be used as observers.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>A <span class="guilabel">Spacecraft</span> resource</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any existing <span class="guilabel">Spacecraft</span>
                    resource with at least one <span class="guilabel">Imager</span>
                    resource attached to it</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SpiceGridFrameFile</span></td><td><p>The frame kernel files used when a FixedGrid
            coordinate frame is selected in the
            <span class="guilabel">ReportCoordinates</span> field.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file path and name</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><code class="filename">N/A</code></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">StepSize</span></td><td><p>Time step used in the event locator. See <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchAlgorithm" title="Search algorithm">Remarks</a> for
            discussion of appropriate values.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">StepSize</span> &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>10</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>s</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseEntireInterval</span></td><td><p>Search the entire available ephemeris interval, after
            adjusting the end-points for light-time delay as appropriate. See
            <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchInterval" title="Search interval">Remarks</a> for
            details. This field can be set in the mission
            sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>true</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseLightTimeDelay</span></td><td><p>Use light-time delay in the event-finding algorithm.
            The clock is always hosted on the
            <span class="guibutton">Observer</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>true</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UseStellarAberration</span></td><td><p>Use stellar aberration in addition to light-time
            delay in the event-finding algorithm. Light-time delay must be
            enabled. Stellar aberration only affects line-of-sight searches,
            not occultation searches.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>true</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">WriteReport</span></td><td><p>Write an event report when event location is
            executed. This field can be set in the mission
            sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true, false</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>true</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1BAC2"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_IntrusionLocator_1.png" align="middle" height="360"></td></tr></table></div></div><p>The default <span class="guilabel">IntrusionLocator</span> GUI for a new
    resource is shown above. One <span class="guilabel">Spacecraft</span> can be
    selected from the <span class="guilabel">Spacecraft</span> drop-down menu, which is
    populated by all the currently configured <span class="guilabel">Spacecraft</span>
    in the mission. Next, the <span class="guilabel">Central Body</span> is selected
    from the drop-down menu populated with all the currently loaded
    <span class="guilabel">CelestialBody</span> resources.</p><p>The <span class="guilabel">Intruding Bodies</span> checklist is used to set
    what <span class="guilabel">CelestialBody</span> the sensors will check for
    intrusion during the mission sequecne. Multiple selections can be made.
    The following <span class="guilabel">Sensors</span> checklist is used to select
    which sensors to utilize for this <span class="guilabel">IntrusionLocator</span>.
    Note that these sensore will also need to be attached to the selected
    <span class="guilabel">Spacecraft's</span> hardware list.</p><p>You can configure the output via <span class="guilabel">Filename</span>,
    <span class="guilabel">Run Mode</span>, and <span class="guilabel">Write Report</span> near
    the bottom. If <span class="guilabel">Write Report</span> is enabled, a text report
    will be written to the file specified in <span class="guilabel">Filename</span>.
    The search will execute during <span class="guilabel">FindEvents</span> commands
    (for <span class="guilabel">Manual</span> or <span class="guilabel">Automatic</span> modes)
    and automatically at the end of the mission (for
    <span class="guilabel">Automatic</span> mode), depending on the <span class="guilabel">Run
    Mode</span>.</p><p>You can configure the search interval via the options in the upper
    right. Uncheck <span class="guilabel">Use Entire Interval</span> to set the search
    interval manually. See the <a class="link" href="IntrusionLocator.html#IntrusionLocator_SearchInterval" title="Search interval">Remarks</a> section for
    considerations when setting the search interval.</p><p>You can control the search algorithm via the options in the bottom
    right. Configure light-time and stellar aberration via the check boxes
    next to each.</p><p>To control the fidelity and execution time of the search, set the
    <span class="guilabel">Step size</span> appropriately. See the <a class="link" href="IntrusionLocator.html#IntrusionLocator_Remarks" title="Remarks">Remarks</a> section for
    details.</p><p>To set the minimum phase required to count an object as intruding,
    set the <span class="guilabel">Minimum Phase</span> appropriately.</p><p>Use the <span class="guilabel">Report Coordinates</span> drop-down menu to
    select the coordinates used in the ouput intrusion report. If
    <span class="guilabel">FixedGrid</span> is selected, the <span class="guilabel">Spice Grid Frame
    File</span> becomes available to set the frame kernel to be used. This
    is set by directly entering the full path or using the browse button next
    to the field.</p></div><div class="refsection"><a name="IntrusionLocator_Remarks"></a><h2>Remarks</h2><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>The <span class="guilabel">CustomFOV</span> shape is currently not
      supported by the <span class="guilabel">IntrusionLocator</span>.</p></div><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>The spacecraft body frame is currently not a supported coordinate
      system for the <span class="guilabel">ReportCoordinates</span> field.</p></div><div class="refsection"><a name="IntrusionLocator_DataConfiguration"></a><h3>Data configuration</h3><p>The <span class="guilabel">IntrusionLocator</span> implementation is based
      on the <a class="link" href="http://naif.jpl.nasa.gov/naif/" target="_top">NAIF SPICE
      toolkit</a>, which uses a different mechanism for environmental data
      such as celestial body shape and orientation, planetary ephemerides,
      body-specific frame definitions, and leap seconds. Therefore, it is
      necessary to maintain two parallel configurations to ensure that the
      event location results are consistent with GMAT's own propagation and
      other parameters. The specific data to be maintained is:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Planetary shape and orientation:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>GMAT core:
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">EquatorialRadius</span>,
              <span class="guilabel">Flattening</span>,
              <span class="guilabel">SpinAxisRAConstant</span>,
              <span class="guilabel">SpinAxisRARate</span>, etc.</p></li><li class="listitem"><p>IntrusionLocator:
              <span class="guilabel">SolarSystem</span>.<span class="guilabel">PCKFilename</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">PlanetarySpiceKernelName</span></p></li></ul></div></li><li class="listitem"><p>Planetary ephemeris:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>GMAT core:
              <span class="guilabel">SolarSystem</span>.<span class="guilabel">DEFilename</span>,
              or
              (<span class="guilabel">SolarSystem</span>.<span class="guilabel">SPKFilename</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">OrbitSpiceKernelName</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">NAIFId</span>)</p></li><li class="listitem"><p>IntrusionLocator:
              <span class="guilabel">SolarSystem</span>.<span class="guilabel">SPKFilename</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">OrbitSpiceKernelName</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">NAIFId</span></p></li></ul></div></li><li class="listitem"><p>Body-fixed frame:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>GMAT core: built-in</p></li><li class="listitem"><p>IntrusionLocator:
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">SpiceFrameId</span>,
              <span class="guilabel">CelestialBody</span>.<span class="guilabel">FrameSpiceKernelName</span></p></li></ul></div></li><li class="listitem"><p>Leap seconds:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>GMAT core: startup file <code class="literal">LEAP_SECS_FILE</code>
              setting</p></li><li class="listitem"><p>IntrusionLocator:
              <span class="guilabel">SolarSystem</span>.<span class="guilabel">LSKFilename</span></p></li></ul></div></li></ul></div><p>See SolarSystem and <a class="link" href="CelestialBody.html#CelestialBody_ConfiguringForEventLocation" title="Configuring for event location">CelestialBody</a>
      for more details.</p></div><div class="refsection"><a name="IntrusionLocator_SearchInterval"></a><h3>Search interval</h3><p>The <span class="guilabel">IntrusionLocator</span> search interval can be
      specified either as the entire ephemeris interval of the
      <span class="guilabel">Spacecraft</span>, or as a user-defined interval. If
      <span class="guilabel">UseEntireInterval</span> is true, the search is performed
      over the entire ephemeris interval of the
      <span class="guilabel">Spacecraft</span>, including any gaps or discontinuities.
      If <span class="guilabel">UseEntireInterval</span> is false, the provided
      <span class="guilabel">InitialEpoch</span> and <span class="guilabel">FinalEpoch</span>
      are used to form the search interval directly. The user must ensure than
      the provided interval results in valid <span class="guilabel">Spacecraft</span>
      and <span class="guilabel">CelestialBody</span> ephemeris epochs.</p></div><div class="refsection"><a name="IntrusionLocator_RunModes"></a><h3>Run modes</h3><p>The <span class="guilabel">IntrusionLocator</span> works in conjunction
      with the <span class="guilabel">FindEvents</span> command: the
      <span class="guilabel">IntrusionLocator</span> resource defines the configuration
      of the event search, and the <span class="guilabel">FindEvents</span> command
      executes the search at a specific point in the mission sequence. The
      mode of interaction is defined by
      <span class="guilabel">ContactLocator</span>.<span class="guilabel">RunMode</span>, which
      has three options:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><code class="literal">Automatic</code>: All
            <span class="guilabel">FindEvents</span> commands are executed as-is, plus
            an additional <span class="guilabel">FindEvents</span> is executed
            automatically at the end of the mission sequence.</p></li><li class="listitem"><p><code class="literal">Manual</code>: All
            <span class="guilabel">FindEvents</span> commands are executed
            as-is.</p></li><li class="listitem"><p><code class="literal">Disabled</code>: <span class="guilabel">FindEvents</span>
            commands are ignored.</p></li></ul></div></div><div class="refsection"><a name="IntrusionLocator_SearchAlgorithm"></a><h3>Search algorithm</h3><p>The <span class="guilabel">IntrusionLocator</span> uses the NAIF SPICE GF
      (geometry finder) subsystem to perform event location. Specifically, the
      following call is used for the search:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><code class="literal"><a class="link" href="https://naif.jpl.nasa.gov/pub/naif/toolkit_docs/C/cspice/gftfov_c.html" target="_top">gftfov_c</a></code>:
            For intrusion event searches</p></li><li class="listitem"><p><code class="literal"><a class="link" href="http://naif.jpl.nasa.gov/pub/naif/toolkit_docs/C/cspice/gfoclt_c.html" target="_top">gfoclt_c</a></code>:
            For third-body occultation searches</p></li></ul></div><p>These functions implement a fixed-step search method through the
      interval, with an embedded root-location step if an event is found.
      <span class="guilabel">StepSize</span> should be set equal to the length of the
      minimum-duration event to be found, or equal to the length of the
      minimum-duration gap between events, whichever is smaller. To guarantee
      location of 10-second intrusions, or 10-second gaps between adjacent
      intrusions, set <span class="guilabel">StepSize</span> = 10.</p><p>For details, see the reference documentation for the functions
      listed above.</p></div><div class="refsection"><a name="IntrusionLocator_ReportFormat"></a><h3>Report format</h3><p>When <span class="guilabel">WriteReport</span> is enabled, the
      <span class="guilabel">IntrusionLocator</span> outputs an event report at the end
      of each search execution. The report contains the following
      data:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Spacecraft name</p></li><li class="listitem"><p>Sensor name</p></li><li class="listitem"><p>Coordinate System name</p></li><li class="listitem"><p>For each time step during an intrusion event:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>Current time (UTC)</p></li><li class="listitem"><p>Intruding body name</p></li><li class="listitem"><p>Angular diameter of intruding body (deg)</p></li><li class="listitem"><p>"X" coordinate of intruding body in specified
                  coordinate system (deg)</p></li><li class="listitem"><p>"Y" coordinate of intruding body in specified
                  coordinate system (deg)</p></li><li class="listitem"><p>Intrusion event type (Intrusion Start, Intrusion,
                  Intrusion End)</p></li></ul></div></li></ul></div><p>The report makes the distinction between an
      <span class="emphasis"><em>individual</em></span> event and a <span class="emphasis"><em>total</em></span>
      event.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>An <span class="emphasis"><em>individual event</em></span> is a single event
            where an intruding body is detected within a sensor's field of
            view. These include intrusion starts, ends, and the events
            in-between occurring at the provided
            <span class="guilabel">StepSize</span>.</p></li><li class="listitem"><p>A <span class="emphasis"><em>total event</em></span> is the entire set of
            nested individual events. Each start of an intrusion is a new
            total event. In other words, the number of total events describes
            the number of times intruding bodies passed into the field of view
            of the sensors.</p></li></ul></div></div><div class="refsection"><a name="N1BC97"></a><h3>Event location with SPK propagator</h3><p>When using the SPK propagator, you load one or more SPK ephemeris
      files using the Spacecraft.OrbitSpiceKernelName field. For the purposes
      of event location, this field causes the appropriate ephemeris files to
      be loaded automatically on run, and so use of the Propagation command is
      not necessary. This is an easy way of performing event location on an
      existing SPK ephemeris file. See the example below.</p></div></div><div class="refsection"><a name="N1BC9C"></a><h2>Examples</h2><div class="informalexample"><p>Perform a basic intrusion search in LEO:</p><pre class="programlisting"><code class="code">Create Spacecraft sat
sat.AddHardware = {camera}

Create ForceModel fm;

Create Propagator prop;
prop.FM = fm;
prop.Type = RungeKutta89;

Create Imager camera;
camera.FieldOfView = cameraFOV;
camera.DirectionX = 1.0;

Create ConicalFOV cameraFOV;
cameraFOV.FieldOfViewAngle = 11.50;

Create IntrusionLocator il;
il.CentralBody = Earth;
il.Spacecraft = sat;
il.Sensors = {camera};
il.IntrudingBodies = {Luna, Sun};
il.MinimumPhase = 0.6;
il.StepSize = 30.0;
il.UseLightTimeDelay = true;
il.UseStellarAberration = true;
il.WriteReport = true;
il.RunMode = Automatic;
il.UseEntireInterval = true;
il.Filename = 'ExampleIntrusionReport.txt';
il.ReportCoordinates = 'FixedGrid';
il.SpiceGridFrameFile = '../data/hardware/FixedGrid_NewVer.tf';

BeginMissionSequence;
Propagate prop(sat) {sat.ElapsedDays = 60.0};
</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ImpulsiveBurn.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="LibrationPoint.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">ImpulsiveBurn&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;LibrationPoint</td></tr></table></div></body></html>