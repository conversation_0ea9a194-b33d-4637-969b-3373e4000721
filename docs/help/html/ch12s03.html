<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Mission Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion"><link rel="prev" href="ch12s02.html" title="Create and Configure Spacecraft Hardware and Finite Burn"><link rel="next" href="ch12s04.html" title="Run the Mission"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Mission Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch12s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;12.&nbsp;Electric Propulsion</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch12s04.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13F6C"></a>Configure the Mission Sequence</h2></div></div></div><p>Now we will configure the mission sequence to apply a finite
    maneuver using electric propulsion for a two day propagation. When we're
    done, the mission sequence will appear as shown below.</p><div class="figure"><a name="Tut_ElectricPropulsion_Fig9_Final_Mission_Sequence"></a><p class="title"><b>Figure&nbsp;12.8.&nbsp;Final Mission Sequence</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_MissionSequence.png" align="middle" height="94" alt="Final Mission Sequence"></td></tr></table></div></div></div></div><br class="figure-break"><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13F7E"></a>Create the Commands</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the Mission Tree, right click on
          <span class="guilabel">Propagate1</span>, select <span class="guilabel">Rename</span>,
          and enter <span class="guilabel">Propagate Two Days</span>.</p></li><li class="step"><p>Right click on the command named <span class="guilabel">Propagate Two
          Days</span>, select <span class="guilabel">Insert Before</span>, then
          select <span class="guilabel">BeginFiniteBurn</span>.</p></li><li class="step"><p>Right click on the command named <span class="guilabel">Propagate Two
          Days</span>, select <span class="guilabel">Insert After</span>, then
          select <span class="guilabel">EndFiniteBurn</span>.</p></li><li class="step"><p>Rename the command named <span class="guilabel">BeginFiniteBurn1</span>
          to <span class="guilabel">StartTheManeuver</span>.</p></li><li class="step"><p>Rename the command named <span class="guilabel">EndFiniteBurn1</span>
          to <span class="guilabel">EndTheManeuver</span>.</p></li></ol></div><p>Note that for more complex analysis that has multiple
      <span class="guilabel">FiniteBurn</span> objects, you will need to configure the
      <span class="guilabel">BeginFiniteBurn</span> and
      <span class="guilabel">EndFiniteBurn</span> commands to select the desired
      <span class="guilabel">FiniteBurn</span> Resource. As there is only one
      <span class="guilabel">FiniteBurn</span> Resource in this example, the system
      automatically selected the correct <span class="guilabel">FiniteBurn</span>
      Resource.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N13FCC"></a>Configure the Propagate Command</h3></div></div></div><p>Configure the <span class="guilabel">Propagate Two Days</span> command to
      propagate for DefaultSC.ElapsedDays = 2.0</p><div class="figure"><a name="Tut_ElectricPropulsion_Fig8"></a><p class="title"><b>Figure&nbsp;12.9.&nbsp;<span class="guilabel">Prop To Perigee </span>Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_ElectricPropulsion_Propagate.png" align="middle" height="520" alt="Prop To Perigee Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch12s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tut_ElectricPropulsion.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch12s04.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Create and Configure Spacecraft Hardware and Finite Burn&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission</td></tr></table></div></body></html>