<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Receiver</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21.html#N27B20" title="Resources"><link rel="prev" href="ProcessNoiseModel.html" title="ProcessNoiseModel"><link rel="next" href="RejectFilter.html" title="RejectFilter"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Receiver</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ProcessNoiseModel.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="RejectFilter.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Receiver"></a><div class="titlepage"></div><a name="N29621" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Receiver</span></h2><p>Receiver &mdash; Hardware that receives an RF signal.</p></div><div class="refsection"><a name="N29632"></a><h2>Description</h2><p>A <span class="guilabel">GroundStation</span> or
    <span class="guilabel">Spacecraft</span> may be configured with a
    <span class="guilabel">Receiver. </span>A <span class="guilabel">Receiver</span> is assigned
    on the <span class="guilabel">AddHardware</span> list of an instance of a
    <span class="guilabel">GroundStation</span> or
    <span class="guilabel">Spacecraft</span>.</p><p>A <span class="guilabel">GroundStation</span> resource, for example, needs to
    receive the RF signal from a tracked spacecraft. The receiver resource is
    also used as the host object for the GPS_PosVec and inter-spacecraft
    measurement error models. When using GPS_PosVec data for estimation or
    simulation, an <span class="guilabel">ErrorModel</span> instance specifying the
    <span class="guilabel">GPS_PosVec</span> measurement type should be assigned on a
    <span class="guilabel">Receiver</span> object, and that receiver should be assigned
    to the associated <span class="guilabel">Spacecraft</span> object. For simulation
    or estimation of inter-spacecraft tracking, the "tracking" spacecraft must
    have a Receiver with the proper ErrorModels assigned and
    configured.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="GroundStation.html" title="GroundStation"><span class="refentrytitle">GroundStation</span></a>, <a class="xref" href="Antenna.html" title="Antenna"><span class="refentrytitle">Antenna</span></a></p></div><div class="refsection"><a name="N29667"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">ErrorModels</span></td><td><p>User-defined list of <span class="guilabel">ErrorModel</span>
            objects that describe the measurement error models used for this
            receiver. The error model types currently supported on the
            receiver object are GPS_PosVec, Range, and RangeRate. This
            parameter is needed when simulating or estimating using GPS_PosVec
            data to describe the measurement error model for the receiver.
            This parameter is also needed for simulation and estimation of
            inter-spacecraft tracking, currently implemented for measurement
            types Range, and RangeRate.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>StringList</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>An instance of <span class="guilabel">ErrorModel</span> using
                    the GPS_PosVec, Range, or RangeRate observation
                    type</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Id</span></td><td><p>Integer identification number for this receiver. This
            should match the receiver ID specified for the GPS_PosVec data in
            the GMD file. This parameter is only needed when simulating or
            estimating using GPS_PosVec data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Integer &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">800</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">PrimaryAntenna</span></td><td><p><span class="guilabel">Antenna</span> resource used by
            <span class="guilabel">Receiver</span> or <span class="guilabel">Spacecraft</span>
            resource to receive a signal</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p><span class="guilabel">Antenna</span> Object</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any valid <span class="guilabel">Antenna</span> object</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">None</span>
                    </p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N29719"></a><h2>Examples</h2><div class="informalexample"><p>Create and configure a <span class="guilabel">Receiver</span> object and
      attach it to a <span class="guilabel">GroundStation</span>.</p><pre class="programlisting">Create Antenna DSNReceiverAntenna;
Create Receiver Receiver1;

Receiver1.PrimaryAntenna = DSNReceiverAntenna;

Create GroundStation DSN
DSN.AddHardware = {Receiver1};
BeginMissionSequence;</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ProcessNoiseModel.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21.html#N27B20">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="RejectFilter.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">ProcessNoiseModel&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;RejectFilter</td></tr></table></div></body></html>