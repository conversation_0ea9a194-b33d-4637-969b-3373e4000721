<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure the Spacecraft</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><link rel="prev" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><link rel="next" href="ch05s03.html" title="Configure the Propagator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure the Spacecraft</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="SimulatingAnOrbit.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;5.&nbsp;Simulating an Orbit</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch05s03.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N10E81"></a>Configure the Spacecraft</h2></div></div></div><p>In this section, you will rename the default
    <code class="classname">Spacecraft</code> and set the
    <code class="classname">Spacecraft</code>&rsquo;s initial epoch and classical orbital
    elements. You&rsquo;ll need GMAT open, with the default mission loaded. To load
    the default mission, click <span class="guibutton">New Mission</span>
    (<span class="inlinemediaobject"><img src="../files/images/icons/NewMission.png" align="middle" height="10"></span>) or start a new GMAT session.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10E97"></a>Rename the Spacecraft</h3></div></div></div><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">Resources</span> tree, right-click
          <span class="guilabel">DefaultSC</span> and click
          <span class="guimenuitem">Rename</span>.</p></li><li class="listitem"><p>Type <strong class="userinput"><code>Sat</code></strong>.</p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10EB4"></a>Set the Spacecraft Epoch</h3></div></div></div><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">Resources</span> tree, double-click
          <span class="guilabel">Sat</span>. Click the <span class="guilabel">Orbit</span> tab
          if it is not already selected.</p></li><li class="listitem"><p>In the <span class="guilabel">Epoch Format</span> list, select
          <span class="guilabel">UTCGregorian</span>. You&rsquo;ll see the value in the
          <span class="guilabel">Epoch</span> field change to the UTC Gregorian epoch
          format.</p></li><li class="listitem"><p>In in the <span class="guilabel">Epoch</span> box, type <strong class="userinput"><code>22
          Jul 2014 11:29:10.811</code></strong>. This field is case-sensitive, and
          must be entered in the exact format shown.</p></li><li class="listitem"><p>Click <span class="guibutton">Apply</span> or press the
          <span class="keycap"><strong>ENTER</strong></span> key to save these changes.</p></li></ol></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N10EE3"></a>Set the Keplerian Orbital Elements</h3></div></div></div><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">StateType</span> list, select
          <span class="guilabel">Keplerian</span>. In the <span class="guilabel">Elements</span>
          list, you will see the GUI reconfigure to display the Keplerian
          state representation.</p></li><li class="listitem"><p>In the <span class="guilabel">SMA</span> box, type
          <strong class="userinput"><code>83474.318</code></strong>.</p></li><li class="listitem"><p>Set the remaining orbital elements as shown in the table
          below.</p><div class="table"><a name="N10F01"></a><p class="title"><b>Table&nbsp;5.1.&nbsp;<span class="guilabel">Sat</span> Orbit State Settings</b></p><div class="table-contents"><table summary="Sat Orbit State Settings" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Field</th><th>Value</th></tr></thead><tbody><tr><td><span class="guilabel">ECC</span></td><td><strong class="userinput"><code>0.89652</code></strong></td></tr><tr><td><span class="guilabel">INC</span></td><td><strong class="userinput"><code>12.4606</code></strong></td></tr><tr><td><span class="guilabel">RAAN</span></td><td><strong class="userinput"><code>292.8362</code></strong></td></tr><tr><td><span class="guilabel">AOP</span></td><td><strong class="userinput"><code>218.9805</code></strong></td></tr><tr><td><span class="guilabel">TA</span></td><td><strong class="userinput"><code>180</code></strong></td></tr></tbody></table></div></div><p><br class="table-break"></p><p></p></li><li class="listitem"><p>Click <span class="guibutton">OK</span>.</p></li><li class="listitem"><p>Click <span class="guilabel">Save</span> (<span class="inlinemediaobject"><img src="../files/images/icons/SaveMission.png" align="middle" height="10"></span>). If this is the first time you
          have saved the mission, you&rsquo;ll be prompted to provide a name and
          location for the file.</p></li></ol></div><div class="figure"><a name="Tut_PropASpacecraft_OrbitDialog"></a><p class="title"><b>Figure&nbsp;5.1.&nbsp;Spacecraft State Setup</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_PropSpacecraft_SpacecraftStateSetup.png" align="middle" height="531" alt="Spacecraft State Setup"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="SimulatingAnOrbit.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="SimulatingAnOrbit.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch05s03.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;5.&nbsp;Simulating an Orbit&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Propagator</td></tr></table></div></body></html>