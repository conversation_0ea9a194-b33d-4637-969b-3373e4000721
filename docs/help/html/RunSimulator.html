<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>RunSimulator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21s02.html" title="Commands"><link rel="prev" href="RunEstimator.html" title="RunEstimator"><link rel="next" href="RunSmoother.html" title="RunSmoother"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">RunSimulator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="RunEstimator.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="RunSmoother.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="RunSimulator"></a><div class="titlepage"></div><a name="N2AD25" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">RunSimulator</span></h2><p>RunSimulator &mdash; Generates simulated navigation measurements</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis">RunSimulator Simulator_InstanceName </pre></div><div class="refsection"><a name="N2AD3B"></a><h2>Description</h2><p>The <span class="guilabel">RunSimulator</span> command generates the
    simulated measurements specified in the user-provided
    <span class="guilabel">Simulator</span> resource. An output file, with name
    specified in the <span class="guilabel">Simulator</span> resource is
    created.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Simulator.html" title="Simulator"><span class="refentrytitle">Simulator</span></a></p></div><div class="refsection"><a name="N2AD50"></a><h2>Remarks</h2><div class="refsection"><a name="N2AD53"></a><h3>Content of the Output File for DSN data</h3><p>After the <span class="guilabel">RunSimulator</span> command has finished
      execution, one or more output files, as defined in the specified
      <span class="guilabel">Simulator</span> object, will be created. Each row of data
      in an output file contains information about one specific measurement at
      a given time. The format for a given row of data is described fully in
      the <a class="xref" href="TrackingFileSet.html" title="TrackingFileSet"><span class="refentrytitle">TrackingFileSet</span></a> resource help.</p><p>When you simulate DSN range or Doppler, you can choose whether or
      not the frequency from the transmitting <span class="guilabel">Ground
      Station</span> is non-ramped or ramped. If you wish to model ramped
      data, you must supply an input ramp table. The format of the input ramp
      table is discussed in the <span class="guilabel"><a class="xref" href="TrackingDataTypes.html#TrackingDataTypes_DsnRampTable" title="Transmit Frequency Ramp Records">the section called &ldquo;Transmit Frequency Ramp Records&rdquo;</a></span> resource
      help.</p><p>The table below shows how the values of uplink band, C, M2, and
      transmit frequency are calculated. The second column shows how the
      uplink band, which is included in the output file for both range and
      Doppler measurements, is calculated. For S-band, a &ldquo;1&rdquo; is output and for
      X-band, a &ldquo;2&rdquo; is output.</p><p>The output GMAT Measurement Data (GMD) file contains the
      observable value which is calculated using the equations shown in <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a>. The third column shows how the value of C
      or M2, which is used to calculate the observation value shown in the GMD
      file, is calculated. Finally, the fourth column shows how the transmit
      frequency, which shows up directly in the GMD file (for DSN range but
      not DSN Doppler) and is also used to calculate the observation value
      given in the GMD file, is calculated.</p><div class="informaltable"><table width="100%" border="1"><colgroup><col align="center" width="10%"><col width="27%"><col width="34%"><col width="29%"></colgroup><thead><tr><th align="center">Measurement Type</th><th align="center">Uplink Band</th><th align="center">Value of C (Range) or M2 (Doppler) used to
              calculate Observation</th><th align="center">Transmit freq used to calculate
              Observation</th></tr></thead><tbody><tr><td align="center"><span class="bold"><strong>Simulate Range without ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon transmitter frequency set by user on
                    the Transmitter.Frequency field. If freq is in [2000-4000]
                    MHz, then Uplink Band is S-band. If freq is in [7000-8000]
                    MHz, then Uplink Band is X-band.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band result shown in previous
                    column. C=&frac12; for S-band and 221/1498 for X-band.</p></li><li class="listitem"><p>Value of Transponder.TurnAroundRatio has no effect
                    on C</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                          <m:msub>
                            <m:mi>f</m:mi>

                            <m:mi>T</m:mi>
                          </m:msub>
                        </m:math>=Transmitter.frequency (This frequency
                    will be written to the GMD range file)</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Simulate Range with ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Uplink Band in ramp table takes precedence over both
                    transmitter frequency set by user and transmit frequency
                    in ramp table.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band result shown in previous
                    column. C=&frac12; for S-band and 221/1498 for X-band.</p></li><li class="listitem"><p>Value of Transponder.TurnAroundRatio has no effect
                    on C</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                          <m:msub>
                            <m:mi>f</m:mi>

                            <m:mi>T</m:mi>
                          </m:msub>
                        </m:math>=Ramp Table frequency (This frequency
                    will be written to the GMD range file)</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Simulate Doppler without ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon transmitter frequency set by user on
                    the Transmitter.Frequency field. If freq is in [2000-4000]
                    MHz, then Uplink Band is S-band. If freq is in [7000-8000]
                    MHz, then Uplink Band is X-band.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>M2=Transponder.TurnAroundRatio</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                          <m:msub>
                            <m:mi>f</m:mi>

                            <m:mi>T</m:mi>
                          </m:msub>
                        </m:math>=Transmitter.frequency</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Simulate Doppler with ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Uplink Band in ramp table takes precedence over both
                    transmitter frequency set by user and transmit frequency
                    in ramp table.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band result shown in previous
                    column. M2=240/221 for S-band and 880/749 for X
                    band.</p></li><li class="listitem"><p>Value of Transponder.TurnAroundRatio has no effect
                    on M2</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="inline">
                          <m:msub>
                            <m:mi>f</m:mi>

                            <m:mi>T</m:mi>
                          </m:msub>
                        </m:math>=Ramp Table frequency</p></li></ul></div></td></tr></tbody></table></div><p>As discussed in the <a class="xref" href="Transponder.html" title="Transponder"><span class="refentrytitle">Transponder</span></a> Help, for both
      ramped and non-ramped data, the turn-around ratio set on the
      <span class="guilabel">Transponder</span> object,
      <span class="guilabel">Transponder.TurnAroundRatio</span>, will be used to
      calculate the media corrections needed to determine the value of the
      simulated range and Doppler measurements.</p></div><div class="refsection"><a name="N2AE25"></a><h3>Earth Nutation Update Interval</h3><p>For the best consistency between the simulation and estimation
      processes you should set the Earth nutation update interval to 0 as
      shown below. It is good general practice to set the Earth nutation
      update interval to zero for all measurement types.</p><pre class="programlisting"><code class="code">Earth.NutationUpdateInterval = 0</code></pre></div></div><div class="refsection"><a name="N2AE2D"></a><h2>Examples</h2><div class="informalexample"><p>Run simulation.</p><pre class="programlisting"><code class="code">%Perform a simulation

Create Simulator mySim

BeginMissionSequence 
RunSimulator mySim</code></pre></div><div class="informalexample"><p>For a comprehensive example of running a simulation, see the <a class="xref" href="Tut_Simulate_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;13.&nbsp;Simulate DSN Range and Doppler Data">Chapter&nbsp;13, <i>Simulate DSN Range and Doppler Data</i></a> tutorial.</p></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="RunEstimator.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="RunSmoother.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">RunEstimator&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;RunSmoother</td></tr></table></div></body></html>