<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>EndFiniteBurn</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18s02.html" title="Commands"><link rel="prev" href="EndFileThrust.html" title="EndFileThrust"><link rel="next" href="FindEvents.html" title="FindEvents"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">EndFiniteBurn</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="EndFileThrust.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="FindEvents.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="EndFiniteBurn"></a><div class="titlepage"></div><a name="N22448" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">EndFiniteBurn</span></h2><p>EndFiniteBurn &mdash; Model finite thrust maneuvers in the mission
    sequence</p></div><div class="refsection"><a name="N22459"></a><h2>Description</h2><p>To implement a finite burn, you use a pair of commands, the
    <span class="guilabel">BeginFiniteBurn</span> command and the
    <span class="guilabel">EndFiniteBurn</span> command. The use of both of these
    commands is described in the <a class="xref" href="BeginFiniteBurn.html" title="BeginFiniteBurn"><span class="refentrytitle">BeginFiniteBurn</span></a> command
    help. </p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="EndFileThrust.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="FindEvents.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">EndFileThrust&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;FindEvents</td></tr></table></div></body></html>