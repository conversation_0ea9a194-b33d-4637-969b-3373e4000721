<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;6.&nbsp;Simple Orbit Transfer</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch05s05.html" title="Run and Analyze the Results"><link rel="next" href="ch06s02.html" title="Configure Maneuvers, Differential Corrector, and Graphics"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;6.&nbsp;Simple Orbit Transfer</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch05s05.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch06s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="SimpleOrbitTransfer"></a>Chapter&nbsp;6.&nbsp;Simple Orbit Transfer</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="SimpleOrbitTransfer.html#N1113D">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch06s02.html">Configure Maneuvers, Differential Corrector, and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s02.html#N1117F">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111A3">Modify the Default Orbit View</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111FB">Create the Maneuvers.</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s03.html#N1124B">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1126A">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1132D">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1137A">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s04.html">Run the Mission</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Beginner</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>30 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Complete <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><i>Simulating an Orbit</i></a></p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Tut_SimpleOrbitTransfer.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N1113D"></a>Objective and Overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>One of the most common problems in space mission design is to
      design a transfer from one circular orbit to another circular orbit that
      lie within the same orbital plane. Circular coplanar transfers are used
      to raise low-Earth orbits that have degraded due to the effects of
      atmospheric drag. They are also used to transfer from a low-Earth orbit
      to a geosynchronous orbit and to send spacecraft to Mars. There is a
      well known sequence of maneuvers, called the Hohmann transfer, that
      performs a circular, coplanar transfer using the least possible amount
      of fuel. A Hohmann transfer employs two maneuvers. The first maneuver
      raises the orbital apoapsis (or lowers orbital periapsis) to the desired
      altitude and places the spacecraft in an elliptical transfer orbit. At
      the apoapsis (or periapsis) of the elliptical transfer orbit, a second
      maneuver is applied to circularize the orbit at the final
      altitude.</p></div><p>In this tutorial, we will use GMAT to perform a Hohmann transfer
    from a low-Earth parking orbit to a geosynchronous mission orbit. This
    requires a targeting sequence to determine the required maneuver
    magnitudes to achieve the desired final orbit conditions. In order to
    focus on the configuration of the targeter, we will make extensive use of
    the default configurations for spacecraft, propagators, and
    maneuvers.</p><p>The target sequence employs two velocity-direction maneuvers and two
    propagation sequences. The purpose of the first maneuver is to raise orbit
    apoapsis to 42,165 km, the geosynchronous radius. The purpose of the
    second maneuver is to nearly circularize the orbit and yield a final
    eccentricity of 0.005. The basic steps of this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Create and configure a
        <code class="classname">DifferentialCorrector</code> resource.</p></li><li class="step"><p>Modify the <code class="classname">DefaultOrbitView</code> to visualize
        the trajectory.</p></li><li class="step"><p>Create two <code class="classname">ImpulsiveBurn</code> resources with
        default settings.</p></li><li class="step"><p>Create a <code class="function">Target</code> sequence to (1) raise
        apoapsis to geosynchronous altitude and (2) circularize the
        orbit.</p></li><li class="step"><p>Run the mission and analyze the results.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch05s05.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch06s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Run and Analyze the Results&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure Maneuvers, Differential Corrector, and Graphics</td></tr></table></div></body></html>