<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Run the Mission with first Target Sequence</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Mars_B_Plane_Targeting.html" title="Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting"><link rel="prev" href="ch08s03.html" title="Configure the Mission Sequence"><link rel="next" href="ch08s05.html" title="Run the Mission with first and second Target Sequences"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Run the Mission with first Target Sequence</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch08s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch08s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N12596"></a>Run the Mission with first Target Sequence</h2></div></div></div><p>Before running the mission, click <span class="guilabel">Save</span>
    (<span class="inlinemediaobject"><img src="../files/images/icons/SaveMission.png" align="middle" height="10"></span>) and save the mission to a file of your
    choice. Now click <span class="guilabel">Run</span> (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>). As the mission runs, you will see
    GMAT solve the targeting problem. Each iteration and perturbation is shown
    in <span class="guilabel">EarthView</span>, <span class="guilabel">SolarSystemView</span>
    and <span class="guilabel">MarsView</span> windows in light blue, and the final
    solution is shown in red. After the mission completes, the 3D views should
    appear as in the images shown below. You may want to run the mission
    several times to see the targeting in progress.</p><div class="figure"><a name="N125BA"></a><p class="title"><b>Figure&nbsp;8.17.&nbsp;3D View of departure hyperbolic trajectory (EarthView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane departure hyperbola EarthView_better.png" align="middle" height="735" alt="3D View of departure hyperbolic trajectory (EarthView)"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N125C6"></a><p class="title"><b>Figure&nbsp;8.18.&nbsp;3D View of heliocentric transfer trajectory
      (SolarSystemView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane departure hyperbola SolarSystemView.png" align="middle" height="725" alt="3D View of heliocentric transfer trajectory (SolarSystemView)"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N125D2"></a><p class="title"><b>Figure&nbsp;8.19.&nbsp;3D View of approach hyperbolic trajectory. MAVEN stopped at
      periapsis (MarsView)</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane departure hyperbola MarsView_1.png" align="middle" height="774" alt="3D View of approach hyperbolic trajectory. MAVEN stopped at periapsis (MarsView)"></td></tr></table></div></div></div></div><br class="figure-break"><p>Since we are going to continue developing the mission tree by
    creating the second <code class="function">Target</code> sequence, we will store
    the final solution of the first <code class="function">Target</code> sequence as
    the initial conditions of the <span class="guilabel">TCM</span> resource. This is
    so that when you make small changes, the subsequent runs will take less
    time. To do this, follow these steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, double-click
        <span class="guilabel">Target desired B-plane Coordinates</span> to edit its
        properties.</p></li><li class="step"><p>Click <span class="guilabel">Apply Corrections</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li><li class="step"><p>Now re-run the mission. If you inspect the results in the
        message window, you will see that the first
        <span class="guilabel">Target</span> sequence converges in one iteration. This
        is because you stored the solution as the initial conditions.</p></li><li class="step"><p>In the <span class="guilabel">Mission</span> tree, double-click
        <span class="guilabel">Vary TCM.V</span>, <span class="guilabel">Vary TCM.N</span> and
        <span class="guilabel">Vary TCM.B</span>, you will notice that the values in
        Initial Value box have been updated to the final solution of the first
        <span class="guilabel">Target</span> sequence.</p></li></ol></div><p>If you want to know TCM maneuver&rsquo;s delta-V vector values and how
    much fuel was expended during the maneuver, do the following steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
        <span class="guilabel">Apply TCM</span>, and click on <span class="guilabel">Command
        Summary</span>.</p></li><li class="step"><p>Scroll down and under <code class="literal">Maneuver Summary</code>
        heading, values for delta-V vector are:</p><p><code class="literal">Delta V Vector:</code></p><p><code class="literal">Element 1: 0.0039376963731 km/s</code></p><p><code class="literal">Element 2: 0.0060423170483 km/s</code></p><p><code class="literal">Element 3: -0.0006747125434 km/s</code></p></li><li class="step"><p>Scroll down and under <code class="literal">Mass depletion from
        MainTank</code> heading, <code class="literal">Delta V</code> and
        <code class="literal">Mass Change</code> tells you TCM maneuver&rsquo;s magnitude and
        how much fuel was used for the maneuver:</p><p><code class="literal">Delta V: 0.0072436375569 km/s</code></p><p><code class="literal">Mass change: -6.3128738639690 kg</code></p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close <span class="guilabel">Command
        Summary</span> window.</p></li></ol></div><p>Just to make sure that the goals of first
    <span class="guilabel">Target</span> sequence were met successfully, let us access
    command summary for <span class="guilabel">Prop to Mars Periapsis</span> command by
    doing the following steps:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
        <span class="guilabel">Prop to Mars Periapsis</span>, and click on
        <span class="guilabel">Command Summary</span>.</p></li><li class="step"><p>Under <span class="guilabel">Coordinate System</span>, select
        <span class="guilabel">MarsInertial</span>.</p></li><li class="step"><p>Under <code class="literal">Hyperbolic Parameters</code> heading, see the
        values of <code class="literal">BdotT</code> and <code class="literal">BdotR</code>. Under
        <code class="literal">Keplerian State</code>, see the value for
        <code class="literal">INC</code>. You can see that the desired B-Plane
        coordinates were achieved which result in a 90 degree inclined
        trajectory:</p><p><code class="literal">BdotT = -0.0000053320678 km</code></p><p><code class="literal">BdotR = -7000.0000019398 km</code></p><p><code class="literal">INC = 90.000000039301 deg</code></p></li></ol></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1268C"></a>Create the Second Target Sequence</h3></div></div></div><p>Recall that we still need to create second
      <span class="guilabel">Target</span> sequence in order to perform Mars Orbit
      Insertion maneuver to achieve the desired capture orbit. In the
      <span class="guilabel">Mission</span> tree, we will create the second
      <span class="guilabel">Target</span> sequence right after the first
      <span class="guilabel">Target</span> sequence.</p><p>Now let&rsquo;s create the commands necessary to perform the second
      <span class="guilabel">Target</span> sequence. <a class="xref" href="ch08s04.html#Tut_Mars_B_Plane_Targeting_B_MissionTree_2" title="Figure&nbsp;8.20.&nbsp;Mission Sequence showing first and second Target sequences">Figure&nbsp;8.20, &ldquo;Mission Sequence showing first and second Target
        sequences&rdquo;</a> illustrates the
      configuration of the <span class="guilabel">Mission</span> tree after you have
      completed the steps in this section. Notice that in <a class="xref" href="ch08s04.html#Tut_Mars_B_Plane_Targeting_B_MissionTree_2" title="Figure&nbsp;8.20.&nbsp;Mission Sequence showing first and second Target sequences">Figure&nbsp;8.20, &ldquo;Mission Sequence showing first and second Target
        sequences&rdquo;</a>, the second
      <span class="guilabel">Target</span> sequence is created after the first
      <span class="guilabel">Target</span> sequence. We&rsquo;ll discuss the second
      <span class="guilabel">Target</span> sequence after it has been created.</p><div class="figure"><a name="Tut_Mars_B_Plane_Targeting_B_MissionTree_2"></a><p class="title"><b>Figure&nbsp;8.20.&nbsp;Mission Sequence showing first and second Target
        sequences</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Targeting_B_MissionTree_2.png" align="middle" height="336" alt="Mission Sequence showing first and second Target sequences"></td></tr></table></div></div></div></div><br class="figure-break"><p>To create the second <span class="guilabel">Target</span> sequence:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click on the <span class="guilabel">Mission</span> tab to show the
          <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click on
          <span class="guilabel">Mission Sequence</span> folder, point to
          <span class="guilabel">Append</span>, and click <span class="guilabel">Target</span>.
          This will insert two separate commands: <span class="guilabel">Target2</span>
          and <span class="guilabel">EndTarget2</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Target2</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>Type <span class="guilabel">Mars Capture</span> and click
          <span class="guilabel">OK</span>.</p></li><li class="step"><p>Right-click <span class="guilabel">Mars Capture</span>, point to
          <span class="guilabel">Append</span>, and click <span class="guilabel">Vary</span>. A
          new command called <span class="guilabel">Vary4</span> will be
          created.</p></li><li class="step"><p>Right-click <span class="guilabel">Vary4</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>In the <span class="guilabel">Rename</span> box, type <span class="guilabel">Vary
          MOI.V</span> and click <span class="guilabel">OK</span>.</p></li><li class="step"><p>Complete the <span class="guilabel">Target</span> sequence by appending
          the commands in <a class="xref" href="ch08s04.html#Tut_Mars_B_Plane_Second_Targeting_CommandTable" title="Table&nbsp;8.10.&nbsp;Additional Second Target Sequence Commands">Table&nbsp;8.10, &ldquo;Additional Second <span class="guilabel">Target</span> Sequence
              Commands&rdquo;</a>.</p><div class="table"><a name="Tut_Mars_B_Plane_Second_Targeting_CommandTable"></a><p class="title"><b>Table&nbsp;8.10.&nbsp;Additional Second <span class="guilabel">Target</span> Sequence
              Commands</b></p><div class="table-contents"><table summary="Additional Second Target Sequence
              Commands" border="1"><colgroup><col width="50%"><col width="50%"></colgroup><thead><tr><th>Command</th><th>Name</th></tr></thead><tbody><tr><td><span class="guilabel">Maneuver</span></td><td><strong class="userinput"><code>Apply MOI</code></strong></td></tr><tr><td><span class="guilabel">Propagate</span></td><td><strong class="userinput"><code>Prop to Mars
                    Apoapsis</code></strong></td></tr><tr><td><span class="guilabel">Achieve</span></td><td><strong class="userinput"><code>Achieve RMAG</code></strong></td></tr></tbody></table></div></div><p><br class="table-break"></p></li></ol></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Let&rsquo;s discuss what the second <span class="guilabel">Target</span>
        sequence does. We know that a maneuver is required for the Mars
        capture orbit. We also know that the desired radius of capture orbit
        at apoapsis must be 12,000 km. However, we don&rsquo;t know the size (or &#916;V
        magnitude) of the <span class="guilabel">MOI</span> maneuver that will
        precisely achieve the desired orbital conditions. You use the second
        <span class="guilabel">Target</span> sequence to solve for that precise
        maneuver value. You must tell GMAT what controls are available (in
        this case, a single maneuver) and what conditions must be satisfied
        (in this case, radius magnitude value). Once again, just like in the
        first <span class="guilabel">Target</span> sequence, here we accomplish this by
        using the <span class="guilabel">Vary</span> and <span class="guilabel">Achieve</span>
        commands. Using the <span class="guilabel">Vary</span> command, you tell GMAT
        what to solve for&mdash;in this case, the &#916;V value for
        <span class="guilabel">MOI</span>. You use the <span class="guilabel">Achieve</span>
        command to tell GMAT what conditions the solution must satisfy&mdash;in this
        case, RMAG value of 12,000 km.</p></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N1276C"></a>Create the Final Propagate Command</h3></div></div></div><p>We need a <span class="guilabel">Propagate</span> command after the second
      <span class="guilabel">Target</span> sequence so that we can see our final
      orbit.</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>In the <span class="guilabel">Mission</span> tree, right-click
          <span class="guilabel">End Mars Capture</span>, point to <span class="guilabel">Insert
          After</span>, and click <span class="guilabel">Propagate</span>. A new
          <span class="guilabel">Propagate6</span> command will appear.</p></li><li class="step"><p>Right-click <span class="guilabel">Propagate6</span> and click
          <span class="guilabel">Rename</span>.</p></li><li class="step"><p>Type <span class="guilabel">Prop for 1 day</span> and click
          <span class="guilabel">OK</span>.</p></li><li class="step"><p>Double-click <span class="guilabel">Prop for 1 day</span> to edit its
          properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace
          <span class="guilabel">NearEarth</span> with
          <span class="guilabel">NearMars</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace
          <span class="guilabel">MAVEN.ElapsedSeconds</span> with
          <span class="guilabel">MAVEN.ElapsedDays</span>.</p></li><li class="step"><p>Under <span class="guilabel">Condition</span>, replace the value
          <span class="guilabel">12000.0</span> with <span class="guilabel">1</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes</p></li></ol></div><div class="figure"><a name="N127CC"></a><p class="title"><b>Figure&nbsp;8.21.&nbsp;<span class="guilabel">Prop for 1 day</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Prop For 1 Day_better.png" align="middle" height="475" alt="Prop for 1 day Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N127DA"></a>Configure the second Target Sequence</h3></div></div></div><p>Now that the structure is created, we need to configure various parts of the second <span class="guilabel">Target</span> sequence to do what we want.</p></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N127E2"></a>Configure the Mars Capture Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Mars Capture</span> to edit its properties.</p></li><li class="step"><p>In the <span class="guilabel">ExitMode</span> list, click <span class="guilabel">SaveAndContinue</span>. This instructs GMAT to save the final solution of the targeting problem after you run it.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes</p></li></ol></div><div class="figure"><a name="N127FB"></a><p class="title"><b>Figure&nbsp;8.22.&nbsp;<span class="guilabel">Mars Capture</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Mars Capture.png" align="middle" height="315" alt="Mars Capture Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12809"></a>Configure the Vary MOI.V Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Vary MOI.V</span> to edit its
          properties. Notice that the variable in the
          <span class="guilabel">Variable</span> box is
          <span class="guilabel">TCM.Element1</span>. We want
          <span class="guilabel">MOI.Element1</span> which is the velocity component of <span class="guilabel">MOI</span> in the local VNB coordinate system. So let&rsquo;s change that.</p></li><li class="step"><p>Next to <span class="guilabel">Variable</span>, click the
          <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>Under <span class="guilabel">Object</span> List, click
          <span class="guilabel">MOI</span>.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list,
          double-click <span class="guilabel">Element1</span> to move it to the
          <span class="guilabel">Selected Value(s)</span> list. See the image below for
          results.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the
          <span class="guilabel">ParameterSelectDialog window</span>.</p></li><li class="step"><p>In the <span class="guilabel">Initial Value</span> box, type
          <span class="guilabel">-1.0</span>.</p></li><li class="step"><p>In the <span class="guilabel">Perturbation</span> box, type
          <span class="guilabel">0.00001</span>.</p></li><li class="step"><p>In the <span class="guilabel">Lower</span> box, type
          <span class="guilabel">-10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Upper</span> box, type
          <span class="guilabel">10e300</span>.</p></li><li class="step"><p>In the <span class="guilabel">Max Step</span> box, type
          <span class="guilabel">0.1</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12879"></a><p class="title"><b>Figure&nbsp;8.23.&nbsp;<span class="guilabel">Vary MOI</span> Parameter Selection</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane_Vary MOI_V Parameter Select.png" align="middle" height="431" alt="Vary MOI Parameter Selection"></td></tr></table></div></div></div></div><br class="figure-break"><div class="figure"><a name="N12887"></a><p class="title"><b>Figure&nbsp;8.24.&nbsp;<span class="guilabel">Vary MOI</span> Command Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Vary MOI_V Command.png" align="middle" height="310" alt="Vary MOI Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12895"></a>Configure the Apply MOI Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Apply MOI</span> to edit its properties.</p></li><li class="step"><p>In the <span class="guilabel">Burn</span> list, click <span class="guilabel">MOI</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N128AE"></a><p class="title"><b>Figure&nbsp;8.25.&nbsp;<span class="guilabel">Apply MOI</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Apply MOI.png" align="middle" height="240" alt="Apply MOI Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N128BC"></a>Configure the Prop to Mars Apoapsis Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Prop to Mars Apoapsis</span> to edit its properties.</p></li><li class="step"><p>Under <span class="guilabel">Propagator</span>, replace <span class="guilabel">NearEarth</span> with <span class="guilabel">NearMars</span>.</p></li><li class="step"><p>Under <span class="guilabel">Parameter</span>, replace <span class="guilabel">MAVEN.ElapsedSeconds</span> with <span class="guilabel">MAVEN.Mars.Apoapsis</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N128E4"></a><p class="title"><b>Figure&nbsp;8.26.&nbsp;<span class="guilabel">Prop to Mars Apoapsis</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Prop to Mars Apoapsis_better.png" align="middle" height="474" alt="Prop to Mars Apoapsis Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N128F2"></a>Configure the Achieve RMAG Command</h3></div></div></div><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Double-click <span class="guilabel">Achieve RMAG</span> to edit its properties.</p></li><li class="step"><p>Next to <span class="guilabel">Goal</span>, click the <span class="guilabel">Edit</span> button.</p></li><li class="step"><p>In the <span class="guilabel">Object Properties</span> list, click <span class="guilabel">RMAG</span>.</p></li><li class="step"><p>Under <span class="guilabel">Central Body</span>, select <span class="guilabel">Mars</span> and double-click on <span class="guilabel">RMAG</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to close the <span class="guilabel">ParameterSelectDialog</span> window.</p></li><li class="step"><p>In the <span class="guilabel">Value</span> box, type <span class="guilabel">12000</span>.</p></li><li class="step"><p>Click <span class="guilabel">OK</span> to save these changes.</p></li></ol></div><div class="figure"><a name="N12932"></a><p class="title"><b>Figure&nbsp;8.27.&nbsp;<span class="guilabel">Achieve RMAG</span> Command
        Configuration</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_Mars_B_Plane Achieve RMAG.png" align="middle" height="230" alt="Achieve RMAG Command Configuration"></td></tr></table></div></div></div></div><br class="figure-break"></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch08s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Mars_B_Plane_Targeting.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch08s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure the Mission Sequence&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Run the Mission with first and second Target Sequences</td></tr></table></div></body></html>