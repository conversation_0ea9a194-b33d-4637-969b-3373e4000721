<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Target</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch20s02.html" title="Commands"><link rel="prev" href="Optimize.html" title="Optimize"><link rel="next" href="Vary.html" title="Vary"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Target</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Optimize.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Vary.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Target"></a><div class="titlepage"></div><a name="N273DD" class="indexterm"></a><a name="N273E0" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Target</span></h2><p>Target &mdash; Solve for condition(s) by varying one or more
    parameters</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">Target</code> <em class="replaceable"><code>SolverName</code></em> [<code class="literal">{</code>[<code class="literal">SolveMode =</code> <em class="replaceable"><code>value</code></em>], [<code class="literal">ExitMode =</code> <em class="replaceable"><code>value</code></em>],
                    [<code class="literal">ShowProgressWindow =</code> <em class="replaceable"><code>value</code></em>]<code class="literal">}</code>]
      <em class="replaceable"><code>Vary command</code></em> &hellip;
      <em class="replaceable"><code>script statement</code></em> &hellip;
      <em class="replaceable"><code>Achieve command</code></em> &hellip;
<code class="literal">EndTarget</code>    </pre><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>See <a class="xref" href="Target.html#Target_Remarks" title="Remarks">the section called &ldquo;Remarks&rdquo;</a>
      and <a class="xref" href="Target.html#Target_Description" title="Description">the section called &ldquo;Description&rdquo;</a> for this complex command.
      Multiple <span class="guilabel">Vary</span> and <span class="guilabel">Achieve</span>
      commands are permitted. Script statements can appear anywhere in the
      <span class="guilabel">Target</span> sequence.</p></div></div><div class="refsection"><a name="Target_Description"></a><h2>Description</h2><p>The <span class="guilabel">Target</span> and <span class="guilabel">EndTarget</span>
    commands are used to define a <span class="guilabel">Target</span> sequence to
    determine, for example, the maneuver components required to raise the
    orbit apogee to 42164 km. Another common targeting example is to determine
    the parking orbit orientation required to align a lunar transfer orbit
    with the moon. <span class="guilabel">Target</span> sequences in GMAT are general
    and these are just examples. Let&rsquo;s define the quantities whose values you
    don&rsquo;t know precisely, but need to determine, as the<em class="firstterm"> control
    variables</em>. Define the conditions that must be satisfied as the
    <em class="firstterm">constraints</em>. A <span class="guilabel">Target</span> sequence
    numerically solves a boundary value problem to determine the value of the
    control variables required to satisfy the constraints. You define your
    control variables by using <span class="guilabel">Vary</span> commands and you
    define the problems constraints using <span class="guilabel">Achieve</span>
    commands. The <span class="guilabel">Target/EndTarget </span> sequence is an
    advanced command. The examples later in this section give additional
    details.</p><p>See also: <a class="xref" href="DifferentialCorrector.html" title="DifferentialCorrector"><span class="refentrytitle">DifferentialCorrector</span></a>,<a class="xref" href="Vary.html" title="Vary"><span class="refentrytitle">Vary</span></a>,<a class="xref" href="Achieve.html" title="Achieve"><span class="refentrytitle">Achieve</span></a>,<a class="xref" href="Optimize.html" title="Optimize"><span class="refentrytitle">Optimize</span></a>,</p></div><div class="refsection"><a name="N27464"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">ApplyCorrections</span></td><td><p>This GUI button replaces the initial guess values
            specified in the <span class="guilabel">Vary</span> commands. If the
            <span class="guilabel">Target</span> sequence converged, the converged
            values are applied. If the <span class="guilabel">Target</span> sequence
            did not converge, the last calculated values are applied. There is
            one situation where the action specified above, where the initial
            guess values specified in the <span class="guilabel">Vary</span> commands
            are replaced, does not occur. This happens when the initial guess
            value specified in the <span class="guilabel">Vary</span> command is given
            by a variable. See the Remarks section of the help for additional
            details. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ExitMode</span></td><td><p>Controls the initial guess values for
            <span class="guilabel">Target</span> sequences nested in control flow. If
            <span class="guilabel">ExitMode</span> is set to
            <span class="guilabel">SaveAndContinue</span>, the solution of a
            <span class="guilabel">Targe</span>t sequence is saved and used as the
            initial guess for the next <span class="guilabel">Target</span> sequence
            execution. The rest of the mission sequence is then executed. If
            <span class="guilabel">ExitMode</span> is set to
            <span class="guilabel">DiscardAndContinue</span>, then the solution is
            discarded and the initial guess values specified in the
            <span class="guilabel">Vary</span> commands are used for each
            <span class="guilabel">Target</span> sequence execution. The rest of the
            mission sequence is then executed. If
            <span class="guilabel">ExitMode</span> is set to <span class="guilabel">Stop</span>,
            the <span class="guilabel">Target</span> sequence is executed, the solution
            is discarded, and the rest of the mission sequence is not
            executed. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">DiscardAndContinue</span>,
                    <span class="guilabel">SaveAndContinue</span>,
                    <span class="guilabel">Stop</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DiscardAndContinue</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ShowProgressWindow</span></td><td><p>Flag to indicate if solver progress window should be
            displayed. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Boolean</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>true,false</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>true</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolveMode</span></td><td><p>Specifies how the <span class="guilabel">Target</span>
            sequence behaves during mission execution. When
            <span class="guilabel">SolveMode</span> is set to
            <span class="guilabel">Solve</span>, the <span class="guilabel">Target</span>
            sequence executes and attempts to solve the boundary value problem
            satisfying the targeter constraints (i.e, goals). When
            <span class="guilabel">SolveMode</span> is set to
            <span class="guilabel">RunInitialGuess</span>, the targeter does not
            attempt to solve the boundary value problem and the commands in
            the <span class="guilabel">Target</span> sequence execute using the initial
            guess values defined in the <span class="guilabel">Vary</span> commands.
            </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Solve</span>,
                    <span class="guilabel">RunInitialGuess</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">Solve</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SolverName</span></td><td><p>Identifies the
            <span class="guilabel">DifferentialCorrector</span> used for a
            <span class="guilabel">Target</span> sequence. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>
                      <span class="guilabel">DifferentialCorrector</span>
                    </p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any user-defined or default
                    <span class="guilabel">DifferentialCorrector</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>
                      <span class="guilabel">DefaultDC</span>
                    </p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N275A6"></a><h2>GUI</h2><p>The <span class="guilabel">Target</span> command allows you to use a
    differential correction process to solve problems. To solve a given
    problem, you need to create a so-called <span class="guilabel">Target</span>
    sequence which we now define. When you add a <span class="guilabel">Target</span>
    command to the mission sequence, an <span class="guilabel">EndTarget</span> command
    is automatically added as shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Target_MissionTree.png" align="middle" height="182"></td></tr></table></div></div><p>In the example above, the <span class="guilabel">Target</span> command
    sequence is defined as all of the commands between the
    <span class="guilabel">Target1</span> and <span class="guilabel">End Target1</span>
    commands, inclusive. Although not shown above, a
    <span class="guilabel">Target</span> command sequence must contain both a
    <span class="guilabel">Vary</span> command and an <span class="guilabel">Achieve</span>
    command. The <span class="guilabel">Vary</span> command is used to define the
    control variables which can be varied in order to achieve a certain goal.
    The <span class="guilabel">Achieve</span> command is used to define the desired
    goal. In order for the <span class="guilabel">Target</span> sequence to be well
    formed, there must be at least one <span class="guilabel">Vary</span> command
    before any <span class="guilabel">Achieve</span> commands, so that the variable
    defined in the <span class="guilabel">Vary</span> command can affect the goal
    specified in the subsequent <span class="guilabel">Achieve</span> commands. Double
    click on <span class="guilabel">Target1</span> command above to bring up the
    <span class="guilabel">Target</span> command dialog box, shown below, which allows
    you to specify your choice of <span class="guilabel">Solver</span> (i.e., your
    choice of <span class="guilabel">DifferentialCorrector</span>), <span class="guilabel">Solver
    Mode</span>, and <span class="guilabel">Exit Mode</span>. As described in the
    Remarks section, the <span class="guilabel">Target</span> command dialog box also
    allows you to apply corrections to your <span class="guilabel">Target</span>
    command sequence.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Target_Default.png" align="middle" height="232"></td></tr></table></div></div><p>If you set <span class="guilabel">ShowProgressWindow</span> to true, then a
    dynamic display is shown during targeting that contains values of
    variables and constraints as shown below.</p><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Target_SolverStatusWindow.png" align="middle" height="261"></td></tr></table></div></div></div><div class="refsection"><a name="Target_Remarks"></a><h2>Remarks</h2><div class="refsection"><a name="N2761C"></a><h3>Content of a Target/EndTarget Sequence</h3><p>A <span class="guilabel">Target/EndTarget</span> sequence must contain at
      least one <span class="guilabel">Vary</span> command and at least one
      <span class="guilabel">Achieve</span> Command. See the <span class="guilabel">Vary</span>
      and <span class="guilabel">Achieve</span> command sections for details on the
      syntax for those commands. The First <span class="guilabel">Vary</span> command
      must occur before the first <span class="guilabel">Achieve</span> command.
      <span class="guilabel">Target</span> commands must be be coupled with one and
      only one <span class="guilabel">EndTarget</span> command. Each
      <span class="guilabel">Targe</span>t command field in the curly braces is
      optional. You can omit the entire list and the curly braces and the
      default values will be used for <span class="guilabel">Target</span>
      configuration fields such as <span class="guilabel">SolveMode</span> and
      <span class="guilabel">ExitMode</span>.</p></div><div class="refsection"><a name="N27648"></a><h3>Use of a Target/EndTarget Sequence</h3><p>GMAT <span class="guilabel">Target</span> sequences can solve square
      problems (the number of Control Variables equals the number of
      constraints), over-determined problems (the number of Control Variables
      is less than the number of constraints) and under-determined problems
      (the number of Control Variables is greater than the number of
      constraints). In any of these cases, there may not be a solution and the
      type of solution found depends on the selection of the targeter
      (currently, only differential correctors are supported). Assuming a
      solution to the problem exists and assuming certain mathematical
      conditions are satisfied, there is often one solution for a square
      problem and many solutions to an under-determined problem. Problems with
      more goals (i.e., constraints) than variables may not have a solution.
      If your problem is under-determined, consider using an
      <span class="guilabel">Optimize</span> sequence to find an optimal solution in
      the space of feasible solutions.</p><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>If you configure a <span class="guilabel">Target</span> sequence and get
        the error &ldquo;Rmatrix error: matrix is singular&rdquo;, then your control
        variables defined in the <span class="guilabel">Vary</span> commands do not
        affect the constraints defined in the <span class="guilabel">Achieve</span>
        commands. A common mistake in this case is that you forgot to apply a
        maneuver.</p></div></div><div class="refsection"><a name="N2765F"></a><h3>Note on Using Apply Corrections</h3><p>After the <span class="guilabel">Target</span> sequence has been run, you
      may choose to apply corrections by navigating to the
      <span class="guilabel">Mission</span> tree, right-clicking the
      <span class="guilabel">Target</span> command to bring up the
      <span class="guilabel">Target</span> window, and clicking the <span class="guilabel">Apply
      Corrections</span> button. The <span class="guilabel">Apply
      Corrections</span> button replaces the initial guess values
      specified in the <span class="guilabel">Vary</span> commands . If the
      <span class="guilabel">Target</span> sequence converged, the converged values are
      applied. If the <span class="guilabel">Target</span> sequence did not converge,
      the last calculated values are applied. Note that the <span class="guilabel">Apply
      Corrections</span> feature is only currently available through the
      GUI interface.</p><p>There is one situation where the action specified above, where the
      initial guess values specified in the <span class="guilabel">Vary</span> commands
      are replaced, does not occur. This happens, as illustrated in the
      example below, when the initial guess value specified in the
      <span class="guilabel">Vary</span> command is given by a variable. In this
      situation, the <span class="guilabel">Apply Corrections</span> button has no
      effect since GMAT does not allow variables to be overwritten.</p><pre class="programlisting"><code class="code">Create Variable InitialGuess_BurnDuration BurnDuration
Create DifferentialCorrector aDC
BeginMissionSequence
Target aDC
Vary aDC(BurnDuration = InitialGuess_BurnDuration)
Achieve aDC(BurnDuration = 10) % atypical Achieve command for
                               % illustrative purposes only
EndTarget</code>     </pre></div><div class="refsection"><a name="N27691"></a><h3>Command Interactions</h3><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><tbody><tr><td><span class="guilabel">Vary command </span></td><td><p> Every <span class="guilabel">Target</span> sequence must
              contain at least one <span class="guilabel">Vary</span> command.
              <span class="guilabel">Vary</span> commands are used to define the
              control variables associated with a <span class="guilabel">Target</span>
              sequence. </p></td></tr><tr><td><span class="guilabel">Achieve command</span></td><td><p> Every <span class="guilabel">Target</span> sequence must
              contain at least one <span class="guilabel">Achieve</span> command.
              <span class="guilabel">Achieve</span> commands are used to define the
              goals associated with a <span class="guilabel">Target</span> sequence.
              </p></td></tr></tbody></table></div></div></div><div class="refsection"><a name="N276C6"></a><h2>Examples</h2><div class="informalexample"><p>Use a <span class="guilabel">Target </span>sequence to solve for a root of
      an algebraic equation. Here we provide an initial guess of 5 for the
      Control Variable (or independent variable) x, and solve for the value of
      x that satisfies the Constraint y = 0, where y :=3*x^3 + 2*x^2 - 4*x +
      8. After executing this example you can look in the message window to
      see the solution for the variable x. You can easily check that the value
      obtained does indeed satisfy the constraint.</p><pre class="programlisting"><code class="code">Create Variable x y
Create DifferentialCorrector aDC

BeginMissionSequence

Target aDC
  Vary aDC(x = 5)
  y = 3*x^3 + 2*x^2 - 4*x + 8
  Achieve aDC(y = 0,{Tolerance = 0.0000001})
EndTarget</code></pre></div><div class="informalexample"><p>Use a <span class="guilabel">Target</span> sequence to raise orbit apogee.
      Here the control variable is the velocity component of an
      <span class="guilabel">ImpulsiveBurn</span> object. The Constraint is that the
      position vector magnitude at orbit apogee is 42164. Report the
      convergence status to a file.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create Propagator aPropagator
Create Variable I

Create ImpulsiveBurn aBurn
Create DifferentialCorrector aDC
Create OrbitView EarthView
EarthView.Add = {Earth,aSat}
EarthView.ViewScaleFactor = 5

Create ReportFile aReport

BeginMissionSequence
Target aDC
   Vary aDC(aBurn.Element1 = 1.0, {Upper = 3})
   Maneuver aBurn(aSat)
   Propagate aPropagator(aSat,{aSat.Apoapsis})
   Achieve aDC(aSat.RMAG = 42164)
EndTarget
Report aReport aDC.SolverStatus aDC.SolverState</code></pre></div><div class="informalexample"><p>Similar to the previous example, we use a
      <span class="guilabel">Target</span> sequence to raise orbit apogee except that
      this time we use a finite burn. Here the control variable is the
      duration of the Velocity component of a <span class="guilabel">FiniteBurn</span>
      object. The Constraint is that the position vector magnitude at orbit
      apogee is 12000. Additional detail on the example below can be found in
      the Target Finite Burn to Raise Apogee tutorial.</p><pre class="programlisting"><code class="code">Create Spacecraft DefaultSC
Create Propagator DefaultProp
Create ChemicalThruster Thruster1
GMAT Thruster1.C1 = 1000
GMAT Thruster1.DecrementMass = true
Create ChemicalTank FuelTank1
GMAT Thruster1.Tank = {FuelTank1}
Create FiniteBurn FiniteBurn1
GMAT FiniteBurn1.Thrusters = {Thruster1}
GMAT DefaultSC.Tanks = {FuelTank1}
GMAT DefaultSC.Thrusters = {Thruster1}
Create Variable BurnDuration
Create DifferentialCorrector DC1

BeginMissionSequence

Propagate DefaultProp(DefaultSC) {DefaultSC.Earth.Periapsis}
Target DC1
  Vary DC1(BurnDuration = 200, {Upper = 10000})
  BeginFiniteBurn FiniteBurn1(DefaultSC)
  Propagate DefaultProp(DefaultSC){DefaultSC.ElapsedSecs=BurnDuration}
  EndFiniteBurn FiniteBurn1(DefaultSC)
  Propagate DefaultProp(DefaultSC) {DefaultSC.Earth.Apoapsis}
  Achieve DC1(DefaultSC.Earth.RMAG = 12000)
EndTarget</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Optimize.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch20s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Vary.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Optimize&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Vary</td></tr></table></div></body></html>