<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>RunEstimator</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch21s02.html" title="Commands"><link rel="prev" href="ch21s02.html" title="Commands"><link rel="next" href="RunSimulator.html" title="RunSimulator"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">RunEstimator</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch21s02.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="RunSimulator.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="RunEstimator"></a><div class="titlepage"></div><a name="N2A915" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">RunEstimator</span></h2><p>RunEstimator &mdash; Ingests navigation measurements and generates an estimated
    state vector</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><span class="bold"><strong>RunEstimator</strong></span> BatchEstimator_InstanceName [{<span class="bold"><strong>SolveMode</strong></span> = <span class="emphasis"><em>value</em></span>}]
<span class="bold"><strong>RunEstimator</strong></span> ExtendedKalmanFilter_InstanceName </pre></div><div class="refsection"><a name="N2A939"></a><h2>Description</h2><p>The <span class="guilabel">RunEstimator</span> command ingests navigation
    measurements and generates an estimated state vector according to the
    specifications of the input <span class="guilabel">BatchEstimator</span> or
    <span class="guilabel">ExtendedKalmanFilter</span> resource.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="BatchEstimator.html" title="BatchEstimator"><span class="refentrytitle">BatchEstimator</span></a>, <a class="xref" href="ExtendedKalmanFilter.html" title="ExtendedKalmanFilter"><span class="refentrytitle">ExtendedKalmanFilter</span></a></p></div><div class="refsection"><a name="N2A951"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">SolveMode</span></td><td><p>Specifies how the <span class="guilabel">BatchEstimator</span>
            behaves during mission execution. When
            <span class="guilabel">SolveMode</span> is set to
            <span class="guilabel">RunInitialGuess</span>, the batch estimator will
            perform an "observed-minus-computed" run. GMAT will compute and
            report residuals with respect to the initial state or orbit, but
            will not attempt a differential correction, and the run will
            terminate at the end of iteration 0. When
            <span class="guilabel">SolveMode</span> is set to
            <span class="guilabel">Solve</span>, the
            <span class="guilabel">BatchEstimator</span> will perform differential
            corrections and will iterate until the estimator stopping
            conditions are met. </p><p>The
            <span class="guilabel">ExtendedKalmanFilter</span> estimator ignores this
            parameter.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p>Reference Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Solve</span>,
                    <span class="guilabel">RunInitialGuess</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Solve</span></p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>no</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2A9A8"></a><h2>Remarks</h2><div class="refsection"><a name="N2A9AB"></a><h3>How GMAT generates &ldquo;Computed (C)&rdquo; DSN data</h3><p>As part of the estimation process, GMAT must calculate the
      so-called observation residual, &ldquo;O-C,&rdquo; where &ldquo;C&rdquo; is the &ldquo;Computed&rdquo;
      measurement. As discussed in the <span class="guilabel"><a class="xref" href="RunSimulator.html" title="RunSimulator"><span class="refentrytitle">RunSimulator</span></a></span> help, GMAT calculates the DSN range
      &ldquo;C&rdquo; measurement as</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mrow>
            <m:mi>C</m:mi>

            <m:mstyle displaystyle="true">
              <m:mrow>
                <m:munderover>
                  <m:mo>&int;</m:mo>

                  <m:mrow>
                    <m:mi>t</m:mi>

                    <m:mn>1</m:mn>
                  </m:mrow>

                  <m:mrow>
                    <m:mi>t</m:mi>

                    <m:mn>3</m:mn>
                  </m:mrow>
                </m:munderover>

                <m:mrow>
                  <m:msub>
                    <m:mi>f</m:mi>

                    <m:mi>T</m:mi>
                  </m:msub>

                  <m:mo stretchy="false">(</m:mo>

                  <m:mi>t</m:mi>

                  <m:mo stretchy="false">)</m:mo>

                  <m:mi>d</m:mi>

                  <m:mi>t</m:mi>
                </m:mrow>
              </m:mrow>
            </m:mstyle>

            <m:mo>,</m:mo>

            <m:mtext>&nbsp;mod&nbsp;M&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(RU)</m:mtext>
          </m:mrow>
        </m:math></div><p>where</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mi>t</m:mi>

            <m:mn>1</m:mn>
          </m:msub>

          <m:mo>,</m:mo>

          <m:msub>
            <m:mi>t</m:mi>

            <m:mn>3</m:mn>
          </m:msub>

          <m:mo>=</m:mo>

          <m:mtext>Transmission&nbsp;and&nbsp;Reception&nbsp;epoch,&nbsp;respectively</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mi>f</m:mi>

            <m:mi>T</m:mi>
          </m:msub>

          <m:mo>=</m:mo>

          <m:mtext>Ground&nbsp;Station&nbsp;transmit&nbsp;frequency</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mi>C</m:mi>

          <m:mo>=</m:mo>

          <m:mtext>transmitter&nbsp;dependent&nbsp;constant&nbsp;(221/1498&nbsp;for&nbsp;X-band&nbsp;and&nbsp;1/2&nbsp;for&nbsp;S-Band)</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mtext>M&nbsp;</m:mtext>

          <m:mo>=</m:mo>

          <m:mtext>&nbsp;length&nbsp;of&nbsp;the&nbsp;ranging&nbsp;code&nbsp;in&nbsp;RU</m:mtext>
        </m:math></div><p>and GMAT calculates the DSN Doppler measurement as</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mrow>
            <m:mi>C</m:mi>

            <m:mo>=</m:mo>

            <m:mo>&minus;</m:mo>

            <m:mfrac>
              <m:mrow>
                <m:msub>
                  <m:mi>M</m:mi>

                  <m:mn>2</m:mn>
                </m:msub>
              </m:mrow>

              <m:mrow>
                <m:mrow>
                  <m:mo>(</m:mo>

                  <m:mrow>
                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>e</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo>&minus;</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>3</m:mn>

                        <m:mi>s</m:mi>
                      </m:mrow>
                    </m:msub>
                  </m:mrow>

                  <m:mo>)</m:mo>
                </m:mrow>
              </m:mrow>
            </m:mfrac>

            <m:mstyle displaystyle="true">
              <m:mrow>
                <m:munderover>
                  <m:mo>&int;</m:mo>

                  <m:mrow>
                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>

                    <m:msub>
                      <m:mrow/>

                      <m:mi>s</m:mi>
                    </m:msub>
                  </m:mrow>

                  <m:mrow>
                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>

                    <m:msub>
                      <m:mrow/>

                      <m:mi>e</m:mi>
                    </m:msub>
                  </m:mrow>
                </m:munderover>

                <m:mrow>
                  <m:msub>
                    <m:mi>f</m:mi>

                    <m:mi>T</m:mi>
                  </m:msub>

                  <m:mo stretchy="false">(</m:mo>

                  <m:msub>
                    <m:mi>t</m:mi>

                    <m:mn>1</m:mn>
                  </m:msub>

                  <m:mo stretchy="false">)</m:mo>

                  <m:mi>d</m:mi>

                  <m:msub>
                    <m:mi>t</m:mi>

                    <m:mn>1</m:mn>
                  </m:msub>
                </m:mrow>
              </m:mrow>
            </m:mstyle>

            <m:mtext>&nbsp;&nbsp;=</m:mtext>

            <m:mo>&minus;</m:mo>

            <m:mfrac>
              <m:mrow>
                <m:msub>
                  <m:mi>M</m:mi>

                  <m:mn>2</m:mn>
                </m:msub>

                <m:mrow>
                  <m:mo>(</m:mo>

                  <m:mrow>
                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>1</m:mn>

                        <m:mi>e</m:mi>
                      </m:mrow>
                    </m:msub>

                    <m:mo>&minus;</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mrow>
                        <m:mn>1</m:mn>

                        <m:mi>s</m:mi>
                      </m:mrow>
                    </m:msub>
                  </m:mrow>

                  <m:mo>)</m:mo>
                </m:mrow>
              </m:mrow>

              <m:mrow>
                <m:mi>D</m:mi>

                <m:mi>C</m:mi>

                <m:mi>I</m:mi>
              </m:mrow>
            </m:mfrac>

            <m:msub>
              <m:mover accent="true">
                <m:mi>f</m:mi>

                <m:mo>&macr;</m:mo>
              </m:mover>

              <m:mi>T</m:mi>
            </m:msub>

            <m:mtext>&nbsp;&nbsp;&nbsp;&nbsp;(Hz)</m:mtext>
          </m:mrow>
        </m:math></div><p>where</p><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mi>t</m:mi>

            <m:mrow>
              <m:mn>1</m:mn>

              <m:mi>s</m:mi>
            </m:mrow>
          </m:msub>

          <m:mo>,</m:mo>

          <m:msub>
            <m:mi>t</m:mi>

            <m:mrow>
              <m:mn>1</m:mn>

              <m:mi>e</m:mi>
            </m:mrow>
          </m:msub>

          <m:mo>=</m:mo>

          <m:mtext>start&nbsp;and&nbsp;end&nbsp;of&nbsp;transmission&nbsp;interval,
          respectively</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mi>f</m:mi>

            <m:mi>T</m:mi>
          </m:msub>

          <m:mo>=</m:mo>

          <m:mtext>transmit&nbsp;frequency</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mi>M</m:mi>

            <m:mn>2</m:mn>
          </m:msub>

          <m:mo>=</m:mo>

          <m:mtext>Transponder&nbsp;turn&nbsp;around&nbsp;ratio&nbsp;(typically,&nbsp;240/221&nbsp;for&nbsp;S-band&nbsp;and&nbsp;880/749&nbsp;for&nbsp;X-band)</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:mtext>DCI&nbsp;=&nbsp;</m:mtext>

          <m:mrow>
            <m:mo>(</m:mo>

            <m:mrow>
              <m:msub>
                <m:mi>t</m:mi>

                <m:mrow>
                  <m:mn>3</m:mn>

                  <m:mi>e</m:mi>
                </m:mrow>
              </m:msub>

              <m:mo>&minus;</m:mo>

              <m:msub>
                <m:mi>t</m:mi>

                <m:mrow>
                  <m:mn>3</m:mn>

                  <m:mi>s</m:mi>
                </m:mrow>
              </m:msub>
            </m:mrow>

            <m:mo>)</m:mo>
          </m:mrow>

          <m:mo>=</m:mo>

          <m:mtext>&nbsp;Doppler&nbsp;Count&nbsp;Interval</m:mtext>
        </m:math></div><div class="informalequation"><m:math xmlns:m="http://www.w3.org/1998/Math/MathML" xmlns="http://docbook.org/ns/docbook" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xi="http://www.w3.org/2001/XInclude" xmlns:svg="http://www.w3.org/2000/svg" xmlns:html="http://www.w3.org/1999/xhtml" xmlns:db="http://docbook.org/ns/docbook" display="block">
          <m:msub>
            <m:mover accent="true">
              <m:mi>f</m:mi>

              <m:mo>&macr;</m:mo>
            </m:mover>

            <m:mi>T</m:mi>
          </m:msub>

          <m:mo>&equiv;</m:mo>

          <m:mfrac>
            <m:mrow>
              <m:mstyle displaystyle="true">
                <m:mrow>
                  <m:munderover>
                    <m:mo>&int;</m:mo>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>

                      <m:msub>
                        <m:mrow/>

                        <m:mi>s</m:mi>
                      </m:msub>
                    </m:mrow>

                    <m:mrow>
                      <m:msub>
                        <m:mi>t</m:mi>

                        <m:mn>1</m:mn>
                      </m:msub>

                      <m:msub>
                        <m:mrow/>

                        <m:mi>e</m:mi>
                      </m:msub>
                    </m:mrow>
                  </m:munderover>

                  <m:mrow>
                    <m:msub>
                      <m:mi>f</m:mi>

                      <m:mi>T</m:mi>
                    </m:msub>

                    <m:mo stretchy="false">(</m:mo>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>

                    <m:mo stretchy="false">)</m:mo>

                    <m:mi>d</m:mi>

                    <m:msub>
                      <m:mi>t</m:mi>

                      <m:mn>1</m:mn>
                    </m:msub>
                  </m:mrow>
                </m:mrow>
              </m:mstyle>
            </m:mrow>

            <m:mrow>
              <m:mrow>
                <m:mo>(</m:mo>

                <m:mrow>
                  <m:msub>
                    <m:mi>t</m:mi>

                    <m:mrow>
                      <m:mn>1</m:mn>

                      <m:mi>e</m:mi>
                    </m:mrow>
                  </m:msub>

                  <m:mo>&minus;</m:mo>

                  <m:msub>
                    <m:mi>t</m:mi>

                    <m:mrow>
                      <m:mn>1</m:mn>

                      <m:mi>s</m:mi>
                    </m:mrow>
                  </m:msub>
                </m:mrow>

                <m:mo>)</m:mo>
              </m:mrow>
            </m:mrow>
          </m:mfrac>

          <m:mtext>&nbsp;</m:mtext>

          <m:mo>=</m:mo>

          <m:mtext>average&nbsp;transmit&nbsp;frequency&nbsp;</m:mtext>
        </m:math></div><p>The value of C and M2 used to calculate the computed range or
      Doppler measurement depends upon the data type and whether the data
      being ingested is ramped or non-ramped according to the table below. The
      value of the transmit frequency used to calculate the computed
      measurement depends upon whether or not the data being ingested is
      ramped or non-ramped.</p><div class="informaltable"><table border="1"><colgroup><col align="center" width="24%"><col width="42%"><col width="34%"></colgroup><thead><tr><th align="center">Data Type</th><th align="center">Value of C (Range) or M2 (Doppler) used to
              calculate &ldquo;Computed&rdquo; measurement</th><th align="center">Value of transmit frequency used to
              calculate &ldquo;Computed&rdquo; measurement</th></tr></thead><tbody><tr><td align="center"><span class="bold"><strong>Estimate Range without ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band in input Range
                    measurement GMD file. C=&frac12; for S-band and 221/1498 for
                    X-band.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Use frequency in input range GMD file</p></li><li class="listitem"><p>Ground Station transmit frequency set via
                    Transmitter.Frequency is not used</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Estimate Range with ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band in input ramp table. C=&frac12;
                    for S-band and 221/1498 for X-band.</p></li><li class="listitem"><p>Value of Uplink Band in input Range measurement file
                    has no effect.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Use frequency in ramp table</p></li><li class="listitem"><p>Frequency in input GMD file is not used</p></li><li class="listitem"><p>Ground Station transmit frequency set via
                    Transmitter.Frequency is not used</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Estimate Doppler without ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>M2=Transponder.TurnAroundRatio</p></li><li class="listitem"><p>Value of Uplink Band in input Range measurement file
                    has no effect.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Use Ground Station transmit frequency set via
                    Transmitter.Frequency (Note that for Doppler data, there
                    is no frequency data in the GMD file)</p></li></ul></div></td></tr><tr><td align="center"><span class="bold"><strong>Estimate Doppler with ramp
              table</strong></span></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Set based upon Uplink Band in input ramp table.
                    M2=240/221 for S-band and 880/749 for X band.</p></li><li class="listitem"><p>Value of Uplink Bank in input Doppler GMD
                    measurement file has no effect.</p></li></ul></div></td><td><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Use frequency in ramp table. (Note that for Doppler
                    data, there is no frequency data in the GMD file)</p></li><li class="listitem"><p>Ground Station transmit frequency set via
                    Transmitter.Frequency is not used</p></li></ul></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N2AD08"></a><h3>Earth Nutation Update Interval</h3><p>If you want the estimator to calculate a Doppler or range rate
      type of measurement (e.g., DSN_TCP and RangeRate) residual precisely,
      you will need to set the Earth nutation update interval to 0 as shown
      below.</p><pre class="programlisting"><code class="code">Earth.NutationUpdateInterval = 0</code></pre><p>It is good general practice to set the Earth nutation update
      interval to zero for all measurement types.</p></div></div><div class="refsection"><a name="N2AD12"></a><h2>Examples</h2><div class="informalexample"><p>Run batch estimator.</p><pre class="programlisting"><code class="code">Create BatchEstimator myBatchEstimator

BeginMissionSequence
RunEstimator myBatchEstimator</code></pre></div><div class="informalexample"><p>For a comprehensive example of reading in measurements and running
      the estimator, see the <a class="xref" href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html" title="Chapter&nbsp;14.&nbsp;Orbit Estimation using DSN Range and Doppler Data">Chapter&nbsp;14, <i>Orbit Estimation using DSN Range and Doppler Data</i></a>
      tutorial.</p></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch21s02.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch21s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="RunSimulator.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Commands&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;RunSimulator</td></tr></table></div></body></html>