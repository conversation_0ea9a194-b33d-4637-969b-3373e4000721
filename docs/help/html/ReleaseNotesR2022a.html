<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>GMAT R2022a Release Notes</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ReleaseNotes.html" title="Release Notes"><link rel="prev" href="ReleaseNotes.html" title="Release Notes"><link rel="next" href="ReleaseNotesR2020a.html" title="GMAT R2020a Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">GMAT R2022a Release Notes</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ReleaseNotes.html">Prev</a>&nbsp;</td><th align="center" width="60%">Release Notes</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReleaseNotesR2020a.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ReleaseNotesR2022a"></a>GMAT R2022a Release Notes</h2></div></div></div><p>The General Mission Analysis Tool (GMAT) version R2022a was released
  in December 2022. This is the first public release since May 2020 and is the
  14<sup>th</sup> release for the project. This is a major
  project release that includes numerous major improvements and enhancements
  in general capabilities and models, navigation and orbit determination,
  scripting and API interfaces, optimal control, and visualization.</p><p>Below, we give a summary of key changes in this release.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30A70"></a>Milestones and Accomplishments</h3></div></div></div><p>We are excited that GMAT continues to see significant adoption for
    operational mission support.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The Fermi low Earth NASA mission, which uses the GPS point
        solution data type for ground based orbit determination, started using
        GMAT's Extended Kalman Filter Smoother (EKFS) for operations in July
        2021.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30A79"></a>Major Improvements and Enhancements</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30A7C"></a>General Capabilities and Models</h4></div></div></div><p>The following general capabilities were added in release
      R2022a:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Full support for Solar Radiation Pressure (SRP) N-Plate force
          modeling (No longer alpha/beta level). See the <a class="xref" href="ForceModel.html" title="ForceModel"><span class="refentrytitle">ForceModel</span></a> section
          for details.</p></li><li class="listitem"><p>Ability to generate Ground Station (G/S) to spacecraft
          visibility reports with an optional ability to specify G/S station
          masks. See the <span class="guilabel">HorizonMaskFileName</span> parameter
          description as well as the <em><span class="remark">Ground Station Masking
          File</span></em> remarks in the <a class="link" href="GroundStation.html" title="GroundStation">GroundStation</a> section for details.
          See also</p><p><code class="filename">samples/Ex_R2022a_Contact_Location_Station_Mask.script</code></p><p></p></li><li class="listitem"><p>Support for reading/writing Version 1 CCSDS Orbit Ephemeris
          Message (OEM) data. To use the OEM propagators, see the <a class="link" href="Propagator.html#Propagator_OEMPropagator" title="CCSDS OEM Ephemeris-Configured Propagator">CCSDS OEM Ephemeris-Configured
          Propagator</a> section of the <span class="guilabel">Propagator</span>
          resource for details. See also</p><p><code class="filename">samples/Ex_R2022a_OEMPropagation.script</code></p><p></p></li><li class="listitem"><p>Computation of generalized planetographic region entry and
          exit times to include South Atlantic Anomaly (SAA) detection and
          reporting. See the <a class="link" href="PlanetographicRegion.html" title="PlanetographicRegion">PlanetographicRegion</a> Resource
          section for details. See also</p><p><code class="filename">samples/Ex_R2022a_PlanetographicRegion.script</code></p><p></p></li><li class="listitem"><p>Updated library usage to the new CSPICE 067 release from
          JPL</p></li><li class="listitem"><p>Two Line Element (TLE) modeling using the SPICE implementation
          of the SGP4 propagator in SPICE version 67. See</p><p><code class="filename">samples/Ex_R2022a_TLE_Propagation.script</code></p></li></ul></div><div class="section"><div class="titlepage"><div><div><h5 class="title"><a name="N30ABC"></a>Beta- and Alpha-level Capabilities</h5></div></div></div><p>These are new features that have not yet undergone full testing
        and may have bugs. To access these features, you may need to edit the
        <code class="filename">bin/gmat_startup_file.txt</code> file to load the
        desired alpha plug-in and/or activate features only available in
        testing mode (set RUN_MODE = TESTING in the file). These features
        should be used with caution.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Beta-level: Force modeling for multiple full field gravity
            perturbations during propagation and force reporting. (Multi-body
            spherical harmonics). See</p><p><code class="filename">samples/AlphaAndBetaFeatures/Ex_R2022a_Beta_MultibodySphericalHarmonicGravity.script</code></p><p></p></li><li class="listitem"><p>Beta-level support for selected torque modeling and
            intrusion detection capabilities, focussed on geosynchronous
            orbits. </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>Beta-level: Modeling of thruster, gravity gradient,
                  and plate based solar radiation pressure torques. See the
                  <a class="link" href="CalculationParameters.html" title="Calculation Parameters">Calculation
                  Parameters</a> section for details.</p></li><li class="listitem"><p>Beta-level: Addition of center of mass and moment of
                  inertia to spacecraft models. See the <a class="link" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties">Spacecraft Ballistic/Mass
                  Properties</a> section for details.</p></li><li class="listitem"><p>Beta-level: Intrusion detection models, used to
                  determine when the Sun or Moon enters a sensor's field of
                  view. See the <a class="link" href="IntrusionLocator.html" title="IntrusionLocator">Intrusion
                  Locator</a> section for more details.</p></li></ul></div></li><li class="listitem"><p>Alpha Level Capability: Sample script that inputs a s/c
            ephemeris and uses an optimization process to output a Two Line
            Element (TLE). See</p><p><code class="filename">samples/AlphaAndBetaFeatures/R2022a_TLEWriterSample/CreateTLEFromEphemeris.script</code></p><p><code class="filename">samples/AlphaAndBetaFeatures/R2022a_TLEWriterSample/CreateTLEFromSPKEphemeris.script</code></p><p></p></li><li class="listitem"><p>Alpha Level Capability: GMAT's
            <span class="guilabel">ForceModel</span> for a given
            <span class="guilabel">Spacecraft</span> can be either replaced or
            augmented using a python function. See</p><p><code class="filename">samples/AlphaAndBetaFeatures/Ex_R2022a_ExternalForceModel.script</code></p><p></p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30AFD"></a>Navigation and Orbit Determination (OD)</h4></div></div></div><p>The following navigation and orbit determination capabilities were
      developed for R2022a:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The <a class="xref" href="ExtendedKalmanFilter.html" title="ExtendedKalmanFilter"><span class="refentrytitle">ExtendedKalmanFilter</span></a> and <a class="xref" href="Smoother.html" title="Smoother"><span class="refentrytitle">Smoother</span></a> (EKFS) resources are now operational
            (no longer alpha/beta level). For an example of using the EKFS on
            the GPS point solution data type, see</p><p><code class="filename">samples/Navigation/Ex_R2022a_FilterSmoother_GpsPosVec.script</code></p><p></p><p>As with all new releases, missions that use GMAT&rsquo;s Batch
            and/or Extended Kalman Filter Smoother (EKFS) capability should
            perform a baseline set of regression/performance tests prior to
            using it for operational purposes.</p></li><li class="listitem"><p>The estimation subsystem now supports a (no longer Beta)
            N-Plate area model for solar radiation pressure (SRP). The user
            may configure a detailed multi-plate area model for SRP modeling
            for both prediction and estimation. The model includes specular
            and diffuse reflectivity, support for moving panels, like solar
            arrays, and estimation of one or more SRP correction scale
            factors. See the <a class="xref" href="Plate.html" title="Plate"><span class="refentrytitle">Plate</span></a>
            resource for more details on activating and configuring this
            model. See also</p><p><code class="filename">samples/Navigation/Ex_R2022a_Estimate_NPlateSRP_AreaCoefficient.script</code></p><p></p></li><li class="listitem"><p>A new covariance propagation capability, with optional State
            Noise Compensation (SNC) process noise, using the
            <span class="guilabel">Covariance</span> parameter of the <a class="link" href="Propagate.html" title="Propagate">Propagate</a> command. See</p><p><code class="filename">samples/Ex_R2022a_Propagate_Covariance.script</code></p><p></p></li><li class="listitem"><p>Sample scripts, implementing an Initial Orbit Determination
            (IOD) routine from Vallado using GMAT's python interface.
            See</p><p><code class="filename">samples/Ex_R2022a_IOD.script</code></p><p></p></li><li class="listitem"><p>The Range, Doppler, DSN_SeqRange, and DSN_TCP measurement
            types have been enhanced to support inter-spacecraft (sometimes
            called "crosslink") tracking. These measurement types may now be
            used to model range and Doppler measurements between on-orbit
            spacecraft. See <a class="xref" href="TrackingDataTypes.html#TrackingDataTypes_InterSpacecraftTracking" title="Inter-spacecraft Tracking Measurements">the section called &ldquo;Inter-spacecraft Tracking Measurements&rdquo;</a> and</p><p><code class="filename">samples/Tut_Inter_Spacecraft_Tracking.script</code></p><p></p></li><li class="listitem"><p>Multiple simultaneous spacecraft estimation in the
            <span class="guilabel">BatchEstimator</span>. A single batch estimator
            instance may be used to estimate multiple spacecraft in a single
            estimation run. This capability is appropriate for spacecraft that
            are coupled in some fashion, either through use of
            inter-spacecraft tracking or estimation of biases on shared
            tracking resources. See</p><p><code class="filename">samples/Navigation/Ex_R2022a_MultiSpacecraft_Simultaneous_BatchEstimation.script</code></p><p></p></li><li class="listitem"><p>Use of DSN TRK-2-23 ionosphere and troposphere media
            corrections for DSN tracking data. See <a class="xref" href="GroundStation.html#GroundStation_TRK-2-23" title="TRK-2-23 Model Implementation">the section called &ldquo;TRK-2-23 Model Implementation&rdquo;</a>.</p></li><li class="listitem"><p>Batch Estimator estimation of measurement biases "by pass."
            This capability allows the user to estimate multiple segmented
            measurement biases for a single station across the batch estimator
            arc. This feature is not implemented for the Extended Kalman
            Filter. See <a class="xref" href="ErrorModel.html#ErrorModel_Remarks_PassBiases" title="Pass-dependent Bias Estimation">the section called &ldquo;Pass-dependent Bias Estimation&rdquo;</a></p></li><li class="listitem"><p>Polynomial thrust angle estimation for finite maneuvers.
            Errors in the nominal direction of finite thrust provided by a
            <span class="guilabel">ThrustHistoryFile</span> can be estimated as a pair
            of angles. These angles can be modeled as constant or as a
            polynomial in time. See <a class="xref" href="ThrustSegment.html#ThrustSegment_Remarks_ThrustAngleEstimation" title="Overview of Thrust Angle Corrections">the section called &ldquo;Overview of Thrust Angle Corrections&rdquo;</a>. See
            also</p><p><code class="filename">samples/Navigation/Ex_R2022a_Estimate_ThrustAngles.script</code></p><p></p></li><li class="listitem"><p>Use of ephemeris propagators in the
            <span class="guilabel">Simulator</span> and
            <span class="guilabel">BatchEstimator</span>. An ephemeris file may be used
            as the orbit source for simulating measurement data or in a
            single-iteration "observed minus computed" batch estimator run.
            State estimation is not possible for spacecraft using an ephemeris
            propagator. Use of an ephemeris propagator is not yet implemented
            for the Extended Kalman Filter. For details on configuring
            ephemeris files for use as propagators, see <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a></p></li><li class="listitem"><p>Ability to run the <span class="guilabel">BatchEstimator</span> in a
            single-iteration "observed minus computed" configuration. In this
            mode, the batch estimator runs a single iteration, computing and
            reporting residuals with respect to the initial state and does not
            attempt state estimation. See the SolveMode option to the <a class="xref" href="RunEstimator.html" title="RunEstimator"><span class="refentrytitle">RunEstimator</span></a> command.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30B75"></a>Scripting and API Interface</h4></div></div></div><p>The following Scripting and API Interface improvements were
      implemented in R2022a.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>The API capability is no longer a Beta feature and is now
          operational. For sample scripts, see the <code class="filename">api</code>
          directory. For a complete description of the API capability, see the
          API UG below.</p><p><code class="filename">docs/GMAT_API_UsersGuide.pdf</code></p><p></p></li><li class="listitem"><p>The python API, which allows the user to call GMAT from a
          python routine, now supports multiple versions of Python 3.</p></li><li class="listitem"><p>The python interface plugin, which allows a user to call a
          python routine from within a GMAT script, now supports multiple
          versions of Python 3.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30B8C"></a>Optimal Control</h4></div></div></div><div class="section"><div class="titlepage"><div><div><h5 class="title"><a name="N30B8F"></a>Beta Capabilities</h5></div></div></div><p>The GMAT Optimal Control capability remains a Beta feature for
        R2022a. The Optimal Control improvements for R2022a include the
        following items.</p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Beta-level: Additional metadata describing optimizer
            performance in CSALT output optimal control history files</p></li><li class="listitem"><p>Beta-level: Improved wrapper around CSALT nonlinear
            programming (NLP) solver to save feasible-but-not-optimal
            solutions</p></li><li class="listitem"><p>Beta-level: Added new initial guess types to CSALT based on
            feasible-but-not-optimal solutions from previous mesh refinement
            iterations</p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30B9F"></a>OpenFramesInterface Visualizations</h4></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>The OFI continues to be the primary 3D visualization component in GMAT.
        <span class="guilabel">OrbitView</span> will continue to be supported for
        backward compatibility purposes but will only be modified for critical
        bug fixes.</p></div><p>The following visualization improvements were implemented in the
      OpenFramesInterface plugin in R2022a. The complete set of documentation for OFI
      is available at
      <a class="link" href="https://gitlab.com/EmergentSpaceTechnologies/OpenFramesInterface/-/wikis/home" target="_top">the OFI Wiki</a>
      </p><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>Added support for high-fidelity celestial body terrain and imagery using
          onling sources on Windows and Linux (not yet supported on macOS). See
          <code class="filename">samples/NeedOpenFramesInterface/Ex_R2022a_OFI_HiFiEarthMoon.script</code>
          for an example of using online sources for high-fidelity Earth terrain and
          imagery, and high-fidelity Moon imagery.</p></li><li class="listitem"><p>Added support for using the DXF 3D format for spacecraft and celestial bodies.</p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30BB9"></a>Other Improvements</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>GMAT R2022a includes a basic script-level debugger accessible in
        the GMAT GUI. This new feature lets the user set a <a class="xref" href="Breakpoint.html" title="Breakpoint"><span class="refentrytitle">Breakpoint</span></a> in their mission control sequence. When the
        script is run, GMAT pauses at the Breakpoint and opens a panel showing
        all of the objects defined in the run. The user can then examine each
        object, step to the next command in the sequence and see that
        command's effect on the objects, continue execution of the run, or end
        the run.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30BC4"></a>Compatibility Changes</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The <span class="guilabel">ExtendedKalmanFilter</span>
        <span class="guilabel">ProcessNoiseTimeStep</span> option has been moved to the
        <span class="guilabel">ProcessNoise</span> resource and renamed to
        <span class="guilabel">UpdateTimeStep</span>. The ProcessNoiseTimeStep option
        on the ExtendedKalmanFilter resource will continue to function in the
        current release. When both
        <span class="guilabel">ProcessNoiseModel.UpdateTimeStep</span> and
        <span class="guilabel">ExtendedKalmanFilter.ProcessNoiseTimeStep</span> have a
        non-zero value, the
        <span class="guilabel">ExtendedKalmanFilter.ProcessNoiseTimeStep</span>
        parameter will be used.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N30BE0"></a>Fixed &amp; Known Issues</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30BE3"></a>Fixed Issues</h4></div></div></div><p>Over 100 high priority bugs were closed in this release. See the
      <a class="link" href="https://gmat.atlassian.net/issues/?filter=14939" target="_top">"Critical
      Issues Fixed in R2022a" report</a> for details. Some significant
      fixes in R2022a include:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td>GMT-7742</td><td>Incorrect handling of DSN_SeqRange transmit frequency
              when not using a ramp table</td></tr></tbody></table></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N30C01"></a>Known Issues</h4></div></div></div><p>See the <a class="link" href="https://gmat.atlassian.net/issues/?filter=14941" target="_top">"All Known
      Critical Issues for R2022a" report</a> for a list of all known major
      issues in R2022a.</p><p>There are several known issues in this release that we consider to
      be significant:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td>GMT-7768</td><td>When using an ephem propagator for Moon-centered OD,
                lunar occultations, in the Simulator resource, are not
                accounted for properly</td></tr></tbody></table></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ReleaseNotes.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ReleaseNotes.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReleaseNotesR2020a.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Release Notes&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;GMAT R2020a Release Notes</td></tr></table></div></body></html>