<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>GroundStation</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="Formation.html" title="Formation"><link rel="next" href="Imager.html" title="Imager"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">GroundStation</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Formation.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Imager.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="GroundStation"></a><div class="titlepage"></div><a name="N1A936" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">GroundStation</span></h2><p><span class="guilabel">GroundStation</span> &mdash; A ground station model.</p></div><div class="refsection"><a name="N1A948"></a><h2>Description</h2><p>A <span class="guilabel">GroundStation</span> models a facility fixed to the
    surface of a <span class="guilabel">CelestialBody</span>. There are several state
    representations available for defining the location of a ground station
    including Cartesian and spherical. This resource cannot be modified in the
    mission sequence.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="ContactLocator.html" title="ContactLocator"><span class="refentrytitle">ContactLocator</span></a>, <a class="xref" href="CoordinateSystem.html" title="CoordinateSystem"><span class="refentrytitle">CoordinateSystem</span></a>, <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a></p></div><div class="refsection"><a name="GroundStation_Resource_Fields"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">AddHardware</span></td><td><p>List of all <span class="guilabel">Transmitter</span>,
            <span class="guilabel">Receiver</span>, and <span class="guilabel">Antenna</span>
            hardware used by ground station</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Object Array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Each element in the list has to be a valid
                    <span class="guilabel">Transmitter</span>,
                    <span class="guilabel">Receiver</span>, or
                    <span class="guilabel">Antenna</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>None</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Altitude</span></td><td><p>The altitude of the station with respect to the
            <span class="guilabel">HorizonReference</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-&infin; &lt; Real &lt; &infin;</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>km</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CentralBody</span></td><td><p>The central body of the
            <span class="guilabel">GroundStation</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Earth</span>. Configured celestial
                    body.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Earth</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DataSource</span></td><td><p>Source of where to get
            <span class="guilabel">Temperature</span>, <span class="guilabel">Pressure</span>,
            <span class="guilabel">Humidity</span>, and
            <span class="guilabel">MinimumElevationAngle</span>. If the value is
            Constant, then the values of these parameters, as set in the
            <span class="guilabel">GroundStation</span> resource, remain constant for
            all relevant measurements. Currently, the value of Constant is the
            only allowed value. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Constant</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Constant</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ErrorModels</span></td><td><p>User-defined list of <span class="guilabel">ErrorModel</span>
            objects that describe the measurement error models used for this
            <span class="guilabel">GroundStation</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>StringList</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any valid user-defined
                    <span class="guilabel">ErrorModel</span> resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HorizonMaskFileName</span></td><td><p>Path to an external file specifying angle pairs that
            describe an orientation-dependent horizon mask profile. The mask
            is only used by the <span class="guilabel">ContactLocator</span> resource
            and has no effect on estimation or simulation. See Remarks below
            for more details on the contents and formatting of this
            file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Path to an existing text mask file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">None</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">HorizonReference</span></td><td><p>The system used for the horizon.
            <span class="guilabel">Sphere</span> is equivalent to Geocentric,
            <span class="guilabel">Ellipsoid</span> is equivalent to
            Geodetic.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Sphere, Ellipsoid</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">Sphere</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Humidity</span></td><td><p>Humidity at ground station used to calculate
            tropospheric correction for the HopfieldSaastamoinen model. GMAT
            only uses this value if <span class="guilabel">DataSource</span> is set to
            Constant. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">0.0 &lt;= Real
                    &lt;=100.0</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">55</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>percentage</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Id</span></td><td><p>Id of the <span class="guilabel">GroundStation</span> used in
            simulation and estimation</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>May contain letters, integers, dashes,
                    underscores</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set,</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>StationId</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">IonosphereModel</span></td><td><p>Specification of ionospheric model used in the light
            time calculations.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">'None',
                    'IRI2007','TRK-2-23'</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">'None'</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Latitude</span></td><td><p>The latitude of the station with respect to
            <span class="guilabel">HorizonReference</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-90 &lt; Real &lt; 90</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg.</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Location1</span></td><td><p>The first component of the
            <span class="guilabel">GroundStation</span> location. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Cartesian</span>,
            <span class="guilabel">Location1</span> is the x-component of station
            location in the body-fixed system. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Spherical</span>
            or <span class="guilabel">Elliposoid</span>, <span class="guilabel">Location1</span>
            is the <span class="guilabel">Latitude</span> (deg.) of the
            <span class="guilabel">GroundStation</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-&infin; &lt; Real &lt; &infin; for
                    <span class="guilabel">Cartesian</span>, See
                    <span class="guilabel">Longitude</span>,
                    <span class="guilabel">Latitude</span>,
                    <span class="guilabel">Altitude</span> for others.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>6378.1363</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>see description</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Location2</span></td><td><p>The second component of the
            <span class="guilabel">GroundStation</span> location. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Cartesian</span>,
            <span class="guilabel">Location2</span> is the y-component of station
            location in the body-fixed system. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Spherical</span>
            or <span class="guilabel">Ellipsoid</span>, <span class="guilabel">Location2</span>
            is the <span class="guilabel">Longitude</span> (deg.) of the
            <span class="guilabel">GroundStation</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-&infin; &lt; Real &lt; &infin; for
                    <span class="guilabel">Cartesian</span>, See
                    <span class="guilabel">Longitude</span>,
                    <span class="guilabel">Latitude</span>,
                    <span class="guilabel">Altitude</span> for others.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>see description</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Location3</span></td><td><p>The third component of the
            <span class="guilabel">GroundStation</span> location. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Cartesian</span>,
            <span class="guilabel">Location3</span> is the z-component of station
            location in the body-fixed system. When
            <span class="guilabel">StateType</span> is <span class="guilabel">Spherical</span>
            or <span class="guilabel">Elliposoid</span>, <span class="guilabel">Location3</span>
            is the height (km) of the <span class="guilabel">GroundStation</span> above
            the reference shape.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Reals</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-&infin; &lt; Real &lt; &infin; for
                    <span class="guilabel">Cartesian</span>, See
                    <span class="guilabel">Longitude</span>,
                    <span class="guilabel">Latitude</span>,
                    <span class="guilabel">Altitude</span> for others.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set,</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>see description</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Longitude</span></td><td><p>The longitude of the station.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>value &gt;=0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg.</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MinimumElevationAngle</span></td><td><p>Minimum elevation angle constraint for use with
            <span class="guilabel">ContactLocator</span>. If the ground station also
            has an Antenna with a <span class="guilabel">FieldOfView</span> mask
            attached, times computed by the contact locator are restricted to
            those satisfying both the minimum elevation angle criteria and the
            field-of-view mask simultaneously.</p><p>For tracking data
            measurement and estimation, this is minimum allowed elevation
            angle for the signal transmitted from spacecraft to ground
            station. During simulation, measurements are only generated if the
            spacecraft elevation from the ground station exceeds this value.
            During estimation, measurements must exceed this value to be
            admitted for processing by the estimator. </p><p>GMAT only
            uses this value if <span class="guilabel">DataSource</span> is set to
            Constant. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>-90 &le; <span class="guilabel">MinimumElevationAngle</span> &le;
                    90</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>7</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>deg</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">OrbitColor</span></td><td><p>Allows you to select available colors for a
            user-defined <span class="guilabel">GroundStation</span>. The
            <span class="guilabel">GroundStation</span> object is drawn on a
            spacecraft's ground track plot created by
            <span class="guilabel">GroundTrackPlot</span> 2D graphics display resource.
            The colors can be identified through a string or an integer array.
            For example: Setting groundstation's color to red can be done in
            following two ways: <code class="literal">GroundStation.OrbitColor =
            Red</code> or <code class="literal">GroundStation.OrbitColor = [255 0
            0]</code>. This field can be modified in the Mission Sequence
            as well.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer Array or String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any color available from the Orbit Color Picker in
                    GUI. Valid predefined color name or RGB triplet value
                    between 0 and 255.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Thistle</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Pressure</span></td><td><p>Air pressure at ground station used to calculate
            tropospheric correction for the HopfieldSaastamoinen model. GMAT
            only uses this value if <span class="guilabel">DataSource</span> is set to
            Constant. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Real &gt;0.0</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">1013.5</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>hPa</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">StateType</span></td><td><p>The type of state used to define the location of the
            ground station.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Cartesian</span>,
                    <span class="guilabel">Spherical</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Cartesian</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SpiceFrameId</span></td><td><p>The station's SPICE frame ID. Note this field does
            not have a default, and is not saved to script, unless it is set
            to a specific allowed value. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String or Integer</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid SPICE frame ID (text or numeric). The
                    convention for stations is '399xyz', where 'xyz' are
                    integers mapped to the station. For example, DSN station
                    'DSS-66' has Id '399066'.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>No default.</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TargetColor</span></td><td><p>Allows you to select available colors for a
            user-defined <span class="guilabel">GroundStation</span> object during
            iterative processes such as Differential Correction or
            Optimization. The target color can be identified through a string
            or an integer array. For example: Setting groundstation's target
            color to yellow color can be done in following two ways:
            <code class="literal">GroundStation.TargetColor = Yellow</code> or
            <code class="literal">GroundStation.TargetColor = [255 255 0]</code>. This
            field can be modified in the Mission Sequence as well.</p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Integer Array or String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any color available from the Orbit Color Picker in
                    GUI. Valid predefined color name or RGB triplet value
                    between 0 and 255.</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>DarkGray</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Temperature</span></td><td><p>Air temperature at ground station used to calculate
            tropospheric correction for the HopfieldSaastamoinen model. GMAT
            only uses this value if <span class="guilabel">DataSource</span> is set to
            Constant. </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Real &gt;0.0</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">295.1</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kelvin</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TroposphereModel</span></td><td><p>Specification of tropospheric model used in the light
            time calculations.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">'None', 'HopfieldSaastamoinen',
                    'Marini','TRK-2-23'</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">'None'</span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1AE6D"></a><h2>GUI</h2><p>To create a <span class="guilabel">GroundSation</span>, starting from the
    <span class="guilabel">Resource Tree</span>:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Right-click the <span class="guilabel">GroundStation</span> folder and
        select <span class="guilabel">Add Ground Station</span>.</p></li><li class="listitem"><p>Double-click <span class="guilabel">GroundStation1</span>.</p></li></ol></div><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_GroundStation_Default2.png" align="middle" height="430"></td></tr></table></div></div><p>You can set the ground station location in several state
    representations. The <span class="guilabel">Cartesian</span> representation is
    illustrated above. To set the <span class="guilabel">Longitude</span>,
    <span class="guilabel">Latitude</span>, and <span class="guilabel">Altitude</span> to 45
    deg., 270 deg., and 0.1 km respectively, with respect to the reference
    ellipsoid:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>In the <span class="guilabel">StateType</span> menu, select
        <span class="guilabel">Spherical</span>.</p></li><li class="listitem"><p>In the <span class="guilabel">HorizonReference</span> menu, select
        <span class="guilabel">Ellipsoid.</span></p></li><li class="listitem"><p>In the <span class="guilabel">Latitude</span> text box, type
        <strong class="userinput"><code>45</code></strong>.</p></li><li class="listitem"><p>In the <span class="guilabel">Longitude</span> text box, type
        <strong class="userinput"><code>270</code></strong>.</p></li><li class="listitem"><p>In the <span class="guilabel">Altitude</span> text box, type
        <strong class="userinput"><code>0.1</code></strong>.</p></li></ol></div><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_GroundStation_Ellipsoid2.png" align="middle" height="430"></td></tr></table></div></div></div><div class="refsection"><a name="N1AED7"></a><h2>Remarks</h2><p>The <span class="guilabel">GroundStation</span> model allows you to configure
    a facility by defining the location in body-fixed coordinates using one of
    several state representations. GMAT supports
    <span class="guilabel">Cartesian</span>, <span class="guilabel">Sphere</span>, and
    <span class="guilabel">Ellipsoid</span> representations and examples below show how
    to configure a <span class="guilabel">GroundStation</span> in each representation.
    When using the <span class="guilabel">Ellipsoid</span> model or
    <span class="guilabel">Sphere</span> representations, GMAT uses the physical
    properties - flattening and radius for example - defined on the
    <span class="guilabel">CelestialBody</span> resource to convert to Cartesian
    coordinates based upon a two-axis ellipsoid model.</p><div class="refsection"><a name="N1AEF4"></a><h3>Ground Station Masking File</h3><p>The user may specify an orientation-dependent masking file for use
      with the <span class="guilabel">ConctactLocator</span> resource. This mask is
      typically used to describe terrain, buildings, or other obstructions in
      the vicinity of the antenna which may block the signal in certain
      directions. The mask file consists of a keyword describing the type of
      angles in the file and two columns of data representing the angle pairs
      that describe the mask as shown in the sample below.</p><pre class="programlisting"><code class="code">AzimuthElevationAngles

  0.0   7.6
  3.0   6.5
  8.0   3.1
 38.0   6.6
 45.0   5.5
 58.0   8.4
 70.0   7.6
 77.0   9.2
... etc ...
360.0   7.6
</code></pre><p>The allowed angle types are
      <code class="literal">AzimuthElevationAngles</code>, to specify that the file
      contains azimuth and elevation angle pairs, or
      <code class="literal">ClockConeAngles</code>, to specify that the file contains
      clock and cone angles.</p><p>GMAT can also read STK-format "aem" mask files, and an STK "aem"
      file may be assigned for use on the
      <span class="guilabel">HorizonMaskFileName</span> parameter.</p></div><div class="refsection"><a name="N1AF0C"></a><h3>Setting Colors On a Ground Station Facility</h3><p>GMAT allows you to set colors on a ground station facility that
      you create. The <span class="guilabel">GroundStations</span> are drawn on the
      <span class="guilabel">GroundTrackPlot</span> 2D graphics display. The
      <span class="guilabel">GroundStation</span> object's
      <span class="guilabel">OrbitColor</span> and <span class="guilabel">TargetColor</span>
      fields are used to set colors on a ground station facility. See the
      <a class="xref" href="GroundStation.html#GroundStation_Resource_Fields" title="Fields">Fields</a>
      section to read more about these two fields. Also See <a class="xref" href="Color.html" title="Color"><span class="refentrytitle">Color</span></a> documentation for discussion and examples on how to
      set colors on a ground station facility.</p></div><div class="refsection"><a name="N1AF27"></a><h3>Marini Troposphere Model Data File</h3><p>The Marini troposphere model utilizes a data file which contains
      monthly mean values for the model calculation for different locations on
      the Earth's surface. This data file's location is specified by the
      <code class="literal">MARINI_TROPO_FILE</code> property in the startup file. Each
      line in the data file contains a latitude longitude pair, followed by 12
      values, one for each month of the year. Each value in the data file
      combines both the refractivity and a scale height factor into a single
      integer, which are both used in the Marini model. The two rightmost
      digits are used to obtain the scale height, while the remaining digits
      to the left represent the refractivity. The digits used for the scale
      height have the decimal point placed between the two digits, while the
      refractivity values have the decimal point placed at the right of its
      rightmost digit. For example, a value in the data file of 37068 would
      correspond to a refractivity of 370, and a scale height of 6.8.</p><p>The line in the data file is selected for use if it is within one
      degree of latitude and one degree of longitude of the ground station
      location. The column is then selected based on the month of the year. If
      the location of the ground station is within one degree of latitude and
      longitude of multiple locations in the data file, the first line is the
      one selected. If the location of the ground station is not within one
      degree of latitude and longitude of a location in the data file, a
      default value of 37068 is used instead, regardless of month. The
      latitude ranges from -90 to 90 degrees, while the longitude spans from 0
      to 360 degrees.</p></div><div class="refsection"><a name="N1AF31"></a><h3>Ionosphere Modeling for Orbit Determination</h3><p>The signal path for a tracking data measurement is bent as is
      passes through Earth's ionosphere. The magnitude of this effect depends
      on the signal frequency and diminishes rapidly at high frequencies.
      Measurements at K-band or higher frequency may often safely ignore or
      disable the ionosphere correction, but it is generally desirable to
      model ionosphere corrections for tracking at S-band or lower frequency.
      The ionosphere correction is also elevation-dependent and increases
      dramatically at low signal path elevation. At low elevation (below 5
      degrees), the computed correction is likely to be inaccurate and
      low-elevation measurements should generally be excluded from use in
      estimation.</p><p>The frequency for the ionosphere correction is not retrieved from
      tracking data and must be set on a <span class="guilabel">Transmitter</span>
      object attached to the transmit ground station. If a Transmitter object
      is not supplied, a default frequency of 2000 MHz is used.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>TDRS user and BRTS tracking employs different frequencies on the
        uplink/downlink (between the ground and TDRS) and the forward/return
        (between the TDRS and user or BRTS) legs. The uplink/downlink
        frequency for ionosphere modeling is set on the TDRS ground station
        Transmitter object. The forward/return frequency for ionosphere
        modeling is set on the BRTS ground station Transmitter object. If the
        BRTS does not have a Transmitter attached, the ground uplink frequency
        is used for computing ionosphere corrections on the forward link.
        Currently no media corrections (neither troposphere nor ionosphere)
        are included on the forward/return leg between TDRS and on-orbit
        users.</p></div></div><div class="refsection"><a name="GroundStation_TRK-2-23"></a><h3>TRK-2-23 Model Implementation</h3><p>The use of TRK-2-23 files for Tropospheric and Ionospheric
      conventions requires the user to follow certain conventions. Each
      spacecraft should have the <span class="guilabel">NAIFId</span> parameter set to
      match their spacecraft number. For each ground station using a TRK-2-23
      model, set the <span class="guilabel">Id</span> parameter to be the DSN station
      number. Additionally, each directory containing .csp and .csp.ql
      (quick-look) files for processing should be assigned to
      <span class="guilabel">Earth.DSNMediaFileDirectories</span> parameter as strings
      in an array, with each entry containing the directory path. Should a
      directory contain .csp and .csp.ql files with overlapping time entries,
      priority will be given to the data from the .csp file.</p><p>TRK-2-23 data entries include correction information for the
      following measurement types: ALL, DOPRNG, RANGE, DOPPLER, and VLBI.
      Coefficients specified as ALL can be applied to any type of measurement
      and are used for tropospheric corrections. Tropospheric corrections can
      be executed with only the seasonal.csp file, but will gain additional
      accuracy if monthly correction files that cover the span of the run are
      included. DOPRNG, RANGE, DOPPLER, and VLBI coefficients are applicable
      to ionospheric corrections. Since GMAT performs media corrections as a
      component of calculating the range measurement between two points, files
      must be of type DOPRNG or RANGE to be compatible with the GMAT
      ionospheric correction model.</p><p>Because GMAT generates Doppler measurements by first calculating
      the range then determining the Doppler shift, media corrections are not
      applied to Doppler measurements directly. For this reason, using
      TRK-2-23 files with corrections for solely DOPPLER measurements is not
      supported. VLBI is also not supported, as GMAT currently does not
      support VLBI based measurements.</p><p>Since the ionospheric entries are defined only for the narrow
      windows in which the ground station has visibility of the spacecraft,
      users should be particularly careful to use accurate initial parameters
      when implementing the model. Further specifications for the TRK-2-23
      data format can be found in Section 3 of Reference 1.</p></div></div><div class="refsection"><a name="N1AF53"></a><h2>Examples</h2><div class="informalexample"><p>Configure a <span class="guilabel">GroundStation</span> in Geodetic
      coordinates.</p><pre class="programlisting"><code class="code">Create GroundStation aGroundStation
aGroundStation.CentralBody      = Earth
aGroundStation.StateType        = Spherical
aGroundStation.HorizonReference = Ellipsoid
aGroundStation.Location1        = 60
aGroundStation.Location2        = 45
aGroundStation.Location3        = 0.01

% or alternatively

aGroundStation.Latitude  = 60
aGroundStation.Longitude = 45
aGroundStation.Altitude  = 0.01</code></pre></div><div class="informalexample"><p>Configure a <span class="guilabel">GroundStation</span> in Geocentric
      coordinates.</p><pre class="programlisting"><code class="code">Create GroundStation aGroundStation
aGroundStation.CentralBody      = Earth
aGroundStation.StateType        = Spherical
aGroundStation.HorizonReference = Sphere
aGroundStation.Location1        = 59.83308194090783
aGroundStation.Location2        = 45
aGroundStation.Location3        = -15.99424674414058

% or alternatively

aGroundStation.Latitude        = 59.83308194090783
aGroundStation.Longitude       = 45
aGroundStation.Altitude        = -15.99424674414058
</code></pre></div><div class="informalexample"><p>Configure a <span class="guilabel">GroundStation</span> in Geocentric
      coordinates.</p><pre class="programlisting"><code class="code">Create GroundStation aGroundStation
aGroundStation.CentralBody = Earth
aGroundStation.StateType   = Cartesian
aGroundStation.Location1   = 2260.697433050543
aGroundStation.Location2   = 2260.697433050542
aGroundStation.Location3   = 5500.485954732006
</code></pre></div><div class="informalexample"><p>Configure a <span class="guilabel">GroundStation</span> that, when used for
      navigation, will model how the RF signal is refracted in the
      atmosphere.</p><pre class="programlisting"><code class="code">Create GroundStation aGroundStation
aGroundStation.IonosphereModel       = 'IRI2007';
aGroundStation.TroposphereModel      = 'HopfieldSaastamoinen';

BeginMissionSequence;</code></pre></div><div class="informalexample"><p>Configure a <span class="guilabel">GroundStation</span> that, when used for
      navigation, will model how the RF signal is refracted in the atmosphere
      using TRK-2-23 data.</p><pre class="programlisting"><code class="code">Create GroundStation aSpacecraft
aSpacecraft.NAIFId       = -64;

Create GroundStation aGroundStation
aGroundStation.Id                    = '026';
aGroundStation.IonosphereModel       = 'TRK-2-23';
aGroundStation.TroposphereModel      = 'TRK-2-23';

Earth.DSNMediaFileDirectories = {'path/to/directory/Troposphere','path/to/directory/Ionosphere','path/to/directory/Additional'}

BeginMissionSequence;</code></pre></div><div class="informalexample"><p>Attach a <span class="guilabel">Transmitter</span> and
      <span class="guilabel">Receiver</span> resource to a
      <span class="guilabel">GroundStation.</span></p><pre class="programlisting"><code class="code">Create Transmitter Transmitter1
Create Receiver Receiver1

Create GroundStation aGroundStation;
aGroundStation.AddHardware = {Transmitter1, Receiver1};

BeginMissionSequence;</code></pre></div></div><div class="refsection"><a name="N1AF91"></a><h2>References</h2><div class="orderedlist"><ol class="orderedlist" type="1"><li class="listitem"><p>Machuzak, Berner, Pham and Stipanuk. <span class="emphasis"><em>TRK-2-23 Media
        Calibration Interface</em></span>. Technical Report JPL D-16765, NASA,
        2008.</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Formation.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Imager.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Formation&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Imager</td></tr></table></div></body></html>