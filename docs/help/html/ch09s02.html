<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Configure Coordinate Systems, Spacecraft, Optimizer, Propagators, Maneuvers, Variables, and Graphics</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="OptimalLunarFlyby.html" title="Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting"><link rel="prev" href="OptimalLunarFlyby.html" title="Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting"><link rel="next" href="ch09s03.html" title="Configure the Mission Sequence"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Configure Coordinate Systems, Spacecraft, Optimizer, Propagators,
    Maneuvers, Variables, and Graphics</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="OptimalLunarFlyby.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch09s03.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N12AB1"></a>Configure Coordinate Systems, Spacecraft, Optimizer, Propagators,
    Maneuvers, Variables, and Graphics</h2></div></div></div><p>For this tutorial, you&rsquo;ll need GMAT open, with a blank script editor
    open. To open a blank script editor, click the <span class="guilabel">New
    Script</span> button in the toolbar.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12AB9"></a>Create a Moon-centered Coordinate System</h3></div></div></div><p>You will need a Moon-centered
      <span class="guilabel">CoordinateSystem</span> for the lunar flyby control point
      so we begin by creating an inertial system centered at the moon. Use the
      <span class="guilabel">MJ2000Eq</span> axes for this system.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure coordinate systems
%----------------------------------------------------

Create CoordinateSystem MoonMJ2000Eq
MoonMJ2000Eq.Origin = Luna
MoonMJ2000Eq.Axes   = MJ2000Eq
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12AC8"></a>Create the Spacecraft</h3></div></div></div><p>You will need 5 <span class="guilabel">Spacecraft</span> for this mission
      design. The epoch and state information will be set in the mission
      sequence and here we only need to configure coordinate systems for the
      <span class="guilabel">Spacecraft</span>. The <span class="guilabel">Spacecraft</span>
      named <span class="guilabel">satTOI</span> models the transfer orbit through the
      first patch point. Use the <span class="guilabel">EarthMJ200Eq</span>
      <span class="guilabel">CoordinateSystem</span> for <span class="guilabel">satTOI</span>.
      <span class="guilabel">satFlyBy_Forward</span> and
      <span class="guilabel">satFlyBy_Backward</span> model the trajectory from the
      flyby backwards to patch point 1 and forward to patch point 2
      respectively. Use the <span class="guilabel">MoonMJ2000Eq</span>
      <span class="guilabel">CoordinateSystem</span> for
      <span class="guilabel">satFlyBy_Forward</span> and
      <span class="guilabel">satFlyBy_Backward</span>. Similarly,
      <span class="guilabel">satMOI_Forward</span> and
      <span class="guilabel">satMOI_Backward</span> model the trajectory on either side
      of the MOI maneuver. Use the <span class="guilabel">EarthMJ2000Eq</span>
      <span class="guilabel">CoordinateSystem</span> for
      <span class="guilabel">satMOI_Forward</span> and
      <span class="guilabel">satMOI_Backward.</span></p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure spacecraft
%----------------------------------------------------

%  The TOI control point
Create Spacecraft satTOI
satTOI.DateFormat                  = TAIModJulian
satTOI.CoordinateSystem            = EarthMJ2000Eq

%  Flyby control point
Create Spacecraft satFlyBy_Forward
satFlyBy_Forward.DateFormat        = TAIModJulian
satFlyBy_Forward.CoordinateSystem  = MoonMJ2000Eq

%  Flyby control point
Create Spacecraft satFlyBy_Backward
satFlyBy_Backward.DateFormat       = TAIModJulian
satFlyBy_Backward.CoordinateSystem = MoonMJ2000Eq

% MOI control point
Create Spacecraft satMOI_Backward
satMOI_Backward.DateFormat         = TAIModJulian
satMOI_Backward.CoordinateSystem   = EarthMJ2000Eq

% MOI control point
Create Spacecraft satMOI_Forward
satMOI_Forward.DateFormat          = TAIModJulian
satMOI_Forward.CoordinateSystem    = EarthMJ2000Eq
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B09"></a>Create the Propagators</h3></div></div></div><p>Modeling the motion of the spacecraft when near the earth and near
      the moon requires two propagators; one Earth-centered, and one
      Moon-centered. The script below configures the
      <span class="guilabel">ForceModel</span> named
      <span class="guilabel">NearEarthForceModel</span> to use JGM-2 8x8 harmonic
      gravity model, with point mass perturbations from the Sun and Moon, and
      the SRP perturbation. The <span class="guilabel">ForceModel</span> named
      <span class="guilabel">NearMoonForceModel</span> is similar but uses point mass
      gravity for all bodies. Note that the integrators are configured for
      performance and not for accuracy to improve run times for the tutorial.
      There are times when integrator accuracy can cause issues with optimizer
      performance due to noise in the numerical solutions.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure propagators and force models
%----------------------------------------------------

Create ForceModel NearEarthForceModel
NearEarthForceModel.CentralBody               = Earth
NearEarthForceModel.PrimaryBodies             = {Earth}
NearEarthForceModel.PointMasses               = {Luna, Sun}
NearEarthForceModel.SRP                       = On
NearEarthForceModel.GravityField.Earth.Degree = 8
NearEarthForceModel.GravityField.Earth.Order  = 8

Create ForceModel NearMoonForceModel
NearMoonForceModel.CentralBody                = Luna
NearMoonForceModel.PointMasses                = {Luna, Earth, Sun}
NearMoonForceModel.Drag                       = None
NearMoonForceModel.SRP                        = On
</code></pre></div><div class="informalexample"><pre class="programlisting"><code class="code">Create Propagator NearEarthProp
NearEarthProp.FM = NearEarthForceModel
NearEarthProp.Type                     = PrinceDormand78
NearEarthProp.InitialStepSize          = 60
NearEarthProp.Accuracy                 = 1e-11
NearEarthProp.MinStep                  = 0.0
NearEarthProp.MaxStep                  = 86400

Create Propagator NearMoonProp
NearMoonProp.FM                        = NearMoonForceModel
NearMoonProp.Type                      = PrinceDormand78
NearMoonProp.InitialStepSize           = 60
NearMoonProp.Accuracy                  = 1e-11
NearMoonProp.MinStep                   = 0
NearMoonProp.MaxStep                   = 86400
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B22"></a>Create the Maneuvers</h3></div></div></div><p>We will require one <span class="guilabel">ImpulsiveBurn</span> to insert
      the spacecraft into the mission orbit. Define the maneuver as
      <span class="guilabel">MOI</span> and configure the maneuver to be applied in the
      <span class="guilabel">VNB</span> (Earth-referenced)
      <span class="guilabel">Axes</span>.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure maneuvers
%----------------------------------------------------

Create ImpulsiveBurn MOI
MOI.CoordinateSystem   = Local
MOI.Origin             = Earth
MOI.Axes               = VNB
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B37"></a>Create the User Variables</h3></div></div></div><p>The optimization sequence requires many user variables that will
      be discussed in detail later in the tutorial when we define those
      variables. For now, we simply create the variables (which initializes
      them to zero). The naming convention used here is that variables used to
      define constraint values begin with &ldquo;con&rdquo;. For example, the variable
      used to define the constraint on TOI inclination is called
      <span class="guilabel">conTOIInclination</span>. Variables beginning with &ldquo;error&rdquo;
      are used to compute constraint variances. For example, the variable used
      to define the error in MOI inclination is called
      <span class="guilabel">errorTOIInclination</span>.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Create user data: variables, arrays, strings
%----------------------------------------------------

%  Variables for defining constraint values
Create Variable conTOIPeriapsis conMOIPeriapsis conTOIInclination
Create Variable conLunarPeriapsis conMOIApoapsis conMOIInclination
Create Variable launchRdotV finalPeriapsisValue

%  Variables for computing constraint violations
Create Variable errorPos1 errorVel1 errorPos2 errorVel2 
Create Variable errorMOIRadApo errorMOIRadPer errorMOIInclination 

%  Variables for managing time calculations
Create Variable patchTwoElapsedDays patchOneEpoch patchTwoEpoch refEpoch
Create Variable toiEpoch flybyEpoch moiEpoch patchOneElapsedDays
Create Variable deltaTimeFlyBy

%  Constants and miscellaneous variables
Create Variable earthRadius earthMu launchEnergy launchVehicleDeltaV
Create Variable toiDeltaV launchCircularVelocity loopIdx Cost
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B46"></a>Create the Optimizer</h3></div></div></div><p>The script below creates a <span class="guilabel">VF13ad</span> optimizer
      provided in the Harwell Subroutine Library. <span class="guilabel">VF13ad</span>
      is an Sequential Quadratic Programming (SQP) optimizer that uses a line
      search method to solve the Non-linear Programming Problem (NLP). Here we
      configure the optimizer to use forward differencing to compute the
      derivatives, define the maximum iterations to 200, and define
      convergence tolerances.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure solvers
%----------------------------------------------------

Create VF13ad NLPOpt
NLPOpt.ShowProgress          = true
NLPOpt.ReportStyle           = Normal
NLPOpt.ReportFile            = 'VF13adVF13ad1.data'
NLPOpt.MaximumIterations     = 200
NLPOpt.Tolerance             = 1e-004
NLPOpt.UseCentralDifferences = false
NLPOpt.FeasibilityTolerance  = 0.1
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B55"></a>Create the 3-D Graphics</h3></div></div></div><p>You will need an <span class="guilabel">OrbitView</span> 3-D graphics
      window to visualize the trajectory and especially the initial guess.
      Below we configure an orbit view to view the entire trajectory in the
      <span class="guilabel">EarthMJ2000Eq</span> coordinate system. Note that we must
      add all five <span class="guilabel">Spacecraft</span> to the
      <span class="guilabel">OrbitView</span>. Updating an
      <span class="guilabel">OrbitView</span> during optimization can dramatically slow
      down the optimization process and they are best used to check initial
      configuration and then use XY plots to track numerical progress. Later
      in the tutorial, we will toggle the <span class="guilabel">ShowPlot</span> field
      to <span class="guilabel">false</span> once we have verified the initial
      configuration is correct.</p><div class="informalexample"><pre class="programlisting"><code class="code">%----------------------------------------------------
% Configure plots, reports, etc.
%----------------------------------------------------

Create OrbitView EarthView
EarthView.ShowPlot               = true
EarthView.SolverIterations       = All
EarthView.UpperLeft              = ...
    [ 0.4960127591706539 0.00992063492063492 ];
EarthView.Size                   = ...
    [ 0.4800637958532695 0.5218253968253969 ];
EarthView.RelativeZOrder         = 501
EarthView.Add                    = ...
{satTOI, satFlyBy_Forward, satFlyBy_Backward, satMOI_Backward, ...
 Earth, Luna, satMOI_Forward}
EarthView.CoordinateSystem       = EarthMJ2000Eq
EarthView.DrawObject             = [ true true true true true]
EarthView.OrbitColor             = ...
[ 255 32768 1743054 16776960 32768 12632256 14268074 ]
EarthView.TargetColor            = ...
[ 65280 124 4227327 255 12345 9843 16711680 ];
EarthView.DataCollectFrequency   = 1
EarthView.UpdatePlotFrequency    = 50
EarthView.NumPointsToRedraw      = 300
EarthView.ViewScaleFactor        = 35
EarthView.ViewUpAxis             = X
EarthView.UseInitialView         = On
</code></pre></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N12B73"></a>Create XYPlots and Reports</h3></div></div></div><p>Below we create several <span class="guilabel">XYPlots</span> and a
      <span class="guilabel">ReportFile</span>. We will use
      <span class="guilabel">XYPlots</span> to monitor the progress of the optimizer in
      satisfying constraints. <span class="guilabel">PositionError1</span> plots the
      position error at the first patch point...
      <span class="guilabel">VelocityError2</span> plots the velocity error at the
      second patch point, and so on. <span class="guilabel">OrbitDimErrors</span> plots
      the errors in the periapsis and apoapsis radii for the mission orbit.
      When optimization is proceeding as expected, these plots should show
      errors driven to zero.</p><div class="informalexample"><pre class="programlisting"><code class="code">Create XYPlot PositionError
PositionError.SolverIterations = All
PositionError.UpperLeft        = [ 0.02318840579710145 0.4358208955223881 ];
PositionError.Size             = [ 0.4594202898550724 0.5283582089552239 ];
PositionError.RelativeZOrder   = 378
PositionError.XVariable        = loopIdx
PositionError.YVariables       = {errorPos1, errorPos2}
PositionError.ShowGrid         = true
PositionError.ShowPlot         = true

Create XYPlot VelocityError
VelocityError.SolverIterations = All
VelocityError.UpperLeft        = [ 0.02463768115942029 0.01194029850746269 ];
VelocityError.Size             = [ 0.4565217391304348 0.4208955223880597 ];
VelocityError.RelativeZOrder   = 410
VelocityError.XVariable        = loopIdx
VelocityError.YVariables       = {errorVel1, errorVel2}
VelocityError.ShowGrid         = true
VelocityError.ShowPlot         = true

Create XYPlot OrbitDimErrors
OrbitDimErrors.SolverIterations = All
OrbitDimErrors.UpperLeft      = [ 0.4960127591706539 0.5337301587301587 ];
OrbitDimErrors.Size           = [ 0.481658692185008 0.4246031746031746 ];
OrbitDimErrors.RelativeZOrder = 347
OrbitDimErrors.XVariable      = loopIdx
OrbitDimErrors.YVariables     = {errorMOIRadApo, errorMOIRadPer}
OrbitDimErrors.ShowGrid       = true
OrbitDimErrors.ShowPlot       = true

Create XYPlot IncError
IncError.SolverIterations = All
IncError.UpperLeft        = [ 0.4953586497890296 0.01306240928882438 ];
IncError.Size             = [ 0.479324894514768 0.5079825834542816 ];
IncError.RelativeZOrder   = 382
IncError.YVariables       = {errorMOIInclination}
IncError.XVariable        = loopIdx
IncError.ShowGrid         = true
IncError.ShowPlot         = true
</code></pre></div><p>Create a <span class="guilabel">ReportFile</span> to allow reporting useful
      information to a text file for review after the optimization process is
      complete.</p><div class="informalexample"><pre class="programlisting"><code class="code">Create ReportFile debugData
debugData.SolverIterations = Current
debugData.Precision        = 16
debugData.WriteHeaders     = Off
debugData.LeftJustify      = On
debugData.ZeroFill         = Off
debugData.ColumnWidth      = 20
debugData.WriteReport      = false
</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="OptimalLunarFlyby.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="OptimalLunarFlyby.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch09s03.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Chapter&nbsp;9.&nbsp;Optimal Lunar Flyby using Multiple Shooting&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Configure the Mission Sequence</td></tr></table></div></body></html>