<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Set</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch19s02.html" title="Commands"><link rel="prev" href="Report.html" title="Report"><link rel="next" href="Toggle.html" title="Toggle"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Set</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Report.html">Prev</a>&nbsp;</td><th align="center" width="60%">Commands</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Toggle.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="Set"></a><div class="titlepage"></div><a name="N252F6" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Set</span></h2><p>Set &mdash; Configure a resource from a data interface</p></div><div class="refsynopsisdiv"><h2>Script Syntax</h2><pre class="synopsis"><code class="literal">Set</code> <em class="replaceable"><code>destination</code></em> <em class="replaceable"><code>source</code></em> <code class="literal">(</code><em class="replaceable"><code>options</code></em><code class="literal">)</code></pre></div><div class="refsection"><a name="N2531A"></a><h2>Description</h2><p>The <span class="guilabel">Set</span> command retrieves data from
    <code class="code"><em class="replaceable"><code>source</code></em></code> according to
    <code class="code"><em class="replaceable"><code>options</code></em></code> and populates
    <code class="code"><em class="replaceable"><code>destination</code></em></code>. Time systems, time
    formats, state types, and coordinate systems are automatically converted
    to those required by
    <code class="code"><em class="replaceable"><code>destination</code></em></code>.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="FileInterface.html" title="FileInterface"><span class="refentrytitle">FileInterface</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a></p></div><div class="refsection"><a name="N2533C"></a><h2>Options</h2><div class="informaltable"><table border="1"><colgroup><col width="14%"><col width="86%"></colgroup><thead><tr><th>Option</th><th>Description</th></tr></thead><tbody><tr><td><code class="literal"><em class="replaceable"><code>destination</code></em></code></td><td><p>The resource to populate from the data
            source.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p><span class="guilabel">Spacecraft</span></p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any <span class="guilabel">Spacecraft</span> resource</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><code class="literal"><em class="replaceable"><code>source</code></em></code></td><td><p>The data source from which to obtain
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Accepted Data Types</span></p></td><td><p><span class="guilabel">FileInterface</span></p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>any <span class="guilabel">FileInterface</span>
                    resource</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>(None)</p></td></tr><tr><td><p><span class="term">Required</span></p></td><td><p>yes</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><code class="literal"><em class="replaceable"><code>options</code></em></code></td><td><p>Options specific to the chosen
            <code class="literal"><em class="replaceable"><code>source</code></em></code>. See the
            following sections for details.</p></td></tr></tbody></table></div><p>The following options are available when
    <code class="literal"><em class="replaceable"><code>source</code></em></code> is a
    <span class="guilabel">FileInterface</span> and the <span class="guilabel">Format</span> is
    &ldquo;<code class="literal">TVHF_ASCII</code>&rdquo;:</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="code">Data={<em class="replaceable"><code>keyword</code></em>[,
        <em class="replaceable"><code>keyword</code></em>, ...]}</code></span></dt><dd><p>Comma-separated list of values to retrieve from the file.
          Defaults to <code class="literal">'All'</code>, which retrieves all available
          elements. The available keywords are documented in the &ldquo;<a class="link" href="FileInterface.html#FileInterface_TVHF_ASCII" title="TVHF_ASCII">TVHF_ASCII</a>&rdquo; section of the
          <a class="link" href="FileInterface.html" title="FileInterface">FileInterface</a> reference.</p></dd></dl></div></div><div class="refsection"><a name="N253DD"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Command_Set_GUI.png" align="middle" height="135"></td></tr></table></div></div><p>The <span class="guilabel">Set</span> GUI is a very simple text box that lets
    you type the command directly. By default, it has no arguments, so you
    must finish the command yourself.</p></div><div class="refsection"><a name="N253EE"></a><h2>Examples</h2><div class="informalexample"><p>Read a TVHF file and use it to configure a spacecraft.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create FileInterface tvhf
tvhf.Filename = 'statevec.txt'
tvhf.Format = 'TVHF_ASCII'

BeginMissionSequence

Set aSat tvhf</code></pre></div><div class="informalexample"><p>Read a TVHF file and use it to set only the epoch and the
      Cartesian state.</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
Create FileInterface tvhf
tvhf.Filename = 'statevec.txt'
tvhf.Format = 'TVHF_ASCII'

BeginMissionSequence

Set aSat tvhf (Data = {'Epoch', 'CartesianState'})</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Report.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch19s02.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Toggle.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Report&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Toggle</td></tr></table></div></body></html>