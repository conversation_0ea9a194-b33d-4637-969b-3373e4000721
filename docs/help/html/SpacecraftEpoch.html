<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Spacecraft Epoch</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="SpacecraftBallisticMass.html" title="Spacecraft Ballistic/Mass Properties"><link rel="next" href="SpacecraftHardware.html" title="Spacecraft Hardware"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Spacecraft Epoch</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="SpacecraftBallisticMass.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SpacecraftHardware.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="SpacecraftEpoch"></a><div class="titlepage"></div><a name="N1F377" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Spacecraft Epoch</span></h2><p>Spacecraft Epoch &mdash; The spacecraft epoch</p></div><div class="refsection"><a name="N1F388"></a><h2>Description</h2><p>The epoch of a <span class="guilabel">Spacecraft </span>is the time and date
    corresponding to the specified orbit state. See the <a class="xref" href="SpacecraftOrbitState.html" title="Spacecraft Orbit State"><span class="refentrytitle">Spacecraft Orbit State</span></a> section for
    interactions between the epoch, coordinate system, and spacecraft state
    fields.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a></p><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>GMAT&rsquo;s Modified Julian Date (MJD) format differs from that of
      other software. The Modified Julian format is a constant offset from the
      full Julian date (JD):</p><div class="informalequation"><span class="mathphrase">MJD = JD - offset</span></div><p>GMAT uses a non-standard offset, as shown in the following
      table.</p><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="33%"><col width="34%"></colgroup><tbody><tr><td>Epoch Type</td><td><span class="bold"><strong>GMAT</strong></span></td><td>common</td></tr><tr><td>reference epoch</td><td><span class="bold"><strong>05 Jan 1941
              12:00:00.000</strong></span></td><td>17 Nov 1858 00:00:00.000</td></tr><tr><td>Modified Julian offset</td><td><span class="bold"><strong>2430000.0</strong></span></td><td>2400000.5</td></tr></tbody></table></div></div></div><div class="refsection"><a name="N1F3C4"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="33%"><col width="67%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">DateFormat</span></td><td><p>The time system and format of the
            <span class="guilabel">Epoch</span> field. In the GUI, this field is called
            <span class="guilabel">EpochFormat</span>.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Enumeration</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">A1ModJulian</span>,
                    <span class="guilabel">TAIModJulian</span>,
                    <span class="guilabel">UTCModJulian</span>,
                    <span class="guilabel">TTModJulian</span>,
                    <span class="guilabel">TDBModJulian</span>,
                    <span class="guilabel">A1Gregorian</span>,
                    <span class="guilabel">TAIGregorian</span>,
                    <span class="guilabel">TTGregorian</span>,
                    <span class="guilabel">UTCGregorian</span>,
                    <span class="guilabel">TDBGregorian</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set only</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="guilabel">TAIModJulian</span></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch</span></td><td><p>The time and date corresponding to the specified
            orbit state.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Time</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Gregorian: <code class="literal">04 Oct 1957
                    12:00:00.000</code> &lt;= <span class="guilabel">Epoch</span>
                    &lt;= <code class="literal">28 Feb 2100 00:00:00.000</code></p><p>Modified Julian: <strong class="userinput"><code>6116.0</code></strong> &lt;=
                    <span class="guilabel">Epoch</span> &lt;=
                    <strong class="userinput"><code>58127.5</code></strong></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set only</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">A1ModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the A.1 system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.00000039794</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Days</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.A1ModJulian</span></td><td><p> The spacecraft orbit epoch in the A.1 system and the
            Modified Julian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.00000039794</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Days</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>none</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CurrA1MJD</span></td><td><p><span class="emphasis"><em>This field has been deprecated and should
            no longer be used.</em></span></p><p>The current epoch in the
            <span class="guilabel">A1ModJulian</span> format. This field can only be
            used within the mission sequence.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Time</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><strong class="userinput"><code>6116.0</code></strong> &lt;=
                    <span class="guilabel">CurrA1MJD</span> &lt;=
                    <strong class="userinput"><code>58127.5</code></strong></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>get, set (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>converted equivalent of 21545 Modified Julian
                    (TAI)</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script only</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">A1Gregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the A.1 system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:00.034</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TAI system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:00.000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Gregorian date</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TAIModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TAI system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See<span class="guilabel"> Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TDB system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:32.184</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TDBModJulian</span></td><td><p> The <span class="guilabel">Spacecraft </span>orbit epoch in
            the TDB system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.00037249916</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TT system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:32.184</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">TTModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TT system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.0003725</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the UTC system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 11:59:28.000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">UTCModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the UTC system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get (mission sequence only)</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21544.99962962963</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.A1Gregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the A.1 system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:00.034</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TAIGregorian</span></td><td><p>The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TAI system and the Gregorian format.</p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>DefaultValue</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>01 Jan 2000 12:00:00.000</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TAIModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TAI system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch.A1ModJulian</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TDBGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TDB system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:32.184</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TDBModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span>orbit epoch in
            the TDB system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.00037249916</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TTGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the TT system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 12:00:32.184</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.TTModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span>orbit epoch in
            the TT system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21545.0003725</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1ModJulian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.UTCGregorian</span></td><td><p> The <span class="guilabel">Spacecraft</span>orbit epoch in
            the UTC system and the Gregorian format. </p> <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>01 Jan 2000 11:59:28.000</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See Epoch.A1Gregorian</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Epoch.UTCModJulian</span></td><td><p> The <span class="guilabel">Spacecraft</span> orbit epoch in
            the UTC system and the Modified Julian format. </p>
            <div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Range</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>See <span class="guilabel">Epoch</span></p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>21544.99962962963</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>See <span class="guilabel">Epoch.A1ModJulian</span></p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1F874"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_SpacecraftOrbit_Default.png" align="middle" height="531"></td></tr></table></div></div><p>A change in <span class="guilabel">EpochFormat</span> causes an immediate
    update to <span class="guilabel">Epoch</span> to reflect the chosen time system and
    format.</p></div><div class="refsection"><a name="N1F888"></a><h2>Remarks</h2><p>GMAT supports five time systems or scales and two formats:</p><div class="informaltable"><table border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td>A.1</td><td>USNO atomic time; GMAT&rsquo;s internal time system</td></tr><tr><td>TAI</td><td>International Atomic Time</td></tr><tr><td>TDB</td><td>Barycentric Dynamical Time</td></tr><tr><td>TT</td><td>Terrestrial Time</td></tr><tr><td>UTC</td><td>Coordinated Universal Time</td></tr></tbody></table></div><div class="informaltable"><table border="1"><colgroup><col width="50%"><col width="50%"></colgroup><tbody><tr><td>Gregorian</td><td><p>Text with the following format: <code class="literal">dd mmm yyyy
            HH:MM:SS.FFF</code></p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term"><code class="literal">dd</code></span></p></td><td><p>two-digit day of month</p></td></tr><tr><td><p><span class="term"><code class="literal">mmm</code></span></p></td><td><p>first three letters of month</p></td></tr><tr><td><p><span class="term"><code class="literal">yyyy</code></span></p></td><td><p>four-digit year</p></td></tr><tr><td><p><span class="term"><code class="literal">HH</code></span></p></td><td><p>two-digit hour</p></td></tr><tr><td><p><span class="term"><code class="literal">MM</code></span></p></td><td><p>two-digit minute</p></td></tr><tr><td><p><span class="term"><code class="literal">SS</code></span></p></td><td><p>two-digit second</p></td></tr><tr><td><p><span class="term"><code class="literal">FFF</code></span></p></td><td><p>three-digit fraction of second</p></td></tr></tbody></table></div></td></tr><tr><td>Modified Julian</td><td>Floating-point number of days from a reference epoch. In
            GMAT, the reference epoch is 05 Jan 1941 12:00:00.000 (JD
            2430000.0).</td></tr></tbody></table></div><p>The epoch can be set in multiple ways. The default method is to set
    the <span class="guilabel">DateFormat</span> field to the desired time system and
    format, then set the <span class="guilabel">Epoch</span> field to the desired
    epoch. This method cannot be used to get the epoch value, such as on the
    right-hand side of an assignment statement.</p><pre class="programlisting"><code class="code">aSat.DateFormat = UTCGregorian
aSat.Epoch = '18 May 2012 12:00:00.000'</code></pre><p>An alternate method is to specify the
    <span class="guilabel">DateFormat</span> in the parameter name. This method works
    in both &ldquo;get&rdquo; and &ldquo;set&rdquo; modes.</p><pre class="programlisting"><code class="code">aSat.Epoch.UTCGregorian = '18 May 2012 12:00:00.000'
Report aReport aSat.Epoch.UTCGregorian</code></pre><p>A third method can be used in &ldquo;get&rdquo; mode everywhere, but in &ldquo;set&rdquo;
    mode only in the mission sequence (i.e. after the
    <span class="guilabel">BeginMissionSequence</span> command).</p><pre class="programlisting"><code class="code">aSat.UTCGregorian = '18 May 2012 12:00:00.000'
Report aReport aSat.UTCGregorian</code></pre><p>GMAT uses the A.1 time system in the Modified Julian format for its
    internal calculations. The system converts all other systems and formats
    on input and again at output.</p><div class="refsection"><a name="N1F90A"></a><h3>Leap Seconds</h3><p>When converting to and from the UTC time system, GMAT includes
      leap seconds as appropriate, according to the
      <code class="filename">tai-utc.dat</code> data file from the IERS. This file
      contains the conversion between TAI and UTC, including all leap seconds
      that have been added or announced.</p><p>GMAT applies the leap second as the last second before the date
      listed in the <code class="filename">tai-utc.dat</code> file, which historically
      has been either January 1 or July 1. In the Gregorian date format, the
      leap second appears as a &ldquo;60<sup>th</sup> second&rdquo;: for
      example, &ldquo;31 Dec 2008 23:59:60.000&rdquo;. From the International Astronomical
      Union's Standards of Fundamental Astronomy "SOFA Time Scale and Calendar
      Tools" documentation: "<span class="emphasis"><em>Note that UTC has to be expressed as
      hours, minutes and seconds (or at least in seconds in a given day) if
      leap seconds are to be taken into account in the correct
      manner</em></span>. In particular, it is inappropriate to express UTC as
      a Julian Date, because there will be an ambiguity during a leap second
      so that for example 1994 June 30 23:59:60:0 and 1994 July 1 00:00:00:0
      would both come out as MJD 49534.00000 and because subtracting two such
      JDs would not yield the correct interval in cases that contain leap
      seconds." For this reason, we discourage use of the UTC modified Julian
      system, and recommend using UTC Gregorian when a UTC time system is
      required. </p><p>For epochs prior to the first entry in the leap-second file, the
      UTC and TAI time systems are considered identical (i.e. zero leap
      seconds are added). For epochs after the last entry, the leap second
      count from the last entry is used.</p><p>The <code class="filename">tai-utc.dat</code> file is periodically updated
      by the IERS when new leap seconds are announced. The latest version of
      this file can always be found at <a class="link" href="http://maia.usno.navy.mil/ser7/tai-utc.dat" target="_top"><code class="uri">http://maia.usno.navy.mil/ser7/tai-utc.dat</code></a>.
      To replace it, download the latest version and replace GMAT&rsquo;s file in
      the location
      <code class="filename"><em class="replaceable"><code>&lt;GMAT&gt;</code></em>/data/time/tai-utc.dat</code>,
      where <code class="filename"><em class="replaceable"><code>&lt;GMAT&gt;</code></em></code> is
      the install directory of GMAT on your system.</p></div></div><div class="refsection"><a name="N1F932"></a><h2>Examples</h2><div class="informalexample"><p>Setting the epoch for propagation</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.DateFormat = TAIModJulian
aSat.Epoch = 25562.5

Create ForceModel aFM
Create Propagator aProp
aProp.FM = aFM

BeginMissionSequence
Propagate aProp(aSat) {aSat.ElapsedDays = 1}</code></pre></div><div class="informalexample"><p>Plotting and reporting the epoch (syntax #1)</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.DateFormat = A1Gregorian
aSat.Epoch = '12 Jul 2015 08:21:45.921'

Create XYPlot aPlot
aPlot.XVariable = aSat.UTCModJulian
aPlot.YVariables = aSat.Earth.Altitude

Create Report aReport
aReport.Add = {aSat.UTCGregorian, aSat.EarthMJ2000Eq.ECC}</code></pre></div><div class="informalexample"><p>Plotting and reporting the epoch (syntax #2)</p><pre class="programlisting"><code class="code">Create Spacecraft aSat
aSat.DateFormat = TTGregorian
aSat.Epoch = '01 Dec 1978 00:00:00.000'

Create XYPlot aPlot
aPlot.XVariable = aSat.Epoch.TTModJulian
aPlot.YVariables = aSat.Earth.RMAG

Create Report aReport
aReport.Add = {aSat.Epoch.A1Gregorian, aSat.Earth.RMAG}</code></pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="SpacecraftBallisticMass.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SpacecraftHardware.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Spacecraft Ballistic/Mass Properties&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Spacecraft Hardware</td></tr></table></div></body></html>