<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;12.&nbsp;Electric Propulsion</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch11s06.html" title="Further Exercises"><link rel="next" href="ch12s02.html" title="Create and Configure Spacecraft Hardware and Finite Burn"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;12.&nbsp;Electric Propulsion</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch11s06.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch12s02.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Tut_ElectricPropulsion"></a>Chapter&nbsp;12.&nbsp;Electric Propulsion</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Tut_ElectricPropulsion.html#N13D95">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch12s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s02.html#N13DD9">Create a Thruster, Fuel Tank, and Solar Power System</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13E21">Configure the Hardware</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13EB0">Attach Hardware to the Spacecraft</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13F2C">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s03.html#N13F7E">Create the Commands</a></span></dt><dt><span class="section"><a href="ch12s03.html#N13FCC">Configure the Propagate Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s04.html">Run the Mission</a></span></dt></dl></div><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Beginner</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>15 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Complete <a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><i>Simulating an Orbit</i></a></p></td></tr><tr><td><p><span class="term">Script File</span></p></td><td><p><code class="filename">Tut_ElectricPropulsionModelling.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N13D95"></a>Objective and Overview</h2></div></div></div><p>In this tutorial, we will use GMAT to perform a finite burn for a
    spacecraft using an electric propulsion system. &nbsp;Note that targeting and
    design using electric propulsion is identical to chemical propulsion and
    we refer you to the tutorial named <a class="xref" href="Tut_TargetFiniteBurn.html" title="Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee"><i>Target Finite Burn to Raise Apogee</i></a> for targeting configuration. This tutorial
    focuses only on configuration and modeling using electric propulsion
    systems.</p><p>The basic steps of this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Create and configure the <span class="guilabel">Spacecraft</span>
        hardware and <span class="guilabel">FiniteBurn</span> Resources</p></li><li class="step"><p>Configure the Mission Sequence. To do this, we will</p><ol type="a" class="substeps"><li class="step"><p>Create <span class="guilabel">Begin/End FiniteBurn</span> commands
            with default settings.</p></li><li class="step"><p>Create a <span class="guilabel">Propagate</span> command to propagate
            while applying thrust from the electric propulsion system.</p></li></ol></li><li class="step"><p>Run the mission</p></li></ol></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch11s06.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch12s02.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Further Exercises&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and Configure Spacecraft Hardware and Finite Burn</td></tr></table></div></body></html>