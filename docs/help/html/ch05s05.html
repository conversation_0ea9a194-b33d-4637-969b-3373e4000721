<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Run and Analyze the Results</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><link rel="prev" href="ch05s04.html" title="Configure the Propagate Command"><link rel="next" href="SimpleOrbitTransfer.html" title="Chapter&nbsp;6.&nbsp;Simple Orbit Transfer"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Run and Analyze the Results</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch05s04.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;5.&nbsp;Simulating an Orbit</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SimpleOrbitTransfer.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N110AC"></a>Run and Analyze the Results</h2></div></div></div><p>Congratulations, you have now configured your first GMAT mission and
    are ready to run the mission and analyze the results.</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Click <span class="guilabel">Save</span> (<sub><span class="inlinemediaobject"><img src="../files/images/icons/SaveMission.png" align="middle" height="10"></span></sub>) to save your mission.</p></li><li class="listitem"><p>Click the <span class="guilabel">Run</span>
        (<span class="inlinemediaobject"><img src="../files/images/icons/RunMission.png" align="middle" height="10"></span>).</p></li></ol></div><p>You will see GMAT propagate the orbit and stop at orbit periapsis.
    <a class="xref" href="ch05s05.html#Tut_PropASpacecraft_FinalResults" title="Figure&nbsp;5.6.&nbsp;Orbit View Plot after Mission Run">Figure&nbsp;5.6, &ldquo;Orbit View Plot after Mission Run&rdquo;</a> illustrates what you
    should see after correctly completing this tutorial. Here are a few things
    you can try to explore the results of this tutorial:</p><div class="orderedlist"><ol class="orderedlist compact" type="1"><li class="listitem"><p>Manipulate the <span class="guilabel">DefaultOrbitView</span> plot using
        your mouse to orient the trajectory so that you can to verify that at
        the final location the spacecraft is at periapsis. See the <a class="xref" href="OrbitView.html" title="OrbitView"><span class="refentrytitle">OrbitView</span></a> reference for details.</p></li><li class="listitem"><p>Display the command summary:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Click the <span class="guilabel">Mission</span> tab to display the
            <span class="guilabel">Mission</span> tree.</p></li><li class="step"><p>Right-click <span class="guilabel">Propagate1</span> and select
            <span class="guilabel">Command Summary</span> to see data on the final
            state of <span class="guilabel">Sat</span>.</p></li><li class="step"><p>Use the <span class="guilabel">Coordinate System</span> list to
            change the coordinate system in which the data is
            displayed.</p></li></ol></div></li><li class="listitem"><p>Click <span class="guilabel">Start Animation</span>
        (<span class="inlinemediaobject"><img src="../files/images/icons/RunAnimation.png" align="middle" height="10"></span>) to animate the mission and watch
        the orbit propagate from the initial state to periapsis.</p></li></ol></div><div class="figure"><a name="Tut_PropASpacecraft_FinalResults"></a><p class="title"><b>Figure&nbsp;5.6.&nbsp;Orbit View Plot after Mission Run</b></p><div class="figure-contents"><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Tut_PropSpacecraft_OrbitviewPlotAfterMissionRun.png" align="middle" height="623" alt="Orbit View Plot after Mission Run"></td></tr></table></div></div></div></div><br class="figure-break"></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch05s04.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="SimulatingAnOrbit.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SimpleOrbitTransfer.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configure the Propagate Command&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;6.&nbsp;Simple Orbit Transfer</td></tr></table></div></body></html>