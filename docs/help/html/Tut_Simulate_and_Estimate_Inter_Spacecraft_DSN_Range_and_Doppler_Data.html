<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="Tutorials.html" title="Tutorials"><link rel="prev" href="ch15s12.html" title="Appendix C. Check covariance matrix conditioning"><link rel="next" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html" title="Create and configure the spacecraft, spacecraft hardware, and related parameters"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch15s12.html">Prev</a>&nbsp;</td><th align="center" width="60%">Tutorials</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data"></a>Chapter&nbsp;16.&nbsp;Simulate and Estimate Inter-Spacecraft Tracking</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html#N1564D">Objective and overview</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Create and configure the spacecraft, spacecraft hardware, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156A3">Create
      the simulation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156B3">Create
      the estimation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156D2">Create
      a Transponder object and attach it to the spacecraft</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#Create_a_Receiver_object_and_attach_it_to_the_measurement_spacecraft">Create a Receiver object and attach it to the measurement
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html">Define the types of measurements to be simulated and their
    associated error models</a></span></dt><dd><dl><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#N1572A">Define
      the TrackingFileSets for the simulation and the estimator</a></span></dt><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#Create_measurement_error_models">Create the measurement error models</a></span></dt></dl></dd><dt><span class="section"><a href="Create_and_configure_force_model_and_propagator.html">Create and configure force model and propagator</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html">Create and configure the simulator and batch estimator
    objects</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_simulator_object">Create the simulator object</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_batch_estimator_object">Create the batch estimator object</a></span></dt></dl></dd><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html">Run the mission and review the output</a></span></dt><dd><dl><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N15832">Review the simulated
      measurements</a></span></dt><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N1589B">Review the estimator
      results</a></span></dt></dl></dd><dt><span class="section"><a href="ch16s07.html">References</a></span></dt></dl></div><a name="N15630" class="indexterm"></a><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Audience</span></p></td><td><p>Intermediate level</p></td></tr><tr><td><p><span class="term">Length</span></p></td><td><p>40 minutes</p></td></tr><tr><td><p><span class="term">Prerequisites</span></p></td><td><p>Basic Mission Design Tutorials</p></td></tr><tr><td><p><span class="term">Script Files</span></p></td><td><p><code class="filename">Tut_Inter_Spacecraft_Tracking.script</code></p></td></tr></tbody></table></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N1564D"></a>Objective and overview</h2></div></div></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>GMAT currently implements a number of different data types for
      orbit determination. Please refer to <a class="xref" href="TrackingDataTypes.html" title="Tracking Data Types for Orbit Determination"><span class="refentrytitle">Tracking Data Types for Orbit Determination</span></a>
      for details on all the measurement types currently supported by GMAT.
      The measurements being considered here are two way range and
      range-rate.</p></div><p>In this tutorial, we will use GMAT to generate simulated range and
    range-rate measurement data between two spacecraft. One spacecraft,
    referred to as the tracking Spacecraft, is performing observations on the
    other, referred to as the observed Spacecraft. The tracking spacecraft is
    in geosynchronous orbit and the observed spacecraft is in low-earth orbit.
    These simulated measurements will then be used to perform state estimation
    on the observed Spacecraft.</p><p>The basic steps of this tutorial are:</p><div class="procedure"><ol class="procedure" type="1"><li class="step"><p>Create and configure the spacecraft, spacecraft hardware, and
        related parameters</p></li><li class="step"><p>Define the types of measurements to be simulated and the
        associated error models</p></li><li class="step"><p>Create and configure the force model and propagator</p></li><li class="step"><p>Create and configure the simulator and batch estimator
        objects</p></li><li class="step"><p>Run the mission and review the results</p></li></ol></div><p>Note that this tutorial, unlike most of the mission design
    tutorials, will be entirely script based. This is because most of the
    resources and commands related to navigation are not implemented in the
    GUI and are only available via the script interface.</p><p>As you go through the tutorial below, it is recommended that you
    paste the script segments into GMAT as you go along. After each paste into
    GMAT, you should perform a syntax check by hitting the Save, Sync button
    (<span class="inlinemediaobject"><img src="../files/images/icons/Save_Sync.png" align="middle" height="11"></span>). To avoid syntax errors, where needed, don&rsquo;t
    forget to add the following command to the last line of the script segment
    you are checking.</p><pre class="programlisting">BeginMissionSequence</pre><p>We note that in addition to the material presented here, you should
    also look at the individual help resources for all the objects and
    commands we create and use here. For example,
    <span class="guilabel">Spacecraft</span>, <span class="guilabel">Transponder</span>,
    <span class="guilabel">Transmitter</span>, <span class="guilabel">Receiver</span>,
    <span class="guilabel">ErrorModel</span>, <span class="guilabel">TrackingFileSet</span>,
    <span class="guilabel">RunSimulator</span>, etc all have their own help
    pages.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch15s12.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="Tutorials.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Appendix C. Check covariance matrix conditioning&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Create and configure the spacecraft, spacecraft hardware, and
    related parameters</td></tr></table></div></body></html>