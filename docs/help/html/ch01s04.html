<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Platform Support</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="WelcomeToGmat.html" title="Chapter&nbsp;1.&nbsp;Welcome to GMAT"><link rel="prev" href="ch01s03.html" title="Licensing"><link rel="next" href="ch01s05.html" title="Component Status"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Platform Support</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch01s03.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;1.&nbsp;Welcome to GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch01s05.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N101A1"></a>Platform Support</h2></div></div></div><p>GMAT has been rigorously tested on the Windows 11 platform and we
    perform nightly regression tests running more than 17,000 test cases for
    the system core and over 4000 test cases for the GUI interface. The Mac
    and Linux console versions are rigorously tested, but the GUI is provided
    in Beta form on those platforms.</p><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>GMAT on macOS is not a notarized application. The
      <code class="filename">GMAT</code> folder must be installed either to the
      system-wide <code class="filename">/Applications</code> folder or to the user's
      <code class="filename">~/Applications</code> folder. The latter is useful for
      users without admin access to their Mac.</p></div><p>The following plugin modules do not run under this release of GMAT
    on Mac and Linux platforms:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Optimizer libFmincon</p></li></ul></div><p>and the Mac release does not support the following plugin:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>libMsise86</p></li></ul></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch01s03.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="WelcomeToGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch01s05.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Licensing&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Component Status</td></tr></table></div></body></html>