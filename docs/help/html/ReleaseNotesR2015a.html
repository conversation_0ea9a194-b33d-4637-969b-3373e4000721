<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>GMAT R2015a Release Notes</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ReleaseNotes.html" title="Release Notes"><link rel="prev" href="ReleaseNotesR2016a.html" title="GMAT R2016a Release Notes"><link rel="next" href="ReleaseNotesR2014a.html" title="GMAT R2014a Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">GMAT R2015a Release Notes</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ReleaseNotesR2016a.html">Prev</a>&nbsp;</td><th align="center" width="60%">Release Notes</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReleaseNotesR2014a.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="ReleaseNotesR2015a"></a>GMAT R2015a Release Notes</h2></div></div></div><p>The General Mission Analysis Tool (GMAT) version R2015a was released
  Nov 2015. This is the first public release since July 2014, and is the 9th
  release for the project.</p><p>Below is a summary of key changes in this release. Please see the full
  <a class="link" href="http://bugs.gmatcentral.org/secure/ReleaseNote.jspa?version=10600&amp;styleName=Html&amp;projectId=10000" target="_top">R2015a
  Release Notes</a> on JIRA for a complete list.</p><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N3125C"></a>New Features</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N3125F"></a>GMAT Functions</h4></div></div></div><p>You can now write functions (sub-routines) in the GMAT script
      language. This powerful feature greatly expands the practical capability
      of the system and makes maintaining complex configurations simpler. This
      feature also enables sharing GMAT script utilities among among projects.
      If you need a new math computation, want to isolate a complex section of
      code, or re-use code, GMAT functions are a great solution.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_GMATFunctions.png" width="569"></div></div></div><p>See the <a class="link" href="Tut_UsingGMATFunctions.html" title="Chapter&nbsp;10.&nbsp;Mars B-Plane Targeting Using GMAT Functions">Using GMAT
      Functions</a> tutorial for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31271"></a>Eclipse Location</h4></div></div></div><p>GMAT now supports eclipse location. Under the hood GMAT calls NAIF
      SPICE routines. Thanks to the NAIF for making this great functionality
      available.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_EclipseLocation.png" width="573"></div></div></div><p>See the <a class="link" href="EclipseLocator.html" title="EclipseLocator">Eclipse Locator</a>
      reference for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31283"></a>Station Contact Location</h4></div></div></div><p>GMAT now supports station contact location. Under the hood GMAT
      calls NAIF SPICE routines. Thanks to the NAIF for making this great
      functionality available.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_ContactLocation.png" width="571"></div></div></div><p>See the <a class="link" href="ContactLocator.html" title="ContactLocator">Contact Locator</a>
      reference for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31295"></a>Python Interface</h4></div></div></div><p>GMAT now supports an interface with Python. The power of the
      Python ecosystem can now be used with GMAT.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_Python.png" width="566"></div></div></div><p>See the <a class="link" href="CallPythonFunction.html" title="CallPythonFunction">Python</a> reference
      for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312A7"></a>Electric Propulsion</h4></div></div></div><p>GMAT now supports modeling of electric propulsion systems. Below
      is an examle showing GMAT modeling a cube-sat with electric propulsion
      in a lunar weak-stablity orbit. You can model electric tanks, thrusters,
      and power systems (both Solar and nuclear).</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_ElectricPropulsionSystem.png" width="585"></div></div></div><p>See the <a class="link" href="Tut_ElectricPropulsion.html" title="Chapter&nbsp;12.&nbsp;Electric Propulsion">Electric
      Propulsion</a> tutorial for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312B9"></a>SNOPT Optimizer</h4></div></div></div><p>GMAT now interfaces to Stanford Business Software, Inc. SNOPT
      Optimizer</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_SNOPT.png" width="395"></div></div></div><p>See the <a class="link" href="SNOPTOptimizer.html" title="SNOPT">SNOPT</a> reference for
      more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312CB"></a>Space Weather Modelling</h4></div></div></div><p>You can now provide flux files for drag modeling including
      Schatten historical files and Center for Space Standards and Innovation
      (CSSI) Space Weather Files. This greatly improves long term orbital
      predictions and reconstructions in the Earth's atmosphere.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_SpaceWeather.png" width="464"></div></div></div><p>See the <a class="link" href="Propagator.html" title="Propagator">Propagator</a> reference for
      more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312DD"></a>Celestial Body 3-D Graphics Models</h4></div></div></div><p>You can now use a 3D model for celestial bodies in 3-D
      graphics.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_Comet3DModel.png"></div></div></div><p>See the <a class="link" href="CelestialBody.html" title="CelestialBody">Celestial Body</a>
      reference for more information.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312EF"></a>Solver Status Window</h4></div></div></div><p>GMAT now displays a window showing solver variables and constraint
      values during execution. This helps track the progress of targeters and
      optimizers and is an important aid in troubleshooting convergence
      issues.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_SolverWindow.png" width="704"></div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N312FB"></a>Improvements</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N312FE"></a>Documentation</h4></div></div></div><p>We've written over 70 pages of new, high-quality user
      documentation! We've also written two conference papers documenting our
      verification and validation process and results, and the flight
      qualification program and results for the Advanced Composition Explorer
      (ACE). Conference papers are located in the "docs" folder of the
      distribution.</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_VandV.png" width="439"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N3130A"></a>Training Videos</h4></div></div></div><p>We've posted training videos on <a class="link" href="https://www.youtube.com/channel/UCt-REODJNr2mB3t-xH6kbjg" target="_top">YouTube
      </a>. You can now take GMAT training even if you are unable to attend
      the live classes!</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/ReleaseNotes_R2015a_TrainingVideos.png" width="458"></div></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N3131A"></a>Other Improvements</h4></div></div></div><div class="itemizedlist"><ul class="itemizedlist compact" style="list-style-type: disc; "><li class="listitem"><p>You can now optionally apply an
          <span class="guilabel">ImpulsiveBurn</span> in the backwards direction which
          is convenient when targeting backwards in time.</p></li><li class="listitem"><p>GMAT is distributed with beta plugin Polyhedral gravity
          model.</p></li><li class="listitem"><p>The system now looks in the working directory for scripts run
          from the command line</p></li><li class="listitem"><p>You can now reference supporting files relative to the script
          file location for ease in sharing complex configurations.</p></li><li class="listitem"><p>You can now define an minimum elevation angle for a
          groundstation used in event location and estimation.</p></li><li class="listitem"><p>The appearance of constellations in 3-D graphics has been
          improved.</p></li><li class="listitem"><p>The 3-D model scaling sensitivity in the GUI has been
          improved.</p></li><li class="listitem"><p>The behavior of the GUI when using large fonts has been
          improved.</p></li></ul></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N3133A"></a>Compatibility Changes</h3></div></div></div><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The <span class="guilabel">ChemicalTank</span> Resource has been renamed
        to <span class="guilabel">ChemicalTank</span> to distinguish between chemical
        and electric systems.</p></li><li class="listitem"><p>The <span class="guilabel">ChemicalThruster</span> Resource has been
        renamed to <span class="guilabel">ChemicalThruster</span> to distinguish
        between chemical and electric systems.</p></li><li class="listitem"><p>The sensitivity of <span class="guilabel">Spacecraft</span> Resource
        settings such as <span class="guilabel">ModelOffsetX</span>,
        <span class="guilabel">ModelRotationY</span>, and
        <span class="guilabel">ModelScale</span> has changed in 3-D graphics.</p></li><li class="listitem"><p>When applying an <span class="guilabel">ImpulsiveBurn</span> during
        backwards targeting, GMAT now attempts to compute maneuver values that
        are consistent with a forward targeting approach. The maneuver values
        reference the pre-manevuer velocity components instead of the
        post-maneuver components.</p></li></ul></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N31365"></a>Development and Tools</h3></div></div></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31368"></a>Developer Documenation</h4></div></div></div><p>We've added extensive documentation describing how to add new
      Resources and Commands to GMAT. Resources and Commands are key to GMAT
      development and application. This documentation is essential reading for
      making fundamental extensions to GMAT. See the <a class="link" href="http://gmatcentral.org/display/GW/How+To+Write+New+Components" target="_top">wiki
      documentation for details</a>.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31371"></a>Developer Tools and Dependencies</h4></div></div></div><p>We developed a new CMake-based build system that is used on all
      platforms. The CMake configuration is maintained by the GMAT team and
      distributed with the source code. Thanks to CMake, it is much easier to
      compile GMAT. See the <a class="link" href="http://gmatcentral.org/display/GW/Compiling" target="_top">wiki
      documentation for details</a>.</p><p>We updated SPICE to version N0065 and updated WxWidgets to version
      3.0.2.</p></div><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N3137C"></a>GMAT Stuff</h4></div></div></div><p>You can now purchase clothing and other items with the GMAT logo
      via &copy;Land's End, Inc at the <a class="link" href="http://ocs.landsend.com/cd/frontdoor?store_name=nasagsfc&amp;store_type=3" target="_top">GSFC
      Store</a> . Once, you've chosen an item, make sure to select the GMAT
      logo!</p><div class="informalfigure"><div class="screenshot"><div class="mediaobject"><img src="../files/images/relnotes/r2015a/SWAG.png" width="587"></div></div></div></div></div><div class="section"><div class="titlepage"><div><div><h3 class="title"><a name="N3138C"></a>Known &amp; Fixed Issues</h3></div></div></div><p>Over 215 bugs were closed in this release. See the <a class="link" href="http://li64-187.members.linode.com:8080/issues/?filter=13220" target="_top">"Critical
    Issues Fixed in R2015a" report</a> for a list of critical bugs and
    resolutions in R2015a. See the <a class="link" href="http://li64-187.members.linode.com:8080/issues/?filter=13221" target="_top">"Minor
    Issues Fixed for R2015a" report</a> for minor issues addressed in
    R2015a.</p><div class="section"><div class="titlepage"><div><div><h4 class="title"><a name="N31399"></a>Known Issues</h4></div></div></div><p>All known issues that affect this version of GMAT can be seen in
      the <a class="link" href="http://li64-187.members.linode.com:8080/issues/?filter=13219" target="_top">"Known
      Issues in R2015a" report</a> in JIRA.</p><p>There are several known issues in this release that we consider to
      be significant:</p><div class="informaltable"><table border="1"><colgroup><col width="16%"><col width="84%"></colgroup><thead><tr><th align="left">ID</th><th align="left">Description</th></tr></thead><tbody><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-5353" target="_top">GMT-5253</a></td><td>GMAT stuck in script state after bad script
                load.</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-5269" target="_top">GMT-5269</a></td><td>Atmosphere model affects propagation at GEO.</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-2561" target="_top">GMT-2561</a></td><td>UTC Epoch Entry and Reporting During Leap Second is
                incorrect.</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-3043" target="_top">GMT-3043</a></td><td>Inconsistent validation when creating variables that
                shadow built-in math functions</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-3289" target="_top">GMT-3289</a></td><td>First step algorithm fails for backwards propagation
                using SPK propagator</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-3350" target="_top">GMT-3350</a></td><td>Single-quote requirements are not consistent across
                objects and modes</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-3669" target="_top">GMT-3669</a></td><td>Planets not drawn during optimization in
                OrbitView</td></tr><tr><td><a class="link" href="http://li64-187.members.linode.com:8080/browse/GMT-3738" target="_top">GMT-3738</a></td><td>Cannot set standalone FuelTank, Thruster fields in
                CallMatlabFunction</td></tr><tr><td><a class="link" href="http://bugs.gmatcentral.org/browse/GMT-4520" target="_top">GMT-4520</a></td><td>Unrelated script line in Optimize changes results
                (causes crash)</td></tr><tr><td><a class="link" href="http://bugs.gmatcentral.org/browse/GMT-4408" target="_top">GMT-4408</a></td><td>Failed to load icon file and to open DE file</td></tr><tr><td><a class="link" href="http://bugs.gmatcentral.org/browse/GMT-4398" target="_top">GMT-4520</a></td><td>Coordinate System Fixed attitudes are held constant in
                SPAD SRP model during a propagation step</td></tr></tbody></table></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ReleaseNotesR2016a.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ReleaseNotes.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReleaseNotesR2014a.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">GMAT R2016a Release Notes&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;GMAT R2014a Release Notes</td></tr></table></div></body></html>