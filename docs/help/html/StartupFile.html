<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Startup File</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch24.html#N2D3E2" title="System Level Components"><link rel="prev" href="ScriptLanguage.html" title="Script Language"><link rel="next" href="ReleaseNotes.html" title="Release Notes"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Startup File</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ScriptLanguage.html">Prev</a>&nbsp;</td><th align="center" width="60%">System Level Components</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ReleaseNotes.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="StartupFile"></a><div class="titlepage"></div><a name="N30556" class="indexterm"></a><a name="N30559" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Startup File</span></h2><p>Startup File &mdash; The <code class="filename">gmat_startup_file.txt</code> configuration
    file</p></div><div class="refsection"><a name="N3056E"></a><h2>Description</h2><p>The GMAT startup file (<code class="filename">gmat_startup_file.txt</code>)
    contains basic configuration settings for the GMAT application. This
    includes the locations of data files and plugins, search paths for
    user-defined functions, and various options that control execution.</p><p>The startup file must be located in the same location as the GMAT
    executable, and must be named <code class="filename">gmat_startup_file.txt</code>.
    GMAT loads the startup file once during program initialization.</p></div><div class="refsection"><a name="N3057B"></a><h2>File Format</h2><div class="refsection"><a name="N3057E"></a><h3>Basic Syntax</h3><p>The startup file is a text file containing characters from the
      7-bit US-ASCII character set. The startup file is case-sensitive.</p><p>Lines are terminated by any of the following ASCII character
      sequences:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>line feed (hex: 0A)</p></li><li class="listitem"><p>carriage return (hex: 0D)</p></li><li class="listitem"><p>carriage return followed by line feed (hex: 0D0A)</p></li></ul></div><p>White space can appear above or below any line and before or after
      any key or value. The following characters are recognized as white
      space:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>space (hex: 20)</p></li><li class="listitem"><p>horizontal tab (hex: 09)</p></li></ul></div><p>Comments begin with the number sign (&ldquo;<code class="literal">#</code>&rdquo;) and
      must appear on their own line. Inline comments are not allowed.</p></div><div class="refsection"><a name="N3059D"></a><h3>Setting Properties</h3><p>Properties are specified via key-value pairs, with the following
      syntax:</p><p><code class="code">PROPERTY = VALUE</code></p><p>Properties are one word, with no spaces. Values extend from the
      first non-whitespace character after the equal sign to the end of the
      line. At least one whitespace character is required on both sides of the
      equal sign.</p><p>Properties are named according to the following
      conventions:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Properties that accept directory paths end with
            &ldquo;<code class="literal">_PATH</code>&rdquo;.</p></li><li class="listitem"><p>Properties that accept file paths end with
            &ldquo;<code class="literal">_FILE</code>&rdquo;.</p></li></ul></div><p>The behavior of duplicate property entries is dependent on the
      individual property. In general:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>Multiple <code class="literal">PLUGIN</code> entries cause GMAT to
            load each named plugin.</p></li><li class="listitem"><p>Multiple identical <code class="literal">*_FUNCTION_PATH</code>
            entries add each path to the search path, starting with the
            first.</p></li><li class="listitem"><p>Multiple identical <code class="literal">*_FILE</code> entries are
            ignored; the last value is used.</p></li></ul></div></div><div class="refsection"><a name="N305CB"></a><h3>Accessing Property Values</h3><p>The value of any property ending in &ldquo;_PATH&rdquo; (including custom
      ones) can be referenced by other values. To reference a value, include
      the property name as part of the value. Repeated slash characters are
      collapsed. For example:</p><pre class="programlisting">ROOT_PATH = ../
OUTPUT_PATH = ROOT_PATH/output/</pre><p>sets
      <code class="literal">OUTPUT_PATH</code> to a value of
      "<code class="literal">../output/</code>".</p></div><div class="refsection"><a name="N305D9"></a><h3>File Paths</h3><p>Forward slashes and backslashes can be used interchangeably, and
      can be mixed in a single path. The following three paths are considered
      identical:</p><div class="literallayout"><p>data/planetary_ephem/spk/de421.bsp<br>
data\planetary_ephem\spk\de421.bsp<br>
data\planetary_ephem/spk\de421.bsp</p></div><p>Absolute paths are passed to the underlying operating system
      as-is, aside from normalizing the slashes.</p><p>Relative paths are relative to the location of the GMAT
      executable.</p></div></div><div class="refsection"><a name="N305E4"></a><h2>Properties</h2><p>The available properties are shown here, with default values where
    appropriate.</p><div class="refsection"><a name="N305E9"></a><h3>System</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">ROOT_PATH=../</code></span></dt><dd><p>Path to GMAT root directory.</p></dd></dl></div></div><div class="refsection"><a name="N305F6"></a><h3>Plugins</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">PLUGIN</code></span></dt><dd><p>Path to plugin library, without extension. Multiple
            <code class="literal">PLUGIN</code> properties are allowed, one per
            plugin.</p></dd></dl></div></div><div class="refsection"><a name="N30606"></a><h3>User Functions</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">GMAT_FUNCTION_PATH</code></span></dt><dd><p>Search path for GMAT function files
            (<code class="filename">.gmf</code> files). May occur multiple times to add
            multiple paths.</p></dd><dt><span class="term"><code class="literal">MATLAB_FUNCTION_PATH</code></span></dt><dd><p>Search path for MATLAB function files
            (<code class="filename">.m</code> files). May occur multiple times to add
            multiple paths.</p></dd><dt><span class="term"><code class="literal">PYTHON_MODULE_PATH</code></span></dt><dd><p>Search path for Python modules. May occur multiple times to
            add multiple paths.</p></dd></dl></div></div><div class="refsection"><a name="N30627"></a><h3>Output</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">LOG_FILE=<em class="replaceable"><code>OUTPUT_PATH</code></em>/GmatLog.txt</code></span></dt><dd><p>Path of application log file</p></dd><dt><span class="term"><code class="literal">MEASUREMENT_PATH=<em class="replaceable"><code>OUTPUT_PATH</code></em>/</code></span></dt><dd><p>Path of simulated measurement data files. Only used with the
            <code class="filename">libGmatEstimation</code> plugin.</p></dd><dt><span class="term"><code class="literal">OUTPUT_PATH=../output/</code></span></dt><dd><p>Output directory path for <span class="guilabel">ReportFile</span>
            resources.</p></dd><dt><span class="term"><code class="literal">SCREENSHOT_FILE=<em class="replaceable"><code>OUTPUT_PATH</code></em>/OUTPUT_PATH</code></span></dt><dd><p>Output path and base filename for screenshots. The base
            filename is appended with
            &ldquo;<code class="literal">_<em class="replaceable"><code>###</code></em>.png</code>&rdquo;, where
            &ldquo;<code class="literal"><em class="replaceable"><code>###</code></em></code>&rdquo; is a number
            sequence starting from <code class="literal">001</code>. If the base
            filename is missing, it defaults to
            &ldquo;<code class="literal">SCREEN_SHOT</code>&rdquo;.</p></dd><dt><span class="term"><code class="literal">VEHICLE_EPHEM_PATH=<em class="replaceable"><code>OUTPUT_PATH</code></em>/</code></span></dt><dd><p>Default output directory path for
            <span class="guilabel">EphemerisFile</span> resources.</p></dd></dl></div></div><div class="refsection"><a name="N30675"></a><h3>Data Files</h3><p>Note this section only discusses the paths that can be set via the
      startup file. See <span class="emphasis"><em><a class="xref" href="ConfiguringGmat_DataFiles.html" title="Configuring Data Files">Configuring Data Files</a></em></span> or a discussion of file contents
      of data files that are regularly updated and how to maintain those
      files.</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal"><em class="replaceable"><code>CELESTIALBODY</code></em>_POT_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/gravity/<em class="replaceable"><code>celestialbody</code></em>/</code></span></dt><dd><p>Search path for gravity potential files for
            <code class="literal"><em class="replaceable"><code>CELESTIALBODY</code></em></code>.
            <code class="literal"><em class="replaceable"><code>CELESTIALBODY</code></em></code> is the
            name of any celestial body defined in a given GMAT mission. This
            property has no default for user-defined celestial bodies.</p></dd><dt><span class="term"><code class="literal">ATMOSPHERE_PATH</code></span></dt><dd><p>Path to directory containing atmosphere model data.</p></dd><dt><span class="term"><code class="literal">BODY_3D_MODEL_PATH</code></span></dt><dd><p>Path to directory containing CelestialBody 3D model
            files.</p></dd><dt><span class="term"><code class="literal">CSSI_FLUX_FILE</code></span></dt><dd><p>Path to default CSSI solar flux file.</p></dd><dt><span class="term"><code class="literal">DATA_PATH=<em class="replaceable"><code>ROOT_PATH</code></em>/data/</code></span></dt><dd><p>Path to directory containing data files.</p></dd><dt><span class="term"><code class="literal">DE405_FILE=<em class="replaceable"><code>DE_PATH</code></em>/leDE1941.405</code></span></dt><dd><p>Path to DE405 DE-file ephemeris file.</p></dd><dt><span class="term"><code class="literal">DE421_FILE</code></span></dt><dd><p>Path to DE421 DE-file ephemeris file.</p></dd><dt><span class="term"><code class="literal">DE424_FILE</code></span></dt><dd><p>Path to DE424 DE-file ephemeris file.</p></dd><dt><span class="term"><code class="literal">EGM96_FILE=<em class="replaceable"><code>EARTH_POT_PATH</code></em>/EGM96.cof</code></span></dt><dd><p>Path to EGM-96 Earth gravity potential file.</p></dd><dt><span class="term"><code class="literal">EOP_FILE</code></span></dt><dd><p>Path to IERS &ldquo;EOP 08 C04 (IAU1980)&rdquo; Earth orientation
            parameters file.</p></dd><dt><span class="term"><code class="literal">ICRF_FILE</code></span></dt><dd><p>Path to data required for computing rotation matrix from FK5
            to ICRF (<code class="filename">ICRF_Table.txt</code>).</p></dd><dt><span class="term"><code class="literal">JGM2_FILE=<em class="replaceable"><code>EARTH_POT_PATH</code></em>/JGM2.cof</code></span></dt><dd><p>Path to JGM-2 Earth gravity potential file.</p></dd><dt><span class="term"><code class="literal">JGM3_FILE=<em class="replaceable"><code>EARTH_POT_PATH</code></em>/JGM3.cof</code></span></dt><dd><p>Path to JGM-3 Earth gravity potential file.</p></dd><dt><span class="term"><code class="literal">LEAP_SECS_FILE=<em class="replaceable"><code>TIME_PATH</code></em>/tai-utc.dat</code></span></dt><dd><p>Path to cumulative leap seconds file from <a class="link" href="http://maia.usno.navy.mil" target="_top"><code class="uri">http://maia.usno.navy.mil</code></a>.</p></dd><dt><span class="term"><code class="literal">LP165P_FILE=<em class="replaceable"><code>LUNA_POT_PATH</code></em>/LP165P.cof</code></span></dt><dd><p>Path to LP165P Moon gravity potential file.</p></dd><dt><span class="term"><code class="literal">LSK_FILE</code></span></dt><dd><p>Path to SPICE leap second kernel.</p></dd><dt><span class="term"><code class="literal">MARINI_TROPO_FILE</code></span></dt><dd><p>Path to file containing location specific atmospheric data
            needed for the Marini tropospheric model.</p></dd><dt><span class="term"><code class="literal">MARS50C_FILE=<em class="replaceable"><code>MARS_POT_PATH</code></em>/Mars50c.cof</code></span></dt><dd><p>Path to Mars50c Mars gravity potential file.</p></dd><dt><span class="term"><code class="literal">MGNP180U_FILE=<em class="replaceable"><code>VENUS_POT_PATH</code></em>/MGNP180U.cof</code></span></dt><dd><p>Path to MGNP180U Venus gravity potential file.</p></dd><dt><span class="term"><code class="literal">NUTATION_COEFF_FILE=<em class="replaceable"><code>PLANETARY_COEFF_PATH</code></em>/NUTATION.DAT</code></span></dt><dd><p>Path to nutation series data for FK5 reduction
            (<code class="filename">NUTATION.DAT</code>).</p></dd><dt><span class="term"><code class="literal">PLANETARY_COEFF_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/planetary_coeff/</code></span></dt><dd><p>Path to directory containing planetary coefficient
            files.</p></dd><dt><span class="term"><code class="literal">PLANETARY_EPHEM_DE_PATH</code></span></dt><dd><p>Path to directory containing DE ephemeris files.</p></dd><dt><span class="term"><code class="literal">PLANETARY_EPHEM_SPK_PATH</code></span></dt><dd><p>Path to directory containing SPICE planetary ephemeris
            files.</p></dd><dt><span class="term"><code class="literal">PLANETARY_PCK_FILE</code></span></dt><dd><p>Path to SPICE planetary constants kernel for default
            celestial bodies.</p></dd><dt><span class="term"><code class="literal">PLANETARY_SPK_FILE</code></span></dt><dd><p>Path to SPICE ephemeris kernel for default celestial
            bodies.</p></dd><dt><span class="term"><code class="literal">SCHATTEN_FILE</code></span></dt><dd><p>Path to default Schatten solar flux predict file.</p></dd><dt><span class="term"><code class="literal">SPACECRAFT_MODEL_FILE</code></span></dt><dd><p>Default spacecraft 3D model file.</p></dd><dt><span class="term"><code class="literal">SPAD_PATH</code></span></dt><dd><p>Path to directory containing SPAD data files.</p></dd><dt><span class="term"><code class="literal">SPAD_SRP_FILE</code></span></dt><dd><p>Path to default SPAD SRP model.</p></dd><dt><span class="term"><code class="literal">TIME_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/time/</code></span></dt><dd><p>Path to directory containing leap-second files.</p></dd><dt><span class="term"><code class="literal">VEHICLE_EPHEM_CCSDS_PATH</code></span></dt><dd><p>Path to directory containing spacecraft CCSDS-OEM ephemeris
            files.</p></dd><dt><span class="term"><code class="literal">VEHICLE_EPHEM_SPK_PATH</code></span></dt><dd><p>Path to directory containing spacecraft SPK ephemeris
            files.</p></dd><dt><span class="term"><code class="literal">VEHICLE_MODEL_PATH</code></span></dt><dd><p>Path to directory containing 3D spacecraft models.</p></dd></dl></div></div><div class="refsection"><a name="N307A8"></a><h3>Application Files</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal"><em class="replaceable"><code>CELESTIALBODY</code></em>_TEXTURE_FILE=<em class="replaceable"><code>TEXTURE_PATH</code></em>/DefaultTextureFile.jpg</code></span></dt><dd><p>Path to texture file for CELESTIALBODY. CELESTIALBODY is the
            name of any of the built-in celestial bodies in GMAT.
            DefaultTextureFile is the default texture file defined for that
            celestial body.</p></dd><dt><span class="term"><code class="literal">BORDER_FILE</code></span></dt><dd><p>Path to constellation border catalog.</p></dd><dt><span class="term"><code class="literal">CONSTELLATION_FILE=<em class="replaceable"><code>STAR_PATH</code></em>/inp_Constellation.txt</code></span></dt><dd><p>Path to constellation catalog.</p></dd><dt><span class="term"><code class="literal">GUI_CONFIG_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/gui_config/</code></span></dt><dd><p>Path to directory containing GUI configuration files.</p></dd><dt><span class="term"><code class="literal">HELP_PATH</code></span></dt><dd><p>Path to directory containing user guide help files.</p></dd><dt><span class="term"><code class="literal">HELP_DIRECTORY_FILE</code></span></dt><dd><p>Path to CHM help file, used to navigate user guide contents through the GMAT GUI.</p></dd><dt><span class="term"><code class="literal">HELP_HTML_FILE</code></span></dt><dd><p>Path to HTML help file, used to open the entire user guide in a browser.</p></dd><dt><span class="term"><code class="literal">ICON_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/graphics/icons/</code></span></dt><dd><p>Path to directory containing application icons.</p></dd><dt><span class="term"><code class="literal">MAIN_ICON_FILE</code></span></dt><dd><p>Path to GUI icon.</p></dd><dt><span class="term"><code class="literal">PERSONALIZATION_FILE=<em class="replaceable"><code>DATA_PATH</code></em>/gui_config/MyGmat.ini</code></span></dt><dd><p>Path to GUI configuration and history file.</p></dd><dt><span class="term"><code class="literal">SPACECRAFT_MODEL_FILE=<em class="replaceable"><code>MODEL_PATH</code></em>/aura.3ds</code></span></dt><dd><p>Path to default Spacecraft 3D model file.</p></dd><dt><span class="term"><code class="literal">SPLASH_FILE=<em class="replaceable"><code>SPLASH_PATH</code></em>/GMATSplashScreen.tif</code></span></dt><dd><p>Path to GUI splash image.</p></dd><dt><span class="term"><code class="literal">SPLASH_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/graphics/splash/</code></span></dt><dd><p>Path to directory containing splash file.</p></dd><dt><span class="term"><code class="literal">STAR_FILE=<em class="replaceable"><code>STAR_PATH</code></em>/inp_StarCatalog.txt</code></span></dt><dd><p>Path to star catalog.</p></dd><dt><span class="term"><code class="literal">STAR_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/graphics/stars/</code></span></dt><dd><p>Path to directory containing star and constellation
            catalogs.</p></dd><dt><span class="term"><code class="literal">TEXTURE_PATH=<em class="replaceable"><code>DATA_PATH</code></em>/graphics/texture/</code></span></dt><dd><p>Path to directory containing celestial body texture
            files.</p></dd></dl></div></div><div class="refsection"><a name="N30841"></a><h3>Program Settings</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">MATLAB_APP_PATH</code></span></dt><dd><p>[macOS only] Path to MATLAB app
            (<code class="filename">.app</code>).</p></dd><dt><span class="term"><code class="literal">MATLAB_MODE=SHARED</code></span></dt><dd><p>MATLAB interface connection mode. The available options
            are:</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">NO_MATLAB</code></span></dt><dd><p>Disables the MATLAB interface.</p></dd><dt><span class="term"><code class="literal">SHARED</code></span></dt><dd><p>Each GMAT instance shares a single MATLAB
                    connection. Default.</p></dd><dt><span class="term"><code class="literal">SINGLE</code></span></dt><dd><p>Each GMAT instance uses its own MATLAB
                    connection.</p></dd></dl></div></dd><dt><span class="term"><code class="literal">WRITE_GMAT_KEYWORD=ON</code></span></dt><dd><p>Write &ldquo;<code class="literal">GMAT </code>&ldquo; prefix before assignment
            lines when saving a GMAT script file. Accepted values are
            <code class="literal">ON</code> and <code class="literal">OFF</code>.</p></dd><dt><span class="term"><code class="literal">WRITE_PERSONALIZATION_FILE=ON</code></span></dt><dd><p>Write data on window locations and other local configuration settings to 
			the GMAT.ini file. Setting to OFF avoids issues encountered when simultaneous instances of GMAT try to write to the user config file at the same time, resulting in a system error. Accepted values are
            <code class="literal">ON</code> and <code class="literal">OFF</code>.</p></dd></dl></div></div><div class="refsection"><a name="N3088D"></a><h3>Debug Settings</h3><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">DEBUG_FILE_PATH=OFF</code></span></dt><dd><p>Debug file path handling. Accepted values are
            <code class="literal">ON</code> and <code class="literal">OFF</code>.</p></dd><dt><span class="term"><code class="literal">DEBUG_MATLAB=OFF</code></span></dt><dd><p>Debug MATLAB Interface connection. Accepted values are
            <code class="literal">ON</code> and <code class="literal">OFF</code>.</p></dd><dt><span class="term"><code class="literal">DEBUG_PARAMETERS=OFF</code></span></dt><dd><p>Write table of available parameters to log file on startup.
            Accepted values are <code class="literal">ON</code> and
            <code class="literal">OFF</code>.</p></dd><dt><span class="term"><code class="literal">HIDE_SAVEMISSION=TRUE</code></span></dt><dd><p>Hide the <span class="guilabel">SaveMission</span> command from the
            GUI. Accepted values are <code class="literal">TRUE</code> and
            <code class="literal">FALSE</code>.</p></dd><dt><span class="term"><code class="literal">PLOT_MODE</code></span></dt><dd><p><span class="guilabel">XYPlot</span> window placement mode. The only
            accepted value is <code class="literal">TILE</code>, which will cause GMAT
            to ignore plot window placement fields and tile the
            windows.</p></dd><dt><span class="term"><code class="literal">RUN_MODE</code></span></dt><dd><p>GMAT execution mode. The available options are:</p><div class="variablelist"><dl class="variablelist"><dt><span class="term"><code class="literal">EXIT_AFTER_RUN</code></span></dt><dd><p>When GMAT is called with the <code class="option">-r</code> or
                    <code class="option">--run</code> command-line argument,
                    automatically exit after the run is finished.</p></dd><dt><span class="term"><code class="literal">TESTING</code></span></dt><dd><p>Shows testing options in the GUI.</p></dd><dt><span class="term"><code class="literal">TESTING_NO_PLOTS</code></span></dt><dd><p>Same as <code class="literal">TESTING</code>, but also
                    disables all graphical output in the GUI.</p></dd></dl></div></dd><dt><span class="term"><code class="literal">ECHO_COMMANDS</code></span></dt><dd><p>Write commands to log file as they are executed. Accepted
            values are TRUE and <code class="literal">FALSE</code>.</p></dd><dt><span class="term"><code class="literal">NO_SPLASH</code></span></dt><dd><p>Skip showing the splash screen on GMAT startup. Accepted
            values are TRUE and <code class="literal">FALSE</code>.</p></dd></dl></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ScriptLanguage.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch24.html#N2D3E2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ReleaseNotes.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Script Language&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Release Notes</td></tr></table></div></body></html>