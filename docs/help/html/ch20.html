<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Chapter&nbsp;20.&nbsp;Targeting/Parameter Optimization</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="RefGuide.html" title="Reference Guide"><link rel="prev" href="Color.html" title="Color"><link rel="next" href="DifferentialCorrector.html" title="DifferentialCorrector"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Chapter&nbsp;20.&nbsp;Targeting/Parameter Optimization</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="Color.html">Prev</a>&nbsp;</td><th align="center" width="60%">Reference Guide</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="DifferentialCorrector.html">Next</a></td></tr></table><hr></div><div class="chapter"><div class="titlepage"><div><div><h2 class="title"><a name="N25D96"></a>Chapter&nbsp;20.&nbsp;Targeting/Parameter Optimization</h2></div></div></div><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="section"><a href="ch20.html#N25D9B">Resources</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="DifferentialCorrector.html">DifferentialCorrector</a></span><span class="refpurpose"> &mdash; A numerical solver</span></dt><dt><span class="refentrytitle"><a href="FminconOptimizer.html">FminconOptimizer</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    fmincon</span></dt><dt><span class="refentrytitle"><a href="SNOPTOptimizer.html">SNOPT</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    SNOPT</span></dt><dt><span class="refentrytitle"><a href="VF13ad.html">VF13ad</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    VF13ad</span></dt><dt><span class="refentrytitle"><a href="Yukon.html">Yukon</a></span><span class="refpurpose"> &mdash; The Sequential Quadratic Programming (SQP) optimizer,
    Yukon</span></dt></dl></dd><dt><span class="section"><a href="ch20s02.html">Commands</a></span></dt><dd><dl><dt><span class="refentrytitle"><a href="Achieve.html">Achieve</a></span><span class="refpurpose"> &mdash; Specify a goal for a <span class="guilabel">Target</span>
    sequence</span></dt><dt><span class="refentrytitle"><a href="Minimize.html">Minimize</a></span><span class="refpurpose"> &mdash; Define the cost function to minimize</span></dt><dt><span class="refentrytitle"><a href="NonlinearConstraint.html">NonlinearConstraint</a></span><span class="refpurpose"> &mdash; Specify a constraint used during optimization, with an optional tolerance used when checking physical constraint values</span></dt><dt><span class="refentrytitle"><a href="Optimize.html">Optimize</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Target.html">Target</a></span><span class="refpurpose"> &mdash; Solve for condition(s) by varying one or more
    parameters</span></dt><dt><span class="refentrytitle"><a href="Vary.html">Vary</a></span><span class="refpurpose"> &mdash; Specifies variables used by a solver</span></dt></dl></dd></dl></div><p> This chapter contains documentation for Resources and Commands related to targeting and parameter optimization. </p><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N25D9B"></a>Resources</h2></div></div></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="Color.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="RefGuide.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="DifferentialCorrector.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Color&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;DifferentialCorrector</td></tr></table></div></body></html>