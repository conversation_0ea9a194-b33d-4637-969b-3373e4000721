<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Python Interface</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch22s03.html" title="System"><link rel="prev" href="MatlabInterface.html" title="MATLAB Interface"><link rel="next" href="ch23.html" title="Chapter&nbsp;23.&nbsp;Optimal Control"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Python Interface</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="MatlabInterface.html">Prev</a>&nbsp;</td><th align="center" width="60%">System</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch23.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="PythonInterface"></a><div class="titlepage"></div><a name="N2D2B0" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Python Interface</span></h2><p>Python Interface &mdash; Interface to the Python programming language</p></div><div class="refsection"><a name="N2D2C1"></a><h2>Description</h2><p>The Python interface provides a link to the Python programming
    language, allowing GMAT to run Python functions as if they were native
    functions in the GMAT script language.</p><p>The interface does not have any parameters that can be controlled
    directly through the GMAT script language. Instead, GMAT starts the Python
    interface automatically when it calls a Python function.</p><p>The Python interface is accessed using GMAT's CallPythonFunction
    command. For details on calling a function and passing data, see the
    <span class="guilabel"><a class="xref" href="CallPythonFunction.html" title="CallPythonFunction"><span class="refentrytitle">CallPythonFunction</span></a></span>
    reference.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="CallPythonFunction.html" title="CallPythonFunction"><span class="refentrytitle">CallPythonFunction</span></a></p></div><div class="refsection"><a name="N2D2D5"></a><h2>GUI</h2><p>The Python interface in GMAT is launched and driven internally.
    Users do not have direct access to the interface from the GMAT graphical
    user interface.</p></div><div class="refsection"><a name="N2D2DA"></a><h2>Remarks</h2><div class="refsection"><a name="N2D2DD"></a><h3>Interface
      Setup</h3><p>The following conditions must all be true for GMAT to successfully
      call into a compatible instance of Python.</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>The GMAT startup file must specify a version of the Python
          interface that matches the installed version of Python (see
          compatibility table below). Only one version of the Python interface
          can be loaded at a time. Note that untested Python interface
          versions are provided purely for convenience. </p><div class="informaltable"><table border="1"><colgroup><col width="24%"><col width="32%"><col width="44%"></colgroup><thead><tr><th>Python Version</th><th>Python Interface Plugin</th><th>Status</th></tr></thead><tbody><tr><td><span class="guilabel">3.6 (64-bit)</span></td><td><p>libPythonInterface_py36 </p></td><td><p>Linux and Windows only,
                    untested</p></td></tr><tr><td><span class="guilabel">3.7 (64-bit)</span></td><td><p>libPythonInterface_py37 </p></td><td><p>Linux and Windows only, untested
                    </p></td></tr><tr><td><span class="guilabel">3.8 (64-bit)</span></td><td><p>libPythonInterface_py38 </p></td><td><p>Untested </p></td></tr><tr><td><span class="guilabel">3.9 (64-bit)</span></td><td><p>libPythonInterface_py39 </p></td><td><p>Tested </p></td></tr><tr><td><span class="guilabel">3.10 (64-bit)</span></td><td>libPythonInterface_py310</td><td>Tested</td></tr><tr><td><span class="guilabel">3.11 (64-bit)</span></td><td>libPythonInterface_py311</td><td>Tested</td></tr><tr><td><span class="guilabel">3.12 (64-bit)</span></td><td>libPythonInterface_py312</td><td>Tested</td></tr></tbody></table></div></li><li class="listitem"><p>GMAT no longer supports 32-bit Python.</p></li><li class="listitem"><p>The Python interface accesses Python modules on the user's
          machine. This functionality is configured, including path
          information used by Python, on a per-OS basis as shown below.</p></li><li class="listitem"><p>On Windows: </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>The following path entries (where
                <code class="filename"><em class="replaceable"><code>Python</code></em></code> is the
                full path to the installed version of Python) must be present
                in the <code class="envar">Path</code> environment variable.</p><p><code class="filename"><em class="replaceable"><code>Python</code></em></code></p><p><code class="filename"><em class="replaceable"><code>Python</code></em>\Scripts</code></p><p></p></li><li class="listitem"><p>The following path (where
                <code class="filename"><em class="replaceable"><code>Python</code></em></code> is the
                path to the installed version of Python) must be present in
                the <code class="envar">PYTHONPATH</code> environment variable.</p><p><code class="filename"><em class="replaceable"><code>Python</code></em>\Lib\site-packages</code></p><p></p></li><li class="listitem"><p>The <code class="envar">PYTHONHOME</code> environment variable must
                be set to the directory containing
                <span class="emphasis"><em>python</em></span>3<span class="emphasis"><em>X.dll</em></span>, where
                <span class="emphasis"><em>3X</em></span> denotes the python version, for
                example <span class="bold"><strong>python39.dll</strong></span>. This
                directory is normally the root directory of your python
                installation or Anaconda environment.</p></li></ul></div><p>If you are using Anaconda Python, these paths may be set to
          point to directories within the Anaconda environment you want GMAT
          to use.</p></li><li class="listitem"><p>On macOS:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>On macOS, the Python interface only works with
                Python.org distributions of Python. These installations must
                be in the following specific directory (default for
                Python.org):
                <code class="filename">/Library/Frameworks/Python.framework/Versions/3.X</code></p><p>The Python interface has not been tested with Anaconda
                Python on macOS.</p></li><li class="listitem"><p>The following entry (where
                <code class="filename"><em class="replaceable"><code>Python</code></em></code> is the
                full path to the installed version of Python 3.X) must be
                present in the GMAT startup file. This allows the Python
                Interface to access Python packages such as Numpy.</p><p><code class="filename">PYTHON_MODULE_PATH =
                <em class="replaceable"><code>Python</code></em>/lib/python3.X/site-packages</code></p><p>Note that multiple
                <code class="filename">PYTHON_MODULE_PATH</code> entries are allowed in
                the startup file, and they can be placed anywhere in the
                file.</p></li><li class="listitem"><p>Python versions 3.6 and 3.7 are not supported for the
                Mac platform.</p></li></ul></div></li><li class="listitem"><p>On Linux: </p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: circle; "><li class="listitem"><p>The Python release used in the GMAT build must be the
                default Python package (e.g. Python 3.6) accessed from the
                terminal.</p><p>Users that enable the MATLAB interface may find that the
                Python interface does not load because of a library conflict.
                If the Python interface reports the issue</p><p><strong class="userinput"><code>undefined symbol:
                XML_SetHashSalt</code></strong></p><p>there is a conflict between the expat library that is
                included with MATLAB and the library required for the
                distribution's Python 3 installation. This issue can be
                corrected by loading the system expat library before GMAT
                starts using the LD_PRELOAD command. The command</p><p><strong class="userinput"><code>LD_PRELOAD=/usr/lib/x86_64-linux-gnu/libexpat.so
                ./GMAT</code></strong></p><p>loads the expat library and then launches GMAT,
                resolving the issue for systems like Ubuntu 18.04.</p></li></ul></div></li></ul></div><div class="note" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Note</h3><p>Common troubleshooting tips on Windows:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>If you are using the officially-released 32-bit version of
            GMAT, make sure you have the 32-bit version of Python
            installed.</p></li><li class="listitem"><p>If the path above exists in your system <code class="envar">Path</code>
            variable, try placing it at the front of the path
            specification.</p></li></ul></div></div></div><div class="refsection"><a name="N2D3BF"></a><h3>Python Engine Connection</h3><div class="warning" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Warning</h3><p>GMAT does not close the Python interface after a run has
        completed. This feature prevents anomalous behavior that can occur
        when loading some Python modules repeatedly during a run, but it can
        lead to confusing behavior if Python files are changed and rerun in
        the same GMAT session.</p><p>We recommend restarting GMAT after editing Python functions in
        order to guarantee that your edits take effect when you rerun your
        script.</p></div><p>When GMAT runs a mission that contains a Python function call, it
      loads Python into memory as an embedded system in GMAT before it makes
      the function call. It then reuses this system for the rest of the GMAT
      session.</p></div></div><div class="refsection"><a name="N2D3C9"></a><h2>Examples</h2><p>See the <a class="xref" href="CallPythonFunction.html" title="CallPythonFunction"><span class="refentrytitle">CallPythonFunction</span></a> reference for common
    examples.</p></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="MatlabInterface.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch22s03.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch23.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">MATLAB Interface&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;23.&nbsp;Optimal Control</td></tr></table></div></body></html>