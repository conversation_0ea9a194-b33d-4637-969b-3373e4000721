<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Component Status</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="WelcomeToGmat.html" title="Chapter&nbsp;1.&nbsp;Welcome to GMAT"><link rel="prev" href="ch01s04.html" title="Platform Support"><link rel="next" href="ch01s06.html" title="Contributors"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Component Status</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ch01s04.html">Prev</a>&nbsp;</td><th align="center" width="60%">Chapter&nbsp;1.&nbsp;Welcome to GMAT</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="ch01s06.html">Next</a></td></tr></table><hr></div><div class="section"><div class="titlepage"><div><div><h2 class="title" style="clear: both"><a name="N101BE"></a>Component Status</h2></div></div></div><p>GMAT is distributed with production and Alpha/Beta components.
    Components that are in Alpha/Beta status are turned off by default. The
    status of plugin components is shown below.</p><p>Production quality plugin components:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>libDataInterface</p></li><li class="listitem"><p>libEphemPropagator</p></li><li class="listitem"><p>libEventLocator</p></li><li class="listitem"><p>libFormation</p></li><li class="listitem"><p>libGmatFunction</p></li><li class="listitem"><p>libNewParameters</p></li><li class="listitem"><p>libPythonInterface_py39</p></li><li class="listitem"><p>libStation</p></li><li class="listitem"><p>libGmatEstimation</p></li><li class="listitem"><p>libMatlabInterface</p></li><li class="listitem"><p>libFminconOptimizer</p></li><li class="listitem"><p>libProductionPropagators</p></li><li class="listitem"><p>libScriptTools</p></li><li class="listitem"><p>libYukonOptimizer</p></li><li class="listitem"><p>libOpenFramesInterface</p></li><li class="listitem"><p>libThrustFile</p></li><li class="listitem"><p>libEKF</p></li><li class="listitem"><p>libTLEPropagator</p></li></ul></div><p>Alpha quality plugin components:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>libExtraPropagators</p></li><li class="listitem"><p>libPolyhedronGravity</p></li><li class="listitem"><p>libSaveCommand</p></li><li class="listitem"><p>libExternalForceModel_py39</p></li></ul></div><p>Internal-only plugins (not included in public releases):</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>proprietary/libMarsGRAM</p></li><li class="listitem"><p>proprietary/libMsise86</p></li><li class="listitem"><p>proprietary/libNRLMsise00</p></li><li class="listitem"><p>proprietary/libSNOptimizer</p></li><li class="listitem"><p>proprietary/libVF13Optimizer</p></li><li class="listitem"><p>proprietary/libEMTGModels</p></li><li class="listitem"><p>proprietary/libCSALTInterface</p></li></ul></div><p>Third-party plugins (developed and maintained by external
    contributors):</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>libOpenFramesInterface [Emergent Space Technologies,
        Inc.]</p></li></ul></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ch01s04.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="WelcomeToGmat.html">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="ch01s06.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Platform Support&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Contributors</td></tr></table></div></body></html>