<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Spacecraft Ballistic/Mass Properties</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="ch18.html#N158F2" title="Resources"><link rel="prev" href="SpacecraftAttitude.html" title="Spacecraft Attitude"><link rel="next" href="SpacecraftEpoch.html" title="Spacecraft Epoch"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Spacecraft Ballistic/Mass Properties</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="SpacecraftAttitude.html">Prev</a>&nbsp;</td><th align="center" width="60%">Resources</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SpacecraftEpoch.html">Next</a></td></tr></table><hr></div><div class="refentry"><a name="SpacecraftBallisticMass"></a><div class="titlepage"></div><a name="N1EA16" class="indexterm"></a><div class="refnamediv"><h2><span class="refentrytitle">Spacecraft Ballistic/Mass Properties</span></h2><p>Spacecraft Ballistic/Mass Properties &mdash; The physical properties of the spacecraft</p></div><div class="refsection"><a name="N1EA27"></a><h2>Description</h2><p>The <span class="guilabel">Spacecraft</span> ballistic and mass properties
    include the drag and SRP areas and coefficients as well as the spacecraft
    dry mass. These quantities are used primarily in orbital dynamics
    modeling. GMAT supports spherical SRP and drag models, and a higher
    fidelity drag and area model called SPAD.</p><p>GMAT also supports an extended set of mass properties that are used
    in modeling torques and changes in angular momentum. The
    <span class="guilabel">Spacecraft</span> extended mass properties are the
    spacecraft dry center of mass and dry moment of inertia.</p><p>Both the standard and extended mass property models collaborate with
    <span class="bold"><strong>FuelTank</strong></span> models to compute the
    aggregation of the dry spacecraft mass properties and the mass properties
    of the tank contents. The details of how this is done are discussed in the
    Remarks.</p><p><span class="ref_seealso">See Also</span>: <a class="xref" href="Propagate.html" title="Propagate"><span class="refentrytitle">Propagate</span></a>, <a class="xref" href="Propagator.html" title="Propagator"><span class="refentrytitle">Propagator</span></a>, <a class="xref" href="Spacecraft.html" title="Spacecraft"><span class="refentrytitle">Spacecraft</span></a>, <a class="xref" href="FuelTank.html" title="ChemicalTank"><span class="refentrytitle">ChemicalTank</span></a>,</p></div><div class="refsection"><a name="N1EA4B"></a><h2>Fields</h2><div class="informaltable"><table border="1"><colgroup><col width="25%"><col width="75%"></colgroup><thead><tr><th>Field</th><th>Description</th></tr></thead><tbody><tr><td><span class="guilabel">AddPlates</span></td><td><p>For use with the NPlate area model. Selection of
            <span class="guilabel">Plate</span> objects that comprise the spacecraft
            area model. See also <a class="xref" href="Plate.html" title="Plate"><span class="refentrytitle">Plate</span></a> </p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Instances of the <span class="guilabel">Plate</span>
                    resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Empty</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Not applicable</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">AtmosDensityScaleFactor</span></td><td><p>A multiplicative scale factor applied to the nominal
            atmospheric density computed by the chosen density
            model.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>dimensionless</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Cd</span></td><td><p>The coefficient of drag used to compute the
            acceleration due to drag for the spherical drag area model. This
            parameter is ignored when using SPAD drag
            modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>2.2</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>dimensionless</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CenterOfMassOffsetX</span></td><td><p>X component of offset to center of mass. This offset
            is only used with the "Lookup" model, allowing for deviations
            caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CenterOfMassOffsetY</span></td><td><p>Y component of offset to center of mass. This offset
            is only used with the "Lookup" model, allowing for deviations
            caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CenterOfMassOffsetZ</span></td><td><p>Z component of offset to center of mass. This offset
            is only used with the "Lookup" model, allowing for deviations
            caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">CenterOfMassTableFileName</span></td><td><p>Name of file containing center of mass interpolation
            data for various values of total spacecraft mass. The total mass
            includes both the dry mass of the spacecraft and the mass of the
            contents of fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file name for the center of mass table lookup
                    file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Cr</span></td><td><p>The coefficient of reflectivity used to compute the
            acceleration due to SRP for the spherical area model. A value of
            zero means the spacecraft is translucent to incoming radiation. A
            value of 1.0 indicates all radiation is absorbed and all the force
            is transmitted to the spacecraft. A value of 2.0 indicates all
            radiation is reflected and twice the force is transmitted to the
            spacecraft. This parameter is ignored when using SPAD SRP
            modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.8</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>dimensionless</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">Drag Area</span></td><td><p>The area used to compute acceleration due to
            atmospheric drag for the spherical drag area model. This parameter
            is ignored when using SPAD drag modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; = 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>15</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassX</span></td><td><p>The X component of dry spacecraft center of mass in
            BCS (m)</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassY</span></td><td><p>The Y component of dry spacecraft center of mass in
            BCS (m)</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryCenterOfMassZ</span></td><td><p>The Z component of dry spacecraft center of mass in
            BCS (m)</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMass</span></td><td><p>The dry mass of the <span class="guilabel">Spacecraft</span>
            (does not include fuel mass).</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;=0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>850</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXX</span></td><td><p>The XX component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>999</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXY</span></td><td><p>The XY component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaXZ</span></td><td><p>The XZ component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaYY</span></td><td><p>The YY component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>999</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaYZ</span></td><td><p>The YZ component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">DryMomentOfInertiaZZ</span></td><td><p>The ZZ component of the dry spacecraft moment of
            inertia</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt;= 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>999</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ExtendedMassPropertiesModel</span></td><td><p>Selection of whether to model center of mass, moment
            of inertia, both, or neither.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">None</span><span class="bold"><strong>,
                    </strong></span><span class="guilabel">CenterOfMass</span><span class="bold"><strong>,
                    </strong></span><span class="guilabel">MomentOfInertia</span><span class="bold"><strong>,
                    </strong></span><span class="guilabel">CenterOfMassAndMomentOfInertia</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p><span class="bold"><strong>None</strong></span></p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">ExtendedMassPropertiesModelType</span></td><td><p>Selection of models used to compute center of mass
            and moment of inertia. The available models are to interpolate
            mass property files ("Lookup") or to compute the system mass
            properties based on the dry spacecraft mass properties and the
            mass properties of the contents of each FuelTank ("Analytic").</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p><span class="guilabel">Lookup</span>,
                    <span class="guilabel">Analytic</span></p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Analytic</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MassPropertiesTableFilePath</span></td><td><p>Path to files used to interpolate center of mass or
            moment of inertia data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid path to directory containing interpolation
                    table text file(s) for center of mass or moment of
                    inertia</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>""</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetXX</span></td><td><p>XX component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetXY</span></td><td><p>XY component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetXZ</span></td><td><p>XZ component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetYY</span></td><td><p>YY component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetYZ</span></td><td><p>YZ component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaOffsetZZ</span></td><td><p>ZZ component of offset to moment of inertia. This
            offset is only used with the "Lookup" model, allowing for
            deviations caused by factors not accounted for in pre-tabulated
            data.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>kg-m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">MomentOfInertiaTableFileName</span></td><td><p>Name of file containing moment of inertia
            interpolation data for various values of total spacecraft mass.
            The total mass includes both the dry mass of the spacecraft and
            the mass of the contents of fuel tanks.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Valid file name for the moment of inertia table
                    lookup file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">NPlateSRPEquateAreaCoefficients</span></td><td><p>Equate or link NPlate model SRP area coefficients for
            estimation. When estimating the AreaCoefficient for one or more
            plates, any plates specified in this list will be forced to have
            the same value of AreaCoefficient during the estimation process.
            Only applicable when the NPlate area model is in use and solving
            for AreaCoefficient.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Resource array</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Instances of the <span class="guilabel">Plate</span>
                    resource</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>Set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Empty</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>Not applicable</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>Script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADDragFile</span></td><td><p>Name (and optionally path information) of SPAD drag
            model file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>valid path and SPAD file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADDragInterpolationMethod</span></td><td><p>Interpolation method for SPAD drag
            vectors.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>'Bicubic' or 'Bilinear'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Bilinear</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADDragScaleFactor</span></td><td><p>Scale factor applied to the drag force when using a
            SPAD drag area model.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>dimensionless</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADSRPFile</span></td><td><p>Name (and optionally path information) of SPAD SRP
            model file.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>valid path and SPAD file</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADSRPInterpolationMethod</span></td><td><p>Interpolation method for SPAD SRP model
            vectors.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>String</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>'Bicubic' or 'Bilinear'</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>Bilinear</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>N/A</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SPADSRPScaleFactor</span></td><td><p>Scale factor applied to SRP force when using a SPAD
            SRP area model.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Any Real number</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1.0</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>dimensionless</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr><tr><td><span class="guilabel">SRPArea</span></td><td><p>The area used to compute acceleration due to solar
            radiation pressure for the spherical SRP area model. This
            parameter is ignored when using SPAD SRP
            modeling.</p><div class="variablelist"><table border="0" class="variablelist"><colgroup><col valign="top" align="left"><col></colgroup><tbody><tr><td><p><span class="term">Data Type</span></p></td><td><p>Real</p></td></tr><tr><td><p><span class="term">Allowed Values</span></p></td><td><p>Real &gt; 0</p></td></tr><tr><td><p><span class="term">Access</span></p></td><td><p>set, get</p></td></tr><tr><td><p><span class="term">Default Value</span></p></td><td><p>1</p></td></tr><tr><td><p><span class="term">Units</span></p></td><td><p>m^2</p></td></tr><tr><td><p><span class="term">Interfaces</span></p></td><td><p>GUI, script</p></td></tr></tbody></table></div></td></tr></tbody></table></div></div><div class="refsection"><a name="N1F0E1"></a><h2>GUI</h2><div class="screenshot"><div class="mediaobject" align="center"><table border="0" summary="manufactured viewport for HTML img" style="cellpadding: 0; cellspacing: 0;" width="100%"><tr><td align="center"><img src="../files/images/Resource_Spacecraft_BallisticMass_GUI.png" align="middle" height="628"></td></tr></table></div></div><p>The GUI interface for ballistic and mass properties is contained on
    the<span class="guilabel"> Ballistic/Mass</span> tab of the
    <span class="guilabel">Spacecraft</span> resource. You can enter physical
    properties such as the drag and SRP areas and coefficients and the
    <span class="guilabel">Spacecraft</span> dry mass which are used in orbital
    dynamics modeling. GMAT supports a spherical SRP model and a SPAD (Solar
    Pressure and Aerodynamic Drag) file.</p></div><div class="refsection"><a name="N1F0F8"></a><h2>Remarks</h2><div class="refsection"><a name="N1F0FB"></a><h3>Configuring Area Properties for the Spherical Area Model</h3><a name="N1F0FE" class="indexterm"></a><p>GMAT supports a spherical (sometimes called a "cannonball") area
      model for drag and SRP modeling. In the spherical model, the area is
      assumed to be independent of the spacecraft's orientation with respect
      to the local velocity vector and the sun vector. The spherical area
      model is selected by setting the configured
      <span class="guilabel">ForceModel</span> <span class="guilabel">SRP.SRPModel</span> and
      <span class="guilabel">Drag.DragModel</span> to <span class="guilabel">Spherical</span>.
      For more details on the computation and configuration of drag and SRP
      models see the <a class="xref" href="ForceModel.html" title="ForceModel"><span class="refentrytitle">ForceModel</span></a> documentation.</p></div><div class="refsection"><a name="N1F115"></a><h3>Configuring Area Properties for the NPlate Area Model</h3><p>GMAT supports a multi-plate area model for SRP modeling. In the
      NPlate model, the area is built up from a collection of
      <span class="guilabel">Plate</span> resources, each specifying a separate area,
      orientation, and reflectivity properties. The NPlate area model is
      selected by setting the configured <span class="guilabel">ForceModel</span>
      <span class="guilabel">SRP.SRPModel</span> to <span class="guilabel">NPlate</span>. The
      NPlate model is not currently available for drag modeling. For more
      details on the configuration of the NPlate model, see the <a class="xref" href="Plate.html" title="Plate"><span class="refentrytitle">Plate</span></a> documentation.</p></div><div class="refsection"><a name="N1F12A"></a><h3>Configuring Area Properties for the SPAD Area Model</h3><a name="N1F12D" class="indexterm"></a><a name="N1F132" class="indexterm"></a><p>SPAD stands for Solar Pressure and Aerodynamic Drag. A SPAD file
      can be used for high fidelity SRP and drag modeling taking into account
      the physical properties of the spacecraft (shape and reflectivity) and
      the spacecraft attitude. SPAD files contain tabulated data representing
      the spacecraft area scaled by physical properties like Cr including
      specular, diffuse, and reflective properties. Area data in the SPAD file
      is tabulated as a function of azimuth and elevation in the spacecraft
      body frame. In the case of SRP modeling, the azimuth and elevation
      tabulated on the file should be the azimuth and elevation of the vector
      from the Spacecraft to the Sun, expressed in the body frame. For drag
      modeling, the azimuth and elevation denote the direction of the
      spacecraft velocity vector relative to the rotating atmosphere. The SPAD
      area model is selected by setting the configured
      <span class="guilabel">ForceModel</span> <span class="guilabel">SRP.SRPModel</span> and
      <span class="guilabel">Drag.DragModel</span> to
      <span class="guilabel">SPADFile</span>.</p><p>To compute the SRP or drag acceleration at each integration point,
      GMAT determines the sun or relative velocity vector&rsquo;s azimuth and
      elevation in the spacecraft body frame at the integration time, and then
      interpolates the SPAD data using bi-linear or bi-cubic interpolation.
      Since the SRP or drag vector is taken in the spacecraft body frame, this
      formulation results in an attitude dependent acceleration. For more
      details on the computation and configuration of drag and SRP models see
      the <a class="xref" href="ForceModel.html" title="ForceModel"><span class="refentrytitle">ForceModel</span></a>
      documentation.</p><div class="caution" style="margin-left: 0.5in; margin-right: 0.5in;"><h3 class="title">Caution</h3><p>When using a SPAD file, GMAT uses the attitude defined on the
        <span class="guilabel">Spacecraft</span> resource to compute the Sun's or
        relative velocity vector in the body frame. If the attitude uses a
        coordinate system with <span class="guilabel">Axes</span> set to
        <span class="guilabel">ObjectReferenced</span>, and those axes refer back to
        the <span class="guilabel">Spacecraft</span> orbit state (i.e. VNB or LVLH
        systems), GMAT holds the attitude constant over a given integration
        step. In those cases, we recommend carefully choosing a maximum step
        size small enough to ensure the resulting approximation is acceptable
        for your application.</p></div><p>A valid SPAD file header, and the first three lines of data are
      shown below for illustrative purposes. Note, GMAT does not use all
      values provide on the file and GMAT's usage of SPAD files is described
      in detail in the table below the example.</p><pre class="programlisting"><code class="code">Version            : 4.21
System             : sphericalSat
Analysis Type      : Area
Pixel Size         : 5
Spacecraft Size    : 436.2
Pressure           : 1
Center of Mass     :  (50.9, 184.9, -49)
Current time       : May  7, 2009  15:53:38.00

Motion    : 1
  Name    : Azimuth
  Method  : Step
  Minimum : -180
  Maximum : 180
  Step    : 5
Motion    : 2
  Name    : Elevation
  Method  : Step
  Minimum : -90
  Maximum : 90
  Step    : 5
: END

Record count       : 2701

 AzimuthElevatio  Force(X)  Force(Y)  Force(Z)  
 degrees degrees      m^2      m^2      m^2      
 ------- ------- --------- --------- --------- --------- 
 -180.00   -90.00 -0.00000000000000 -0.00000000000000 -8.94500000000000 
 -180.00   -85.00 -0.77960811887780 -0.00000000000000 -8.91096157443066 
 -180.00   -80.00 -1.55328294923069 -0.00000000000000 -8.80910535069420 </code></pre><p>A SPAD file contains three sections as illustrated below. Data
      specifications for items in each section are described in the tables
      below. A SPAD file header may contain many fields but only a few are
      used by GMAT as described below. Other fields are ignored.</p><div class="informaltable"><table border="1"><colgroup><col width="26%"><col width="7%"><col width="67%"></colgroup><thead><tr><th>Keyword</th><th>Required</th><th>Description and Supported Values</th></tr></thead><tbody><tr><td><span class="guilabel">Analysis Type</span></td><td><p>Y</p></td><td><p>The SPAD software can creates files with Analysis
              Types of Solar Pressure, Area, and Drag. GMAT only supports the
              Area option. </p><p>Example: </p> <pre class="programlisting"><code class="code">Analysis Type : Area</code></pre></td></tr><tr><td><span class="guilabel">Pressure</span></td><td><p>N</p></td><td><p>SPAD supports the ability to apply a scale factor
              for SRP and drag. GMAT does not read this value, and its purpose
              in the SPAD file is to inform the user that the properties on
              the file have been scaled by the Pressure factor. The value is
              usually &ldquo;1&rdquo;. However, when not 1, it is possible to apply a
              scale factor twice, once from the value applied on the data in
              the SPAD file, and once from the
              <span class="guilabel">SPADSRPScaleFactor</span> or
              <span class="guilabel">SPADDragScaleFactor</span>. Care should be taken
              to ensure that if the desired scale factor was applied during
              file creation that it is not reapplied in GMAT. </p></td></tr></tbody></table></div><p>The SPAD file Motion Data section describes the data contained in
      the body of the file. The Motion Data fields used by GMAT are described
      below. Others are ignored.</p><div class="informaltable"><table border="1"><colgroup><col width="26%"><col width="7%"><col width="67%"></colgroup><thead><tr><th>Keyword</th><th>Required</th><th>Description and Supported Values</th></tr></thead><tbody><tr><td><span class="guilabel">Motion</span></td><td><p>Y</p></td><td><p>Together, the Motion and Name fields specify the
              type of data in the first two columns of the body of the file.
              GMAT currently supports Azimuth and Elevation Motion only (no
              articulating appendages) and requires that the first Motion is
              Azimuth and the second Motion is Elevation as shown
              below.</p> <p> Examples:</p> <pre class="programlisting"><code class="code">Motion : 1
Name : Azimuth</code></pre> <p>and</p><pre class="programlisting"><code class="code">Motion : 2 
Name : Elevation</code></pre></td></tr><tr><td><span class="guilabel">Name</span></td><td><p>Y</p></td><td><p>Together, the Motion and Name fields specify the
              type of data in the first two columns of the body of the file.
              GMAT currently supports Azimuth and Elevation Motion only (no
              articulating appendages) and requires that the first Motion is
              Azimuth and the second Motion is Elevation as shown
              below.</p><p> Examples:</p> <pre class="programlisting"><code class="code">Motion : 1
Name : Azimuth</code></pre> <p>and</p><pre class="programlisting"><code class="code">Motion : 2 
Name : Elevation</code></pre></td></tr><tr><td><span class="guilabel">Method</span></td><td><p>Y</p></td><td><p>The step size in the independent variable. The only
              supported value is Step.</p><p>Example:</p>
              <pre class="programlisting"><code class="code">Motion : 1 
Method : Step </code></pre></td></tr><tr><td><span class="guilabel">Maximum</span></td><td><p>Y</p></td><td><p>The maximum value for an independent variable
              (Motion Type). For Azimuth, Maximum must be 180, and for
              Elevation Maximum must be 90.</p><p>Example:</p>
              <pre class="programlisting"><code class="code">Motion : 1
Name : Azimuth 
Maximum : 180 

Motion : 2
Name : Elevation
Maximum : 90</code></pre></td></tr><tr><td><span class="guilabel">Minimum</span></td><td><p>Y</p></td><td><p>The minimum value for an independent variable.
              (Motion Type). For Azimuth, minimum must be -180, and for
              Elevation minimum must be -90. </p><p>Example:</p>
              <pre class="programlisting"><code class="code">Motion : 1
Name : Azimuth
Minimum : -180

Motion : 2
Name : Elevation
Minimum : -90</code></pre></td></tr><tr><td><span class="guilabel">Step</span></td><td><p>Y</p></td><td><p>The step size for the independent variable (Motion
              Type). If Step does not divide evenly into the variable range,
              then errors may occur because the maximum and/or minimum values
              may not be on the file. </p><p>Example:</p><p>Motion
              : 1 </p><p> Step : 15</p></td></tr><tr><td><span class="guilabel">Record count </span></td><td><p>Y</p></td><td><p>Record count is the number of rows of data in the
              data segment. Record count = (360/(Azimuth Step)
              +1)*(180/(Elevation Step) +1).</p><p>Example:</p>
              <pre class="programlisting"><code class="code">Record count : 325</code></pre></td></tr></tbody></table></div><p>The SPAD file data block contains tabulated force modeling data as
      described below.</p><div class="informaltable"><table border="1"><colgroup><col width="26%"><col width="7%"><col width="67%"></colgroup><thead><tr><th>Keyword</th><th>Required</th><th>Description and Supported Values</th></tr></thead><tbody><tr><td><span class="guilabel">Azimuth</span></td><td><p>Y</p></td><td><p>Azimuth data column. Must be first column in the
              data. Units must be degrees. Azimuth is the azimuth of the
              vector from spacecraft to Sun (for SRP, atan2(ySun,xSun)), or
              the relative velocity vector (for drag) expressed in the body
              frame.</p><p>Example: </p> <pre class="programlisting"><code class="code">AzimuthElevatio
degrees degrees
------- -------
-180.00 -90.00
-180.00 -75.00
-180.00 -60.00</code></pre></td></tr><tr><td><span class="guilabel">Elevation</span></td><td><p>N</p></td><td><p>Elevation data column. Must be second column in the
              data. Units must be degrees. Elevation is the elevation of the
              vector from spacecraft to Sun (for SRP, atan2(zSun,sqrt(xSun^2 +
              ySun^2)), or the relative velocity vector (for drag) expressed
              in the body frame.</p><p>Example: </p><pre class="programlisting"><code class="code">AzimuthElevatio
degrees degrees
------- -------
-180.00 -90.00
-180.00 -75.00
-180.00 -60.00</code></pre></td></tr><tr><td><span class="guilabel">Force(*)</span></td><td><p>N</p></td><td><p>Area vector columns. Must be columns 3-5 in the
              data. Quantities must be in base units of m^2,mm^2,cm^2,in^2, or
              ft^2. If another unit is provided in the header lines, an
              exception is thrown. The area vector is the direction of the
              resulting SRP force in the spacecraft body frame, scaled by area
              and reflectivity properties. </p><p>Example: See code
              listing above.</p></td></tr></tbody></table></div></div><div class="refsection"><a name="N1F273"></a><h3>Total Mass Computation</h3><p>The <span class="guilabel">TotalMass</span> property of a
      <span class="guilabel">Spacecraft</span> is a read-only property that is the sum
      of the <span class="guilabel">DryMass</span> value and the sum of the fuel mass
      in all attached fuel tanks. GMAT&rsquo;s propagators will not allow the total
      mass of a spacecraft to be negative. However, GMAT will allow the mass
      of a <span class="guilabel">ChemicalTank</span> to be negative. See the <a class="xref" href="FuelTank.html" title="ChemicalTank"><span class="refentrytitle">ChemicalTank</span></a> documentation for
      details.</p></div><div class="refsection"><a name="N1F288"></a><h3>Extended Mass Properties: Overview</h3><p>In addition to computing the <span class="guilabel">TotalMass</span>
      property of a <span class="guilabel">Spacecraft</span>, GMAT can compute the
      <span class="guilabel">SystemCenterOfMass</span> and
      <span class="guilabel">SystemMomentOfInertia</span> properties. Like
      <span class="guilabel">TotalMass</span> these properties are read-only and they
      are computed from the corresponding properties of the dry
      <span class="guilabel">Spacecraft</span>. The resulting
      <span class="guilabel">SystemCenterOfMass</span> is expressed in the
      <span class="guilabel">Spacecraft</span>'s body coordinate system (BCS), and the
      resulting <span class="guilabel">SystemMomentOfInertia</span> is with respect to
      the <span class="guilabel">SystemCenterOfMass</span>.</p><p>There are two models for computing the extended mass
      properties</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>the <span class="guilabel">Lookup</span> option which interpolates
          lookup tables where mass is the independent variable and either
          center of mass or moment of inertia is the dependent variable,
          and</p></li><li class="listitem"><p>the <span class="guilabel">Analytic</span> option, which computes the
          system properties from the dry properties and the fuel
          properties.</p></li></ul></div><p>At this writing the <span class="guilabel">Lookup</span> model is fully
      implemented and the <span class="guilabel">Analytic</span> model is under
      development.</p><p>The selection of models is controlled by two input parameters. The
      first, <span class="guilabel">ExtendedMassPropertiesModelType</span>, controls
      whether the <span class="guilabel">Lookup</span> or <span class="guilabel">Analytic</span>
      model is to be used. The second,
      <span class="guilabel">ExtendedMassPropertiesModeled</span>, controls which
      properties are to be computed. The <span class="guilabel">TotalMass</span> is
      always computed, if neither center of mass nor moment of inertia are
      needed, the value of <span class="guilabel">ExtendedMassPropertiesModeled</span>
      is set to <span class="guilabel">None</span>. This is the default. The default
      value of <span class="guilabel">ExtendedMassPropertiesModelType</span> is
      <span class="guilabel">Analytic</span>; this was chosen so that no lookup files
      would need to be specified in the default case. Until the analytic model
      for extended mass properties is completed choosing the
      <span class="guilabel">Analytic</span> model is <span class="emphasis"><em>only</em></span> valid
      if <span class="guilabel">ExtendedMassPropertiesModel</span> is set to
      <span class="guilabel">None</span>. Put another way, the default is to not model
      the extended mass properties, and only compute
      <span class="guilabel">TotalMass</span><span class="bold"><strong>. </strong></span>The
      other options are <span class="guilabel">CenterOfMass</span>,
      <span class="guilabel">MomentOfInertia</span>, and
      <span class="guilabel">CenterOfMassAndMomentOfInertia</span>, and should be
      self-explanatory.</p></div><div class="refsection"><a name="N1F2FA"></a><h3>Extended Mass Properties: Lookup Model</h3><p>There are three elements to using the <span class="guilabel">Lookup</span>
      model: specifying the filenames to be used, understanding the formats of
      the lookup table files, and the offsets from interpolated data. The
      center of mass and moment of inertia lookup data are stored in separate
      files and interpolated independently. It is up to the user to get a
      consistent set of data for a given spacecraft; the
      <span class="guilabel">Lookup</span> model is more often used in mission
      operations for spacecraft whose properties are well understood. It is
      expected that the two files will be stored in the same folder; the
      parameter <span class="guilabel">MassPropertiesTableFilePath</span> is used to
      set a path to that folder. The file names are provided by the parameters
      <span class="guilabel">CenterOfMassTableFileName</span> and
      <span class="guilabel">MomentOfInertiaTableFileName</span>.</p><p>Both files are text files containing spacecraft masses as the
      first parameter in each row, and the corresponding center of mass or
      moment of inertia values completing the row.</p><p>The center of mass data is tabulated in a text file indexed by
      this value. A sample file is shown below. The header data in this file
      is informational only in the current implementation. The data shown
      there is not used in the current GMAT code, but may be used in a later
      implementation.</p><pre class="programlisting"><code class="code">
% Center of Mass Datafile

Spacecraft:  SampleSat
Index:  TotalMass
CoordinateSystem:  BCS
Units:   Meters

% Data order:
% RefMass, COMx COMy COMz

BeginData
2200    0.0220    0.0500    0.3000
2100    0.0215    0.0366    0.2888
2000    0.0211    0.0233    0.2777
1900    0.0206    0.0100    0.2666
1800    0.0202   -0.0033    0.2555
1700    0.0197   -0.0166    0.2444
1600    0.0193   -0.0300    0.2333
1500    0.0188   -0.0433    0.2222
1400    0.0184   -0.0566    0.2111
1300    0.0180   -0.0700    0.2000
EndData</code></pre><p>The data tabulated between the BeginData and EndData lines are
      used in GMAT to interpolate the location of the center of mass, using
      GMAT&rsquo;s Lagrange interpolator. The first column of the data is
      spacecraft&rsquo;s total mass, providing the index into the table. This index
      column must be monotonic, but can be either increasing or, as shown
      here, decreasing. The location of the center of mass is tabulated in
      Cartesian body fixed coordinates. Each row specifies the center of mass
      location corresponding to the total mass value in the first column. The
      data are in X-Y-Z order. The center of mass location is specified in
      meters. If the <span class="guilabel">TotalMass</span> used to interpolate is
      outside of the range given by the upper and lower bounds of the
      tabulated masses in the first column, GMAT throws an exception
      indicating that the spacecraft center of mass cannot be
      interpolated.</p><p>The moment of inertia tensor is tabulated similarly. A sample file
      is shown below. As in the case of center of mass, the header information
      in the moment of inertial table file is not currently used, but may be
      in a later implementation.</p><pre class="programlisting"><code class="code">
% Moment of Inertia Datafile

Spacecraft:  SampleSat
Index:  TotalMass
Origin:  Spacecraft Center of Mass
Units:   kg-m^2

% Data order:
% RefMass, MOIxx MOIyy MOIzz MOIxy MOIxz MOIyz

BeginData
1300    12.755    17.423   14.111     0.187   -0.622    0.455
1400    12.655    17.001   14.116     0.186   -0.622    0.445
1500    12.455    16.722   14.120     0.185   -0.622    0.435
1600    12.155    16.432   14.123     0.184   -0.622    0.425
1700    12.055    16.395   14.124     0.183   -0.622    0.415
1800    12.155    16.388   14.124     0.182   -0.622    0.405
1900    12.455    16.376   14.124     0.181   -0.622    0.395
2000    12.555    16.368   14.123     0.180   -0.622    0.385
2100    12.755    16.365   14.119     0.179   -0.622    0.375
2200    12.955    16.365   14.107     0.178   -0.622    0.365
EndData</code></pre><p>The data tabulated between the BeginData and EndData lines are
      used in GMAT to interpolate the moments and products of inertia relative
      to the spacecraft&rsquo;s center of mass, using GMAT&rsquo;s Lagrange interpolator.
      The first column of the data is spacecraft&rsquo;s total mass, providing the
      index into the table. This index column must be monotonic, but can be
      either increasing, as shown here, or decreasing. Each tensor element is
      tabulated in the Cartesian system. Each row specifies the inertia tensor
      component relative to the spacecraft center of mass that corresponds to
      the total mass value in the first column. The data are in
      XX-YY-ZZ-XY-XZ-YZ order. The moments and products of inertia are
      specified in kilogram-meters squared. If the
      <span class="guilabel">TotalMass</span> used to interpolate is outside of the
      range given by the upper and lower bounds of the tabulated masses in the
      first column, GMAT throws an exception indicating that the spacecraft
      center of mass cannot be interpolated.</p><p>The spacecraft center of mass as tabulated above is specified as a
      nominal location for the center of mass. That location may shift based
      on factors in the spacecraft configuration that were not accounted for
      in the tabulated data at the start of a project. GMAT scripting
      accommodates these factors through a set of offset values on the
      spacecraft. Center of mass has 3 offset parameters representing an
      offset in the X, Y and Z directions. Moment of inertia has 6 offsets,
      representing the XX, YY, ZZ, XY, XZ, and YZ. In both cases the offsets
      default to zero. Of the two, the center of mass offset is more likely to
      be used in practice; the moment of inertia offsets are there for
      completeness and the unanticipated.</p></div><div class="refsection"><a name="N1F328"></a><h3>Extended Mass Properties: Analytic Model</h3><p>As this model is not yet completed, this section will outline the
      computations and not emphasize the different user options, which will be
      documented here upon complete implementation. We will assume that both
      center of mass and moment of inertia have been selected.</p><p>The analytic model divides the responsibility for computing the
      extended mass properties between the <span class="guilabel">Spacecraft</span> and
      <span class="guilabel">FuelTank</span> classes. The <span class="guilabel">FuelTank</span>
      will be capable of computing its own center of mass in BCS and moment of
      inertia about the system center of mass. The
      <span class="guilabel">Spacecraft</span> is responsible for adding up all the
      fuel mass properties and the dry spacecraft mass properties to get the
      system mass properties. For moment of inertia, since all the components
      are computing their moments of inertia around the system center of mass
      the system moment of inertia is the sum of all the component moments of
      inertia. The computational steps are:</p><div class="itemizedlist"><ul class="itemizedlist" style="list-style-type: disc; "><li class="listitem"><p>For each <span class="guilabel">FuelTank</span>, compute center of mass
          and moment of inertia of its contents -- this is the part of the
          model that has not yet been completed.</p></li><li class="listitem"><p>For each <span class="guilabel">FuelTank</span>, compute the center of
          mass in BCS. This is a function of the tank's center of mass in tank
          coordinates, the position of the tank coordinate system's origin in
          BCS and the orientation of the tank coordinate system to the
          BCS.</p></li><li class="listitem"><p>In the <span class="guilabel">Spacecraft</span> object, compute the
          system center of mass. This is the weighted average of the
          individual centers of mass of fuel and the dry spacecraft, weighted
          by the mass of each.</p></li><li class="listitem"><p>For each <span class="guilabel">FuelTank</span>, compute the moment of
          inertia about the system center of mass using the Parallel Axis
          Theorem. Note that this computation uses the system center of mass
          computed in the previous step.</p></li><li class="listitem"><p>In the <span class="guilabel">Spacecraft</span> object, compute system
          moment of inertia. This is simply the sum of each of the individual
          moments of inertia, as they are all computed about the system center
          of mass.</p></li></ul></div><p>The code for the last 4 steps is implemented and integrated into
      both classes. When the last piece is implemented the code raising an
      exception if analytic modeling of extended mass properties is selected
      will be removed.</p></div></div><div class="refsection"><a name="N1F35C"></a><h2>Examples</h2><div class="informalexample"><p>Configure physical properties for a spherical SRP model.</p><pre class="programlisting"><code class="code">Create Spacecraft aSpacecraft
aSpacecraft.Cd       = 2.2
aSpacecraft.Cr       = 1.8
aSpacecraft.DragArea = 40
aSpacecraft.SRPArea  = 35
aSpacecraft.DryMass  = 2000
Create Propagator aPropagator

BeginMissionSequence

Propagate aPropagator(aSpacecraft, {aSpacecraft.ElapsedSecs = 600})</code></pre></div><div class="informalexample"><p>Configure a SPAD SRP model.</p><pre class="programlisting"><code class="code">Create Spacecraft aSpacecraft;
aSpacecraft.DryMass = 2000
aSpacecraft.SPADSRPFile = '../data/vehicle/spad/SphericalModel.spo'
aSpacecraft.SPADSRPScaleFactor = 1
aSpacecraft.SPADDragInterpolationMethod = Bicubic

Create ForceModel aFM;
aFM.SRP          = On;
aFM.SRP.SRPModel = SPADFile

Create Propagator aProp;
aProp.FM = aFM;

BeginMissionSequence

Propagate aProp(aSpacecraft) {aSpacecraft.ElapsedDays = 0.2}</code></pre></div><div class="informalexample"><p>Computing center of mass and moment of inertia changes.</p><pre class="programlisting"><code class="code">
         %---------- Spacecraft ----------------
         Create Spacecraft JSL;
         JSL.Tanks = {TankA}
         JSL.Thrusters = {ThrusterA}

         %% Added Mass Properties' lookup table files
         JSL.DryMass = 2440;
         JSL.MassPropertiesTableFilePath = '../include'
         JSL.ExtendedMassPropertiesModelType = 'Lookup'
         JSL.ExtendedMassPropertiesModel     = 'CenterOfMassAndMomentOfInertia'
         JSL.CenterOfMassTableFileName       = 'FakeLinearCM.table'
         JSL.MomentOfInertiaTableFileName    = 'FakeLinearMOI.table'

         Create ChemicalTank TankA
         TankA.Volume = 2.0
         TankA.FuelMass = 1500
         Create Thruster ThrusterA
         ThrusterA.Tank = {TankA}
         Create FiniteBurn fb1;
         fb1.Thrusters = {ThrusterA};
         ThrusterA.DecrementMass = true;

         %---------- Propagator ----------------
         Create Propagator EarthProp;
         Create ForceModel TorqueForces;
         EarthProp.FM = TorqueForces

         %---------- Reports -------------------
         Create ReportFile CM;
         CM.Filename = 'CM.report'
         Create ReportFile MOI;
         MOI = 'MOI.report'

         %---------- Mission Sequence ----------
         BeginMissionSequence;

         BeginFiniteBurn fb1(JSL);
            For loop = 1:14
               Report  CM JSL.TotalMass JSL.SystemCenterOfMassX JSL.SystemCenterOfMassY JSL.SystemCenterOfMassZ
               Report MOI JSL.SystemMomentOfInertiaXX JSL.SystemMomentOfInertiaXY JSL.SystemMomentOfInertiaXZ  ...
                          JSL.SystemMomentOfInertiaYY JSL.SystemMomentOfInertiaYZ JSL.SystemMomentOfInertiaZZ
               nextFuelMass = JSL.TankA.FuelMass - 100
               Propagate EarthProp(JSL) {JSL.TankA.FuelMass = nextFuelMass}
            EndFor
            Report  CM JSL.TotalMass JSL.SystemCenterOfMassX JSL.SystemCenterOfMassY JSL.SystemCenterOfMassZ
            Report MOI JSL.SystemMomentOfInertiaXX JSL.SystemMomentOfInertiaXY JSL.SystemMomentOfInertiaXZ  ...
                       JSL.SystemMomentOfInertiaYY JSL.SystemMomentOfInertiaYZ JSL.SystemMomentOfInertiaZZ

         EndFiniteBurn fb1(JSL);</code>code&gt;</pre></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="SpacecraftAttitude.html">Prev</a>&nbsp;</td><td align="center" width="20%"><a accesskey="u" href="ch18.html#N158F2">Up</a></td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SpacecraftEpoch.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Spacecraft Attitude&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Spacecraft Epoch</td></tr></table></div></body></html>