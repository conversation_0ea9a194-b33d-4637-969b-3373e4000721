<html><head><META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1"><title>Tutorials</title><link href="../files/style.css" type="text/css" rel="stylesheet"><meta content="DocBook XSL Stylesheets V1.78.1" name="generator"><link rel="home" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="up" href="index.html" title="General Mission Analysis Tool (GMAT)"><link rel="prev" href="ConfiguringGmat_DataFiles.html" title="Configuring Data Files"><link rel="next" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"></head><body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF"><div class="navheader"><table summary="Navigation header" width="100%"><tr><th align="center" colspan="3">Tutorials</th></tr><tr><td align="left" width="20%"><a accesskey="p" href="ConfiguringGmat_DataFiles.html">Prev</a>&nbsp;</td><th align="center" width="60%">&nbsp;</th><td align="right" width="20%">&nbsp;<a accesskey="n" href="SimulatingAnOrbit.html">Next</a></td></tr></table><hr></div><div class="part"><div class="titlepage"><div><div><h1 class="title"><a name="Tutorials"></a>Tutorials</h1></div></div></div><div class="partintro"><div></div><p>The <span class="emphasis"><em><a class="xref" href="Tutorials.html" title="Tutorials">Tutorials</a></em></span> section contains in-depth tutorials
    that show you how to use GMAT for end-to-end analysis. The tutorials are
    designed to teach you how to use GMAT in the context of performing
    real-world analysis and are intended to take between 30 minutes and
    several hours to complete. Each tutorial has a difficulty level and an
    approximate duration listed with any prerequisites in its introduction,
    and are arranged in a general order of difficulty. </p><p>Here is a summary of selected Tutorials. For a complete list of
    tutorials see the <span class="emphasis"><em><a class="xref" href="Tutorials.html" title="Tutorials">Tutorials</a></em></span> chapter.</p><p>The <span class="emphasis"><em><a class="xref" href="SimulatingAnOrbit.html" title="Chapter&nbsp;5.&nbsp;Simulating an Orbit"><i>Simulating an Orbit</i></a></em></span> tutorial is the first tutorial you
    should take to learn how to use GMAT to solve mission design problems. You
    will learn how to specify an orbit and propagate to orbit
    periapsis.</p><p>The <span class="emphasis"><em><a class="xref" href="Mars_B_Plane_Targeting.html" title="Chapter&nbsp;8.&nbsp;Mars B-Plane Targeting"><i>Mars B-Plane Targeting</i></a></em></span> tutorial shows how to use GMAT to
    design a Mars transfer trajectory by targeting desired B-plane conditions
    at Mars.</p><p>The <span class="emphasis"><em><a class="xref" href="Tut_TargetFiniteBurn.html" title="Chapter&nbsp;7.&nbsp;Target Finite Burn to Raise Apogee"><i>Target Finite Burn to Raise Apogee</i></a></em></span> tutorial shows how to raise orbit
    apogee using finite maneuver targeting.</p><div class="toc"><p><b>Table of Contents</b></p><dl class="toc"><dt><span class="chapter"><a href="SimulatingAnOrbit.html">5. Simulating an Orbit</a></span></dt><dd><dl><dt><span class="section"><a href="SimulatingAnOrbit.html#N10E53">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch05s02.html">Configure the Spacecraft</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s02.html#N10E97">Rename the Spacecraft</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EB4">Set the Spacecraft Epoch</a></span></dt><dt><span class="section"><a href="ch05s02.html#N10EE3">Set the Keplerian Orbital Elements</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s03.html">Configure the Propagator</a></span></dt><dd><dl><dt><span class="section"><a href="ch05s03.html#N10F61">Rename the Propagator</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10F7E">Configure the Force Model</a></span></dt><dt><span class="section"><a href="ch05s03.html#N10FF5">Configuring the Orbit View Plot</a></span></dt></dl></dd><dt><span class="section"><a href="ch05s04.html">Configure the Propagate Command</a></span></dt><dt><span class="section"><a href="ch05s05.html">Run and Analyze the Results</a></span></dt></dl></dd><dt><span class="chapter"><a href="SimpleOrbitTransfer.html">6. Simple Orbit Transfer</a></span></dt><dd><dl><dt><span class="section"><a href="SimpleOrbitTransfer.html#N1113D">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch06s02.html">Configure Maneuvers, Differential Corrector, and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s02.html#N1117F">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111A3">Modify the Default Orbit View</a></span></dt><dt><span class="section"><a href="ch06s02.html#N111FB">Create the Maneuvers.</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch06s03.html#N1124B">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1126A">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1132D">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch06s03.html#N1137A">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch06s04.html">Run the Mission</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_TargetFiniteBurn.html">7. Target Finite Burn to Raise Apogee</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_TargetFiniteBurn.html#N11577">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch07s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s02.html#N115C7">Create a Thruster and a Fuel Tank</a></span></dt><dt><span class="section"><a href="ch07s02.html#N11659">Modify Thruster1 Thrust Coefficients</a></span></dt><dt><span class="section"><a href="ch07s02.html#N1169C">Attach ChemicalTank1 and Thruster1 to DefaultSC</a></span></dt><dt><span class="section"><a href="ch07s02.html#N116F7">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s03.html">Create the Differential Corrector and Target Control
    Variable</a></span></dt><dt><span class="section"><a href="ch07s04.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s04.html#N117B6">Configure the Initial Propagate Command</a></span></dt><dt><span class="section"><a href="ch07s04.html#N117E4">Create the Target Sequence</a></span></dt><dt><span class="section"><a href="ch07s04.html#N11880">Configure the Target Sequence</a></span></dt></dl></dd><dt><span class="section"><a href="ch07s05.html">Run the Mission</a></span></dt><dd><dl><dt><span class="section"><a href="ch07s05.html#N119F2">Inspect Orbit View and Message Window</a></span></dt><dt><span class="section"><a href="ch07s05.html#N11A13">Explore the Command Summary Reports</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="Mars_B_Plane_Targeting.html">8. Mars B-Plane Targeting</a></span></dt><dd><dl><dt><span class="section"><a href="Mars_B_Plane_Targeting.html#N11AA6">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch08s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s02.html#N11B1F">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11B91">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11C5A">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11CE9">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11EF7">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11F29">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch08s02.html#N11FA1">Create the Orbit Views</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s03.html#N1212E">Create the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1222A">Configure the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12232">Configure the Target desired B-plane Coordinates Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12259">Configure the Prop 3 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12298">Configure the Prop 12 Days to TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N122DA">Configure the Vary TCM.V Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1232E">Configure the Vary TCM.N Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N123C0">Configure the Vary TCM.B Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12452">Configure the Apply TCM Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N12470">Configure the Prop 280 Days Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124B2">Configure the Prop to Mars Periapsis Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N124E8">Configure the Achieve BdotT Command</a></span></dt><dt><span class="section"><a href="ch08s03.html#N1253F">Configure the Achieve BdotR Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch08s04.html#N1268C">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N1276C">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127DA">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch08s04.html#N127E2">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12809">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N12895">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128BC">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch08s04.html#N128F2">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch08s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></dd><dt><span class="chapter"><a href="OptimalLunarFlyby.html">9. Optimal Lunar Flyby using Multiple Shooting</a></span></dt><dd><dl><dt><span class="section"><a href="OptimalLunarFlyby.html#N12A34">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch09s02.html">Configure Coordinate Systems, Spacecraft, Optimizer, Propagators,
    Maneuvers, Variables, and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s02.html#N12AB9">Create a Moon-centered Coordinate System</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12AC8">Create the Spacecraft</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B09">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B22">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B37">Create the User Variables</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B46">Create the Optimizer</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B55">Create the 3-D Graphics</a></span></dt><dt><span class="section"><a href="ch09s02.html#N12B73">Create XYPlots and Reports</a></span></dt></dl></dd><dt><span class="section"><a href="ch09s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s03.html#N12B9A">Overview of the Mission Sequence</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BA3">Define Initial Guesses</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BC3">Initialize Variables</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12BED">Vary and Set Spacecraft Epochs</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C0D">Vary Control Point States</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C37">Apply Constraints at Control Points</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C43">Propagate the Segments</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C79">Compute Some Quantities and Apply Patch Constraints</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C88">Apply Patch Point Constraints</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12C94">Apply Constraints on Mission Orbit</a></span></dt><dt><span class="section"><a href="ch09s03.html#N12CA0">Apply Cost Function</a></span></dt></dl></dd><dt><span class="section"><a href="ch09s04.html">Design the Trajectory</a></span></dt><dd><dl><dt><span class="section"><a href="ch09s04.html#N12CAF">Overview</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12CC5">Step 1: Verify Your Configuration</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12D0B">Step 2: Find a Smooth Trajectory</a></span></dt><dt><span class="section"><a href="ch09s04.html#N12D8F">Step 5: Apply a New Constraint</a></span></dt></dl></dd></dl></dd><dt><span class="chapter"><a href="Tut_UsingGMATFunctions.html">10. Mars B-Plane Targeting Using GMAT Functions</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_UsingGMATFunctions.html#N12DC0">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch10s02.html">Configure Fuel Tank, Spacecraft properties, Maneuvers, Propagators,
    Differential Corrector, Coordinate Systems and Graphics</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s02.html#N12E53">Create Fuel Tank</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12EC5">Modify the DefaultSC Resource</a></span></dt><dt><span class="section"><a href="ch10s02.html#N12F8E">Create the Maneuvers</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1301D">Create the Propagators</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1322B">Create the Differential Corrector</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1325D">Create the Coordinate Systems</a></span></dt><dt><span class="section"><a href="ch10s02.html#N132D5">Create the Orbit Views</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1344F">Create single Report File</a></span></dt><dt><span class="section"><a href="ch10s02.html#N1348A">Create a GMAT Function</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s03.html#N134F3">Create Commands to Initiate the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N1358F">Configure the Mission Tree to Run the First Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s03.html#N13597">Configure the Make Objects Global Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135BE">Configure the Target Desired B-Plane Coord. From Inside Function Command</a></span></dt><dt><span class="section"><a href="ch10s03.html#N135E5">Configure the Report Parameters Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s04.html">Run the Mission with first Target Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch10s04.html#N1369D">Create the Second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13783">Create the Final Propagate Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F1">Configure the second Target Sequence</a></span></dt><dt><span class="section"><a href="ch10s04.html#N137F9">Configure the Mars Capture Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13820">Configure the Vary MOI.V Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138AC">Configure the Apply MOI Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N138D3">Configure the Prop to Mars Apoapsis Command</a></span></dt><dt><span class="section"><a href="ch10s04.html#N13909">Configure the Achieve RMAG Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch10s05.html">Run the Mission with first and second Target Sequences</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_EventLocation.html">11. Finding Eclipses and Station Contacts</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_EventLocation.html#N13A1E">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch11s02.html">Load the Mission</a></span></dt><dt><span class="section"><a href="ch11s03.html">Configure GMAT for Event Location</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s03.html#N13A94">Verify SolarSystem Configuration</a></span></dt><dt><span class="section"><a href="ch11s03.html#N13AE1">Configure CelestialBody Resources</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s04.html">Configure and Run the Eclipse Locator</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s04.html#N13B59">Create and Configure the EclipseLocator</a></span></dt><dt><span class="section"><a href="ch11s04.html#N13BCF">Run the Mission</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s05.html">Configure and Run the Contact Locator</a></span></dt><dd><dl><dt><span class="section"><a href="ch11s05.html#N13C15">Create and Configure a Ground Station</a></span></dt><dt><span class="section"><a href="ch11s05.html#N13CAD">Create and Configure the ContactLocator</a></span></dt><dt><span class="section"><a href="ch11s05.html#N13D04">Run the Mission</a></span></dt></dl></dd><dt><span class="section"><a href="ch11s06.html">Further Exercises</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_ElectricPropulsion.html">12. Electric Propulsion</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_ElectricPropulsion.html#N13D95">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch12s02.html">Create and Configure Spacecraft Hardware and Finite Burn</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s02.html#N13DD9">Create a Thruster, Fuel Tank, and Solar Power System</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13E21">Configure the Hardware</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13EB0">Attach Hardware to the Spacecraft</a></span></dt><dt><span class="section"><a href="ch12s02.html#N13F2C">Create the Finite Burn Maneuver</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s03.html">Configure the Mission Sequence</a></span></dt><dd><dl><dt><span class="section"><a href="ch12s03.html#N13F7E">Create the Commands</a></span></dt><dt><span class="section"><a href="ch12s03.html#N13FCC">Configure the Propagate Command</a></span></dt></dl></dd><dt><span class="section"><a href="ch12s04.html">Run the Mission</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_Simulate_DSN_Range_and_Doppler_Data.html">13. Simulate DSN Range and Doppler Data</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_Simulate_DSN_Range_and_Doppler_Data.html#N14023">Objective and Overview</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N1407E">Create a satellite and set its epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14091">Create a Transponder object and attach it to our
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html">Create and configure the Ground Station and related
    parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N140DA">Create Ground Station Transmitter, Receiver, and Antenna
      objects</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N14113">Create Ground Station</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_Ground_Station_and_related_parameters.html#N14125">Create Ground Station Error Models</a></span></dt></dl></dd><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated.html">Define the types of measurements to be simulated</a></span></dt><dt><span class="section"><a href="Create_and_configure_Force_model_and_propagator.html">Create and configure Force model and propagator</a></span></dt><dt><span class="section"><a href="Create_and_configure_Simulator_object.html">Create and configure Simulator object</a></span></dt><dt><span class="section"><a href="Run_the_mission_and_analyze_the_results.html">Run the mission and analyze the results</a></span></dt><dt><span class="section"><a href="Create_Realistic_GMD.html">Create a more realistic GMAT Measurement Data (GMD)</a></span></dt><dt><span class="section"><a href="ch13s09.html">References</a></span></dt><dt><span class="section"><a href="Appendix_A_Determination_of_Measurement_Noise_Values.html">Appendix A &ndash; Determination of Measurement Noise Values</a></span></dt></dl></dd><dt><span class="chapter"><a href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html">14. Orbit Estimation using DSN Range and Doppler Data</a></span></dt><dd><dl><dt><span class="section"><a href="Orbit_Estimation_using_DSN_Range_and_Doppler_Data.html#N14D17">Objective and Overview</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html">Create and configure the spacecraft, spacecraft transponder, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D6F">Create a satellite and set its epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_spacecraft_spacecraft_transponder_and_related_parameters.html#N14D8D">Create a Transponder object and attach it to our
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html">Create and configure the Ground Station and related
    parameters</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14DDC">Create Ground Station Transmitter, Receiver, and Antenna
      objects</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E26">Create Ground Station</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_the_Ground_Station_and_related_parameters.html#N14E54">Create Ground Station Error Models</a></span></dt></dl></dd><dt><span class="section"><a href="DSN_Estimation_Define_the_types_of_measurements_that_will_be_processed.html">Define the types of measurements that will be processed</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_Force_model_and_propagator.html">Create and configure Force model and propagator</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Create_and_configure_BatchEstimator_object.html">Create and configure BatchEstimator object</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html">Run the mission and analyze the results</a></span></dt><dd><dl><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Message_Window_Output">Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Plots_of_Observation_Residuals">Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Batch_Estimator_Output_Report">Batch Estimator Output Report</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Run_the_mission_and_analyze_the_results.html#DSN_Estimation_Matlab_Output_File">Matlab Output File</a></span></dt></dl></dd><dt><span class="section"><a href="ch14s08.html">References</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_A.html">Appendix A &ndash; GMAT Message Window Output</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_B.html">Appendix B &ndash; Zeroth Iteration Plots of Observation
    Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_C.html">Appendix C &ndash; First Iteration Plots of Observation Residuals</a></span></dt><dt><span class="section"><a href="DSN_Estimation_Appendix_D.html">Appendix D &ndash; Change Scripts to use Ground Network (GN) Data</a></span></dt></dl></dd><dt><span class="chapter"><a href="FilterSmoother_GpsPosVec.html">15. Filter and Smoother Orbit Determination using GPS_PosVec Data</a></span></dt><dd><dl><dt><span class="section"><a href="FilterSmoother_GpsPosVec.html#N151E3">Objective and Overview</a></span></dt><dt><span class="section"><a href="ch15s02.html">Simulate GPS_PosVec measurements</a></span></dt><dt><span class="section"><a href="ch15s03.html">Estimate the orbit</a></span></dt><dt><span class="section"><a href="ch15s04.html">Review and quality check the filter run</a></span></dt><dt><span class="section"><a href="ch15s05.html">Modify the estimation script to use a smoother to improve the
    estimates</a></span></dt><dt><span class="section"><a href="ch15s06.html">Review and quality check the smoother run</a></span></dt><dt><span class="section"><a href="ch15s07.html">Warm-start the filter</a></span></dt><dt><span class="section"><a href="ch15s08.html">A few words about filter tuning</a></span></dt><dt><span class="section"><a href="ch15s09.html">References</a></span></dt><dt><span class="section"><a href="ch15s10.html">Appendix A. Generate an ephemeris while running the filter and
    smoother</a></span></dt><dt><span class="section"><a href="ch15s11.html">Appendix B. Run the script from the command-line</a></span></dt><dt><span class="section"><a href="ch15s12.html">Appendix C. Check covariance matrix conditioning</a></span></dt></dl></dd><dt><span class="chapter"><a href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html">16. Simulate and Estimate Inter-Spacecraft Tracking</a></span></dt><dd><dl><dt><span class="section"><a href="Tut_Simulate_and_Estimate_Inter_Spacecraft_DSN_Range_and_Doppler_Data.html#N1564D">Objective and overview</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html">Create and configure the spacecraft, spacecraft hardware, and
    related parameters</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156A3">Create
      the simulation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156B3">Create
      the estimation satellites, set their epoch and Cartesian
      coordinates</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#N156D2">Create
      a Transponder object and attach it to the spacecraft</a></span></dt><dt><span class="section"><a href="Create_and_configure_the_spacecraft_spacecraft_hardware_and_related_parameters.html#Create_a_Receiver_object_and_attach_it_to_the_measurement_spacecraft">Create a Receiver object and attach it to the measurement
      spacecraft</a></span></dt></dl></dd><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html">Define the types of measurements to be simulated and their
    associated error models</a></span></dt><dd><dl><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#N1572A">Define
      the TrackingFileSets for the simulation and the estimator</a></span></dt><dt><span class="section"><a href="Define_the_types_of_measurements_to_be_simulated_and_their_associated_error_models.html#Create_measurement_error_models">Create the measurement error models</a></span></dt></dl></dd><dt><span class="section"><a href="Create_and_configure_force_model_and_propagator.html">Create and configure force model and propagator</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html">Create and configure the simulator and batch estimator
    objects</a></span></dt><dd><dl><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_simulator_object">Create the simulator object</a></span></dt><dt><span class="section"><a href="Create_and_configure_simulator_and_batch_estimator_Objects.html#Create_the_batch_estimator_object">Create the batch estimator object</a></span></dt></dl></dd><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html">Run the mission and review the output</a></span></dt><dd><dl><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N15832">Review the simulated
      measurements</a></span></dt><dt><span class="section"><a href="Run_the_mission_and_analyze_the_output.html#N1589B">Review the estimator
      results</a></span></dt></dl></dd><dt><span class="section"><a href="ch16s07.html">References</a></span></dt></dl></dd></dl></div></div></div><div class="navfooter"><hr><table summary="Navigation footer" width="100%"><tr><td align="left" width="40%"><a accesskey="p" href="ConfiguringGmat_DataFiles.html">Prev</a>&nbsp;</td><td align="center" width="20%">&nbsp;</td><td align="right" width="40%">&nbsp;<a accesskey="n" href="SimulatingAnOrbit.html">Next</a></td></tr><tr><td valign="top" align="left" width="40%">Configuring Data Files&nbsp;</td><td align="center" width="20%"><a accesskey="h" href="index.html">Home</a></td><td valign="top" align="right" width="40%">&nbsp;Chapter&nbsp;5.&nbsp;Simulating an Orbit</td></tr></table></div></body></html>