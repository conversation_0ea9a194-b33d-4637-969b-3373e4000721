%**************************************************************************
%************ Create Objects for Use in Mission Sequence ******************
%**************************************************************************

%--------------------------------------------------------------------------
%----------------- SpaceCraft, Formations, Constellations -----------------
%--------------------------------------------------------------------------

Create Spacecraft MMSRef DummySat;
GMAT MMSRef.Epoch.UTCGregorian = 22 Jul 2014 11:29:10.811;
GMAT MMSRef.CoordinateSystem = EarthMJ2000Eq;
GMAT MMSRef.DisplayStateType = Cartesian;
GMAT MMSRef.AnomalyType = TA;
GMAT MMSRef.X = -137380.19843385062;
GMAT MMSRef.Y = 75679.878675370544;
GMAT MMSRef.Z = 21487.638751878556;
GMAT MMSRef.VX = -0.23245320142355028;
GMAT MMSRef.VY = -0.44627539677580192;
GMAT MMSRef.VZ = 0.085612056628771024;
GMAT MMSRef.DryMass = 1000;
GMAT MMSRef.Cd = 2.2000000000000002;
GMAT MMSRef.Cr = 1.7;
GMAT MMSRef.DragArea = 15;
GMAT MMSRef.SRPArea = 1;

Create CoordinateSystem EarthSunRot;
GMAT EarthSunRot.Origin = Earth;
GMAT EarthSunRot.J2000Body = Earth;
GMAT EarthSunRot.Axes = ObjectReferenced;
GMAT EarthSunRot.Epoch = 21545.000000397937;
GMAT EarthSunRot.UpdateInterval = 60;
GMAT EarthSunRot.XAxis = R;
GMAT EarthSunRot.ZAxis = N;
GMAT EarthSunRot.Primary = Sun;
GMAT EarthSunRot.Secondary = Earth;

%--------------------------------------------------------------------------
%--------------------------------- Burns ----------------------------------
%--------------------------------------------------------------------------

Create ImpulsiveBurn LSI;
GMAT LSI.Origin = Earth;
GMAT LSI.Axes = VNB;
GMAT LSI.VectorFormat = Cartesian;
GMAT LSI.V = 0.14676929889000001;
GMAT LSI.N = 0.046042675892;
GMAT LSI.B = 0.090223244096999999;

Create ImpulsiveBurn ALM;
GMAT ALM.Origin = Earth;
GMAT ALM.Axes = VNB;
GMAT ALM.VectorFormat = Cartesian;
GMAT ALM.V = -0.3198120104;

Create ImpulsiveBurn LOI
GMAT LOI.Origin = Luna;
GMAT LOI.Axes   = VNB;
GMAT LOI.V      = -0.652;

%--------------------------------------------------------------------------
%------------------------------ Propagators -------------------------------
%--------------------------------------------------------------------------

Create ForceModel LunarSB_ForceModel;
GMAT LunarSB_ForceModel.CentralBody = Earth;
GMAT LunarSB_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT LunarSB_ForceModel.Drag = None;
GMAT LunarSB_ForceModel.SRP = Off;
GMAT LunarSB_ForceModel.ErrorControl = RSSStep;

Create Propagator LunarSB;
GMAT LunarSB.FM = LunarSB_ForceModel;
GMAT LunarSB.Type = RungeKutta89;
GMAT LunarSB.InitialStepSize = 60;
GMAT LunarSB.Accuracy = 1e-011;
GMAT LunarSB.MinStep = 0.001;
GMAT LunarSB.MaxStep = 45000;
GMAT LunarSB.MaxStepAttempts = 50;

Create ForceModel MoonCentered_ForceModel;
GMAT MoonCentered_ForceModel.CentralBody = Luna;
GMAT MoonCentered_ForceModel.PointMasses = {Earth, Sun, Luna};
GMAT MoonCentered_ForceModel.Drag = None;
GMAT MoonCentered_ForceModel.SRP = Off;
GMAT MoonCentered_ForceModel.ErrorControl = RSSStep;

Create Propagator MoonCentered;
GMAT MoonCentered.FM = MoonCentered_ForceModel;
GMAT MoonCentered.Type = RungeKutta89;
GMAT MoonCentered.InitialStepSize = 60;
GMAT MoonCentered.Accuracy = 1e-011;
GMAT MoonCentered.MinStep = 0.001;
GMAT MoonCentered.MaxStep = 15000;
GMAT MoonCentered.MaxStepAttempts = 50;

%--------------------------------------------------------------------------
%-------------------------------- Solvers ---------------------------------
%--------------------------------------------------------------------------
GMAT SolarSystem.EphemerisUpdateInterval = 0.0

Create FminconOptimizer SQP1;
GMAT SQP1.ShowProgress = true;
GMAT SQP1.ReportStyle = Normal;
GMAT SQP1.TargeterTextFile = FminconOptimizerSQP1.data;
GMAT SQP1.MaximumIterations = 25;
GMAT SQP1.ObjectiveFunction = Objective;
GMAT SQP1.Tolerance = 0;
GMAT SQP1.SourceType = MATLAB;
GMAT SQP1.DiffMaxChange = 0.1000;
GMAT SQP1.DiffMinChange = 1.0000e-5;
GMAT SQP1.MaxFunEvals = 1000;
GMAT SQP1.MaxIter = 50;
GMAT SQP1.TolX = 1.0000e-03;
GMAT SQP1.TolFun = 1.0000e-03;
GMAT SQP1.TolCon = 1.0000e-03;
GMAT SQP1.DerivativeCheck = Off;
GMAT SQP1.Diagnostics = Off;
GMAT SQP1.Display = iter;
GMAT SQP1.GradObj = Off; 
GMAT SQP1.GradConstr = Off; 

%--------------------------------------------------------------------------
%-------------------------------- Solvers ---------------------------------
%--------------------------------------------------------------------------

Create DifferentialCorrector DC1;

%--------------------------------------------------------------------------
%-------------------------- Plots and Reports -----------------------------
%--------------------------------------------------------------------------

Create OpenGLPlot OGL_EarthMJ2K;
GMAT OGL_EarthMJ2K.Add = {MMSRef, Earth, Luna};
GMAT OGL_EarthMJ2K.CoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.ViewPointReference = Earth;
GMAT OGL_EarthMJ2K.ViewDirection = Earth;
GMAT OGL_EarthMJ2K.ViewScaleFactor = 30;
GMAT OGL_EarthMJ2K.FixedFovAngle = 50;
GMAT OGL_EarthMJ2K.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT OGL_EarthMJ2K.ViewUpAxis = X;
GMAT OGL_EarthMJ2K.CelestialPlane = Off;
GMAT OGL_EarthMJ2K.XYPlane = On;
GMAT OGL_EarthMJ2K.WireFrame = Off;
GMAT OGL_EarthMJ2K.SolverIterations = None;
GMAT OGL_EarthMJ2K.Axes = On;
GMAT OGL_EarthMJ2K.PerspectiveMode = Off;
GMAT OGL_EarthMJ2K.UseFixedFov = Off;
GMAT OGL_EarthMJ2K.DataCollectFrequency = 5;
GMAT OGL_EarthMJ2K.UpdatePlotFrequency = 50;
GMAT OGL_EarthMJ2K.NumPointsToRedraw = 150;
GMAT OGL_EarthMJ2K.UseInitialView = On;

Create OpenGLPlot OGL_MoonMJ2K;
GMAT OGL_MoonMJ2K.Add = {MMSRef, Earth, Luna};
GMAT OGL_MoonMJ2K.CoordinateSystem = MoonMJ2000Eq;
GMAT OGL_MoonMJ2K.ViewPointReference = Luna;
GMAT OGL_MoonMJ2K.ViewDirection = Luna;
GMAT OGL_MoonMJ2K.ViewScaleFactor = 1;
GMAT OGL_MoonMJ2K.FixedFovAngle = 45;
GMAT OGL_MoonMJ2K.ViewUpCoordinateSystem = MoonMJ2000Eq;
GMAT OGL_MoonMJ2K.ViewUpAxis = X;
GMAT OGL_MoonMJ2K.CelestialPlane = Off;
GMAT OGL_MoonMJ2K.XYPlane = On;
GMAT OGL_MoonMJ2K.WireFrame = Off;
GMAT OGL_MoonMJ2K.SolverIterations = All;
GMAT OGL_MoonMJ2K.Axes = On;
GMAT OGL_MoonMJ2K.SunLine = Off;
GMAT OGL_MoonMJ2K.PerspectiveMode = Off;
GMAT OGL_MoonMJ2K.UseFixedFov = Off;
GMAT OGL_MoonMJ2K.DataCollectFrequency = 5;
GMAT OGL_MoonMJ2K.UpdatePlotFrequency = 50;
GMAT OGL_MoonMJ2K.NumPointsToRedraw = 75;


%Create OpenGLPlot OGL_EarthMoonRot;
%GMAT OGL_EarthMoonRot.Add = {MMSRef, Earth, Luna};
%GMAT OGL_EarthMoonRot.CoordinateSystem = EarthMoonRot;
%GMAT OGL_EarthMoonRot.ViewPointReference = Luna;
%GMAT OGL_EarthMoonRot.ViewDirection = Luna;
%GMAT OGL_EarthMoonRot.ViewScaleFactor = 10;
%GMAT OGL_EarthMoonRot.FixedFovAngle = 5;
%GMAT OGL_EarthMoonRot.ViewUpCoordinateSystem = EarthMoonRot;
%GMAT OGL_EarthMoonRot.ViewUpAxis = X;
%GMAT OGL_EarthMoonRot.CelestialPlane = Off;
%GMAT OGL_EarthMoonRot.XYPlane = On;
%GMAT OGL_EarthMoonRot.WireFrame = Off;
%GMAT OGL_EarthMoonRot.SolverIterations = All;
%GMAT OGL_EarthMoonRot.Axes = On;
%GMAT OGL_EarthMoonRot.SunLine = Off;
%GMAT OGL_EarthMoonRot.PerspectiveMode = Off;
%GMAT OGL_EarthMoonRot.UseFixedFov = Off;
%GMAT OGL_EarthMoonRot.DataCollectFrequency = 5;
%GMAT OGL_EarthMoonRot.UpdatePlotFrequency = 50;
%GMAT OGL_EarthMoonRot.NumPointsToRedraw = 75;

Create XYPlot LunarINC
GMAT LunarINC.IndVar = Iteration;
GMAT LunarINC.Add    = { MMSRef.MoonMJ2000Eq.INC };
GMAT LunarINC.Grid 	 = On;
GMAT LunarINC.SolverIterations = All;

Create XYPlot LunarSMA
GMAT LunarSMA.IndVar = Iteration;
GMAT LunarSMA.Add    = { MMSRef.Luna.SMA };
GMAT LunarSMA.Grid   = On;
GMAT LunarSMA.SolverIterations = All;

Create XYPlot LunarECC
GMAT  LunarECC.IndVar = Iteration;
GMAT LunarECC.Add     = { MMSRef.Luna.ECC };
GMAT LunarECC.Grid    = On;
GMAT LunarECC.SolverIterations = All;

Create XYPlot TotaldVPlot;
TotaldVPlot.IndVar = Iteration;
TotaldVPlot.Add    = TotaldV;
TotaldVPlot.SolverIterations = All;

Create ReportFile IterateData;
IterateData.Filename = ./output/SampleMissions/Ex_MinFuelLunarCapture.report;


%--------------------------------------------------------------------------
%-------------------------- Coordinate Systems ----------------------------
%--------------------------------------------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem EarthMoonRot;
GMAT EarthMoonRot.Origin = Luna;
GMAT EarthMoonRot.Axes = ObjectReferenced;
GMAT EarthMoonRot.XAxis = R;
GMAT EarthMoonRot.ZAxis = N;
GMAT EarthMoonRot.Primary = Earth;
GMAT EarthMoonRot.Secondary = Luna;

Create Variable dV1Mag dV2Mag TotaldV Iteration;
Create String SolutionData
SolutionData = '----------------- Solution Data -------------------';

%**************************************************************************
%**************************The Mission Sequence****************************
%**************************************************************************

%  This can be removed when we implement Plot Command
Toggle LunarINC Off; 
Toggle LunarSMA Off; 
Toggle LunarECC Off;
Toggle TotaldVPlot Off;

%------------------------------
%  Propagate to Earth periapsis
%------------------------------
Propagate LunarSB(MMSRef, {MMSRef.Periapsis});

%------------------------------
%  Target Lunar B-plane
%------------------------------
Iteration = 0;
Optimize SQP1

     %  Increment loop counter
     Iteration = Iteration + 1;

     %-------------------------------------------------
     %  Vary and apply the maneuver at Earth Periapsis
     %-------------------------------------------------
     Vary SQP1(LSI.V = 0.1462 ,   { Lower = -1, Upper = 1, MultiplicativeScaleFactor = 1});
     Vary SQP1(LSI.N = 0.0460 ,   { Lower = -1, Upper = 1, MultiplicativeScaleFactor = 1});
     Vary SQP1(LSI.B = 0.1086 ,   { Lower = -1, Upper = 1, MultiplicativeScaleFactor = 1});
     Maneuver LSI(MMSRef);
     
     %-------------------------------------------------
     %  Propagate to Lunar Periapsis
     %-------------------------------------------------
     Propagate LunarSB(MMSRef, {MMSRef.ElapsedDays = 1.5});
     Propagate MoonCentered(MMSRef, {MMSRef.Luna.Periapsis, MMSRef.ElapsedDays = 8, MMSRef.Luna.RMAG = 1000, MMSRef.Earth.RMAG = 3000});

     %-------------------------------------------------
     %  Apply constraints on BPlane to make sure the 
     %  solution doesn't drift of too far.  Not these are 
     %  inequaltiy constraints.  We know the Bplane coordinates
     %  of the solution must be less than this.
     %-------------------------------------------------
     NonlinearConstraint SQP1(MMSRef.MoonMJ2000Eq.BdotT <= 20000.0); 
     NonlinearConstraint SQP1(MMSRef.Luna.RMAG >= 2000);
     NonlinearConstraint SQP1(MMSRef.MoonMJ2000Eq.BdotR <= 15000.0); 

     %-------------------------------------------------
     % Vary velocity so we go into lunar orbit
     %-------------------------------------------------
     Vary SQP1(LOI.V = -0.75198120104, { Lower = -1, Upper = 1});
     Vary SQP1(LOI.N = 0, {  Lower = -1, Upper = 1});
     Vary SQP1(LOI.B = 0, { Lower = -1, Upper = 1});
     Maneuver LOI(MMSRef); 

     %-------------------------------------------------
     % Apply the constraints so we achieve the desired orbit
     %-------------------------------------------------
     NonlinearConstraint SQP1(MMSRef.Luna.SMA = 7600);   
     NonlinearConstraint SQP1(MMSRef.Luna.ECC = 0.2);  
     NonlinearConstraint SQP1(MMSRef.MoonMJ2000Eq.INC = 85);   

     %-------------------------------------------------
     % Propagate for a short period of time to update plot
     % so we can watch progress
     %-------------------------------------------------
     Propagate MoonCentered(MMSRef, {MMSRef.Luna.Periapsis, MMSRef.ElapsedDays = 5, MMSRef.Luna.RMAG = 1000, MMSRef.Earth.RMAG = 3000}); 
     
     %-------------------------------------------------
     % Calculate the total dV and tell optimizer to 
     % to minimize it.
     %-------------------------------------------------
     dV1Mag = sqrt( LSI.Element1^2 + LSI.Element2^2 + LSI.Element3^2 );
     dV2Mag = sqrt( LOI.Element1^2 + LOI.Element2^2 + LOI.Element3^2 ); 
     TotaldV = ( dV1Mag + dV2Mag );
     Minimize SQP1(TotaldV);

     %-------------------------------------------------
     % Update plots and reports
     %-------------------------------------------------
     Report IterateData TotaldV  MMSRef.Luna.SMA MMSRef.Luna.ECC MMSRef.MoonMJ2000Eq.INC;


     %  This can be removed when we implement Plot Command
     Toggle LunarINC On; 
     Toggle LunarSMA On; 
     Toggle LunarECC On;
     Toggle TotaldVPlot On;
     Propagate LunarSB(DummySat);
     Toggle LunarINC Off; 
     Toggle LunarSMA Off; 
     Toggle LunarECC Off;
     Toggle TotaldVPlot Off;

EndOptimize

%-------------------------------------------------
% Write report containing solution data
%-------------------------------------------------
Report IterateData TotaldV dV1Mag dV2Mag 
Report IterateData MMSRef.Luna.SMA MMSRef.Luna.ECC MMSRef.MoonMJ2000Eq.INC;
Report IterateData LSI.Element1 LSI.Element2 LSI.Element3
Report IterateData LOI.Element1 LOI.Element2 LOI.Element3


%-------------------------------------------------
% Propagate for a few weeks
%-------------------------------------------------
Propagate MoonCentered(MMSRef, {MMSRef.ElapsedDays = 15});
