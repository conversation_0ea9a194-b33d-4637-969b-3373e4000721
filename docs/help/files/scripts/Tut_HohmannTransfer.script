%General Mission Analysis Tool(GMAT) Script
%Created: 2012-04-05 10:21:34


%----------------------------------------
%---------- Spacecraft
%----------------------------------------

Create Spacecraft DefaultSC;
GMAT DefaultSC.DateFormat = UTCGregorian;
GMAT DefaultSC.Epoch = '01 Jan 2000 11:59:28.000';
GMAT DefaultSC.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultSC.DisplayStateType = Keplerian;
GMAT DefaultSC.SMA = 7191.93881762902;
GMAT DefaultSC.ECC = 0.02454974900598116;
GMAT DefaultSC.INC = 12.85008005658097;
GMAT DefaultSC.RAAN = 306.6148021947984;
GMAT DefaultSC.AOP = 314.1905515359926;
GMAT DefaultSC.TA = 99.88774933204827;
GMAT DefaultSC.DryMass = 850;
GMAT DefaultSC.Cd = 2.2;
GMAT DefaultSC.Cr = 1.8;
GMAT DefaultSC.DragArea = 15;
GMAT DefaultSC.SRPArea = 1;
GMAT DefaultSC.NAIFId = -123456789;
GMAT DefaultSC.NAIFIdReferenceFrame = -123456789;
GMAT DefaultSC.Id = 'SatId';
GMAT DefaultSC.Attitude = CoordinateSystemFixed;
GMAT DefaultSC.ModelFile = '../data/vehicle/models/aura.3ds';
GMAT DefaultSC.ModelOffsetX = 0;
GMAT DefaultSC.ModelOffsetY = 0;
GMAT DefaultSC.ModelOffsetZ = 0;
GMAT DefaultSC.ModelRotationX = 0;
GMAT DefaultSC.ModelRotationY = 0;
GMAT DefaultSC.ModelRotationZ = 0;
GMAT DefaultSC.ModelScale = 3;
GMAT DefaultSC.AttitudeDisplayStateType = 'Quaternion';
GMAT DefaultSC.AttitudeRateDisplayStateType = 'AngularVelocity';
GMAT DefaultSC.AttitudeCoordinateSystem = 'EarthMJ2000Eq';
GMAT DefaultSC.Q1 = 0;
GMAT DefaultSC.Q2 = 0;
GMAT DefaultSC.Q3 = 0;
GMAT DefaultSC.Q4 = 1;
GMAT DefaultSC.EulerAngleSequence = '321';
GMAT DefaultSC.AngularVelocityX = 0;
GMAT DefaultSC.AngularVelocityY = 0;
GMAT DefaultSC.AngularVelocityZ = 0;



%----------------------------------------
%---------- ForceModels
%----------------------------------------

Create ForceModel DefaultProp_ForceModel;
GMAT DefaultProp_ForceModel.CentralBody = Earth;
GMAT DefaultProp_ForceModel.PrimaryBodies = {Earth};
GMAT DefaultProp_ForceModel.Drag = None;
GMAT DefaultProp_ForceModel.SRP = Off;
GMAT DefaultProp_ForceModel.RelativisticCorrection = Off;
GMAT DefaultProp_ForceModel.ErrorControl = RSSStep;
GMAT DefaultProp_ForceModel.GravityField.Earth.Degree = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.Order = 4;
GMAT DefaultProp_ForceModel.GravityField.Earth.PotentialFile = 'JGM2.cof';
GMAT DefaultProp_ForceModel.GravityField.Earth.TideModel = 'None';

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator DefaultProp;
GMAT DefaultProp.FM = DefaultProp_ForceModel;
GMAT DefaultProp.Type = RungeKutta89;
GMAT DefaultProp.InitialStepSize = 60;
GMAT DefaultProp.Accuracy = 9.999999999999999e-012;
GMAT DefaultProp.MinStep = 0.001;
GMAT DefaultProp.MaxStep = 2700;
GMAT DefaultProp.MaxStepAttempts = 50;
GMAT DefaultProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Burns
%----------------------------------------

Create ImpulsiveBurn TOI;
GMAT TOI.CoordinateSystem = Local;
GMAT TOI.Origin = Earth;
GMAT TOI.Axes = VNB;
GMAT TOI.Element1 = 0;
GMAT TOI.Element2 = 0;
GMAT TOI.Element3 = 0;
GMAT TOI.DecrementMass = false;
GMAT TOI.Isp = 300;
GMAT TOI.GravitationalAccel = 9.810000000000001;

Create ImpulsiveBurn GOI;
GMAT GOI.CoordinateSystem = Local;
GMAT GOI.Origin = Earth;
GMAT GOI.Axes = VNB;
GMAT GOI.Element1 = 0;
GMAT GOI.Element2 = 0;
GMAT GOI.Element3 = 0;
GMAT GOI.DecrementMass = false;
GMAT GOI.Isp = 300;
GMAT GOI.GravitationalAccel = 9.810000000000001;

%----------------------------------------
%---------- Solvers
%----------------------------------------

Create DifferentialCorrector DC1;
GMAT DC1.ShowProgress = true;
GMAT DC1.ReportStyle = Normal;
GMAT DC1.ReportFile = 'DifferentialCorrectorDC1.data';
GMAT DC1.MaximumIterations = 25;
GMAT DC1.DerivativeMethod = ForwardDifference;

%----------------------------------------
%---------- Subscribers
%----------------------------------------

Create OrbitView DefaultOrbitView;
GMAT DefaultOrbitView.SolverIterations = Current;
GMAT DefaultOrbitView.UpperLeft = [ -0.07789740341988601 0.1017214397496088 ];
GMAT DefaultOrbitView.Size = [ 1.010132995566814 1.330203442879499 ];
GMAT DefaultOrbitView.RelativeZOrder = 1287;
GMAT DefaultOrbitView.Add = {DefaultSC, Earth};
GMAT DefaultOrbitView.CoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.DrawObject = [ true true ];
GMAT DefaultOrbitView.OrbitColor = [ 255 32768 ];
GMAT DefaultOrbitView.TargetColor = [ 8421440 0 ];
GMAT DefaultOrbitView.DataCollectFrequency = 1;
GMAT DefaultOrbitView.UpdatePlotFrequency = 50;
GMAT DefaultOrbitView.NumPointsToRedraw = 0;
GMAT DefaultOrbitView.ShowPlot = true;
GMAT DefaultOrbitView.ViewPointReference = Earth;
GMAT DefaultOrbitView.ViewPointVector = [ 0 0 120000 ];
GMAT DefaultOrbitView.ViewDirection = Earth;
GMAT DefaultOrbitView.ViewScaleFactor = 1;
GMAT DefaultOrbitView.ViewUpCoordinateSystem = EarthMJ2000Eq;
GMAT DefaultOrbitView.ViewUpAxis = X;
GMAT DefaultOrbitView.CelestialPlane = Off;
GMAT DefaultOrbitView.XYPlane = On;
GMAT DefaultOrbitView.WireFrame = Off;
GMAT DefaultOrbitView.Axes = On;
GMAT DefaultOrbitView.Grid = Off;
GMAT DefaultOrbitView.SunLine = Off;
GMAT DefaultOrbitView.UseInitialView = On;
GMAT DefaultOrbitView.StarCount = 7000;
GMAT DefaultOrbitView.EnableStars = On;
GMAT DefaultOrbitView.EnableConstellations = On;

Create GroundTrackPlot DefaultGroundTrackPlot;
GMAT DefaultGroundTrackPlot.SolverIterations = Current;
GMAT DefaultGroundTrackPlot.UpperLeft = [ -0.00506649778340722 0.003129890453834116 ];
GMAT DefaultGroundTrackPlot.Size = [ 1.010132995566814 1.067292644757434 ];
GMAT DefaultGroundTrackPlot.RelativeZOrder = 1289;
GMAT DefaultGroundTrackPlot.Add = {DefaultSC, Earth};
GMAT DefaultGroundTrackPlot.DataCollectFrequency = 1;
GMAT DefaultGroundTrackPlot.UpdatePlotFrequency = 50;
GMAT DefaultGroundTrackPlot.NumPointsToRedraw = 0;
GMAT DefaultGroundTrackPlot.ShowPlot = true;
GMAT DefaultGroundTrackPlot.CentralBody = Earth;
GMAT DefaultGroundTrackPlot.TextureMap = '../data/graphics/texture/ModifiedBlueMarble.jpg';

Create ReportFile DefaultReportFile;
GMAT DefaultReportFile.SolverIterations = Current;
GMAT DefaultReportFile.UpperLeft = [ -0.00506649778340722 0.004694835680751174 ];
GMAT DefaultReportFile.Size = [ 1.009499683343889 1.068857589984351 ];
GMAT DefaultReportFile.RelativeZOrder = 1271;
GMAT DefaultReportFile.Filename = 'hohmann_tutorial_report.txt';
GMAT DefaultReportFile.Precision = 16;
GMAT DefaultReportFile.WriteHeaders = On;
GMAT DefaultReportFile.LeftJustify = On;
GMAT DefaultReportFile.ZeroFill = Off;
GMAT DefaultReportFile.ColumnWidth = 20;
GMAT DefaultReportFile.WriteReport = true;

Create XYPlot XYPlot1;
GMAT XYPlot1.SolverIterations = Current;
GMAT XYPlot1.UpperLeft = [ -0.00506649778340722 0.05477308294209703 ];
GMAT XYPlot1.Size = [ 1.010132995566814 1.082942097026604 ];
GMAT XYPlot1.RelativeZOrder = 1285;
GMAT XYPlot1.XVariable = DefaultSC.A1ModJulian;
GMAT XYPlot1.YVariables = {DefaultSC.Earth.Altitude};
GMAT XYPlot1.ShowGrid = true;
GMAT XYPlot1.ShowPlot = true;

Create ReportFile first_periapsis;
GMAT first_periapsis.SolverIterations = Current;
GMAT first_periapsis.UpperLeft = [ -0.00506649778340722 0.003129890453834116 ];
GMAT first_periapsis.Size = [ 1.009499683343889 1.059467918622848 ];
GMAT first_periapsis.RelativeZOrder = 1199;
GMAT first_periapsis.Filename = 'Reportfile_periapsis.txt';
GMAT first_periapsis.Precision = 16;
GMAT first_periapsis.WriteHeaders = On;
GMAT first_periapsis.LeftJustify = On;
GMAT first_periapsis.ZeroFill = Off;
GMAT first_periapsis.ColumnWidth = 20;
GMAT first_periapsis.WriteReport = true;


%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate 'Propagat to Periapsis' DefaultProp(DefaultSC) {DefaultSC.Earth.Periapsis};
Target 'Hohmann Transfer' DC1 {SolveMode = Solve, ExitMode = SaveAndContinue};
   Vary 'Vary TOI' DC1(TOI.Element1 = 2.240269210749966, {Perturbation = 0.0001, Lower = 0.0, Upper = 3.14159, MaxStep = 0.5});
   Maneuver 'Perform TOI' TOI(DefaultSC);
   Propagate 'Prop to Apoapsis' DefaultProp(DefaultSC) {DefaultSC.Earth.Apoapsis};
   Achieve 'Achieve RMAG = 42165' DC1(DefaultSC.Earth.RMAG = 42164.169, {Tolerance = 0.1});
   Vary 'Vary GOI' DC1(GOI.Element1 = 1.4406428582523, {Perturbation = 0.0001, Lower = 0.0, Upper = 3.14159, MaxStep = 0.2});
   Maneuver 'Perform GOI' GOI(DefaultSC);
   Achieve 'Achieve ECC = 0.005' DC1(DefaultSC.Earth.ECC = 0.005, {Tolerance = 0.0001});
EndTarget;  % For targeter DC1
Propagate 'Prop one Day' DefaultProp(DefaultSC) {DefaultSC.ElapsedDays = 1.0};
