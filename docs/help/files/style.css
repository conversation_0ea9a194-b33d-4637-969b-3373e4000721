body {
    font-family: <PERSON><PERSON><PERSON>, Helvetica, <PERSON><PERSON>, serif, sans-serif;
}

h1.title, h2.title, h3.title, h4.title, h5.title, h6.title, .refentry h1, .refentry h2, .refentry h3, .refentry h4, .refentry h5, .refentry h6 {
    font-family: Helvetica, Arial, sans-serif;
    color: #365f91;
}

.figure .title {
    text-align: center;
}

pre {
    background-color: #e0e0e0;
}

.guibutton, .guiicon, .guilabel, .guimenu, .guimenuitem, .guisubmenu {
    font-weight: bold;
}

div.note {
    border: 1px solid black;
    background-color: #b8cce4;
    padding-left: 1em;
    padding-right: 1em;
}

div.caution, div.warning {
    border: 1px solid black;
    background-color: #fecec7;
    margin-top: 2em;
    padding-left: 1em;
    padding-right: 1em;
}

div.orderedlist, div.procedure {
    margin-top: 2em;
}

.procedure li,.orderedlist li  {
    margin-top: -1em;
}

/*
 * Table properties
 */
.table table, .informaltable table {
    border-collapse: collapse;
    border-top: black solid 1px;
    border-left: none;
    border-right: none;
    border-bottom: black solid 1px;
    border-spacing: 0px;
}

.table th, .informaltable th, .table td, .informaltable td {
    padding: 0.25em;
}

.table th, .informaltable th {
    background: #365f91;
    color:#fff;
    border: none;
    text-align: left;
}

.table td, .informaltable td {
    background:#e5f1f4;
    border-top: black solid 1px;
    border-left: none;
    border-right: none;
    border-bottom: none;
    vertical-align: top;
}

.variablelist table, .variablelist th, .variablelist td {
    border: none;
}

.variablelist .term {
    font-weight: bold;
}

/* tables inside note, caution, warning */
div.caution td, div.caution th, div.note td, div.note th, div.warning td, div.warning th {
    background: inherit;
}

div.caution table, div.note table, div.warning table {
    margin-bottom: 1em; /* helps with spacing in SpacecraftEpoch.html */
}
