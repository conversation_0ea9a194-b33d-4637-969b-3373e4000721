GMAT API Cheat Sheet
====================

Loading the API
---------------

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| import gmatpy as gmat                | load_gmat                            |
+--------------------------------------+--------------------------------------+


Asking for Help
---------------

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.Help()                          | GMATAPI.Help()                       |
+--------------------------------------+--------------------------------------+
| gmat.Help(<topic>)                   | GMATAPI.Help(<topic>)                |
+--------------------------------------+--------------------------------------+
| object.Help()                        | object.Help()                        |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.Help("ScriptUsage")             | GMATAPI.Help("Objects")              |
+--------------------------------------+--------------------------------------+
| burn.Help()                          | burn.Help()                          | 
+--------------------------------------+--------------------------------------+

GMAT Objects
---------------

Listing Available Classes
*************************
+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.ShowClasses()                   | GMATAPI.ShowClasses()                |
+--------------------------------------+--------------------------------------+
| gmat.ShowClasses(<type>)             | GMATAPI.ShowClasses(<type>)          |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.ShowClasses()                   | GMATAPI.ShowClasses()                |
+--------------------------------------+--------------------------------------+
| gmat.ShowClasses("PhysicalModel")    | GMATAPI.ShowClasses("Propagator")    |
+--------------------------------------+--------------------------------------+


Listing Created Objects
***********************
+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.ShowObjects()                   | GMATAPI.ShowObjects()                |
+--------------------------------------+--------------------------------------+
| gmat.ShowObjects(<type>)             | GMATAPI.ShowObjects(<type>)          |
+--------------------------------------+--------------------------------------+
| gmat.GetCommands()                   | GMATAPI.GetCommands()                |
+--------------------------------------+--------------------------------------+
| gmat.GetCommands(<type>)             | GMATAPI.GetCommands(<type>)          |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.ShowObjects()                   | GMATAPI.ShowObjects()                |
+--------------------------------------+--------------------------------------+
| gmat.ShowObjects("Spacecraft")       | GMATAPI.ShowObjects("Burn")          |
+--------------------------------------+--------------------------------------+
| gmat.GetCommands()                   | GMATAPI.GetCommands()                |
+--------------------------------------+--------------------------------------+
| gmat.GetCommands("Propagate")        | GMATAPI.GetCommands("Target")        |
+--------------------------------------+--------------------------------------+

Object Creation
***************

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| obj = gmat.Construct(<type>,<name>)  | obj = GMATAPI.Construct(<type>,      |
|                                      | <name>)                              |
+--------------------------------------+--------------------------------------+
| cmd = gmat.Command(<type>,<desc>)    | cmd = GMATAPI.Command(<type>,        |
|                                      | <desc>)                              |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| burn = gmat.Construct                | counter = GMATAPI.Construct          |
| ("ImpulsiveBurn", "Burn")            | ("Variable","Counter")               |
+--------------------------------------+--------------------------------------+
| maneuver = gmat.Command              | assign = GMATAPI.Command             |
| ("Maneuver", "Burn(Sat)")            | ("GMAT", "Counter = 0")              |
+--------------------------------------+--------------------------------------+


Object Field Access
*******************

+-------------------------------------------+-------------------------------------------+
| Python                                    | MATLAB                                    |
+===========================================+===========================================+
| value = obj.GetField(<FieldLabel>)        | value = obj.GetField(<FieldLabel>)        |
+-------------------------------------------+-------------------------------------------+
| obj.SetField(<FieldLabel>,<value>)        | obj.SetField(<FieldLabel>,<value>)        |
+-------------------------------------------+-------------------------------------------+
| obj.GetNumber(<FieldLabel>)               | obj.GetNumber(<FieldLabel>)               |
+-------------------------------------------+-------------------------------------------+
| obj.GetMatrix(<FieldLabel>)               | obj.GetMatrix(<FieldLabel>)               |
+-------------------------------------------+-------------------------------------------+
| obj.GetVector(<FieldLabel>)               | obj.GetVector(<FieldLabel>)               |
+-------------------------------------------+-------------------------------------------+
| **Examples**                                                                          |
+-------------------------------------------+-------------------------------------------+
| V = burn.GetField("Element1")             | V = burn.GetField("Element1")             |
+-------------------------------------------+-------------------------------------------+
| burn.SetField("Element1",1.5)             | SetField("Element1",1.5)                  |
+-------------------------------------------+-------------------------------------------+
| burn.SetField("Origin","Mars")            | SetField("Origin","Mars")                 |
+-------------------------------------------+-------------------------------------------+
| V = burn.GetNumber("Element1")       	    | V = burn.GetNumber("Element1")            |
+-------------------------------------------+-------------------------------------------+
| V = Sat.GetMatrix("OrbitErrorCovariance") | V = Sat.GetMatrix("OrbitErrorCovariance") |
+-------------------------------------------+-------------------------------------------+
| V = plate.GetVector("PlateNormal")        | V = plate.GetVector("PlateNormal")        |
+-------------------------------------------+-------------------------------------------+

Object to Object Connections
****************************

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| obj.SetReference(<RefObject>)        | obj.SetReference(<RefObject>)        |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| jrdrag.SetReference(atmos)           | jrdrag.SetReference(atmos)           |
+--------------------------------------+--------------------------------------+


GMAT Script Access
------------------

Loading a GMAT script
*********************

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.LoadScript(<script>)            | GMATAPI.LoadScript(<script>)         |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.LoadScript                      | GMATAPI.LoadScript                   |
| ("../samples/Ex_GEOTransfer.script") | ("../samples/Ex_GEOTransfer.script") |
+--------------------------------------+--------------------------------------+

Saving a GMAT script
********************

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.SaveScript(<script>)            | GMATAPI.SaveScript(<script>)         |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.SaveScript                      | GMATAPI.SaveScript                   |
| ("../scripts/New_GEOTransfer.script")| ("../scripts/New_GEOTransfer.script")|
+--------------------------------------+--------------------------------------+


Running GMAT 
------------

Running GMAT from a loaded script
*********************************
+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.RunScript()                     | GMATAPI.RunScript()                  |
+--------------------------------------+--------------------------------------+

Running GMAT Commands
*********************
+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.Execute()                       | GMATAPI.Execute()                    |
+--------------------------------------+--------------------------------------+

Accessing Run Data After a Run
******************************

+--------------------------------------+--------------------------------------+
| Python                               | MATLAB                               |
+======================================+======================================+
| gmat.GetRuntimeObject(<name>)        | GMATAPI.GetRuntimeObject(<name>)     |
+--------------------------------------+--------------------------------------+
| gmat.GetRunSummary()                 | GMATAPI.GetRunSummary()              |
+--------------------------------------+--------------------------------------+
| **Examples**                                                                |
+--------------------------------------+--------------------------------------+
| gmat.GetRuntimeObject("geoSat")      | GMATAPI.GetRuntimeObject("geoSat")   |
+--------------------------------------+--------------------------------------+
