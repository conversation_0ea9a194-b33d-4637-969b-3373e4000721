Coding Guidelines for the GMAT API
==================================

Classes exposed through the GMAT API conform to a set of coding guidelines 
designed to make the interface clean and well documented for API users.  
This appendix described the coding style employed by GMAT comnponents that 
conform to the API requirements.

Code Comments
-------------

The API documentation includes links to the Doxygen descriptions of the 
supported classes.  Those classes must follow the GMAT Style Guide, so that the
class is fully documented.  Additionally, the comments in the code must be both
useful and reviewed for grammar and consistency.

Help Methods
------------

