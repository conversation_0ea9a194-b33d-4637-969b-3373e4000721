Bibliography
============

.. _GmatWiki: 

   **[GmatWiki]** `gmatcentral.org <http://gmatcentral.org>`_

   The GMAT Wiki at gmatcentral.org is the main public facing interface for GMAT 
   development and release activities.

.. _ArchSpec:

   **[Architecture]**  The GMAT Development Team, “General Mission Analysis Tool (GMAT) Architectural Specification,” NASA GSFC. 

   The GMAT Architectural Specification provides a good overview for the GMAT 
   system.  The document is included with each GMAT release.  The document overview can be viewed at 
   `http://gmatcentral.org:8090/display/GW/Architectural+Specification <http://gmatcentral.org:8090/display/GW/Architectural+Specification>`_.

.. _CInterfaceAPI:

   **[CInterface]** <PERSON><PERSON>, "GMAT API Tradeoff Study," Thinking Systems, Inc., February 2012.

   The GMAT CInterface plugin was an artifact of the original GMAT API study 
   described here. One recommendation arising from this study was a set of 
   automatic API generation tools, including SWIG.

.. _PrototypeAPI:

   **[SWIGExperiment]** <PERSON><PERSON>, "GMAT API Consultation Support," Thinking Systems, Inc., December 2016.

   Goddard personnel, assisted by contractors at Thinking Systems, Inc. and 
   Emergent Space Technologies, performed an in-house study of the use of SWIG 
   as a tool for a production GMAT API.  This document describes that study.

.. _SWIG:

   **[SWIG]** `Simplified Wrapper and Interface Generator (SWIG) <http://www.swig.org/>`_

   SWIG is an open source software development tool that connects programs 
   written in C and C++ with other high-level programming languages.  The GMAT 
   API is generated using the SWIG tool.

.. _Doxygen:

   **[Doxygen]** `www.doxygen.nl <http://www.doxygen.nl/>`_

   The detailed design information for GMAT is generated using the open source
   Doxygen documentation generation tool.

.. _Jupyter:

   **[Jupyter]** `www.jupyter.org <http://www.jupyter.org/>`_

   Jupyter is an interactive tool that provides writers with the ability to 
   intersperse documentation and Python code.  The resulting notebook files can 
   be run interactively, enriching the user's learning experience through 
   editable Python code that can be executed inside the notebook. 
