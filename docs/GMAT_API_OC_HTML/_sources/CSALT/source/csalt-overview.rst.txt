Overview
=========

The Collocation Stand Alone Library and Toolkit (CSALT) is a C++ library that employs collocation to solve the optimal control problem. The library contains approximately 22,000 Source Lines of Code (SLOC) for the CSALT library and about 17,000 SLOC reused from utilities in the General Mission Analysis Tool (GMAT).  The system has (loose) dependencies on the Boost C++ library for sparse matrix arithmetic and SNOPT for nonlinear programming. (See :ref:`Sec_GMATOC_Installation` for more details on how to install CSALT.) The software was developed in collaboration between GSFC engineers and software developers, GSFC support contractors, the Korea Aerospace Research Institute (KARI), and Yonsei University.  CSALT is licensed using the Apache License 2.0.

The CSALT library is integrated into GMAT in the GMAT Optimal Control subsystem plugin.  This document contains the user guide and the high-level software design documentation for the CSALT and the GMAT Optimal Control plugin. The first few chapters discuss the software contents (:ref:`Sec_GMATOC_Organization`), the optimal control problem, and CSALT algorithms (:ref:`Sec_GMATOC_Concepts`) to a level appropriate to provide context for the software user guide and design sections. The :ref:`Sec_GMATOC_UserGuide` presents the user interfaces for CSALT (which are C++ interfaces) and for the GMAT script interfaces.  The documentation includes an overview of components used to solve optimal control problems, a tutorial for CSALT and GMAT applications, and extensive examples (:ref:`Sec_GMATOC_UserGuide`).  Reference material illustrates detailed user class/Resource interfaces. The reference material also incudes a complete user interface specification for the GMAT Optimal Control plugin (:ref:`Sec_GMATOC_UISpec`) and a file specification for Evolutionary Mission Trajectory Generator (EMTG) spacecraft files (:ref:`EMTGSpacecraftFileSpec-label`), which are used to specify spacecraft propulsion system properties when using the GMAT Optimal Control plugin. 
