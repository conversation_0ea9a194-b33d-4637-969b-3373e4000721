Search.setIndex({"docnames": ["API/source/Bibliography", "API/source/ChangeHistory", "API/source/apiIndex", "API/source/appendix/Coding", "API/source/appendix/LessonsLearned", "API/source/appendix/OpenIssues", "API/source/appendix/PrototypeProductionComparison", "API/source/appendix/ReviewActions", "API/source/appendix/ReviewInfo", "API/source/appendix/Stuff", "API/source/design/APIDesign", "API/source/design/Architecture", "API/source/design/Design", "API/source/intro/Overview", "API/source/referenceguide/ReferenceGuide", "API/source/userguide/APIFunctions", "API/source/userguide/BestPractices", "API/source/userguide/GettingStarted", "API/source/userguide/ScriptUsage", "API/source/userguide/Setup", "API/source/userguide/StyleGuide", "API/source/userguide/UsersGuide", "API/source/userguide/commands/SingleCommands", "API/source/userguide/commands/TopLevelInterface", "API/source/userguide/notebooks/GMAT_Propagation", "API/source/userguide/notebooks/GMAT_States", "API/source/userguide/notebooks/notebooks", "API/source/userguide/usage/CheatSheet", "API/source/userguide/usage/CommandsExample", "API/source/userguide/usage/Conversion", "API/source/userguide/usage/Examples", "API/source/userguide/usage/JavaExamples", "API/source/userguide/usage/PythonExamples", "API/source/userguide/usage/UseCase1", "API/source/userguide/usage/UseCase2", "CSALT/source/csalt-ConceptsAndAlgorithms", "CSALT/source/csalt-EMTGSpacecraftFileSpec-smallTables", "CSALT/source/csalt-SoftwareOrganizationAndCompilation", "CSALT/source/csalt-UserGuide", "CSALT/source/csalt-UserInterfaceSpec-smallTables", "CSALT/source/csalt-overview", "CSALT/source/csaltIndex", "index"], "filenames": ["API/source/Bibliography.rst", "API/source/ChangeHistory.rst", "API/source/apiIndex.rst", "API/source/appendix/Coding.rst", "API/source/appendix/LessonsLearned.rst", "API/source/appendix/OpenIssues.rst", "API/source/appendix/PrototypeProductionComparison.rst", "API/source/appendix/ReviewActions.rst", "API/source/appendix/ReviewInfo.rst", "API/source/appendix/Stuff.rst", "API/source/design/APIDesign.rst", "API/source/design/Architecture.rst", "API/source/design/Design.rst", "API/source/intro/Overview.rst", "API/source/referenceguide/ReferenceGuide.rst", "API/source/userguide/APIFunctions.rst", "API/source/userguide/BestPractices.rst", "API/source/userguide/GettingStarted.rst", "API/source/userguide/ScriptUsage.rst", "API/source/userguide/Setup.rst", "API/source/userguide/StyleGuide.rst", "API/source/userguide/UsersGuide.rst", "API/source/userguide/commands/SingleCommands.rst", "API/source/userguide/commands/TopLevelInterface.rst", "API/source/userguide/notebooks/GMAT_Propagation.rst", "API/source/userguide/notebooks/GMAT_States.rst", "API/source/userguide/notebooks/notebooks.rst", "API/source/userguide/usage/CheatSheet.rst", "API/source/userguide/usage/CommandsExample.rst", "API/source/userguide/usage/Conversion.rst", "API/source/userguide/usage/Examples.rst", "API/source/userguide/usage/JavaExamples.rst", "API/source/userguide/usage/PythonExamples.rst", "API/source/userguide/usage/UseCase1.rst", "API/source/userguide/usage/UseCase2.rst", "CSALT/source/csalt-ConceptsAndAlgorithms.rst", "CSALT/source/csalt-EMTGSpacecraftFileSpec-smallTables.rst", "CSALT/source/csalt-SoftwareOrganizationAndCompilation.rst", "CSALT/source/csalt-UserGuide.rst", "CSALT/source/csalt-UserInterfaceSpec-smallTables.rst", "CSALT/source/csalt-overview.rst", "CSALT/source/csaltIndex.rst", "index.rst"], "titles": ["Bibliography", "Change History", "GMAT API Design and User\u2019s Guide", "Coding Guidelines for the GMAT API", "GMAT API Prototypes", "Open issues", "Comparison of the SWIG Prototype with the Production API", "Review Responses", "To Be Removed: Review and Prototype Information", "GMAT Code Updates", "The API Design", "GMAT Architectural Components", "System Design", "Introduction", "GMAT API Reference Guide", "Functions Used in the GMAT API", "API Best Practices", "Object Usage with the GMAT API", "Script Usage", "Setting up the GMAT API", "Conventions in the API Examples and Use Cases", "GMAT API User\u2019s Guide", "Configuring a Command", "API Access to GMAT Commands", "Propagation with the GMAT API", "State Management with the GMAT API", "API Notebook Walkthroughs", "GMAT API Cheat Sheet", "Propagation Command", "Time and Coordinate Conversions", "Usage Examples", "API Examples in Java", "API Examples", "Tutorial: Accessing GMAT Propagation and Navigation Features", "Example: MONTE-GMAT Interoperability", "Concepts and Algorithms", "EMTG Spacecraft File Specification", "Software Organization and Compilation", "User Guide", "User Interface Specification", "Overview", "GMAT Optimal Control", "GMAT Tools and Extensions"], "terms": {"gmatwiki": [0, 13], "gmatcentr": [0, 37], "org": [0, 7, 37], "The": [0, 2, 3, 4, 5, 6, 7, 12, 13, 15, 16, 20, 22, 23, 24, 25, 32, 34, 36, 37, 38, 39, 40, 41], "gmat": [0, 1, 5, 6, 8, 10, 12, 13, 16, 22, 26, 28, 29, 30, 31, 35, 36, 37, 39, 40], "wiki": [0, 13], "main": [0, 7, 11, 19, 22, 23, 31, 37], "public": [0, 7, 19, 31, 37], "face": [0, 7], "interfac": [0, 3, 4, 8, 9, 10, 11, 13, 15, 16, 19, 21, 26, 33, 34, 37, 40, 41, 42], "develop": [0, 4, 6, 7, 10, 13, 17, 18, 20, 34, 38, 40], "releas": [0, 1, 5, 7, 19, 33], "activ": [0, 7, 13], "architectur": [0, 2, 12, 13, 38], "team": [0, 4, 7, 10, 20, 34], "gener": [0, 2, 4, 5, 6, 7, 9, 10, 11, 13, 17, 18, 21, 33, 34, 36, 38, 39, 40], "mission": [0, 9, 11, 13, 17, 22, 23, 32, 34, 38, 40], "analysi": [0, 11, 13, 17, 40], "tool": [0, 4, 7, 10, 13, 17, 33, 38, 40], "specif": [0, 1, 5, 7, 9, 10, 11, 13, 15, 16, 17, 18, 19, 22, 23, 25, 26, 33, 34, 37, 38, 40, 41, 42], "nasa": 0, "gsfc": [0, 4, 13, 38, 40], "provid": [0, 4, 7, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 22, 23, 25, 32, 33, 34, 35, 36, 37, 38, 39, 40], "good": 0, "overview": [0, 2, 10, 13, 21, 41, 42], "system": [0, 2, 4, 7, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 26, 30, 33, 34, 35, 36, 37, 39, 40, 42], "document": [0, 3, 4, 5, 6, 7, 10, 11, 13, 16, 17, 19, 20, 24, 32, 35, 37, 38, 39, 40], "includ": [0, 3, 4, 5, 7, 9, 13, 15, 17, 18, 19, 20, 22, 26, 32, 33, 35, 37, 38, 39, 40], "each": [0, 7, 9, 18, 19, 22, 23, 24, 32, 33, 35, 38, 39], "can": [0, 4, 6, 7, 9, 10, 13, 15, 16, 17, 18, 19, 20, 22, 24, 25, 26, 32, 33, 35, 37, 38, 39], "view": [0, 11, 33, 38], "http": [0, 7], "8090": 0, "displai": [0, 9, 11, 20, 24, 25, 33, 38], "gw": 0, "cinterfac": 0, "d": [0, 35], "conwai": 0, "api": [0, 5, 8, 11, 12, 13, 22, 29, 30, 34, 42], "tradeoff": 0, "studi": [0, 4], "think": 0, "inc": [0, 24, 25, 28, 33], "februari": [0, 7], "2012": 0, "plugin": [0, 4, 9, 22, 34, 37, 38, 40], "wa": [0, 4, 7, 9, 19, 22, 33, 38, 40], "an": [0, 4, 5, 6, 7, 9, 10, 11, 13, 15, 16, 17, 18, 19, 22, 23, 24, 25, 31, 32, 33, 34, 35, 36, 37, 39, 40, 41], "artifact": 0, "origin": [0, 1, 7, 9, 25, 27, 32, 38], "describ": [0, 3, 4, 6, 9, 11, 13, 15, 16, 17, 18, 19, 22, 23, 33, 36, 38, 39], "here": [0, 4, 7, 9, 14, 23, 24, 25, 29, 32, 33, 36, 38, 39], "One": [0, 7, 17, 29, 38], "recommend": [0, 7, 36, 38], "aris": [0, 32], "from": [0, 4, 5, 7, 9, 13, 15, 16, 17, 20, 23, 24, 25, 28, 29, 32, 33, 35, 36, 38, 39, 40], "thi": [0, 3, 4, 5, 6, 7, 9, 10, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 31, 32, 33, 35, 36, 37, 38, 39, 40], "set": [0, 2, 3, 4, 5, 6, 7, 10, 11, 15, 16, 17, 18, 20, 21, 23, 24, 25, 28, 31, 33, 35, 36, 37, 39, 41], "automat": [0, 7, 9, 16, 32, 33, 38], "swig": [0, 4, 5, 7, 8, 10, 17, 20, 25, 32, 33], "swigexperi": [0, 4], "consult": [0, 38], "support": [0, 3, 4, 5, 8, 19, 20, 25, 32, 34, 35, 38, 39, 40], "decemb": [0, 1], "2016": [0, 4, 6, 35], "goddard": [0, 4, 13, 33, 34], "personnel": 0, "assist": 0, "contractor": [0, 40], "emerg": 0, "space": [0, 4, 13, 20, 33, 35, 38], "technolog": 0, "perform": [0, 7, 10, 16, 18, 19, 20, 22, 23, 24, 32, 33, 35, 38], "hous": 0, "us": [0, 2, 3, 4, 5, 6, 8, 9, 10, 11, 13, 16, 17, 19, 21, 22, 23, 24, 25, 28, 31, 32, 33, 34, 35, 36, 37, 39, 40], "product": [0, 4, 7, 8, 10, 17, 18, 19, 32, 38], "simplifi": [0, 4, 8, 10, 15, 17, 19], "wrapper": [0, 4, 5, 7, 10, 17, 33, 38], "open": [0, 4, 7, 8, 13, 19, 37], "sourc": [0, 9, 10, 13, 37, 39, 40], "softwar": [0, 38, 40, 41, 42], "connect": [0, 7, 9, 11, 17, 18, 25, 26, 32, 38], "program": [0, 4, 7, 11, 13, 34, 35, 40], "written": [0, 5, 7, 9, 10, 17, 33, 35, 38, 39], "c": [0, 4, 8, 17, 40], "other": [0, 4, 7, 9, 10, 13, 16, 19, 20, 25, 26, 32, 33, 34, 35, 36, 37, 38, 39], "high": [0, 7, 11, 13, 35, 38, 40], "level": [0, 4, 7, 9, 10, 11, 13, 19, 22, 24, 36, 37, 38, 40], "languag": [0, 4, 5, 7, 10, 13, 17, 19, 24, 25], "doxygen": [0, 3, 5, 17, 37, 38], "www": [0, 19], "nl": 0, "detail": [0, 4, 9, 10, 17, 19, 23, 38, 39, 40], "design": [0, 3, 4, 7, 9, 11, 13, 17, 18, 22, 23, 32, 40, 42], "inform": [0, 4, 7, 9, 13, 15, 18, 19, 21, 22, 23, 25, 32, 38, 39], "jupyt": [0, 26], "interact": [0, 4, 7, 10, 11, 13, 15, 20, 22, 25, 26, 32, 33, 39], "writer": 0, "abil": [0, 22, 33, 38], "interspers": [0, 7], "python": [0, 2, 4, 5, 6, 8, 10, 13, 15, 20, 23, 24, 25, 27, 31, 32, 33, 34, 37, 38], "code": [0, 4, 5, 7, 8, 10, 13, 15, 16, 17, 19, 20, 22, 23, 24, 25, 32, 33, 34, 37, 38, 40], "result": [0, 4, 7, 9, 11, 17, 19, 20, 22, 24, 32, 33, 35, 37, 38, 39], "notebook": [0, 2, 25, 42], "file": [0, 5, 7, 11, 15, 16, 17, 18, 20, 24, 25, 32, 33, 34, 37, 40, 41, 42], "run": [0, 2, 4, 7, 9, 10, 11, 13, 15, 16, 17, 22, 23, 24, 25, 26, 28, 32, 37], "enrich": 0, "user": [0, 3, 7, 8, 9, 10, 11, 13, 16, 17, 19, 22, 23, 24, 25, 26, 32, 33, 35, 37, 40, 41, 42], "s": [0, 4, 7, 9, 10, 11, 13, 15, 16, 17, 18, 20, 22, 23, 24, 25, 32, 33, 34, 35, 36, 38, 39, 42], "learn": [0, 16], "experi": [0, 7, 8, 33], "through": [0, 3, 7, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 32, 33, 34, 38, 39], "edit": [0, 5, 19, 37, 38], "execut": [0, 2, 7, 11, 18, 19, 27, 28, 32, 33, 36, 37, 38, 39], "insid": [0, 7, 9, 10, 19, 25, 33, 38], "rev": 1, "0": [1, 6, 9, 15, 18, 22, 23, 24, 25, 27, 28, 29, 31, 32, 33, 35, 36, 38, 39, 40], "05": [1, 8, 24, 33, 39], "2018": [1, 18], "draft": [1, 7], "1": [1, 6, 7, 11, 18, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 35, 39], "march": 1, "29": [1, 38], "2019": [1, 7, 24], "incorpor": [1, 4, 7, 10], "review": [1, 3, 13], "comment": [1, 19, 38], "2": [1, 6, 7, 10, 11, 18, 22, 23, 24, 25, 29, 30, 33, 35, 39, 40], "octob": 1, "11": [1, 18, 25, 38], "updat": [1, 7, 8, 15, 25, 35, 39], "first": [1, 4, 7, 10, 13, 16, 18, 19, 23, 25, 28, 32, 33, 38, 39, 40], "beta": [1, 33], "3": [1, 7, 10, 18, 22, 23, 24, 25, 29, 30, 33, 35, 39], "januari": [1, 25, 32], "15": [1, 7, 24, 33], "2020": [1, 33], "r2020a": [1, 19, 33], "4": [1, 15, 18, 22, 23, 25, 29, 30, 35, 37, 39], "septemb": 1, "27": [1, 24, 25], "2022": 1, "r2022a": [1, 34], "5": [1, 7, 18, 22, 23, 24, 25, 27, 28, 29, 30, 33, 37, 39], "june": 1, "30": [1, 38], "2023": [1, 28], "command": [1, 2, 8, 9, 11, 18, 19, 20, 30, 32, 33, 37, 39, 42], "function": [1, 2, 4, 5, 7, 8, 10, 11, 16, 17, 19, 21, 22, 24, 25, 33, 35, 36, 37, 41], "6": [1, 6, 18, 22, 24, 25, 32, 33, 35, 39], "2025": 1, "recompil": 1, "r2025a": 1, "introduct": [2, 23, 42], "compon": [2, 4, 5, 7, 9, 10, 12, 13, 16, 20, 22, 24, 32, 35, 37, 40], "up": [2, 7, 9, 11, 15, 21, 23, 24, 25, 28, 32, 33, 37, 41], "convent": [2, 21], "exampl": [2, 5, 6, 7, 13, 15, 17, 19, 21, 22, 24, 27, 29, 33, 35, 36, 37, 39, 40, 42], "case": [2, 7, 9, 13, 21, 30, 31, 33, 35, 38, 39], "object": [2, 4, 5, 6, 8, 10, 11, 13, 18, 19, 20, 21, 22, 23, 25, 26, 32, 33, 39], "usag": [2, 4, 7, 9, 13, 15, 20, 21, 32, 33, 42], "script": [2, 4, 7, 9, 10, 11, 13, 15, 19, 20, 22, 23, 24, 25, 30, 31, 33, 34, 37, 39, 40, 42], "tutori": [2, 13, 17, 21, 40, 41, 42], "access": [2, 4, 7, 13, 18, 20, 22, 25, 26, 32, 34, 37, 38, 39, 42], "propag": [2, 4, 7, 8, 9, 11, 16, 17, 22, 23, 26, 27, 30, 38, 42], "navig": [2, 9, 13, 17, 19, 42], "featur": [2, 4, 5, 7, 10, 13, 15, 16, 17, 18, 19, 23, 26, 32, 34, 35, 42], "verifi": 2, "setup": [2, 5, 6, 8, 9, 15, 16, 19, 23, 24, 31, 32, 35, 38], "get": [2, 5, 6, 7, 9, 13, 19, 25, 31, 32, 38], "start": [2, 4, 13, 17, 18, 19, 24, 25, 28, 38, 39], "spacecraft": [2, 6, 7, 11, 13, 15, 18, 19, 20, 22, 23, 26, 27, 28, 32, 34, 35, 40, 41, 42], "configur": [2, 6, 7, 9, 11, 15, 17, 18, 19, 23, 26, 31, 32, 37, 39, 41, 42], "forc": [2, 8, 11, 16, 17, 26, 30, 34, 39], "model": [2, 4, 7, 8, 9, 10, 11, 13, 16, 17, 19, 24, 30, 34, 35, 39], "plug": [2, 19], "modul": [2, 16, 19, 24, 25, 32], "measur": [2, 4, 17], "mc": 2, "sequenc": [2, 11, 18, 22, 28], "categori": [2, 9, 10], "initi": [2, 4, 5, 6, 7, 9, 13, 15, 17, 18, 23, 25, 26, 28, 29, 31, 32, 39], "time": [2, 4, 5, 7, 8, 11, 16, 18, 22, 24, 25, 30, 33, 34, 35, 36, 39], "coordin": [2, 7, 8, 11, 17, 26, 30, 33, 38, 39], "convers": [2, 8, 17, 30, 34], "java": [2, 4, 5, 7, 10, 13, 15, 20, 30, 32, 33], "target": [2, 4, 7, 11, 18, 22, 23, 27, 30, 33], "mont": [2, 18, 30], "interoper": [2, 30], "best": [2, 5, 7, 38, 39, 42], "practic": [2, 5, 35, 38, 39, 42], "matlab": [2, 4, 7, 10, 13, 20, 27, 33, 37, 41], "cheat": [2, 42], "sheet": [2, 42], "load": [2, 4, 6, 7, 16, 17, 18, 21, 23, 24, 25, 32, 33, 37], "ask": 2, "help": [2, 4, 5, 7, 8, 10, 13, 15, 17, 18, 20, 24, 33, 38], "walkthrough": [2, 42], "state": [2, 6, 7, 11, 16, 17, 22, 23, 24, 26, 31, 32, 35, 39], "manag": [2, 6, 7, 9, 10, 11, 16, 22, 23, 24, 26, 31, 32, 33, 38], "bibliographi": [2, 42], "chang": [2, 4, 7, 17, 18, 19, 23, 26, 32, 33, 35, 38, 39, 42], "histori": [2, 8, 35, 38, 39, 42], "class": [3, 4, 5, 7, 9, 10, 11, 15, 16, 17, 19, 22, 24, 31, 32, 33, 37, 40], "expos": [3, 4, 5, 7, 9, 10, 11, 22, 33, 39], "conform": 3, "make": [3, 4, 5, 7, 9, 10, 18, 19, 22, 25, 32, 33, 38], "clean": [3, 7], "well": [3, 7, 19, 32, 33, 38], "appendix": [3, 6], "style": [3, 4, 8, 17, 38], "emploi": [3, 35, 38, 40], "comnpon": 3, "requir": [3, 4, 6, 7, 9, 10, 19, 32, 33, 35, 36, 38, 39], "link": [3, 7, 22, 23, 35, 38, 39], "descript": [3, 7, 9, 11, 13, 15, 18, 19, 23, 32, 35, 36, 37, 38, 39], "those": [3, 4, 5, 7, 9, 10, 11, 20, 22, 23, 31, 33, 35, 38, 39], "must": [3, 4, 7, 16, 19, 32, 35, 36, 37, 38, 39], "follow": [3, 7, 9, 16, 17, 18, 19, 20, 22, 23, 32, 33, 35, 38, 39], "guid": [3, 8, 9, 13, 17, 32, 33, 37, 40, 41, 42], "so": [3, 5, 6, 7, 16, 18, 19, 22, 23, 25, 28, 33, 34, 35, 37, 38, 39], "fulli": [3, 25, 33], "addition": [3, 7, 25, 38], "both": [3, 4, 18, 33, 34, 35, 38, 39], "grammar": 3, "consist": [3, 7, 9, 11, 32, 33, 38, 39], "ha": [4, 5, 7, 9, 10, 13, 19, 20, 25, 33, 35, 38, 39, 40], "avail": [4, 7, 9, 13, 15, 23, 32, 33, 38], "extern": [4, 32, 34, 36, 38], "two": [4, 7, 10, 11, 13, 18, 19, 20, 22, 29, 32, 33, 34, 35, 38, 39], "previou": [4, 7, 25, 33, 38, 39], "gather": 4, "lead": [4, 7, 9], "These": [4, 7, 9, 10, 15, 16, 17, 22, 25, 26, 32, 33, 35, 38, 39], "lesson": [4, 16], "ar": [4, 5, 7, 9, 10, 11, 13, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25, 26, 28, 29, 32, 33, 34, 35, 36, 37, 38, 39, 40], "present": [4, 6, 7, 9, 13, 20, 21, 29, 32, 33, 35, 38, 40], "In": [4, 7, 9, 11, 13, 16, 17, 18, 19, 22, 23, 24, 25, 32, 33, 34, 35, 39], "2011": 4, "core": [4, 5, 7, 9, 11, 13, 16, 19, 20, 22, 23, 28, 32, 33, 38], "were": [4, 7, 9, 32, 37, 38], "intern": [4, 6, 7, 9, 10, 23, 25, 32, 33, 38, 39], "project": [4, 5], "flight": [4, 13, 33, 38], "center": [4, 13, 25, 32, 33, 38, 39], "librari": [4, 5, 7, 9, 10, 16, 17, 19, 33, 37, 38, 40, 41], "cinterfaceapi": 4, "That": [4, 7, 19, 22, 24, 32, 33], "libcinterfac": 4, "allow": [4, 16, 17, 34, 35, 37, 38, 39], "sandbox": [4, 7, 11, 18, 22, 23], "call": [4, 7, 9, 10, 11, 15, 16, 17, 18, 19, 22, 23, 24, 25, 32, 33, 35, 38, 39], "deriv": [4, 31, 32, 33, 38, 41], "orbit": [4, 17, 19, 22, 23, 24, 32, 33, 35, 39], "determin": [4, 7, 16, 17, 19, 22, 32, 33, 36, 38, 39, 41], "toolbox": 4, "odtbx": 4, "applic": [4, 5, 10, 13, 16, 18, 19, 20, 23, 29, 33, 37, 38, 40], "attempt": [4, 35, 38, 39], "comput": [4, 17, 18, 32, 33, 35, 38], "while": [4, 7, 9, 17, 19, 22, 32, 38], "earli": [4, 38], "need": [4, 5, 6, 7, 9, 10, 16, 17, 18, 19, 22, 23, 24, 25, 32, 33, 37, 38], "piec": [4, 5, 6, 15, 19, 24, 25, 32, 33], "natur": [4, 17, 38], "rebuild": 4, "when": [4, 5, 8, 9, 10, 11, 13, 16, 17, 18, 19, 20, 23, 25, 32, 33, 35, 38, 39, 40], "new": [4, 7, 9, 15, 17, 18, 22, 25, 31, 33, 38, 39], "dure": [4, 7, 10, 11, 17, 19, 22, 23, 32, 33, 38, 39], "cycl": [4, 39], "share": [4, 7, 10, 17, 19, 30, 33, 37], "mechan": [4, 5, 7, 9, 10, 16, 33], "still": [4, 5, 10, 33, 38], "part": [4, 7, 9, 32, 38], "deliveri": 4, "packag": [4, 5, 17, 33, 37], "write": [4, 11, 22, 33, 34, 38, 39], "onc": [4, 7, 17, 19, 23, 25, 32, 33, 38], "suffici": [4, 38], "complet": [4, 9, 10, 19, 22, 23, 24, 28, 32, 33, 38, 40], "elig": 4, "replac": [4, 24, 25, 38, 39], "newer": 4, "more": [4, 7, 16, 17, 18, 19, 25, 32, 33, 38, 39, 40], "build": [4, 5, 7, 16, 17, 22, 24, 26, 33, 37], "built": [4, 9, 10, 11, 13, 17, 20, 22, 23, 24, 25, 32, 33, 34, 37], "research": [4, 40], "identifi": [4, 5, 7, 9, 10, 17, 19, 33], "creat": [4, 5, 6, 8, 9, 11, 15, 16, 18, 19, 20, 22, 23, 24, 25, 31, 32, 33, 39], "robust": [4, 7], "capabl": [4, 10, 13, 32, 33, 34, 38], "export": [4, 5, 37], "most": [4, 10, 11, 19, 32, 33, 37, 38, 39], "been": [4, 7, 9, 13, 15, 19, 20, 24, 25, 33, 34, 38, 39], "test": [4, 5, 7, 17, 19, 37, 38], "work": [4, 5, 7, 9, 10, 11, 16, 17, 20, 24, 25, 30, 33, 34, 35], "expect": [4, 7, 25], "context": [4, 17, 32, 38, 40], "approach": [4, 7, 13, 16, 22, 24, 25, 33, 38], "much": [4, 7, 9, 38, 39], "than": [4, 7, 9, 10, 16, 19, 25, 35, 38, 39], "exposur": 4, "varieti": [4, 17, 19, 22], "unavail": 4, "branch": [4, 22, 23, 28], "repositori": [4, 37], "survei": 4, "produc": [4, 5, 9, 33, 38], "familiar": [4, 10, 23], "abov": [4, 7, 18, 19, 20, 23, 24, 32, 33, 38], "feedback": 4, "collect": [4, 24, 25, 32, 33, 38], "usabl": [4, 10], "earlier": [4, 39], "highlight": [4, 38], "process": [4, 7, 9, 11, 19, 22, 23, 32, 33], "kei": [4, 23, 32, 33, 38], "element": [4, 6, 7, 9, 11, 20, 22, 23, 25, 32, 33, 36, 38, 39], "group": [4, 10, 11, 22, 39], "three": [4, 6, 15, 17, 18, 35, 38], "framework": [4, 11, 25, 33, 34], "four": [4, 31, 32, 34, 38], "term": [4, 18, 23, 35, 38], "singl": [4, 7, 22, 24, 32, 35, 36, 38, 39], "overrid": [4, 9, 36, 38, 39], "want": [4, 10, 13, 15, 16, 17, 18, 19, 32, 33], "data": [4, 6, 7, 9, 11, 17, 18, 19, 22, 23, 24, 25, 31, 32, 33, 34, 36, 39], "analyst": [4, 9], "easi": [4, 7, 9, 38], "valid": [4, 7, 33, 36, 38, 39], "astrodynam": 4, "instanc": [4, 6, 7, 15, 18, 19, 20, 32, 33], "desir": [4, 36, 38], "via": [4, 7, 20, 37, 39], "see": [4, 7, 19, 24, 25, 32, 33, 36, 38, 39, 40], "note": [4, 6, 7, 9, 13, 19, 20, 25, 28, 31, 33, 36, 38, 39, 41], "below": [4, 7, 9, 11, 17, 18, 19, 22, 23, 24, 33, 37, 38], "Near": 4, "dynam": [4, 6, 7, 13, 24, 28, 30, 31, 32, 33, 35, 37, 39, 41], "jacobian": [4, 17, 32, 38, 39], "should": [4, 5, 6, 7, 22, 25, 36, 38, 39], "all": [4, 7, 9, 11, 16, 19, 22, 23, 24, 33, 35, 36, 37, 38, 39], "estim": [4, 11, 19, 22, 33, 35], "abl": [4, 19, 34], "without": [4, 5, 9, 33, 38], "knowledg": [4, 9], "onlin": 4, "consider": [4, 5], "guidanc": [4, 9], "nativ": [4, 7, 34], "platform": [4, 7, 20, 33, 37], "presum": 4, "coder": 4, "simpli": [4, 19, 38], "directli": [4, 7, 9, 10, 11, 13, 16, 17, 19, 23, 25, 32, 33, 38], "There": [4, 5, 10, 19, 23, 38], "per": [4, 36, 38, 39], "se": 4, "ad": [4, 5, 7, 9, 13, 16, 23, 28, 32, 33, 36, 38, 39], "some": [4, 5, 6, 7, 16, 17, 19, 20, 22, 24, 25, 33, 34, 35, 38], "like": [4, 7, 9, 10, 11, 16, 18, 20, 22, 23, 32, 33, 37, 38], "limit": [4, 7, 39], "compil": [4, 7, 10, 38, 40, 41, 42], "util": [5, 11, 17, 19, 22, 37, 38, 40], "wrap": [5, 7, 18, 38], "separ": [5, 7, 16, 22, 32, 33, 37, 38], "base": [5, 7, 9, 10, 13, 17, 18, 22, 32, 33, 34, 35, 36, 38, 39], "resolut": 5, "made": [5, 7, 9, 25, 33, 34, 38], "smaller": 5, "mai": [5, 7, 9, 15, 16, 17, 24, 25, 33, 35, 36, 37, 38, 39], "board": 5, "ensur": [5, 7, 33, 38], "modular": [5, 38], "repackag": 5, "plan": [5, 32, 33, 34], "cmake": [5, 37], "folder": [5, 16, 17, 18, 24, 25, 26, 32, 33, 37, 38, 39], "bin": [5, 16, 17, 18, 19, 24, 25, 33, 37, 38], "default": [5, 6, 7, 16, 18, 19, 25, 32, 38, 39], "current": [5, 7, 9, 18, 19, 22, 23, 33, 34, 35, 37, 38, 39], "Is": [5, 7, 36], "tie": [5, 32, 33], "between": [5, 6, 7, 11, 15, 19, 23, 32, 33, 34, 35, 36, 38, 39, 40], "csalt": [5, 37, 39, 40, 41], "Not": [5, 19, 35, 39], "e": [5, 7, 17, 18, 34, 35, 37, 38, 39], "g": [5, 7, 17, 18, 35, 37, 38, 39], "x": [5, 7, 9, 15, 22, 28, 35, 36, 38], "differ": [5, 6, 7, 16, 17, 19, 20, 22, 23, 25, 32, 33, 34, 35, 39], "address": [5, 7, 10, 19, 20, 23, 32], "wai": [5, 7, 10, 16, 17, 19, 22, 25, 33, 38], "approv": 5, "yet": [5, 25, 33], "tell": [5, 33, 38], "onli": [5, 6, 7, 19, 32, 33, 35, 36, 37, 38, 39], "lot": [5, 7], "hand": [5, 7, 9, 16, 17, 32, 33, 38, 39], "full": [5, 22, 23, 32, 33, 39], "item": [5, 15, 16, 19, 38], "under": [5, 7, 9, 13, 33], "mark": [5, 19], "alreadi": [5, 38], "name": [5, 7, 15, 18, 19, 20, 22, 25, 27, 32, 33, 36, 37, 38], "conflict": [5, 7], "For": [5, 7, 9, 16, 19, 20, 22, 23, 24, 32, 33, 34, 35, 37, 38, 39], "now": [5, 6, 7, 24, 25, 33, 38], "i": [5, 6, 7, 24, 31, 32, 35, 36, 38, 39], "m": [5, 7, 17, 18, 19, 33, 38], "construct": [5, 6, 7, 9, 15, 16, 19, 20, 22, 23, 24, 25, 27, 28, 29, 31, 32, 38], "creation": [5, 7, 9, 22, 31, 33], "we": [5, 6, 7, 16, 24, 25, 28, 33, 35, 38], "report": [5, 7, 9, 15, 17, 22, 25, 33, 38, 39], "thing": [5, 7], "fail": [5, 38], "thisisntastartupfil": 5, "txt": [5, 6, 7, 15, 16, 19, 24, 31, 37, 38, 39], "moder": [5, 6, 9, 11, 18, 19, 31, 33], "hoc": 5, "encount": [5, 9, 35, 38], "throw": 5, "except": [5, 7, 9, 22, 35, 38, 39], "failur": [5, 7], "r2018": 6, "show": [6, 9, 10, 11, 18, 20, 23, 24, 25, 31, 32, 33, 38], "version": [6, 8, 26, 33, 37, 38], "designexampl": [6, 31], "r2018a": 6, "import": [6, 7, 16, 17, 18, 19, 20, 23, 24, 25, 27, 28, 29, 31, 32, 33, 34, 38], "gmat_pi": [6, 7, 17, 25, 33], "convert": [6, 7, 9, 11, 17, 29, 31, 32, 35, 38], "gmat_startup_fil": [6, 16, 19], "timeconvert": [6, 31, 32], "timesystemconvert": [6, 31, 32], "epoch": [6, 24, 25, 28, 31, 32, 33, 38, 39], "utcepoch": [6, 31, 32], "21738": [6, 31, 32], "22145": [6, 31, 32], "taiepoch": [6, 31, 32], "thetimesystemconvert": [6, 31], "utc": [6, 31, 32, 38], "tai": [6, 31, 32, 38], "mod": [6, 25], "structur": [6, 9, 19, 22, 25, 31, 32, 39], "mjd": [6, 7, 31, 32], "a1mjd": [6, 31, 32, 38], "22326": [6, 31, 32], "977184": [6, 31, 32], "rvin": [6, 7, 31, 32], "rvector6": [6, 29, 31, 32], "6988": [6, 31, 32], "426918": 6, "1073": [6, 31, 32], "884261": 6, "2247": [6, 31, 32], "332981": 6, "019982": [6, 31, 32], "7": [6, 7, 18, 22, 24, 31, 32, 37, 38, 39], "226988": [6, 31, 32], "554962": [6, 31, 32], "rvout": [6, 7, 31, 32], "csconvert": [6, 7, 31, 32], "coordinateconvert": [6, 7, 29, 31, 32], "solar": [6, 25, 32, 33, 36], "central": [6, 38, 39], "bodi": [6, 7, 19, 32, 33], "ss": 6, "getsolarsysteminus": 6, "earth": [6, 7, 9, 19, 23, 24, 25, 28, 29, 31, 32, 33, 35, 38, 39], "getbodi": 6, "input": [6, 9, 15, 18, 19, 31, 32, 33, 38], "output": [6, 7, 9, 20, 31, 32, 33, 39], "eci": [6, 7, 25, 31, 32], "coordinatesystem": [6, 7, 9, 24, 25, 29, 31, 32, 33, 38], "createlocalcoordinatesystem": 6, "mj2000eq": [6, 7, 29, 31, 32, 33, 38], "none": [6, 15, 24, 33, 36, 38, 39], "ecef": [6, 7, 31, 32], "bodyfix": [6, 7, 25, 31, 32], "sc": 6, "evalu": [6, 32, 35, 38, 39], "cartesian": [6, 25, 32, 33, 38, 39], "7000": [6, 31, 32], "1000": [6, 29, 31, 32, 38, 39], "8": [6, 18, 22, 24, 31, 32, 33, 35, 38], "25": [6, 9, 31, 32, 38], "pstate": [6, 32, 33], "new_doublearrai": 6, "rang": [6, 7, 24, 32, 33, 35], "len": 6, "doublearray_setitem": 6, "getdefaultsolarsystem": 6, "psm": [6, 16, 24, 32, 33], "propagationstatemanag": [6, 16, 24, 32, 33], "setobject": [6, 24, 32, 33], "buildstat": [6, 24, 32, 33], "mapobjectstovector": 6, "odemodel": [6, 24, 31, 32, 33], "epointmassdynam": 6, "setforceorigin": 6, "epm": [6, 28, 31, 32], "pointmassforc": [6, 24, 28, 31, 32, 33], "earthpointmass": [6, 31, 32], "addforc": [6, 24, 28, 31, 32, 33], "memori": [6, 8, 16, 18, 31, 33], "own": [6, 16, 38], "delet": [6, 7, 9, 16, 23, 38], "thisown": [6, 16], "setpropstatemanag": [6, 16, 24, 32, 33], "setsolarsystem": 6, "setinternalcoordsystem": 6, "bufferst": 6, "buildmodelfrommap": [6, 24, 32, 33], "updateinitialdata": [6, 24, 32, 33], "pderiv": [6, 32], "getderivativearrai": [6, 31, 32, 33], "getderiv": [6, 31, 32, 33], "sun": [6, 19, 24, 25, 31, 33, 36, 38, 39], "moon": [6, 24, 31, 33, 38], "won": [6, 31], "t": [6, 7, 24, 31, 35, 36, 38], "segfault": 6, "createphysicalmodel": 6, "spm": [6, 31], "sunpointmass": [6, 31], "mpm": [6, 31], "moonpointmass": [6, 31], "setstringparamet": [6, 9, 31], "bodynam": [6, 24, 31, 33], "luna": [6, 19, 24, 29, 31, 33, 38], "refer": [6, 7, 11, 15, 20, 23, 24, 25, 32, 33, 37, 39, 40, 41], "od": [6, 38], "rv": 6, "prop": [6, 22, 23, 24, 28, 31, 32, 33], "princedormand78": [6, 24, 31, 32, 33], "setphysicalmodel": [6, 31, 32], "getstat": [6, 24, 25, 32, 33], "count": [6, 32, 38], "step": [6, 7, 10, 17, 18, 19, 22, 23, 25, 26, 31, 32, 33, 36, 39], "60": [6, 24, 31, 32, 33], "setfield": [6, 8, 15, 22, 24, 25, 27, 28, 32, 33], "interest": 7, "parti": [7, 38], "five": [7, 18, 22, 32, 38], "summar": [7, 22, 23, 32], "id": [7, 9], "titl": 7, "apirfa01": 7, "joel": 7, "parker": 7, "apirfa02": 7, "apirfa03": 7, "jacob": 7, "england": 7, "apirfa04": 7, "steve": 7, "hugh": 7, "apirfa05": 7, "apirfa06": 7, "which": [7, 11, 16, 17, 19, 23, 24, 25, 33, 36, 37, 38, 39, 40], "free": [7, 13], "sinc": [7, 13, 22, 25], "But": 7, "intend": [7, 39], "end": [7, 17, 18, 23, 32, 33, 35, 38, 39], "stabl": 7, "exist": [7, 9, 15, 32, 35, 38, 39], "caus": [7, 16, 19, 32, 35], "unintend": 7, "consequ": 7, "look": [7, 32, 38], "straightforward": [7, 34], "safe": [7, 38], "qualiti": [7, 35, 38], "confus": [7, 9, 25], "misl": 7, "mix": 7, "could": [7, 32, 33, 35], "inadvert": 7, "avoid": [7, 35, 38], "altogeth": 7, "bit": [7, 25], "misnom": 7, "reason": [7, 22, 32, 38], "As": [7, 32, 33, 37, 38, 39], "second": [7, 9, 13, 17, 24, 25, 32, 33, 35, 38, 39], "typic": [7, 9, 11, 23, 38, 39], "one": [7, 17, 19, 20, 22, 24, 29, 32, 33, 35, 38, 39], "procedur": [7, 18, 22, 38], "session": [7, 17, 19], "either": [7, 9, 10, 11, 16, 19, 24, 33, 38, 39], "exercis": [7, 10], "benefit": 7, "autom": 7, "interconnect": [7, 15], "eventu": 7, "over": [7, 36, 38], "broader": 7, "environ": [7, 10, 17, 18, 19, 20, 23, 26, 33], "suppli": [7, 9, 33, 35, 36, 38], "paradigm": 7, "doe": [7, 16, 19, 20, 22, 25, 32, 33, 35, 38, 39], "thei": [7, 9, 10, 11, 19, 22, 25, 32, 33, 35, 38], "after": [7, 11, 18, 22, 24, 25, 33, 35, 38, 39], "similarli": [7, 20, 25, 33, 38], "addit": [7, 9, 10, 13, 19, 22, 23, 32, 35, 36, 39], "clarifi": [7, 20], "how": [7, 11, 16, 18, 19, 20, 23, 25, 32, 33, 35, 36, 38, 40], "fit": [7, 20, 38], "relianc": 7, "major": [7, 38, 39], "minor": [7, 38, 39], "ideal": 7, "extens": [7, 37, 38, 40], "issu": [7, 8, 9, 10, 16, 32, 35, 38], "side": [7, 16, 39], "match": [7, 13, 18, 33, 38], "alongsid": [7, 37], "potenti": [7, 19, 26, 35, 38], "have": [7, 9, 15, 16, 18, 20, 22, 24, 25, 32, 33, 34, 35, 38, 39], "investig": 7, "mitig": 7, "confirm": 7, "necessari": [7, 10, 20, 25, 33, 34, 38], "b": [7, 35, 36], "chosen": [7, 38, 39], "better": 7, "option": [7, 9, 18, 19, 32, 33, 35, 38], "select": [7, 9, 35, 36, 37, 38, 39], "enhanc": 7, "eas": [7, 9], "reli": [7, 37], "being": [7, 9, 16, 22, 36, 38, 39], "machin": 7, "problemat": 7, "further": [7, 17, 38], "schedul": 7, "reconfigur": 7, "upgrad": 7, "sure": [7, 19, 33], "don": 7, "broken": [7, 15], "It": [7, 13, 22, 32, 33, 35, 36, 38], "less": [7, 9, 10, 38, 39], "adopt": [7, 35], "known": [7, 9, 38], "difficult": [7, 10], "fragil": 7, "distinguish": 7, "howev": [7, 9, 10, 19, 35, 38, 39], "come": [7, 32, 38], "depend": [7, 19, 35, 38, 39, 40], "its": [7, 19, 24, 25, 32, 33, 36, 38, 39], "about": [7, 9, 15, 22, 23, 32, 38, 40], "binari": 7, "found": [7, 9, 10, 17, 19, 22, 26, 38, 39], "doc": [7, 19, 37], "html": 7, "did": [7, 25, 38], "try": [7, 39], "py_limited_api": 7, "discuss": [7, 13, 35, 38, 40], "incompat": [7, 9], "track": [7, 9, 33], "implement": [7, 15, 18, 32, 33, 35, 38, 39], "If": [7, 9, 22, 23, 35, 36, 37, 38, 39], "solut": [7, 33, 35, 39], "inclus": [7, 38], "linkag": [7, 10, 35, 39], "same": [7, 9, 19, 22, 23, 25, 31, 35, 37, 38, 39], "order": [7, 11, 17, 23, 24, 32, 33, 35, 37, 38, 39], "might": [7, 17, 18, 32, 38], "leak": 7, "go": 7, "out": [7, 23], "scope": [7, 38], "explor": [7, 35], "understand": [7, 9, 10, 11, 38], "correctli": [7, 19, 33], "instanti": [7, 39], "workspac": [7, 21], "pass": [7, 9, 11, 16, 22, 24, 33, 38, 39], "my": [7, 38], "question": 7, "what": [7, 35, 38, 39], "happen": 7, "just": [7, 16, 25, 33], "goe": 7, "myth": 7, "createth": 7, "dostuff": 7, "del": 7, "therefor": [7, 38], "Or": [7, 38], "suppos": 7, "you": [7, 13, 16, 17, 18, 19, 24, 33, 37, 38], "do": [7, 9, 11, 16, 18, 19, 20, 33, 35, 37, 38, 39], "loop": [7, 33], "variabl": [7, 19, 20, 25, 27, 33, 35, 36, 39], "vanish": 7, "soon": 7, "particular": [7, 22, 38], "iter": [7, 35, 39], "destroi": 7, "n": [7, 35, 36, 39], "too": [7, 38, 39], "them": [7, 9, 17, 32, 33, 38], "latter": [7, 11, 33], "ani": [7, 9, 16, 19, 33, 35, 38, 39], "rid": 7, "close": [7, 16, 18, 38], "re": [7, 38], "answer": 7, "vs": 7, "know": [7, 9, 33], "right": [7, 35, 36, 38, 39], "gmatbas": [7, 9, 11, 16, 18, 19, 33], "expert": [7, 14, 17], "codebas": 7, "worri": 7, "mani": [7, 9, 10, 22, 32, 33, 35, 38], "contain": [7, 9, 13, 16, 17, 19, 21, 24, 25, 32, 33, 35, 36, 37, 38, 39, 40], "By": [7, 38], "facil": [7, 13], "constructor": [7, 9, 16, 38], "garbag": [7, 16], "collector": [7, 16], "destructor": 7, "overridden": 7, "underli": [7, 9, 16, 33], "basi": [7, 9, 38], "me": 7, "repeat": [7, 38], "return": [7, 9, 15, 16, 18, 19, 22, 23, 33, 38], "pointer": [7, 25, 38], "long": 7, "remain": [7, 9, 16, 22, 33], "outsid": [7, 11, 13, 16, 17, 24, 25, 38], "watch": 7, "proce": [7, 19], "transpar": [7, 38], "possibl": [7, 18, 32, 34, 35, 38, 39], "simpl": [7, 10, 17, 29, 32, 33, 39], "rel": [7, 35, 36, 38, 39], "eo1": 7, "special": [7, 20], "where": [7, 10, 33, 35, 38, 39], "certain": [7, 19, 35], "ax": [7, 9, 25, 32, 33, 38, 39], "objectreferenc": 7, "instead": [7, 16, 19, 23], "primari": [7, 32, 35, 38], "secondari": [7, 32], "field": [7, 11, 15, 22, 26, 32, 33, 37, 38, 41], "especi": [7, 38], "choic": 7, "dramat": 7, "type": [7, 9, 15, 16, 17, 18, 19, 22, 23, 24, 25, 27, 32, 33, 34, 36], "spk": [7, 34], "integr": [7, 9, 26, 32, 33, 35, 39, 40], "also": [7, 15, 16, 17, 19, 20, 22, 24, 32, 33, 37, 38, 39, 40], "numer": [7, 9, 11, 13, 15, 24, 32, 33, 35, 36, 38], "method": [7, 10, 16, 21, 22, 23, 25, 32, 33, 35, 38, 39], "quit": 7, "error": [7, 22, 35, 38, 39], "prone": 7, "straigtforward": 7, "place": [7, 10, 19, 23, 37, 38], "control": [7, 9, 11, 17, 18, 22, 33, 37, 39, 40, 42], "engin": [7, 9, 10, 11, 16, 18, 19, 40], "databas": 7, "concern": 7, "prior": [7, 11, 38], "miss": [7, 9, 15, 33], "challeng": 7, "cover": [7, 17, 22, 38], "concentr": 7, "effort": 7, "regard": 7, "focu": 7, "clear": [7, 9, 33, 38], "messag": 7, "our": [7, 38], "year": [7, 36], "revisit": 7, "hope": 7, "refin": [7, 39, 41], "strategi": 7, "larger": 7, "danger": 7, "becaus": [7, 10, 19, 22, 25, 32, 33, 38, 39], "omit": [7, 23], "undesir": [7, 38], "mycustomstartupfil": [7, 31], "sound": 7, "elimin": 7, "modifi": [7, 32, 33, 37, 38, 39], "line": [7, 10, 20, 22, 32, 33, 37, 39, 40], "startup": [7, 15, 16, 18, 19, 24, 25, 33], "peopl": 7, "forget": 7, "obviou": 7, "readi": [7, 10, 17, 18, 19, 32, 33, 38], "appear": [7, 10, 25, 38, 39], "similar": [7, 10, 33, 35], "beginmissionsequ": [7, 22, 23, 38], "due": [7, 19, 38], "correct": [7, 25, 32, 33], "consid": [7, 16, 33, 38], "initializeobject": 7, "leav": [7, 9, 10, 18, 24, 25, 38], "rest": [7, 19, 32, 38], "alon": [7, 22, 32, 37, 38, 40], "final": [7, 22, 23, 24, 32, 38, 39], "befor": [7, 24, 25, 32, 33, 38], "back": [7, 17], "probabl": 7, "biggest": 7, "care": [7, 22, 38], "taken": [7, 18, 38], "trap": 7, "un": 7, "non": [7, 35, 38, 39], "mode": [7, 18, 36, 39], "propos": [7, 35], "member": [7, 15, 23, 25, 32, 33], "agre": 7, "ll": [7, 24, 25], "longer": [7, 16, 38, 39], "purpos": [7, 11, 13, 33, 38], "establish": [7, 9], "behavior": [7, 38], "though": [7, 33], "occur": [7, 22, 38], "list": [7, 9, 15, 16, 18, 22, 23, 24, 33, 38, 39], "friendli": 7, "format": [7, 32, 34, 36, 39], "maintain": [7, 32], "progress": [7, 17, 38], "low": [7, 17, 35, 37, 38, 39], "alwai": [7, 32, 38], "A": [7, 8, 9, 10, 17, 19, 23, 25, 32, 33, 36, 38, 39, 41], "placehold": 7, "tabl": [7, 15, 18, 23, 32, 35, 36, 39], "content": [7, 17, 19, 24, 36, 38, 40], "respons": [8, 16, 32, 33, 38], "rfa": 8, "01": [8, 38], "unsaf": 8, "02": [8, 28, 38], "compat": 8, "03": [8, 33, 38], "handl": [8, 19, 33, 38], "persist": 8, "04": [8, 38], "complex": [8, 21, 22, 32, 33, 38], "renam": [8, 16], "06": [8, 38], "comparison": [8, 32], "short": 8, "getfield": [8, 15, 18, 27], "fast": [9, 39], "meet": [9, 17, 38], "simul": [9, 11, 18, 22, 25, 33, 38], "necessarili": [9, 35], "nor": [9, 25], "graphic": [9, 11, 39], "sever": [9, 13, 15, 20, 23, 26, 32, 33, 36, 37, 38], "facilit": [9, 15], "paramet": [9, 18, 35, 38, 39], "setrealparamet": 9, "etc": [9, 16, 35, 38, 39], "adapt": [9, 10, 36, 38], "strongli": [9, 38], "overload": [9, 38, 39], "multipl": [9, 13, 35, 36, 38, 39], "check": [9, 32, 33, 38], "accordingli": [9, 39], "signatur": 9, "bool": [9, 15, 18, 38], "valu": [9, 15, 17, 18, 23, 24, 27, 33, 35, 36, 38], "7100": [9, 38], "13": [9, 38], "take": [9, 18, 19, 24, 25, 32, 33, 38], "integ": [9, 24, 33, 36, 38, 39], "string": [9, 11, 15, 18, 19, 24, 31, 36, 38, 39], "real": [9, 33, 36, 38, 39], "number": [9, 20, 22, 32, 33, 35, 36, 38, 39], "boolean": [9, 19, 33, 36, 39], "On": [9, 19, 37, 38, 39], "success": [9, 19, 22], "true": [9, 18, 19, 24, 25, 29, 33, 38, 39], "fals": [9, 16, 19, 33, 38, 39], "getstringparamet": 9, "getrealparamet": 9, "form": [9, 32, 33, 35, 38], "getnumb": [9, 15, 27], "retriev": [9, 15, 17, 18], "empti": [9, 24, 38, 39], "rather": [9, 10, 19, 25, 38], "along": [9, 10, 19, 21, 25, 33, 38, 39], "impulsiveburn": [9, 23, 27, 28, 38], "ib": 9, "mock": 9, "impulse1": 9, "print": [9, 18, 24, 25, 29, 33, 39], "9": [9, 32, 38], "local": [9, 32, 33, 36, 38], "enum": [9, 38], "vnb": [9, 32], "element1": [9, 18, 23, 27, 28], "element2": [9, 18], "element3": 9, "decrementmass": 9, "isp": [9, 36], "300": [9, 22], "gravitationalaccel": 9, "81": 9, "programm": [9, 17, 33], "word": [9, 19, 23, 35, 38, 39], "interobject": [9, 25], "prepar": [9, 26, 33, 38], "composit": [9, 32], "jointli": 9, "task": [9, 10, 20, 22], "burden": 9, "apihelpfunct": 9, "hide": [9, 33], "servic": 9, "would": [9, 10, 20, 32, 38, 39], "otherwis": [9, 36, 38, 39], "briefli": 9, "reentrant": 9, "subsequ": [9, 22, 38], "reconnect": 9, "continu": [9, 35, 38, 39], "statu": [9, 18, 33, 38], "objecttyp": 9, "objectnam": [9, 33], "showobject": [9, 15, 19, 27, 33], "showclass": [9, 15, 27], "creatabl": 9, "topic": [9, 27], "top": [9, 11, 19, 24], "enter": [9, 33], "correspond": [9, 22, 23, 33, 37, 38, 39], "inter": 9, "prevent": 9, "succeed": 9, "problem": [9, 17, 19, 32, 33, 37, 39, 40, 41], "configurationmanag": 9, "singleton": [9, 32], "action": [9, 10, 22, 23], "invis": 9, "direct": [9, 17, 18, 25, 32, 33, 38], "fig": [10, 11, 38], "stack": [10, 11], "shown": [10, 11, 15, 18, 19, 20, 22, 23, 25, 32, 33, 37, 38], "layer": 10, "pretti": 10, "expertis": 10, "imit": 10, "helper": [10, 16, 32, 38], "encapsul": [10, 11, 19], "behind": [10, 32], "drive": [10, 15, 17, 19, 33], "commun": [10, 13, 33], "prototyp": [10, 32], "lack": 10, "appar": 10, "inconsist": 10, "defin": [11, 25, 32, 33, 35, 39], "former": [11, 33], "inner": 11, "clone": [11, 18, 23, 24], "factori": 11, "store": [11, 18, 20, 22, 32, 37], "interpret": [11, 19, 22, 25, 39], "gui": [11, 16, 22], "publish": [11, 22, 39], "subscrib": [11, 38], "nutshel": 11, "anoth": [11, 17, 22, 25, 29, 38], "diagram": [11, 23, 38], "vector": [11, 15, 25, 32, 33, 35, 39], "matrix": [11, 15, 32, 33, 35, 40], "manipul": [11, 17, 18, 22, 23, 25, 33], "oper": [11, 13, 19, 25, 36], "represent": [11, 25, 32, 35], "serial": 11, "properti": [11, 33, 38, 40], "read": [11, 18, 33, 34, 38], "panel": 11, "resourc": [11, 18, 22, 23, 28, 33, 35, 39, 40], "environment": 11, "hardwar": [11, 39], "optim": [11, 22, 33, 37, 39, 40, 42], "timelin": [11, 22], "fall": 11, "focus": [11, 22, 33], "maneuv": [13, 18, 22, 23, 27, 28, 30, 39], "organ": [13, 35, 36, 38, 39, 40, 41, 42], "throughout": [13, 33], "world": 13, "sampl": [13, 19, 27, 32, 33], "orient": [13, 25, 32, 33], "2002": 13, "domain": 13, "materi": [13, 14, 37, 38, 40], "divid": [13, 35], "section": [13, 17, 18, 19, 20, 25, 31, 32, 33, 36, 38, 39, 40], "find": [13, 19, 35, 38, 39], "extrem": 13, "philosophi": 13, "govern": [13, 35], "enabl": [13, 22, 36], "request": [13, 32, 38], "instruct": [13, 19, 37, 38], "instal": [13, 21, 33, 38, 40, 41], "hint": 13, "tip": 13, "conclud": 13, "appendic": 13, "guidelin": [13, 21], "advis": 14, "captur": [14, 16, 24, 25, 39], "\u00bd": [15, 17], "\u00bc": [15, 17], "\u215b": [15, 17], "\u00be": [15, 17], "\u215c": [15, 17], "\u215d": [15, 17], "\u215e": [15, 17], "_": [15, 17], "\u00b5": [15, 17], "\u03c9": [15, 17], "\u00aa": [15, 17], "\u00ba": [15, 17], "\u00b9": [15, 17], "\u00b2": [15, 17], "\u00b3": [15, 17], "chapter": [15, 17, 21, 22, 23, 26, 32, 35, 38, 40], "tailor": [15, 17], "block": [15, 17, 20, 22, 23, 38, 41], "mysat": [15, 19, 20, 25, 38], "startfil": 15, "void": [15, 19, 31, 38], "custom": [15, 19, 39], "burn": [15, 23, 27, 28, 32, 34, 39], "given": [15, 17, 23, 35, 36, 38, 39], "sat": [15, 18, 19, 22, 23, 24, 25, 27, 28, 32, 33], "add": [15, 16, 19, 23, 26, 32, 33, 37, 38, 39], "copi": [15, 16, 18, 19, 23, 25, 33], "sat2": 15, "getobject": [15, 16, 32], "doubl": [15, 19, 31, 38], "getvector": [15, 27], "rvector": [15, 38], "getmatrix": [15, 27], "rmatrix": [15, 38], "consol": [16, 19], "retain": 16, "sometim": 16, "client": 16, "assign": [16, 22, 23, 24, 27, 32, 33, 39], "setswigownership": 16, "fm": [16, 23, 24, 28, 31, 33], "indic": [16, 22, 38], "your": [16, 19, 37, 38], "path": [16, 19, 33, 35, 36, 37, 39, 41], "directori": [16, 18, 19, 33, 37, 38, 39], "gmatapi": [16, 18, 19, 27, 33], "getruntimeobject": [16, 18, 27], "cast": [16, 33, 35], "setclass": [16, 33], "gmatpi": [16, 17, 18, 19, 20, 24, 25, 27, 29, 32, 33], "station": [16, 17, 19], "conveni": [16, 38], "often": [16, 38], "path_and_startup_file_nam": 16, "goal": [17, 32, 33], "quickli": [17, 38], "introduc": [17, 18, 22, 25, 33, 38], "solv": [17, 33, 35, 37, 38, 40], "primarili": 17, "suffix": 17, "_py": 17, "py": [17, 19, 24, 25, 33], "associ": [17, 25, 32, 33, 35], "jar": [17, 19], "subsystem": [17, 19, 32, 33, 38, 40], "tabul": 17, "station_pi": [17, 33], "groundstat": [17, 33], "navigation_pi": 17, "load_gmat": [17, 18, 19, 20, 23, 27, 28, 33], "act": [17, 19, 32, 38], "front": [17, 18], "runtim": 17, "At": [17, 19, 25, 32, 38], "point": [17, 19, 24, 25, 32, 33, 35, 37, 38, 39], "intermedi": [17, 38], "portion": [17, 32, 33, 38], "feed": 17, "proven": [17, 38], "acceler": [17, 35, 38], "calcul": [17, 18, 33, 38, 39], "walk": [17, 23, 24, 33, 38], "techniqu": 17, "fine": 17, "grain": 17, "extend": [17, 32, 33], "monitor": 17, "anticip": 17, "headless": 18, "remot": 18, "larg": [18, 19, 32, 35, 38], "scale": [18, 36, 39], "carlo": 18, "scan": 18, "loadscript": [18, 27, 32], "runscript": [18, 23, 27, 32], "savescript": [18, 27], "save": [18, 19], "getrunsummari": [18, 27], "togeth": [18, 22, 23, 25, 26, 32, 33, 35, 38], "locat": [18, 33, 35, 36, 37, 38, 39], "sequenti": 18, "script_path_and_nam": 18, "ex_geotransf": [18, 27], "total": [18, 38, 39], "delta": [18, 35, 38], "v": [18, 24, 27, 35, 38], "geo": 18, "transfer": [18, 35, 38], "python3": [18, 19, 33], "oct": [18, 38], "22": [18, 40], "32": [18, 32, 38], "17": [18, 32, 40], "gcc": [18, 37], "linux": [18, 19], "copyright": 18, "credit": 18, "licens": [18, 40], "toi": 18, "mcc": 18, "moi": 18, "toidv": 18, "float": 18, "mccdv": 18, "moidv": 18, "deltav": 18, "ab": 18, "cost": [18, 35, 39], "km": [18, 33, 35, 36, 38, 39], "394839062410714": 18, "exit": [18, 33, 38], "complic": [18, 32, 37, 38], "argument": [18, 38, 39], "blank": 18, "No": [18, 19, 32, 33, 38, 39], "logic": 18, "str2num": 18, "sqrt": [18, 38], "begin": [19, 33, 35, 38], "sourceforg": 19, "bundl": [19, 37], "workstat": [19, 37], "immedi": [19, 32, 38, 39], "later": [19, 33, 38], "paragraph": 19, "text": [19, 22, 23, 38], "planetari": 19, "ephemerid": [19, 34], "gravit": [19, 25, 38], "accomplish": 19, "definit": [19, 32, 35, 38], "scipt": 19, "buildapistartupfil": 19, "api_readm": 19, "api_startup_fil": 19, "simplest": [19, 32, 38], "editor": 19, "toplevelgmatfold": 19, "entri": [19, 22, 25, 38, 39], "absolut": [19, 39], "enclos": 19, "quotat": 19, "window": [19, 20, 38], "backslash": 19, "charact": 19, "forward": 19, "slash": 19, "advantag": [19, 38], "home": [19, 38], "preserv": [19, 23], "symbol": [19, 35, 36, 38], "done": [19, 24, 25, 34, 38], "syntax": [19, 22, 23, 32, 38], "cd": [19, 24, 33], "apifromher": 19, "earthmj2000eq": [19, 24, 25, 33, 38, 39], "earthmj2000ec": 19, "earthfix": 19, "earthicrf": 19, "solarsystembarycent": 19, "solarsystem": [19, 33], "mercuri": [19, 33], "venu": [19, 33], "mar": [19, 27, 33, 38], "jupit": [19, 33], "saturn": [19, 33], "uranu": [19, 33], "neptun": [19, 33], "pluto": [19, 33], "nodesktop": 19, "To": [19, 23, 32, 33, 37], "visit": 19, "mathwork": 19, "com": 19, "addpath": 19, "gmatutil": 19, "ground": 19, "basic": [19, 22, 23, 24, 25, 32, 38], "mymod": [19, 33], "flag": [19, 25, 39], "unsuccess": 19, "extra": [19, 38], "compar": [19, 38], "static": [19, 26, 31, 33, 35], "arg": [19, 31], "loadlibrari": 19, "few": [19, 24, 25, 38, 40], "dir": 19, "filenam": [19, 24, 33, 38], "gmatstartuppath": [19, 33], "everi": [19, 35, 38, 39], "explicitli": [19, 38], "itself": [19, 25, 39], "common": [20, 31, 38], "normal": [20, 32, 33], "16": 20, "digit": 20, "truncat": [20, 33], "page": [20, 38], "extran": 20, "white": 20, "remov": [20, 23, 24, 33, 38], "segment": [20, 35, 38, 39], "offset": 20, "tripl": 20, "bracket": 20, "marker": 20, "typeset": 20, "involv": [20, 33, 38], "observ": [20, 33], "dichotomi": 20, "camel": 20, "lowercas": 20, "fashion": 21, "typographi": 21, "gmatcommand": [22, 23], "doubli": 22, "mean": [22, 25, 32, 33, 38, 39], "chain": [22, 23, 39], "illustr": [22, 26, 32, 38, 40], "commandsequ": 22, "propagationenabledcommand": 22, "toggl": [22, 25, 33], "beginfiniteburn": 22, "endfiniteburn": 22, "branchcommand": 22, "conditionalbranch": 22, "endfor": 22, "els": [22, 36, 38], "endif": 22, "endwhil": 22, "solver": [22, 35], "solversequencecommand": 22, "solverbranchcommand": 22, "runsolv": 22, "endtarget": [22, 23, 28], "endoptim": 22, "vari": [22, 23, 28, 35, 38, 39], "achiev": [22, 23, 28, 35, 38, 39], "minim": [22, 35, 38, 39], "nonlinearconstraint": 22, "beginfunct": 22, "endfunct": 22, "callfunct": 22, "callbuiltingmatfunct": 22, "stand": [22, 32, 37, 38, 40], "stop": [22, 38, 39], "condit": [22, 33, 35, 36, 39], "forth": 22, "noop": 22, "beginscript": 22, "endscript": 22, "clearplot": 22, "pendown": 22, "penup": 22, "markpoint": 22, "plotcommand": 22, "savemiss": 22, "updatedynamicdata": 22, "findev": 22, "hidden": [22, 38], "tab": 22, "Of": 22, "runsimul": 22, "runestim": 22, "pai": 22, "attent": 22, "6600": [22, 33], "y": [22, 28, 35, 38], "z": [22, 28, 35, 38], "vx": [22, 28, 35, 38], "vy": [22, 28, 38], "vz": [22, 28, 35, 38], "evolv": 22, "rmag": [22, 23, 28], "7200": 22, "apiinterpret": 22, "r2024a": 22, "scriptinterrpt": 22, "fire": [22, 24], "int": [22, 23, 31], "invalid": [22, 35], "thrown": [22, 35, 38], "unknown": 22, "interrupt": [22, 38], "unkown": 22, "respond": 23, "reproduc": 23, "referenc": [23, 25, 32, 38], "abbrevi": 23, "reserv": 23, "repres": [23, 32, 35, 38, 39], "bubbl": 23, "pars": 23, "everyth": 23, "until": [23, 28, 35, 38, 39], "reach": [23, 38], "node": [23, 33], "preform": 23, "bullet": [23, 38], "desc": [23, 27], "deletecommand": 23, "cmd": [23, 27], "insert": [23, 38], "termin": [23, 37, 39], "move": [23, 38], "next": [23, 24, 33, 38], "children": 23, "sketch": 23, "perige": [23, 28], "periapsi": [23, 28, 35, 38], "19": [23, 32], "forcemodel": [23, 24, 28, 33, 38], "geosynchron": 23, "distanc": [23, 36, 38, 39], "burn1": 23, "differentialcorrectordc": 23, "dc": [23, 28], "perturb": [23, 28], "0001": [23, 28], "maxstep": 23, "42165": [23, 28], "toler": [23, 28, 33, 35, 39], "differentialcorrector": [23, 28], "tc": 23, "tend": [24, 25], "annoi": [24, 25], "run_gmat": [24, 25], "leosat": 24, "dateformat": [24, 28, 33], "utcgregorian": [24, 28, 33, 38, 39], "sep": 24, "00": [24, 25, 28, 33, 38], "000": [24, 25, 28, 33, 35, 38, 40], "displaystatetyp": [24, 33], "keplerian": [24, 25, 28, 33], "sma": [24, 25, 28, 33], "7005": 24, "ecc": [24, 25, 28, 33], "008": 24, "28": [24, 28, 38], "raan": [24, 25, 28, 33], "75": [24, 25, 33, 38], "aop": [24, 25, 28, 33], "90": [24, 25, 28, 33], "ta": [24, 25, 28, 33], "45": [24, 25, 28, 33, 38], "drymass": [24, 32, 33, 38], "50": [24, 25, 38], "cr": [24, 32, 33], "dragarea": [24, 33], "srparea": [24, 32, 33], "8x8": [24, 33], "mass": [24, 32, 33, 35, 36, 39], "jacchia": [24, 33], "robert": [24, 33], "drag": [24, 33, 38], "accept": [24, 39], "theforc": 24, "centralbodi": [24, 33, 38], "primarybodi": [24, 33], "objectarrai": 24, "polyhedralbodi": 24, "pointmass": [24, 38], "srp": [24, 32, 33, 38], "onoff": 24, "off": [24, 38], "relativisticcorrect": 24, "errorcontrol": [24, 33], "rssstep": 24, "userdefin": 24, "largest": 24, "graviti": [24, 33, 38], "jgm": [24, 33], "earthgrav": [24, 33], "gravityfield": [24, 33], "degre": [24, 33, 35, 38], "potentialfil": [24, 33], "jgm2": 24, "cof": [24, 33], "moongrav": 24, "sungrav": 24, "jrdrag": [24, 27], "dragforc": 24, "atmospheremodel": 24, "jacchiarobert": 24, "atmospher": [24, 35], "atmo": [24, 27], "setrefer": [24, 27, 33], "examin": 24, "individu": [24, 33, 38], "stmlimit": 24, "100": [24, 35, 38], "tidefil": 24, "tidemodel": 24, "littl": [24, 32], "getgeneratingstr": 24, "historicweathersourc": 24, "constantfluxandgeomag": 24, "predictedweathersourc": 24, "cssispaceweatherfil": 24, "spaceweath": 24, "v1": 24, "schattenfil": 24, "schattenpredict": 24, "f107": 24, "150": [24, 25], "f107a": 24, "magneticindex": 24, "schattenerrormodel": 24, "nomin": 24, "schattentimingmodel": 24, "nominalcycl": 24, "dragmodel": 24, "spheric": [24, 33], "princ": 24, "dormand": 24, "rung": [24, 33, 38], "kutta": [24, 33, 38], "pdprop": [24, 33], "gator": [24, 33], "initialsteps": [24, 33], "accuraci": [24, 33, 35, 38], "0e": [24, 33], "12": [24, 25, 28, 32, 33, 38, 39], "minstep": [24, 33], "basicfm": 24, "getpropstatemanag": 24, "fold": 24, "refresh": [24, 33], "getodemodel": [24, 33], "getpropag": [24, 33], "setstat": [24, 31, 32, 33], "assembl": [24, 32, 33], "finish": [24, 33], "altern": [24, 33, 38], "prepareintern": [24, 33], "manual": [24, 33, 37], "condens": 24, "seen": [24, 33, 38], "addpropobject": [24, 33], "accumul": [24, 32], "buffer": [24, 25], "po": 24, "vel": 24, "gatorst": 24, "r": [24, 33, 38], "j": [24, 35, 38], "append": 24, "5455": 24, "495852919224": 24, "3637": 24, "0485868833093": 24, "2350": 24, "0571814448517": 24, "1318014092807545": 24, "423940627548709": 24, "545227573417657": 24, "5256": 24, "1378692623475": 24, "4014": 24, "4902800853415": 24, "2192": 24, "4488937848546": 24, "51104729424038": 24, "153042432990329": 24, "7064897093764597": 24, "360": 24, "matplotlib": 24, "pyplot": 24, "plt": 24, "rcparam": 24, "figur": 24, "figsiz": 24, "plot": [24, 38, 39], "istat": 25, "j2000": [25, 32, 33], "equatori": [25, 32, 33], "posit": [25, 33, 34, 38, 39], "veloc": [25, 34, 38, 39], "gmatstat": 25, "proxi": [25, 33], "0x7f13ceed97e0": 25, "getepoch": 25, "getsiz": 25, "21545": 25, "000000397937": 25, "999": 25, "2000": [25, 32, 38], "taimodjulian": [25, 38], "00000039794": 25, "modjulian": 25, "julian": [25, 32, 38, 39], "fill": 25, "dummi": 25, "statetyp": [25, 33], "7015": 25, "0011": 25, "98": 25, "33": 25, "333": [25, 31, 32], "glanc": 25, "realli": 25, "cannot": [25, 33, 38], "constant": [25, 35, 36, 38], "uniniti": 25, "hold": [25, 37, 38], "popul": [25, 38], "post": [25, 33], "queri": 25, "preiniti": 25, "getkeplerianst": 25, "99058171804361": 25, "3946": 25, "626071010534": 25, "5789": 25, "742898439815": 25, "23046049968889": 25, "931020059857665": 25, "095581409074377": 25, "000000000001": 25, "00110000000000004": 25, "59999999999999": 25, "00000000000402": 25, "33299999999598": 25, "appli": [25, 33, 35, 38, 39], "8000": 25, "cartesianian": 25, "getcartesianst": 25, "172": 25, "19168264352888": 25, "4500": 25, "785255607168": 25, "6602": 25, "700383110265": 25, "088638988531676": 25, "553902317709759": 25, "835168124650226": 25, "1916826435289": 25, "000000000007": 25, "001100000000000707": 25, "00000000001161": 25, "3329999999884": 25, "5697": 25, "7414619496": 25, "3020": 25, "2186545041395": 25, "4721": 25, "90552145557": 25, "1208679033712874": 25, "413887097497282": 25, "7427113896944304": 25, "21865450414": 25, "120867903371287": 25, "74271138969443": 25, "000000000011": 25, "001100000000000964": 25, "00000000000001": 25, "00000000001836": 25, "33299999998167": 25, "5094": 25, "78342738948": 25, "4974": 25, "9069027511405": 25, "3633": 25, "5822378210464": 25, "5169011956354206": 25, "379735538828468": 25, "8235178821457656": 25, "90690275114": 25, "582237821046": 25, "516901195635421": 25, "823517882145766": 25, "000000000012": 25, "001100000000001075": 25, "00000000002314": 25, "49": 25, "99999999999523": 25, "coupl": [25, 39], "fix": [25, 32, 36, 39], "ecf": 25, "eclipt": 25, "solareclipt": 25, "sec": 25, "mj2000ec": [25, 29], "previous": [25, 37, 38], "scratch": 25, "solsat": 25, "solarsat": 25, "ve": 25, "reset": [25, 38], "reassign": 25, "reflect": [25, 33], "getnam": 25, "isiniti": [25, 33, 38], "bug": 25, "again": 25, "And": 25, "3980": 25, "769626359613": 25, "5904": 25, "072200723337": 25, "84663580491": 25, "31337371013498": 25, "221190102125526": 25, "82343374194999": 25, "7197": 25, "708272712511": 25, "1106817774544226": 25, "47": 25, "10837940070086": 25, "152": 25, "2889386356222": 25, "322": 25, "0637563061007": 25, "179": 25, "5887814511714": 25, "26505087": 25, "9080278": 25, "144694001": 25, "6268158": 25, "4700": 25, "442019894719": 25, "27732399951776": 25, "92620879192113": 25, "367891168160935": 25, "144849901": 25, "1130946": 25, "2292154440704447": 25, "702016602265948": 25, "280": [25, 32], "4191667873194": 25, "286": 25, "9680459339144": 25, "252": 25, "9931176051724": 25, "scriptusag": 27, "physicalmodel": [27, 31], "getcommand": 27, "obj": [27, 38], "counter": 27, "fieldlabel": 27, "orbiterrorcovari": 27, "plate": 27, "platenorm": 27, "refobject": 27, "new_geotransf": 27, "geosat": 27, "jan": [28, 38], "3100": 28, "11591": 28, "155": 28, "10": [28, 31, 32, 35, 38], "feb": [28, 38], "dispaystatetyp": 28, "6900": 28, "005": 28, "180": [28, 33], "apoapsi": 28, "entir": 28, "routin": [29, 32], "systm": 29, "moonec": [29, 33], "eartheq": [29, 33], "instat": 29, "outstat": 29, "4000": [29, 33, 38], "cconvert": 29, "28718": 29, "278537": 29, "716": 29, "272130": 29, "548": 29, "84187": 29, "769": 29, "64373770": 29, "28543905": 29, "30709590": 29, "ephemeri": [30, 33, 38, 39], "covari": 30, "timeconvnew": 31, "coordconvnew": 31, "427": [31, 32], "884": [31, 32], "forcemodelnew": 31, "dt": 31, "propexamplenew": 31, "seg": 31, "fault": 31, "trivial": 32, "rework": 32, "preview": 32, "atom": 32, "a1": [32, 38], "univers": [32, 40], "barycentr": 32, "tdb": 32, "terrestri": 32, "tt": 32, "1941": 32, "noon": 32, "gregorian": [32, 38], "pattern": [32, 35], "ineract": 32, "convertmjdtogregorian": 32, "jul": [32, 33, 38], "18": 32, "53": 32, "leap": 32, "specifi": [32, 33, 35, 36, 38, 39, 40], "numberofleapsecondsfrom": 32, "axistyp": 32, "axissystem": 32, "ye": [32, 38], "spacepoint": 32, "axi": [32, 33], "recent": [32, 33, 38], "demonstr": [32, 33], "19053": 32, "293": 32, "properli": 32, "constitu": 32, "mind": 32, "size": [32, 33], "flow": [32, 36], "tank": [32, 36, 39], "transit": [32, 33, 36], "scene": 32, "third": [32, 38], "225": 32, "epm_srp": 32, "solarradiationpressur": 32, "myintegr": 32, "toward": 32, "finit": [32, 34, 35, 39], "ex_finiteburn": 32, "chemic": [32, 36, 38], "thruster": [32, 36], "thrust": [32, 35, 36, 38, 39], "finiteburn": [32, 38], "chemicalthrust": 32, "engine1": 32, "thrustdirection1": 32, "thrustdirection2": 32, "thrustdirection3": 32, "suit": 33, "light": 33, "earthorbit": 33, "80": 33, "kg": [33, 36, 38, 39], "vehicl": [33, 38], "polar": 33, "semimajor": 33, "fair": 33, "frame": [33, 38], "planet": 33, "emj2k": 33, "remaind": 33, "comfort": 33, "ex_r2020a_basicforcemodel": 33, "ex_r2020a_completeforcemodel": 33, "circular": 33, "juli": 33, "20": [33, 38], "preliminari": [33, 38], "earthorb": 33, "78": 33, "effect": [33, 38], "radiat": 33, "pressur": [33, 38], "coeffici": [33, 36, 38], "surfac": 33, "area": 33, "resembl": 33, "ballist": 33, "worth": 33, "nest": 33, "identif": 33, "becom": 33, "alias": 33, "label": 33, "jgm3": 33, "ident": 33, "ownership": 33, "unnam": 33, "last": [33, 38, 39], "infrastructur": 33, "map": [33, 38], "physic": [33, 38, 39], "raw": 33, "dv": 33, "getderivativesforspacecraft": 33, "vec": 33, "scderiv": 33, "1018": 33, "819261": 33, "6778": 33, "562873": 33, "226958": 33, "374825e": 33, "00121383": 33, "00121392": 33, "00809840": 33, "turn": [33, 38], "amatrix": 33, "setproperti": 33, "ex_r2020a_propagationstep": 33, "ex_r2020a_basicfm": 33, "strip": 33, "down": [33, 38], "propagateloop": 33, "propsetup": 33, "analyt": [33, 35, 36, 38, 39], "algorithm": [33, 38, 39, 40, 41, 42], "predictor": 33, "corrector": 33, "actual": [33, 35], "relationship": [33, 38], "0x7f26b76a57e0": 33, "preced": 33, "018819261603825": 33, "018819261603827": 33, "778562873085272": 33, "005226958779502": 33, "000000000000000": 33, "330028382856595": 33, "703241487939055": 33, "763990149325915": 33, "005142965479731": 33, "005288543267909": 33, "000485610549370": 33, "dai": [33, 38], "standard": [33, 39], "restrict": [33, 36, 39], "reduc": [33, 38], "auto": 33, "0x7fccdc983f60": 33, "0x7fccdc983fc0": 33, "treat": 33, "subclass": 33, "explic": 33, "getclass": 33, "manner": 33, "vf13ad": 33, "vf13": 33, "0x7f615f50c2a0": 33, "traceback": 33, "stdin": 33, "attributeerror": 33, "attribut": 33, "0x7f614d9cff90": 33, "showprogress": 33, "reportstyl": 33, "reportfil": 33, "maximumiter": 33, "200": [33, 38], "1e": [33, 38, 39], "usecentraldiffer": 33, "feasibilitytoler": [33, 38], "001": 33, "perspect": 33, "ex_r2020a_rangemeasur": 33, "driven": 33, "trackingfileset": 33, "signal": 33, "travers": 33, "antenna": 33, "transmitt": 33, "receiv": 33, "transpond": 33, "media": 33, "ancillari": 33, "setnumb": 33, "simsat": 33, "gd": 33, "a1modjulian": [33, 38], "21550": 33, "horizonrefer": 33, "ellipsoid": 33, "location1": 33, "location2": 33, "location3": 33, "ant1": 33, "antenna1": 33, "tmit": 33, "transmitter1": 33, "frequenc": 33, "2067": 33, "rec": 33, "receiver1": 33, "ant2": 33, "antenna2": 33, "tpond": 33, "transponder1": 33, "turnaroundratio": 33, "240": 33, "221": 33, "primaryantenna": 33, "addhardwar": 33, "tem": 33, "errormodel": 33, "theerrormodel": 33, "noisesigma": 33, "050": 33, "deviat": 33, "nois": 33, "bia": 33, "doppler": 33, "rate": [33, 36, 39], "tem2": 33, "theerrormodel2": 33, "ranger": 33, "5e": [33, 38], "tf": 33, "simdata": 33, "trkfile_api_gn": 33, "gmd": 33, "even": [33, 38], "uselighttim": 33, "userelativitycorrect": 33, "useetminustai": 33, "addtrackingconfig": 33, "setpropag": 33, "tda": 33, "getadapt": 33, "nummea": 33, "md0": 33, "calculatemeasur": 33, "disp": 33, "getvalu": 33, "satstat": 33, "gspo": 33, "getmj2000posit": 33, "getepochgt": 33, "getdatavector": 33, "satpo": 33, "rnorm": 33, "norm": 33, "lighttim": 33, "xid": 33, "getparameterid": 33, "cartesianx": 33, "calculatemeasurementderiv": 33, "ii": 33, "apigetderivativevalu": 33, "md1": 33, "osiri": 34, "rex": 34, "luci": 34, "contact": 34, "500": [34, 38], "stk": 34, "ccsd": 34, "oem": 34, "spice": [34, 38], "interchang": 34, "impuls": [34, 36, 39], "arrai": [34, 35, 36, 39], "slightli": [34, 38], "alpha": [34, 38], "formal": 35, "mathemat": 35, "notat": 35, "bett": 35, "discret": [35, 38, 39], "colloc": [35, 38, 40], "trajectori": [35, 40, 41], "phase": [35, 41], "express": [35, 38, 41], "monei": 35, "subject": [35, 38, 39], "constraint": [35, 36, 39], "bolza": 35, "sum_": [35, 36], "k": [35, 36], "left": [35, 36, 38, 39], "phi": 35, "t_0": [35, 38], "t_f": [35, 38], "int_": 35, "lambda": 35, "u": [35, 38], "f": 35, "dot": [35, 38], "algebra": 35, "g_": 35, "min": 35, "leq": [35, 36, 38, 39], "max": [35, 38], "boundari": [35, 39, 41], "phi_": 35, "index": [35, 38, 39], "sub": [35, 38, 39], "lunar": 35, "escap": 35, "sphere": 35, "influenc": 35, "soi": 35, "whose": [35, 38, 39], "evolut": 35, "ordinari": 35, "differenti": [35, 38], "equat": [35, 38, 39], "yv": 35, "usual": 35, "NOT": [35, 38, 39], "maxim": [35, 38, 39], "satisfi": 35, "quadratur": [35, 39], "radiu": [35, 38, 39], "greater": [35, 38, 39], "psi": 35, "discontinu": 35, "joint": 35, "decis": [35, 39], "mathbf": 35, "subvector": 35, "quad": 35, "ldot": 35, "kth": 35, "q": 35, "stage": [35, 39, 41], "length": [35, 38, 39], "approxim": [35, 36, 38, 39, 40], "mathrm": 35, "numstat": 35, "nummeshpoint": 35, "numstag": 35, "transcript": [35, 39], "spars": [35, 37, 40], "linear": 35, "nlp": [35, 38], "noced": 35, "2006": 35, "accur": [35, 38], "implicit": [35, 38, 39], "scheme": 35, "hermit": [35, 38], "simpson": [35, 38], "lobatto": [35, 38], "iiia": [35, 38], "radau": [35, 38], "orthogon": [35, 38], "patterson": 35, "2014": 35, "pg": 35, "146": 35, "matric": 35, "mayer": 35, "lagrang": 35, "eq": 35, "frac": [35, 36, 38], "partial": [35, 38], "differenc": [35, 38], "randomli": 35, "within": [35, 36, 38, 39], "bound": [35, 36, 39], "respect": [35, 38, 39], "role": 35, "proper": 35, "interv": [35, 38, 39], "polynomi": [35, 36, 38], "obtain": [35, 38, 39], "p": [35, 36], "log_": 35, "epsilon": 35, "k_": 35, "th": 35, "maximum": [35, 36, 39], "tune": 35, "n_": 35, "textrm": [35, 38], "14": [35, 38], "subinterv": 35, "enforc": [35, 36, 38], "converg": [35, 39], "difficulti": 35, "altitud": 35, "ceas": 35, "carefulli": 35, "highli": [36, 38], "conjunct": 36, "emtg_spacecraft": [36, 38], "acronym": 36, "p_0": 36, "deliv": 36, "r_": 36, "csi": 36, "vsi": 36, "unit": [36, 37, 38, 39], "enableglobalelectricpropellanttankconstraint": 36, "enableglobalchemicalpropellanttankconstraint": 36, "enableglobaldrymassconstraint": 36, "globalelectricpropellanttankcapac": 36, "geq": [36, 39], "globalfueltankcapac": 36, "globaloxidizertankcapac": 36, "globaldrymassbound": 36, "lower": [36, 38, 39], "global": [36, 38], "dry": [36, 38], "upper": [36, 38, 39], "basedrymass": 36, "adaptermass": 36, "enableelectricpropellanttankconstraint": 36, "electr": 36, "propel": 36, "capac": 36, "enablechemicalpropellanttankconstraint": 36, "enabledrymassconstraint": 36, "electricpropellanttankcapac": 36, "chemicalfueltankcapac": 36, "fuel": [36, 39], "chemicaloxidizertankcapac": 36, "oxid": 36, "throttlelog": 36, "minimum": [36, 38, 39], "throttlesharp": 36, "smooth": 36, "sharp": 36, "throttl": 36, "heavisid": 36, "h": 36, "exp": 36, "powersystem": 36, "electricpropulsionsystem": 36, "chemicalpropulsionsystem": 36, "curv": [36, 38], "sauer": 36, "p_": 36, "gamma_0": 36, "gamma_1": 36, "gamma_2": 36, "gamma_3": 36, "gamma_4": 36, "bu": 36, "quadrat": 36, "p_bu": 36, "bus_pow": 36, "p_provid": 36, "p0": 36, "kw": 36, "decai": 36, "texttt": [36, 38, 39], "_rate": 36, "cdot": 36, "gamma": 36, "assum": [36, 38, 39], "au": [36, 38], "denomin": [36, 38], "effici": 36, "1d": 36, "l": 36, "mdot": 36, "throttlet": [36, 38], "relev": [36, 38], "constrant": 36, "monoprop": 36, "mixtur": 36, "ratio": 36, "factor": [36, 38, 39], "mn": 36, "asum": 36, "mg": 36, "search": 37, "src": [37, 38], "userfunutil": [37, 38], "collutil": 37, "among": [37, 38], "benchmark": 37, "snapshot": 37, "csalttest": [37, 38], "testoptctrl": [37, 38], "subdirectori": 37, "driver": [37, 38], "cpp": [37, 38], "pointpath": [37, 38], "fact": [37, 38], "nonlinear": [37, 38, 40], "snopt": [37, 39, 40], "fortran": 37, "redistribut": 37, "pre": [37, 38], "snopt7": 37, "dll": 37, "snopt7_cpp": 37, "gfortran": 37, "macconfigur": 37, "matlabconfigur": 37, "csaltplugin": 37, "snopt_lib_path": 37, "dylib": 37, "gfortran_lib_path": 37, "gmatconsol": 37, "bashrc": 37, "someth": 37, "dyld_library_path": 37, "lib": 37, "libgfortran": 37, "ld_library_path": 37, "upon": 38, "distribut": 38, "brachistichron": 38, "statement": 38, "compos": 38, "userpathfunct": 38, "userfunct": 38, "abstract": 38, "concaten": 38, "defect": 38, "across": 38, "duplic": 38, "six": 38, "header": 38, "hpp": 38, "csalttestdriv": 38, "_test": 38, "_src": 38, "cmakelist": 38, "driver21": 38, "hohmanntransferdriv": 38, "hohmanntransf": 38, "msg": 38, "unus": 38, "declar": 38, "scroll": 38, "bottom": 38, "let": 38, "sin": 38, "co": 38, "g_0": 38, "evaluatefunct": 38, "evaluatejacobian": 38, "brachistichronepathobject": 38, "174": 38, "extract": 38, "statevec": 38, "getstatevector": 38, "controlvec": 38, "getcontrolvector": 38, "xdot": 38, "ydot": 38, "vdot": 38, "setfunct": 38, "dynfunct": 38, "dxdot_dv": 38, "dydot_dv": 38, "dynstat": 38, "dxdot_du": 38, "dydot_du": 38, "dvdot_du": 38, "dyncontrol": 38, "dyntim": 38, "setjacobian": 38, "brachistochron": 38, "brachistichronepointobject": 38, "stateinit": 38, "getinitialstatevector": 38, "statefin": 38, "getfinalstatevector": 38, "tinit": 38, "getinitialtim": 38, "tfinal": 38, "getfinaltim": 38, "xinit": 38, "yinit": 38, "vinit": 38, "xfinal": 38, "yfinal": 38, "vfinal": 38, "numfunct": 38, "funcvalu": 38, "lowerbound": [38, 39], "upperbound": [38, 39], "costfunc": 38, "setfunctionbound": 38, "explain": 38, "equal": [38, 39], "radauphas": 38, "phase1": [38, 39], "std": 38, "initialguessmod": 38, "linearnocontrol": 38, "meshintervalfract": [38, 39], "integerarrai": [38, 39], "meshintervalnumpoint": [38, 39], "push_back": 38, "timelowerbound": 38, "timeupperbound": 38, "initialguesstim": 38, "finalguesstim": 38, "numstatevar": 38, "statelowerbound": 38, "stateupperbound": 38, "initialguessst": 38, "finalguessst": 38, "numcontrolvar": 38, "controlupperbound": 38, "controllowerbound": 38, "setinitialguessmod": 38, "setinitialguessarrai": 38, "timearrai": 38, "statearrai": 38, "controlarrai": 38, "setnumstatevar": 38, "setnumcontrolvar": 38, "setmeshintervalfract": 38, "setmeshintervalnumpoint": 38, "setstatelowerbound": 38, "setstateupperbound": 38, "setstateinitialguess": 38, "setstatefinalguess": 38, "settimelowerbound": 38, "settimeupperbound": 38, "settimeinitialguess": 38, "settimefinalguess": 38, "setcontrollowerbound": 38, "setcontrolupperbound": 38, "traj": 38, "pathobject": 38, "pointobject": 38, "setuserpathfunct": 38, "setuserpointfunct": 38, "phaselist": 38, "setphaselist": 38, "solutionfil": 38, "doctestfil": 38, "writetofil": 38, "subfold": 38, "thirdparti": 38, "classdef": 38, "getstatevec": 38, "getcontrolvec": 38, "setdynfunct": 38, "w": 38, "setdynstatejac": 38, "setdyncontroljac": 38, "setdyntimejac": 38, "inittim": 38, "finaltim": 38, "initst": 38, "getinitialstatevec": 38, "finalst": 38, "getfinalstatevec": 38, "setcostfunct": 38, "setalgfunct": 38, "setalgfunclowerbound": 38, "setalgfuncupperbound": 38, "brachistichrone_driv": 38, "exec": 38, "x0": 38, "y0": 38, "v0": 38, "xf": 38, "yf": 38, "vf": 38, "1s": 38, "pathfunctionobject": 38, "pointfunctionobject": 38, "showplot": 38, "plotupdater": 38, "costlowerbound": 38, "inf": [38, 39], "costupperbound": 38, "maxmeshrefinementcount": 38, "info": 38, "statesol": 38, "getstatearrai": 38, "grid": 38, "lowthrustmatlabtestproblem": 38, "optimalcontrolguess": [38, 41], "customlinkageconstraint": [38, 41], "dynamicsconfigur": [38, 41], "chemicaltank": 38, "electrictank": 38, "uml": 38, "diamond": 38, "brief": 38, "whole": 38, "subsect": 38, "constrain": [38, 39], "scalar": 38, "integratedflybi": [38, 41], "patchedconiclaunch": [38, 41], "parser": 38, "30542": 38, "125291184": 38, "75613036": 38, "32788527": 38, "438": 38, "234": 38, "903": 38, "3930": 38, "250": 38, "30792": 38, "attach": [38, 39], "accord": [38, 39], "whenev": 38, "least": [38, 39], "themselv": 38, "neg": 38, "asat": 38, "chemtank": 38, "fuelmass": 38, "volum": 38, "fueldens": 38, "1260": 38, "deepspaceforcemodel": 38, "emtgthrustmodel": 38, "spacecraftfil": 38, "spacecraftmodel": 38, "emtg_spacecraftopt": [38, 39], "dutycycl": 38, "sunthrustdynconfig": 38, "emtgtankconfig": 38, "signific": 38, "quasi": 38, "random": 38, "trajectoryguess": 38, "collocationguessfil": [38, 39], "misc": 38, "guesswithunitycontrol": 38, "metadata": 38, "row": [38, 39], "eme2000": 38, "ignor": 38, "beyond": 38, "interpol": 38, "timespan": 38, "encompass": 38, "meta_start": 38, "central_bodi": 38, "ref_fram": 38, "time_system": 38, "num_stat": 38, "num_control": 38, "num_integr": 38, "meta_stop": 38, "data_start": 38, "30537": 38, "58130403295": 38, "120045407": 38, "6646147": 38, "84769659": 38, "07938783": 38, "36758301": 38, "62764073": 38, "68515738249333": 38, "26": 38, "23213523803535": 38, "71343366494688": 38, "3928": 38, "8979": 38, "68130403294": 38, "120166876": 38, "6672654": 38, "84550219": 38, "83076148": 38, "36666345": 38, "46750365": 38, "29149185537749": 38, "24": 38, "96927614272317": 38, "58473151780541": 38, "78130403294": 38, "120291284": 38, "2924708": 38, "84335930": 38, "88030888": 38, "36575177": 38, "55088668": 38, "47799877262803": 38, "68188855218928": 38, "5266647230637": 38, "data_stop": 38, "thephas": 38, "radaupseudospectr": [38, 39], "thrustmod": 38, "subphaseboundari": 38, "pointspersubphas": 38, "guesssourc": 38, "epochformat": 38, "epochlowerbound": 38, "30500": 38, "epochupperbound": 38, "31000": 38, "initialepoch": 38, "finalepoch": 38, "49598e": 38, "09": 38, "5000": 38, "initialcondit": 38, "constraintmod": 38, "initialphas": 38, "initialphaseboundarytyp": 38, "setmodelparamet": 38, "positionlowerbound": 38, "positionupperbound": 38, "velocitylowerbound": 38, "velocityupperbound": 38, "masslowerbound": 38, "massupperbound": 38, "past": 38, "duration_thrust": 38, "finalphas": 38, "finalphaseboundarytyp": 38, "elapseddai": 38, "builtincost": 38, "rmagfin": [38, 39], "significantli": 38, "statescalemod": 38, "canon": [38, 39], "massscalefactor": 38, "maxmeshrefinementiter": 38, "customlinkag": 38, "majoriterationslimit": 38, "totaliterationslimit": 38, "20000": [38, 39], "majoroptimalitytoler": 38, "optimizationmod": 38, "summari": 38, "violat": 38, "snippet": 38, "reformat": 38, "000000000000e": 38, "883374691010e": 38, "252911840000e": 38, "08": 38, "220179595724e": 38, "554290155413e": 38, "054200000000e": 38, "561303600000e": 38, "07": 38, "278852700000e": 38, "343800000000e": 38, "523400000000e": 38, "090300000000e": 38, "930000000000e": 38, "500000000000e": 38, "feasibl": [38, 39], "closest": 38, "variou": 38, "metric": 38, "whether": [38, 39], "infeas": [38, 39], "00000001": 38, "32788526": 38, "999997117": 38, "438000000000237": 38, "233999999999995": 38, "2065315270878699": 38, "89816159643679194": 38, "38813718716109147": 38, "30791": 38, "999999999993": 38, "167583548": 38, "9232918": 38, "75171810": 38, "936078712": 38, "32391739": 38, "681220464": 38, "6520747091844967": 38, "867812869758279": 38, "0315868085278339": 38, "3822": 38, "5243683003659": 38, "89871563615191197": 38, "40299676014008162": 38, "17294310281002576": 38, "tradit": 38, "phase2": [38, 39], "virtual": 38, "plist": 38, "setcostlowerbound": 38, "costlow": 38, "setcostupperbound": 38, "costupp": 38, "rapid": [38, 39], "coars": 38, "stringent": 38, "tight": 38, "wast": 38, "magnitud": [38, 39], "Then": 38, "zero": [38, 39], "setfeasibilitytoler": 38, "const": 38, "tol": 38, "feastol": 38, "mytraj": 38, "setfeasiblitytoler": 38, "snset": 38, "setoptimalitytoler": 38, "opttol": 38, "setmajoriterationslimit": 38, "iterlimit": 38, "snseti": 38, "10000": 38, "settotaliterationslimit": 38, "3000": 38, "setmaxmeshrefinementcount": 38, "tocount": 38, "choos": 38, "setoptimizationmod": 38, "stringarrai": [38, 39], "optmod": 38, "event": [38, 39], "allowfailedmeshoptim": 38, "setfailedmeshoptimizationallow": 38, "toallow": 38, "unconverg": 38, "unexpectedli": 38, "lastsolutionmostrecentmesh": [38, 39], "bestsolutionmostrecentmesh": [38, 39], "bestsolutionanymesh": [38, 39], "smallest": [38, 39], "regardless": [38, 39], "merit": [38, 39], "mostrecentmesh": 38, "anymesh": 38, "meshrefinementguessmod": 38, "setmeshrefinementguessmod": 38, "toguessmod": 38, "snoptoutputfil": 38, "snoptoptim": 38, "setoptimizeroutputfil": 38, "optfil": 38, "usernam": 38, "circumst": 38, "quiet": 38, "suppress": 38, "setsnoptconsoleoutputlevel": 38, "astronom": [38, 39], "gm": [38, 39], "42000": [38, 39], "period": 38, "TO": 38, "IN": 38, "dimension": [38, 39], "IS": 38, "BUT": 38, "keyword": [38, 39], "usephasecoordinatesystem": [38, 39], "path_and_filenam": 38, "extenst": 38, "outputcoordinatesystem": 38, "endpoint": 38, "addsimplelinkagechain": 38, "phase3": [38, 39], "sent": [38, 39], "publishupdater": 38, "userpathobject": 38, "func": 38, "mypathobject": 38, "thepathobject": 38, "userpointobject": 38, "prototpy": 38, "addfunct": 38, "funclist": 38, "myboundaryobject": 38, "theboundaryobject": 38, "gmattrajectori": 38, "setexecutioninterfac": 38, "csaltexecutioninterfac": 38, "intf": 38, "csaltinterfac": 38, "execinterfac": 38, "break": 38, "improv": 38, "sensit": 38, "poor": 38, "motion": 38, "multipli": 38, "maxcontrolmagnitud": 38, "homotopi": [38, 39], "troubleshoot": [38, 39], "critic": 38, "singular": 38, "pseudospectr": 38, "span": 38, "39": 38, "40": 38, "increas": [38, 39], "monoton": [38, 39], "subphas": [38, 39], "region": 38, "half": 38, "slow": [38, 39], "enough": 38, "maxrelativeerrortoler": 38, "fraction": 38, "setrelativeerrortol": 38, "implicitrungekutta": 38, "implicitrkphas": 38, "settranscript": 38, "rungekutta8": 38, "relat": 38, "settabl": 38, "seven": 38, "32402": 38, "32405": 38, "warn": [38, 39], "BE": 38, "WITH": 38, "FOR": 38, "OR": 38, "AND": [38, 39], "WILL": 38, "mincontrolmagnitud": 38, "numstaticvar": 38, "staticupperbound": 38, "staticlowerbound": 38, "setnumstaticvar": 38, "setstaticlowerbound": 38, "setstaticupperbound": 38, "easili": 38, "atd": 38, "malto": 38, "thephaseguess": 38, "date": 38, "34050": 38, "99962787928": 38, "34513": 38, "39999998195": 38, "2034": 38, "58": 38, "52": 38, "849": 38, "2035": 38, "21": 38, "35": 38, "998": 38, "linearunitycontrol": 38, "linearzerocontrol": 38, "straight": 38, "ones": 38, "linearli": 38, "021836090339203817": 38, "065059858061501996": 38, "11298609481175435": 38, "14731810202287049": 38, "15624006535589879": 38, "1780761556951026": 38, "22129992341740082": 38, "26922616016765311": 38, "30355816737876928": 38, "31248013071179759": 38, "setsiz": 38, "000558": 38, "0076368": 38, "70114": 38, "014536": 38, "065705": 38, "0561": 38, "072888": 38, "1842": 38, "4429": 38, "15442": 38, "2898": 38, "3183": 38, "18169": 38, "31831": 38, "5258": 38, "25921": 38, "38763": 38, "9943": 38, "4556": 38, "51198": 38, "7398": 38, "72747": 38, "607": 38, "2497": 38, "94294": 38, "63533": 38, "394": 38, "63662": 38, "4004": 38, "10977": 38, "32704": 38, "56797": 38, "74055": 38, "78541": 38, "89516": 38, "1125": 38, "3534": 38, "5259": 38, "5705": 38, "guessarrai": 38, "ochfil": 38, "initialguessfil": 38, "path_to_guess_fil": 38, "guess_file_nam": 38, "setguessfilenam": 38, "userguessfunct": 38, "thetrajectori": 38, "guessfunctionnam": 38, "myguessfunct": 38, "stateguess": 38, "controlguess": 38, "myguessfunc": 38, "timevector": 38, "timevectortyp": 38, "phaseobj": 38, "staticguess": 38, "setstaticguess": 38, "deepspacedynconfig": 38, "coast": [38, 39], "chemicaltank1": 38, "allownegativefuelmass": 38, "4150": 38, "1500": 38, "temperatur": 38, "reftemperatur": 38, "pressuremodel": 38, "pressureregul": 38, "thrustmodel": 38, "fixedthrustandisp_model1": 38, "color": [38, 39], "overridecoloringraph": 38, "drawn": 38, "green": 38, "orbitcolor": 38, "boundaryfunct": 38, "keep": 38, "builtinboundaryconstraint": 38, "artifici": 38, "exampledynamicsconfigur": 38, "examplephas": 38, "exampleforcemodel1": 38, "examplespacecraft1": 38, "exampleemtgspacecraft1": 38, "deplet": [38, 39], "amount": 38, "he": 38, "sum": [38, 39], "fueltank": 38, "examplechemtank1": 38, "fewer": 38, "gmatarrai": [38, 39], "suggest": 38, "column": [38, 39], "rx": 38, "ry": 38, "rz": 38, "ux": 38, "uy": 38, "uz": 38, "subscript": 38, "instant": 38, "earthicrfexampl": 38, "icrf": 38, "timesystem": 38, "tdbmodjulian": 38, "meta": 38, "evolutionari": [38, 40], "interplanetari": 38, "excerpt": 38, "exampleemtgspacecraftfil": 38, "uniqu": 38, "power": [38, 41], "emtgspacecraftstag": [38, 39], "spacecraftstag": 38, "delimit": 38, "unblock": 38, "thu": 38, "benefici": 38, "comma": 38, "next_tt11_discovery14_bol_20190523": 38, "contribut": 38, "appropri": [38, 40], "enumer": 38, "brysondenhampointobject": 38, "costfunct": 38, "algfunct": 38, "algfunclow": 38, "algfuncupp": 38, "granularli": 38, "bookkeep": 38, "overal": 38, "granular": 38, "particularli": 38, "hundr": 38, "branchboundconstraint": 38, "statedata": 38, "brachboundconstraint": 38, "algf": 38, "timedata": 38, "phasedepend": 38, "pointdepend": 38, "numphas": 38, "booleanarrai": 38, "statedepmap": 38, "controldepmap": 38, "timedepmap": 38, "paramdepmap": 38, "param": 38, "brachfunc": 38, "brachbound": 38, "setnumphas": 38, "setnumfunct": 38, "setphasedepend": 38, "setpointdepend": 38, "setstatedepmap": 38, "setcontroldepmap": 38, "settimedepmap": 38, "setparamdepmap": 38, "setupperbound": 38, "setlowerbound": 38, "functionlist": 38, "functionlb": 38, "functionub": 38, "setscalefactor": 38, "referenceepoch": 38, "timeunit": 38, "distunit": 38, "velunit": 38, "massunit": 38, "algebraicfunct": 38, "simplic": 38, "pleas": 38, "statefunc": 38, "fullstate1": 38, "statebound": 38, "refepoch": 38, "28437": 38, "tu": 38, "du": 38, "vu": 38, "mu": 38, "fullstatepointobject": 38, "brachistichronebound": 38, "brachistchronepointobject": 38, "funcnam": 38, "numpoint": 38, "resiz": 38, "evaluateanalyticjacobian": 38, "variabletyp": 38, "vartyp": 38, "pointidx": 38, "hasanalyticjac": 38, "jacarrai": 38, "recal": 38, "_i": 38, "_j": 38, "give": 38, "fullstatelinkag": 38, "validatepointidx": 38, "switch": 38, "time_1": 38, "time_0": 38, "state_element_j": 38, "firstphas": 38, "secondphas": 38, "atrajectori": 38, "subtract": 38, "v_y": 38, "thirdphas": 38, "fourthphas": 38, "quantiti": [38, 39], "phaseboundarytyp": 38, "launchphas": 38, "thrustphas": 38, "nif": 38, "acustomlinkag": 38, "23": 38, "111": 38, "unset": 38, "constraintnam": 38, "arg1": 38, "argn": 38, "1e7": 38, "1e2": 38, "1e4": 38, "parameternam": 38, "32060": 38, "32560": 38, "1957": 38, "2100": 38, "elapsedsecond": 38, "arrivalphas": 38, "rq36": 38, "cometrendezv": 38, "celestialbodyrendezv": [38, 41], "algebraicconstraint": [38, 39], "celestialbodi": 38, "preega1": 38, "postega1": 38, "earthgravassist": 38, "periapsisradiuslowerbound": 38, "6000": 38, "periapsisradiusupperbound": 38, "100000": 38, "pclaunch": 38, "vehiclenam": 38, "atlas_v_401": 38, "emtglaunchvehicleoptionsfil": 38, "emtg_launchvehicleopt": 38, "nlsii_august2018_aug": 38, "proceed": 38, "token": 38, "whitespac": 38, "c3": 38, "declin": 38, "asymptot": 38, "dla": 38, "0th": 38, "1st": 38, "boundcon": 38, "phase4": [38, 39], "bdott": 38, "scalefactor": [38, 39], "consolid": 38, "totalmassfin": [38, 39], "absoluteepochfin": [38, 39], "yvec": 38, "uvec": 38, "y2": 38, "y3": 38, "evaluatefuncjacobian": 38, "emtgdatapath": 38, "dynamicsmodel": [38, 39], "integrand": 38, "hypersenst": 38, "hypersensitivepathobject": 38, "ysquar": 38, "ycube": 38, "usquar": 38, "costf": 38, "coststatejac": 38, "costcontroljac": 38, "costtimejac": 38, "obstacleavoid": 38, "obstacleavoidancepathobject": 38, "138": 38, "theta": 38, "con1": 38, "con2": 38, "algstatejac": 38, "algcontroljac": 38, "algtimejac": 38, "pure": 38, "csaltstat": 38, "evaluatingmesh": 38, "reinitializingmesh": 38, "phaseindex": 38, "getcontrolarrai": 38, "gettimearrai": 38, "myexecutioninterfac": 38, "mypub": 38, "setpublish": 38, "myexecutioninterfaceb": 38, "footnot": 38, "scaleutil": 38, "guess": [39, 41], "mesh": [39, 41], "caution": 39, "dimensionless": 39, "sqp": 39, "qp": 39, "maxrelativeerrortol": 39, "tightli": 39, "optimz": 39, "mytrajectori": 39, "phase5": 39, "customlinkagecosntraint": 39, "oppos": 39, "trajectoryresourcenam": 39, "och": 39, "_min": 39, "_max": 39, "2250738585072014e": 39, "308": 39, "optmiz": 39, "independ": 39, "mimin": 39, "attain": 39, "3d": 39, "hermitesimpson": 39, "implicitrkorder4": 39, "implicitrkorder6": 39, "implicitrkorder8": 39, "unless": 39, "descipt": 39, "finer": 39, "implictrkord": 39, "irk": 39, "j2000eq": 39, "utcmodjulian": 39, "tdbgregorian": 39, "author": 39, "profil": 39, "colortyp": 39, "red": 39, "predefin": 39, "rgb": 39, "triplet": 39, "255": 39, "ith": 39, "emtg": [39, 40, 41, 42], "emtginterfac": 39, "arraynam": 39, "coordinatesystemresourc": 39, "tupl": 39, "emtgspacecraftfil": 39, "skip": 39, "trail": 39, "averag": 39, "duti": 39, "propuls": [39, 40, 41], "patchedconicflybi": 39, "celesti": 39, "patch": 39, "conic": 39, "characterist": 39, "rendezv": 39, "exactli": 39, "preceed": 39, "flybi": 39, "6978": 39, "periaps": 39, "1000000": 39, "toolkit": 40, "sloc": 40, "reus": 40, "loos": 40, "boost": 40, "arithmet": 40, "collabor": 40, "korea": 40, "aerospac": 40, "institut": 40, "kari": 40, "yonsei": 40, "apach": 40, "concept": [40, 41, 42], "incud": 40, "sparsiti": 41, "executioninterfac": 41, "emtgspacecraft": 41, "optimalcontrolfunct": 41}, "objects": {}, "objtypes": {}, "objnames": {}, "titleterms": {"bibliographi": 0, "chang": [1, 25], "histori": [1, 4], "gmat": [2, 3, 4, 7, 9, 11, 14, 15, 17, 18, 19, 20, 21, 23, 24, 25, 27, 32, 33, 34, 38, 41, 42], "api": [2, 3, 4, 6, 7, 9, 10, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 31, 32, 33, 38], "design": [2, 10, 12], "user": [2, 4, 15, 18, 20, 21, 38, 39], "s": [2, 19, 21], "guid": [2, 7, 14, 21, 38], "content": [2, 8, 12, 14, 21, 26, 30, 41, 42], "appendic": 2, "code": [3, 9], "guidelin": 3, "comment": 3, "help": [3, 9, 27], "method": [3, 9, 15], "prototyp": [4, 6, 8, 38], "A": [4, 35], "short": 4, "experi": 4, "open": 5, "issu": 5, "comparison": 6, "swig": 6, "product": 6, "time": [6, 29, 31, 32, 38], "system": [6, 12, 24, 25, 29, 31, 32, 38], "convers": [6, 29, 31, 32], "coordin": [6, 25, 29, 31, 32], "forc": [6, 24, 31, 32, 33, 38], "model": [6, 31, 32, 33, 38], "propag": [6, 24, 28, 31, 32, 33], "review": [7, 8], "respons": 7, "rfa": 7, "01": 7, "unsaf": 7, "c": [7, 38], "request": 7, "action": 7, "support": [7, 9], "rational": 7, "02": 7, "python": [7, 16, 17, 18, 19], "version": 7, "compat": 7, "03": 7, "handl": 7, "persist": 7, "object": [7, 9, 15, 16, 17, 24, 27, 38], "memori": 7, "when": 7, "us": [7, 15, 18, 20, 38], "04": 7, "complex": [7, 17], "creat": [7, 27, 38], "interfac": [7, 17, 38, 39], "05": 7, "renam": 7, "simplifi": 7, "setup": [7, 33], "command": [7, 22, 23, 27, 28, 38], "06": 7, "style": 7, "To": [8, 38], "Be": 8, "remov": 8, "inform": 8, "updat": [9, 38], "setfield": 9, "getfield": 9, "The": [9, 10, 11, 17, 18, 19, 33, 35], "field": [9, 24, 27, 39], "set": [9, 19, 22, 32, 38], "exampl": [9, 18, 20, 23, 30, 31, 32, 34, 38], "spacecraft": [9, 24, 25, 33, 36, 38, 39], "name": [9, 39], "sat": 9, "access": [9, 17, 19, 23, 27, 33], "function": [9, 15, 18, 23, 38, 39], "helper": 9, "architectur": 11, "compon": [11, 17, 19, 33, 38], "introduct": 13, "refer": [14, 38], "plan": 14, "gener": [15, 16, 20], "purpos": 15, "control": [15, 23, 35, 38, 41], "ad": 15, "best": 16, "practic": 16, "understand": 16, "ownership": 16, "java": [16, 17, 19, 31], "matlab": [16, 17, 18, 19, 38], "usag": [17, 18, 19, 30], "overview": [17, 38, 40], "archiv": 17, "script": [17, 18, 27, 32, 38], "driver": 17, "high": 17, "level": 17, "run": [18, 19, 27, 33, 38], "background": 18, "process": [18, 38], "drive": 18, "from": [18, 19, 27], "sampl": 18, "mission": 18, "up": [19, 22, 38], "instal": [19, 37], "direct": 19, "outsid": 19, "folder": 19, "file": [19, 36, 38, 39], "locat": 19, "extern": 19, "load": [19, 27], "packag": 19, "built": [19, 38], "initi": [19, 22, 24, 33, 38], "convent": 20, "case": [20, 32], "workspac": 20, "typographi": 20, "configur": [22, 24, 25, 33, 38], "categori": 22, "execut": [22, 23], "mc": 23, "sequenc": 23, "prepar": [24, 25], "environ": [24, 25], "add": 24, "potenti": 24, "other": 24, "integr": [24, 38], "connect": [24, 27, 33], "togeth": 24, "step": [24, 38], "state": [25, 33, 38], "manag": 25, "notebook": 26, "walkthrough": 26, "cheat": 27, "sheet": 27, "ask": 27, "list": 27, "avail": 27, "class": [27, 38], "creation": 27, "save": 27, "data": [27, 38], "after": 27, "target": 28, "1": [32, 36, 38], "2": [32, 36, 38], "3": [32, 36, 38], "4": [32, 36, 38], "5": [32, 36, 38], "work": 32, "tutori": [33, 38], "navig": 33, "featur": 33, "verifi": 33, "get": 33, "start": 33, "construct": 33, "addit": [33, 38], "paramet": 33, "basic": 33, "test": 33, "exercis": 33, "final": 33, "plug": 33, "modul": 33, "wrap": 33, "plugin": 33, "unwrap": 33, "measur": 33, "ground": 33, "station": 33, "hardwar": 33, "error": 33, "mont": 34, "interoper": 34, "ephemeri": 34, "share": 34, "maneuv": 34, "covari": 34, "dynam": [34, 38], "concept": 35, "algorithm": 35, "optim": [35, 38, 41], "problem": [35, 38], "csalt": [35, 38], "terminolog": 35, "deriv": 35, "sparsiti": 35, "determin": 35, "mesh": [35, 38], "refin": [35, 38], "note": 35, "emtg": [36, 38], "specif": [36, 39], "block": 36, "line": [36, 38], "entri": 36, "6": [36, 38], "7": 36, "8": 36, "stage": [36, 38], "9": 36, "10": 36, "11": 36, "12": 36, "13": 36, "14": 36, "power": 36, "librari": 36, "15": 36, "16": 36, "17": 36, "propuls": [36, 38], "18": 36, "19": 36, "20": 36, "21": 36, "22": 36, "23": 36, "24": 36, "25": 36, "26": 36, "27": 36, "softwar": 37, "organ": 37, "compil": 37, "window": 37, "mac": 37, "linux": 37, "an": 38, "path": 38, "boundari": 38, "phase": [38, 39], "transcript": 38, "trajectori": [38, 39], "examin": 38, "solut": 38, "resourc": 38, "orbit": 38, "thruster": 38, "emtgspacecraft": [38, 39], "guess": 38, "condit": 38, "durat": 38, "constraint": 38, "cost": 38, "bound": 38, "converg": 38, "toler": 38, "iter": 38, "limit": 38, "mode": 38, "snopt": 38, "output": 38, "print": 38, "consol": 38, "verbos": 38, "scale": 38, "simpl": 38, "linkag": 38, "sourc": 38, "publish": 38, "rate": 38, "executioninterfac": 38, "represent": 38, "decis": 38, "vector": 38, "dimens": 38, "independ": 38, "variabl": 38, "linear": 38, "arrai": 38, "och": 38, "static": 38, "graphic": 38, "finit": 38, "burn": 38, "tank": 38, "duti": 38, "cycl": 38, "format": 38, "throttl": 38, "tabl": 38, "via": 38, "userpointfunct": 38, "optimalcontrolfunct": [38, 39], "full": 38, "custom": 38, "differ": 38, "absolut": 38, "defin": 38, "type": [38, 39], "celesti": 38, "bodi": [38, 39], "rendezv": 38, "flybi": 38, "patch": 38, "conic": 38, "launch": [38, 39], "In": 38, "maximum": 38, "mass": 38, "rmag": 38, "fuel": 38, "algebra": 38, "guesssourc": 39, "majoroptimalitytoler": 39, "majoriterationslimit": 39, "totaliterationslimit": 39, "feasibilitytoler": 39, "maxmeshrefinementiter": 39, "phaselist": 39, "snoptoutputfil": 39, "addsimplelinkagechain": 39, "customlinkag": 39, "outputcoordinatesystem": 39, "solutionfil": 39, "allowfailedmeshoptim": 39, "statescalemod": 39, "massscalefactor": 39, "addboundaryfunct": 39, "publishupdater": 39, "optimizationmod": 39, "meshrefinementguessmod": 39, "statedimens": 39, "controldimens": 39, "thrustmod": 39, "maxrelativeerrortoler": 39, "subphaseboundari": 39, "pointspersubphas": 39, "statelowerbound": 39, "stateupperbound": 39, "controllowerbound": 39, "controlupperbound": 39, "epochformat": 39, "initialepoch": 39, "finalepoch": 39, "epochlowerbound": 39, "epochupperbound": 39, "dynamicsconfigur": 39, "maxcontrolmagnitud": 39, "mincontrolmagnitud": 39, "overridecoloringraph": 39, "builtincost": 39, "builtinboundaryconstraint": 39, "orbitcolor": 39, "forcemodel": 39, "finiteburn": 39, "impulsiveburn": 39, "emtgtankconfig": 39, "optimalcontrolguess": 39, "timesystem": 39, "coordinatesystem": 39, "guessarrai": 39, "filenam": 39, "customlinkageconstraint": 39, "constraintmod": 39, "initialphas": 39, "initialphaseboundarytyp": 39, "finalphas": 39, "finalphaseboundarytyp": 39, "setmodelparamet": 39, "spacecraftfil": 39, "spacecraftstag": 39, "dutycycl": 39, "express": 39, "patchedconiclaunch": 39, "centralbodi": 39, "emtglaunchvehicleoptionsfil": 39, "vehicl": 39, "option": 39, "vehiclenam": 39, "celestialbodyrendezv": 39, "celestialbodi": 39, "integratedflybi": 39, "periapsisradiuslowerbound": 39, "valu": 39, "periapsisradiusupperbound": 39, "tool": 42, "extens": 42}, "envversion": {"sphinx.domains.c": 2, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 6, "sphinx.domains.index": 1, "sphinx.domains.javascript": 2, "sphinx.domains.math": 2, "sphinx.domains.python": 3, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.todo": 2, "sphinx": 56}})