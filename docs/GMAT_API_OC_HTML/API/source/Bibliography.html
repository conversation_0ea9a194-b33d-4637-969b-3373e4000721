
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Bibliography &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Change History" href="ChangeHistory.html" />
    <link rel="prev" title="Propagation with the GMAT API" href="userguide/notebooks/GMAT_Propagation.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="ChangeHistory.html" title="Change History"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="userguide/notebooks/GMAT_Propagation.html" title="Propagation with the GMAT API"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Bibliography</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="bibliography">
<h1>Bibliography<a class="headerlink" href="#bibliography" title="Permalink to this heading">¶</a></h1>
<blockquote id="gmatwiki">
<div><p><strong>[GmatWiki]</strong> <a class="reference external" href="http://gmatcentral.org">gmatcentral.org</a></p>
<p>The GMAT Wiki at gmatcentral.org is the main public facing interface for GMAT
development and release activities.</p>
</div></blockquote>
<blockquote id="archspec">
<div><p><strong>[Architecture]</strong>  The GMAT Development Team, “General Mission Analysis Tool (GMAT) Architectural Specification,” NASA GSFC.</p>
<p>The GMAT Architectural Specification provides a good overview for the GMAT
system.  The document is included with each GMAT release.  The document overview can be viewed at
<a class="reference external" href="http://gmatcentral.org:8090/display/GW/Architectural+Specification">http://gmatcentral.org:8090/display/GW/Architectural+Specification</a>.</p>
</div></blockquote>
<blockquote id="cinterfaceapi">
<div><p><strong>[CInterface]</strong> D. Conway, “GMAT API Tradeoff Study,” Thinking Systems, Inc., February 2012.</p>
<p>The GMAT CInterface plugin was an artifact of the original GMAT API study
described here. One recommendation arising from this study was a set of
automatic API generation tools, including SWIG.</p>
</div></blockquote>
<blockquote id="prototypeapi">
<div><p><strong>[SWIGExperiment]</strong> D. Conway, “GMAT API Consultation Support,” Thinking Systems, Inc., December 2016.</p>
<p>Goddard personnel, assisted by contractors at Thinking Systems, Inc. and
Emergent Space Technologies, performed an in-house study of the use of SWIG
as a tool for a production GMAT API.  This document describes that study.</p>
</div></blockquote>
<blockquote id="swig">
<div><p><strong>[SWIG]</strong> <a class="reference external" href="http://www.swig.org/">Simplified Wrapper and Interface Generator (SWIG)</a></p>
<p>SWIG is an open source software development tool that connects programs
written in C and C++ with other high-level programming languages.  The GMAT
API is generated using the SWIG tool.</p>
</div></blockquote>
<blockquote id="doxygen">
<div><p><strong>[Doxygen]</strong> <a class="reference external" href="http://www.doxygen.nl/">www.doxygen.nl</a></p>
<p>The detailed design information for GMAT is generated using the open source
Doxygen documentation generation tool.</p>
</div></blockquote>
<blockquote id="jupyter">
<div><p><strong>[Jupyter]</strong> <a class="reference external" href="http://www.jupyter.org/">www.jupyter.org</a></p>
<p>Jupyter is an interactive tool that provides writers with the ability to
intersperse documentation and Python code.  The resulting notebook files can
be run interactively, enriching the user’s learning experience through
editable Python code that can be executed inside the notebook.</p>
</div></blockquote>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="userguide/notebooks/GMAT_Propagation.html"
                          title="previous chapter">Propagation with the GMAT API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ChangeHistory.html"
                          title="next chapter">Change History</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/API/source/Bibliography.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="ChangeHistory.html" title="Change History"
             >next</a> |</li>
        <li class="right" >
          <a href="userguide/notebooks/GMAT_Propagation.html" title="Propagation with the GMAT API"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Bibliography</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>