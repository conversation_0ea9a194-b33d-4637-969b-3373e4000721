
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Introduction &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="System Design" href="../design/Design.html" />
    <link rel="prev" title="GMAT API Design and User’s Guide" href="../apiIndex.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../design/Design.html" title="System Design"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../apiIndex.html" title="GMAT API Design and User’s Guide"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Introduction</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="introduction">
<h1>Introduction<a class="headerlink" href="#introduction" title="Permalink to this heading">¶</a></h1>
<p>The General Mission Analysis Tool, GMAT, is a general purpose spacecraft mission
design, analysis, and operations tool.  GMAT is in active development, and has
been used for multiple spacecraft missions.  GMAT is the operational tool for
maneuver design and navigation on several missions in the Goddard Space Flight
Center’s Flight Dynamics Facility.  It is used for mission design at GSFC and at
numerous other organizations throughout the world.  GMAT is a free and open
source tool, available at the GMAT wiki <a class="reference internal" href="../Bibliography.html#gmatwiki"><span class="std std-ref">[GmatWiki]</span></a>.</p>
<p>Core capabilities of GMAT can be accessed using an Application Programming
Interface (API).  This document describes the GMAT API, and includes sample
usage from Python and MATLAB using Java.</p>
<p>GMAT is coded using an object oriented approach documented in the GMAT
Architectural Specification <a class="reference internal" href="../Bibliography.html#archspec"><span class="std std-ref">[Architecture]</span></a>.  The system has
been under development since 2002.  Users interact with GMAT through a
spacecraft domain specific language built into the system, modeled on the MATLAB
programming language.  The GMAT API opens the system’s object model to users
that want to interact directly with the core system components, outside of the
scripted interfaces used when running the application.</p>
<p>The materials presented in this document are divided into two sections:</p>
<ul class="simple">
<li><p>The first section documents the design of the API.  In it, you will find an
extremely high level overview of the GMAT code and a matching API overview,
a discussion of the philosophy governing the API that includes use cases, and a
description of the additions to the GMAT code base added for the API that enable
the examples and features requested by the user community.</p></li>
<li><p>The second section is a user’s guide for the GMAT API.  It contains instructions
for installing the API code, a “Getting Started” tutorial for initial use of the
API, and additional hints, tips, and use case descriptions designed to help you
start using the GMAT API.</p></li>
</ul>
<p>This documentation concludes with appendices that provide additional API
guidelines, review notes, and other information for developers and API users.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../apiIndex.html"
                          title="previous chapter">GMAT API Design and User’s Guide</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../design/Design.html"
                          title="next chapter">System Design</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/intro/Overview.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../design/Design.html" title="System Design"
             >next</a> |</li>
        <li class="right" >
          <a href="../apiIndex.html" title="GMAT API Design and User’s Guide"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Introduction</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>