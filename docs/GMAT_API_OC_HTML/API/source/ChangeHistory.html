
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Change History &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="GMAT Optimal Control" href="../../CSALT/source/csaltIndex.html" />
    <link rel="prev" title="Bibliography" href="Bibliography.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../../CSALT/source/csaltIndex.html" title="GMAT Optimal Control"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Bibliography.html" title="Bibliography"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Change History</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="change-history">
<h1>Change History<a class="headerlink" href="#change-history" title="Permalink to this heading">¶</a></h1>
<table class="docutils align-default">
<colgroup>
<col style="width: 10%" />
<col style="width: 30%" />
<col style="width: 60%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p><strong>Rev 0</strong></p></td>
<td><p>December 05, 2018</p></td>
<td><p>Original draft specification.</p></td>
</tr>
<tr class="row-even"><td><p><strong>Rev 1</strong></p></td>
<td><p>March 29, 2019</p></td>
<td><p>Draft specification incorporating review comments.</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Rev 2</strong></p></td>
<td><p>October 11, 2019</p></td>
<td><p>Updates for the first beta release.</p></td>
</tr>
<tr class="row-even"><td><p><strong>Rev 3</strong></p></td>
<td><p>January 15, 2020</p></td>
<td><p>Updates for GMAT R2020a.</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Rev 4</strong></p></td>
<td><p>September 27, 2022</p></td>
<td><p>Updates for GMAT R2022a.</p></td>
</tr>
<tr class="row-even"><td><p><strong>Rev 5</strong></p></td>
<td><p>June 30, 2023</p></td>
<td><p>Updates for command functionality.</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Rev 6</strong></p></td>
<td><p>March 11, 2025</p></td>
<td><p>Recompiled for GMAT R2025a</p></td>
</tr>
</tbody>
</table>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Bibliography.html"
                          title="previous chapter">Bibliography</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../../CSALT/source/csaltIndex.html"
                          title="next chapter">GMAT Optimal Control</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/API/source/ChangeHistory.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../../CSALT/source/csaltIndex.html" title="GMAT Optimal Control"
             >next</a> |</li>
        <li class="right" >
          <a href="Bibliography.html" title="Bibliography"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Change History</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>