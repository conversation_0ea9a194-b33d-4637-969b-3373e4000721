
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Time and Coordinate Conversions &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="API Examples" href="PythonExamples.html" />
    <link rel="prev" title="Usage Examples" href="Examples.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="PythonExamples.html" title="API Examples"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Examples.html" title="Usage Examples"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" accesskey="U">Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Time and Coordinate Conversions</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="time-and-coordinate-conversions">
<h1>Time and Coordinate Conversions<a class="headerlink" href="#time-and-coordinate-conversions" title="Permalink to this heading">¶</a></h1>
<p>One simple application of the GMAT API is the construction of routines that
convert from one systm to another.  Two examples of that application are
presented here: time system conversion and coordinate system conversion.</p>
<section id="time-system-conversion">
<h2>Time System Conversion<a class="headerlink" href="#time-system-conversion" title="Permalink to this heading">¶</a></h2>
</section>
<section id="coordinate-system-conversion">
<h2>Coordinate System Conversion<a class="headerlink" href="#coordinate-system-conversion" title="Permalink to this heading">¶</a></h2>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">gmatpy</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">moonec</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;CoordinateSystem&quot;</span><span class="p">,</span> <span class="s2">&quot;MoonEc&quot;</span><span class="p">,</span> <span class="s2">&quot;Luna&quot;</span><span class="p">,</span> <span class="s2">&quot;MJ2000Ec&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">eartheq</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;CoordinateSystem&quot;</span><span class="p">,</span> <span class="s2">&quot;EarthEq&quot;</span><span class="p">,</span> <span class="s2">&quot;Earth&quot;</span><span class="p">,</span> <span class="s2">&quot;MJ2000Eq&quot;</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">outstate</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="mf">4000.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="mf">1000.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span> <span class="o">=</span> <span class="mf">0.4</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">instate</span><span class="p">[</span><span class="mi">5</span><span class="p">]</span> <span class="o">=</span> <span class="mf">3.1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">instate</span><span class="p">)</span>
<span class="go">4000          1000          0             0.1           0.4           3.1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cconverter</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateConverter</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">cconverter</span><span class="o">.</span><span class="n">Convert</span><span class="p">(</span><span class="mi">28718</span><span class="p">,</span> <span class="n">instate</span><span class="p">,</span> <span class="n">moonec</span><span class="p">,</span> <span class="n">outstate</span><span class="p">,</span> <span class="n">eartheq</span><span class="p">)</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">outstate</span><span class="p">)</span>
<span class="go">278537.716 272130.548 84187.769 -0.64373770 -0.28543905 3.30709590</span>
</pre></div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Time and Coordinate Conversions</a><ul>
<li><a class="reference internal" href="#time-system-conversion">Time System Conversion</a></li>
<li><a class="reference internal" href="#coordinate-system-conversion">Coordinate System Conversion</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Examples.html"
                          title="previous chapter">Usage Examples</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="PythonExamples.html"
                          title="next chapter">API Examples</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/Conversion.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="PythonExamples.html" title="API Examples"
             >next</a> |</li>
        <li class="right" >
          <a href="Examples.html" title="Usage Examples"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" >Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Time and Coordinate Conversions</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>