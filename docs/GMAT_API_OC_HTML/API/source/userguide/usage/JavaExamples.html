
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>API Examples in Java &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="Propagation Command" href="CommandsExample.html" />
    <link rel="prev" title="API Examples" href="PythonExamples.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="CommandsExample.html" title="Propagation Command"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="PythonExamples.html" title="API Examples"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" accesskey="U">Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Examples in Java</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="api-examples-in-java">
<span id="javaexamples"></span><h1>API Examples in Java<a class="headerlink" href="#api-examples-in-java" title="Permalink to this heading">¶</a></h1>
<p><span class="xref std std-ref">DesignExamples</span> shows Python scripting for four common GMAT API use cases.
This section shows those same use cases in Java.</p>
<section id="time-system-conversion">
<h2>Time System Conversion<a class="headerlink" href="#time-system-conversion" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-number">Listing 15 </span><span class="caption-text">Time System Conversion in Java</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span><span class="w"> </span><span class="nn">gmat.*</span><span class="p">;</span><span class="w"></span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">TimeConvNew</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="n">String</span><span class="o">[]</span><span class="w"> </span><span class="n">args</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="w">        </span><span class="c1">// Get the converter</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">        </span><span class="n">TimeSystemConverter</span><span class="w"> </span><span class="n">timeConverter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">theTimeSystemConverter</span><span class="p">;</span><span class="w"></span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="w">        </span><span class="c1">// Convert an epoch</span><span class="w"></span>
<span class="linenos">11</span><span class="w">        </span><span class="kt">double</span><span class="w"> </span><span class="n">UTCepoch</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">21738.22145</span><span class="p">;</span><span class="w"></span>
<span class="linenos">12</span><span class="w">        </span><span class="kt">double</span><span class="w"> </span><span class="n">TAIepoch</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeConverter</span><span class="p">.</span><span class="na">Convert</span><span class="p">(</span><span class="n">UTCepoch</span><span class="p">,</span><span class="w"> </span><span class="n">UTC</span><span class="p">,</span><span class="w"> </span><span class="n">TAI</span><span class="p">);</span><span class="w"></span>
<span class="linenos">13</span><span class="w">  </span><span class="p">}</span><span class="w"></span>
<span class="linenos">14</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
<section id="coordinate-system-conversion">
<h2>Coordinate System Conversion<a class="headerlink" href="#coordinate-system-conversion" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-number">Listing 16 </span><span class="caption-text">Coordinate Conversion in Java</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span><span class="w"> </span><span class="nn">gmat.*</span><span class="p">;</span><span class="w"></span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">CoordConvNew</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="n">String</span><span class="o">[]</span><span class="w"> </span><span class="n">args</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="w">        </span><span class="c1">// Initialize GMAT</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">        </span><span class="n">gmat</span><span class="p">.</span><span class="na">Setup</span><span class="p">(</span><span class="s">&quot;MyCustomStartupFile.txt&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="w">        </span><span class="c1">// Setup the GMAT data structures for the conversion</span><span class="w"></span>
<span class="linenos">11</span><span class="w">        </span><span class="n">A1Mjd</span><span class="w"> </span><span class="n">mjd</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">A1Mjd</span><span class="p">(</span><span class="mf">22326.977184</span><span class="p">);</span><span class="w"></span>
<span class="linenos">12</span><span class="w">        </span><span class="n">Rvector6</span><span class="w"> </span><span class="n">rvIn</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">6988.427</span><span class="p">,</span><span class="w"> </span><span class="mf">1073.884</span><span class="p">,</span><span class="w"> </span><span class="mf">2247.333</span><span class="p">,</span><span class="w"> </span><span class="mf">0.019982</span><span class="p">,</span><span class="w"> </span><span class="mf">7.226988</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mf">1.554962</span><span class="p">);</span><span class="w"></span>
<span class="linenos">13</span><span class="w">        </span><span class="n">Rvector6</span><span class="w"> </span><span class="n">rvOut</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="linenos">14</span>
<span class="linenos">15</span><span class="w">        </span><span class="c1">// Create the converter</span><span class="w"></span>
<span class="linenos">16</span><span class="w">        </span><span class="n">CoordinateConverter</span><span class="w"> </span><span class="n">csConverter</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">CoordinateConverter</span><span class="p">();</span><span class="w"></span>
<span class="linenos">17</span>
<span class="linenos">18</span><span class="w">        </span><span class="c1">// Create the input and output coordinate systems</span><span class="w"></span>
<span class="linenos">19</span><span class="w">        </span><span class="n">CoordinateSystem</span><span class="w"> </span><span class="n">eci</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;CoordinateSystem&quot;</span><span class="p">,</span><span class="w"></span>
<span class="linenos">20</span><span class="w">                </span><span class="s">&quot;ECI&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Earth&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;MJ2000Eq&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">21</span><span class="w">        </span><span class="n">CoordinateSystem</span><span class="w"> </span><span class="n">ecef</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;CoordinateSystem&quot;</span><span class="p">,</span><span class="w"></span>
<span class="linenos">22</span><span class="w">                </span><span class="s">&quot;ECEF&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Earth&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;BodyFixed&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">23</span>
<span class="linenos">24</span><span class="w">        </span><span class="n">csConverter</span><span class="p">.</span><span class="na">Convert</span><span class="p">(</span><span class="n">mjd</span><span class="p">,</span><span class="w"> </span><span class="n">rvIn</span><span class="p">,</span><span class="w"> </span><span class="n">eci</span><span class="p">,</span><span class="w"> </span><span class="n">rvOut</span><span class="p">,</span><span class="w"> </span><span class="n">ecef</span><span class="p">);</span><span class="w"></span>
<span class="linenos">25</span><span class="w">  </span><span class="p">}</span><span class="w"></span>
<span class="linenos">26</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
<section id="force-modeling">
<h2>Force Modeling<a class="headerlink" href="#force-modeling" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id3">
<div class="code-block-caption"><span class="caption-number">Listing 17 </span><span class="caption-text">Force Model Creation and Use in Java</span><a class="headerlink" href="#id3" title="Permalink to this code">¶</a></div>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span><span class="w"> </span><span class="nn">gmat.*</span><span class="p">;</span><span class="w"></span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">ForceModelNew</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="w">  </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="n">String</span><span class="o">[]</span><span class="w"> </span><span class="n">args</span><span class="p">)</span><span class="w"></span>
<span class="linenos"> 6</span><span class="w">  </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 7</span><span class="w">    </span><span class="n">ODEModel</span><span class="w"> </span><span class="n">dynamics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;ODEModel&quot;</span><span class="p">,</span><span class="s">&quot;FM&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos"> 8</span>
<span class="linenos"> 9</span><span class="w">    </span><span class="n">PointMassForce</span><span class="w"> </span><span class="n">epm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;PointMassForce&quot;</span><span class="p">,</span><span class="s">&quot;EPM&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">10</span><span class="w">    </span><span class="n">dynamics</span><span class="p">.</span><span class="na">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">);</span><span class="w"></span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="w">    </span><span class="n">gmat</span><span class="p">.</span><span class="na">Initialize</span><span class="p">();</span><span class="w"></span>
<span class="linenos">13</span>
<span class="linenos">14</span><span class="w">    </span><span class="n">dynamics</span><span class="p">.</span><span class="na">GetDerivatives</span><span class="p">(</span><span class="n">state</span><span class="p">,</span><span class="w"> </span><span class="n">dt</span><span class="p">);</span><span class="w"></span>
<span class="linenos">15</span><span class="w">    </span><span class="kt">double</span><span class="o">[]</span><span class="w"> </span><span class="n">derivatives</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">dynamics</span><span class="p">.</span><span class="na">GetDerivativeArray</span><span class="p">();</span><span class="w"></span>
<span class="linenos">16</span><span class="w">  </span><span class="p">}</span><span class="w"></span>
<span class="linenos">17</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
<section id="propagation">
<h2>Propagation<a class="headerlink" href="#propagation" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id4">
<div class="code-block-caption"><span class="caption-number">Listing 18 </span><span class="caption-text">Propagation in Java</span><a class="headerlink" href="#id4" title="Permalink to this code">¶</a></div>
<div class="highlight-java notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span><span class="w"> </span><span class="nn">gmat.*</span><span class="p">;</span><span class="w"></span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="kd">public</span><span class="w"> </span><span class="kd">class</span> <span class="nc">PropExampleNew</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="w">    </span><span class="kd">public</span><span class="w"> </span><span class="kd">static</span><span class="w"> </span><span class="kt">void</span><span class="w"> </span><span class="nf">main</span><span class="p">(</span><span class="n">String</span><span class="o">[]</span><span class="w"> </span><span class="n">args</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="w">        </span><span class="c1">// Setup the state for propagation</span><span class="w"></span>
<span class="linenos"> 8</span><span class="w">        </span><span class="kt">double</span><span class="o">[]</span><span class="w"> </span><span class="n">state</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="mf">7000.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1000.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">8.0</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mf">0.25</span><span class="p">};</span><span class="w"></span>
<span class="linenos"> 9</span><span class="w">        </span><span class="c1">// Setup a Earth/Sun/Moon force model</span><span class="w"></span>
<span class="linenos">10</span><span class="w">        </span><span class="c1">// note: Use Moderator for the forces and Python memory management won&#39;t seg fault</span><span class="w"></span>
<span class="linenos">11</span><span class="w">        </span><span class="n">ODEModel</span><span class="w"> </span><span class="n">dynamics</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;ODEModel&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Forces&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">12</span>
<span class="linenos">13</span><span class="w">        </span><span class="n">PhysicalModel</span><span class="w"> </span><span class="n">epm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;PointMassForce&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;EarthPointMass&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">14</span><span class="w">        </span><span class="n">PhysicalModel</span><span class="w"> </span><span class="n">spm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;PointMassForce&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;SunPointMass&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">15</span><span class="w">        </span><span class="n">PhysicalModel</span><span class="w"> </span><span class="n">mpm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gmat</span><span class="p">.</span><span class="na">Construct</span><span class="p">(</span><span class="s">&quot;PointMassForce&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;MoonPointMass&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">16</span><span class="w">        </span><span class="n">spm</span><span class="p">.</span><span class="na">SetStringParameter</span><span class="p">(</span><span class="s">&quot;BodyName&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Sun&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">17</span><span class="w">        </span><span class="n">mpm</span><span class="p">.</span><span class="na">SetStringParameter</span><span class="p">(</span><span class="s">&quot;BodyName&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Luna&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">18</span>
<span class="linenos">19</span><span class="w">        </span><span class="n">dynamics</span><span class="p">.</span><span class="na">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">);</span><span class="w"></span>
<span class="linenos">20</span><span class="w">        </span><span class="n">dynamics</span><span class="p">.</span><span class="na">AddForce</span><span class="p">(</span><span class="n">spm</span><span class="p">);</span><span class="w"></span>
<span class="linenos">21</span><span class="w">        </span><span class="n">dynamics</span><span class="p">.</span><span class="na">AddForce</span><span class="p">(</span><span class="n">mpm</span><span class="p">);</span><span class="w"></span>
<span class="linenos">22</span>
<span class="linenos">23</span><span class="w">        </span><span class="c1">// Propagator configuration</span><span class="w"></span>
<span class="linenos">24</span><span class="w">        </span><span class="n">PrinceDormand78</span><span class="w"> </span><span class="n">prop</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">PrinceDormand78</span><span class="p">(</span><span class="s">&quot;Propagator&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">25</span><span class="w">        </span><span class="n">prop</span><span class="p">.</span><span class="na">SetPhysicalModel</span><span class="p">(</span><span class="n">dynamics</span><span class="p">);</span><span class="w"></span>
<span class="linenos">26</span>
<span class="linenos">27</span><span class="w">        </span><span class="n">gmat</span><span class="p">.</span><span class="na">Initialize</span><span class="p">();</span><span class="w"></span>
<span class="linenos">28</span>
<span class="linenos">29</span><span class="w">        </span><span class="c1">// Set the propagation state</span><span class="w"></span>
<span class="linenos">30</span><span class="w">        </span><span class="n">dynamics</span><span class="p">.</span><span class="na">SetState</span><span class="p">(</span><span class="n">state</span><span class="p">);</span><span class="w"></span>
<span class="linenos">31</span>
<span class="linenos">32</span><span class="w">        </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kt">int</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="o">++</span><span class="p">)</span><span class="w"> </span><span class="p">{</span><span class="w"></span>
<span class="linenos">33</span><span class="w">            </span><span class="n">prop</span><span class="p">.</span><span class="na">Step</span><span class="p">(</span><span class="mf">60.0</span><span class="p">);</span><span class="w"></span>
<span class="linenos">34</span><span class="w">        </span><span class="p">}</span><span class="w"></span>
<span class="linenos">35</span><span class="w">  </span><span class="p">}</span><span class="w"></span>
<span class="linenos">36</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">API Examples in Java</a><ul>
<li><a class="reference internal" href="#time-system-conversion">Time System Conversion</a></li>
<li><a class="reference internal" href="#coordinate-system-conversion">Coordinate System Conversion</a></li>
<li><a class="reference internal" href="#force-modeling">Force Modeling</a></li>
<li><a class="reference internal" href="#propagation">Propagation</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="PythonExamples.html"
                          title="previous chapter">API Examples</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="CommandsExample.html"
                          title="next chapter">Propagation Command</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/JavaExamples.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="CommandsExample.html" title="Propagation Command"
             >next</a> |</li>
        <li class="right" >
          <a href="PythonExamples.html" title="API Examples"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" >Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Examples in Java</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>