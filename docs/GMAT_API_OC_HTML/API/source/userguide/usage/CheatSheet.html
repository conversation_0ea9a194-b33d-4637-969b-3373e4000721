
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>GMAT API Cheat Sheet &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="API Notebook Walkthroughs" href="../notebooks/notebooks.html" />
    <link rel="prev" title="API Best Practices" href="../BestPractices.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../notebooks/notebooks.html" title="API Notebook Walkthroughs"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../BestPractices.html" title="API Best Practices"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT API Cheat Sheet</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="gmat-api-cheat-sheet">
<h1>GMAT API Cheat Sheet<a class="headerlink" href="#gmat-api-cheat-sheet" title="Permalink to this heading">¶</a></h1>
<section id="loading-the-api">
<h2>Loading the API<a class="headerlink" href="#loading-the-api" title="Permalink to this heading">¶</a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>import gmatpy as gmat</p></td>
<td><p>load_gmat</p></td>
</tr>
</tbody>
</table>
</section>
<section id="asking-for-help">
<h2>Asking for Help<a class="headerlink" href="#asking-for-help" title="Permalink to this heading">¶</a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.Help()</p></td>
<td><p>GMATAPI.Help()</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.Help(&lt;topic&gt;)</p></td>
<td><p>GMATAPI.Help(&lt;topic&gt;)</p></td>
</tr>
<tr class="row-even"><td><p>object.Help()</p></td>
<td><p>object.Help()</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-even"><td><p>gmat.Help(“ScriptUsage”)</p></td>
<td><p>GMATAPI.Help(“Objects”)</p></td>
</tr>
<tr class="row-odd"><td><p>burn.Help()</p></td>
<td><p>burn.Help()</p></td>
</tr>
</tbody>
</table>
</section>
<section id="gmat-objects">
<h2>GMAT Objects<a class="headerlink" href="#gmat-objects" title="Permalink to this heading">¶</a></h2>
<section id="listing-available-classes">
<h3>Listing Available Classes<a class="headerlink" href="#listing-available-classes" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.ShowClasses()</p></td>
<td><p>GMATAPI.ShowClasses()</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.ShowClasses(&lt;type&gt;)</p></td>
<td><p>GMATAPI.ShowClasses(&lt;type&gt;)</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-odd"><td><p>gmat.ShowClasses()</p></td>
<td><p>GMATAPI.ShowClasses()</p></td>
</tr>
<tr class="row-even"><td><p>gmat.ShowClasses(“PhysicalModel”)</p></td>
<td><p>GMATAPI.ShowClasses(“Propagator”)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="listing-created-objects">
<h3>Listing Created Objects<a class="headerlink" href="#listing-created-objects" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.ShowObjects()</p></td>
<td><p>GMATAPI.ShowObjects()</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.ShowObjects(&lt;type&gt;)</p></td>
<td><p>GMATAPI.ShowObjects(&lt;type&gt;)</p></td>
</tr>
<tr class="row-even"><td><p>gmat.GetCommands()</p></td>
<td><p>GMATAPI.GetCommands()</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.GetCommands(&lt;type&gt;)</p></td>
<td><p>GMATAPI.GetCommands(&lt;type&gt;)</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-odd"><td><p>gmat.ShowObjects()</p></td>
<td><p>GMATAPI.ShowObjects()</p></td>
</tr>
<tr class="row-even"><td><p>gmat.ShowObjects(“Spacecraft”)</p></td>
<td><p>GMATAPI.ShowObjects(“Burn”)</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.GetCommands()</p></td>
<td><p>GMATAPI.GetCommands()</p></td>
</tr>
<tr class="row-even"><td><p>gmat.GetCommands(“Propagate”)</p></td>
<td><p>GMATAPI.GetCommands(“Target”)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="object-creation">
<h3>Object Creation<a class="headerlink" href="#object-creation" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>obj = gmat.Construct(&lt;type&gt;,&lt;name&gt;)</p></td>
<td><p>obj = GMATAPI.Construct(&lt;type&gt;,
&lt;name&gt;)</p></td>
</tr>
<tr class="row-odd"><td><p>cmd = gmat.Command(&lt;type&gt;,&lt;desc&gt;)</p></td>
<td><p>cmd = GMATAPI.Command(&lt;type&gt;,
&lt;desc&gt;)</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-odd"><td><p>burn = gmat.Construct
(“ImpulsiveBurn”, “Burn”)</p></td>
<td><p>counter = GMATAPI.Construct
(“Variable”,”Counter”)</p></td>
</tr>
<tr class="row-even"><td><p>maneuver = gmat.Command
(“Maneuver”, “Burn(Sat)”)</p></td>
<td><p>assign = GMATAPI.Command
(“GMAT”, “Counter = 0”)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="object-field-access">
<h3>Object Field Access<a class="headerlink" href="#object-field-access" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>value = obj.GetField(&lt;FieldLabel&gt;)</p></td>
<td><p>value = obj.GetField(&lt;FieldLabel&gt;)</p></td>
</tr>
<tr class="row-odd"><td><p>obj.SetField(&lt;FieldLabel&gt;,&lt;value&gt;)</p></td>
<td><p>obj.SetField(&lt;FieldLabel&gt;,&lt;value&gt;)</p></td>
</tr>
<tr class="row-even"><td><p>obj.GetNumber(&lt;FieldLabel&gt;)</p></td>
<td><p>obj.GetNumber(&lt;FieldLabel&gt;)</p></td>
</tr>
<tr class="row-odd"><td><p>obj.GetMatrix(&lt;FieldLabel&gt;)</p></td>
<td><p>obj.GetMatrix(&lt;FieldLabel&gt;)</p></td>
</tr>
<tr class="row-even"><td><p>obj.GetVector(&lt;FieldLabel&gt;)</p></td>
<td><p>obj.GetVector(&lt;FieldLabel&gt;)</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-even"><td><p>V = burn.GetField(“Element1”)</p></td>
<td><p>V = burn.GetField(“Element1”)</p></td>
</tr>
<tr class="row-odd"><td><p>burn.SetField(“Element1”,1.5)</p></td>
<td><p>SetField(“Element1”,1.5)</p></td>
</tr>
<tr class="row-even"><td><p>burn.SetField(“Origin”,”Mars”)</p></td>
<td><p>SetField(“Origin”,”Mars”)</p></td>
</tr>
<tr class="row-odd"><td><p>V = burn.GetNumber(“Element1”)</p></td>
<td><p>V = burn.GetNumber(“Element1”)</p></td>
</tr>
<tr class="row-even"><td><p>V = Sat.GetMatrix(“OrbitErrorCovariance”)</p></td>
<td><p>V = Sat.GetMatrix(“OrbitErrorCovariance”)</p></td>
</tr>
<tr class="row-odd"><td><p>V = plate.GetVector(“PlateNormal”)</p></td>
<td><p>V = plate.GetVector(“PlateNormal”)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="object-to-object-connections">
<h3>Object to Object Connections<a class="headerlink" href="#object-to-object-connections" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>obj.SetReference(&lt;RefObject&gt;)</p></td>
<td><p>obj.SetReference(&lt;RefObject&gt;)</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-even"><td><p>jrdrag.SetReference(atmos)</p></td>
<td><p>jrdrag.SetReference(atmos)</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="gmat-script-access">
<h2>GMAT Script Access<a class="headerlink" href="#gmat-script-access" title="Permalink to this heading">¶</a></h2>
<section id="loading-a-gmat-script">
<h3>Loading a GMAT script<a class="headerlink" href="#loading-a-gmat-script" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.LoadScript(&lt;script&gt;)</p></td>
<td><p>GMATAPI.LoadScript(&lt;script&gt;)</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-even"><td><p>gmat.LoadScript
(“../samples/Ex_GEOTransfer.script”)</p></td>
<td><p>GMATAPI.LoadScript
(“../samples/Ex_GEOTransfer.script”)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="saving-a-gmat-script">
<h3>Saving a GMAT script<a class="headerlink" href="#saving-a-gmat-script" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.SaveScript(&lt;script&gt;)</p></td>
<td><p>GMATAPI.SaveScript(&lt;script&gt;)</p></td>
</tr>
<tr class="row-odd"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-even"><td><p>gmat.SaveScript
(“../scripts/New_GEOTransfer.script”)</p></td>
<td><p>GMATAPI.SaveScript
(“../scripts/New_GEOTransfer.script”)</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<section id="running-gmat">
<h2>Running GMAT<a class="headerlink" href="#running-gmat" title="Permalink to this heading">¶</a></h2>
<section id="running-gmat-from-a-loaded-script">
<h3>Running GMAT from a loaded script<a class="headerlink" href="#running-gmat-from-a-loaded-script" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.RunScript()</p></td>
<td><p>GMATAPI.RunScript()</p></td>
</tr>
</tbody>
</table>
</section>
<section id="running-gmat-commands">
<h3>Running GMAT Commands<a class="headerlink" href="#running-gmat-commands" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.Execute()</p></td>
<td><p>GMATAPI.Execute()</p></td>
</tr>
</tbody>
</table>
</section>
<section id="accessing-run-data-after-a-run">
<h3>Accessing Run Data After a Run<a class="headerlink" href="#accessing-run-data-after-a-run" title="Permalink to this heading">¶</a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Python</p></th>
<th class="head"><p>MATLAB</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.GetRuntimeObject(&lt;name&gt;)</p></td>
<td><p>GMATAPI.GetRuntimeObject(&lt;name&gt;)</p></td>
</tr>
<tr class="row-odd"><td><p>gmat.GetRunSummary()</p></td>
<td><p>GMATAPI.GetRunSummary()</p></td>
</tr>
<tr class="row-even"><td colspan="2"><p><strong>Examples</strong></p></td>
</tr>
<tr class="row-odd"><td><p>gmat.GetRuntimeObject(“geoSat”)</p></td>
<td><p>GMATAPI.GetRuntimeObject(“geoSat”)</p></td>
</tr>
</tbody>
</table>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">GMAT API Cheat Sheet</a><ul>
<li><a class="reference internal" href="#loading-the-api">Loading the API</a></li>
<li><a class="reference internal" href="#asking-for-help">Asking for Help</a></li>
<li><a class="reference internal" href="#gmat-objects">GMAT Objects</a><ul>
<li><a class="reference internal" href="#listing-available-classes">Listing Available Classes</a></li>
<li><a class="reference internal" href="#listing-created-objects">Listing Created Objects</a></li>
<li><a class="reference internal" href="#object-creation">Object Creation</a></li>
<li><a class="reference internal" href="#object-field-access">Object Field Access</a></li>
<li><a class="reference internal" href="#object-to-object-connections">Object to Object Connections</a></li>
</ul>
</li>
<li><a class="reference internal" href="#gmat-script-access">GMAT Script Access</a><ul>
<li><a class="reference internal" href="#loading-a-gmat-script">Loading a GMAT script</a></li>
<li><a class="reference internal" href="#saving-a-gmat-script">Saving a GMAT script</a></li>
</ul>
</li>
<li><a class="reference internal" href="#running-gmat">Running GMAT</a><ul>
<li><a class="reference internal" href="#running-gmat-from-a-loaded-script">Running GMAT from a loaded script</a></li>
<li><a class="reference internal" href="#running-gmat-commands">Running GMAT Commands</a></li>
<li><a class="reference internal" href="#accessing-run-data-after-a-run">Accessing Run Data After a Run</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../BestPractices.html"
                          title="previous chapter">API Best Practices</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../notebooks/notebooks.html"
                          title="next chapter">API Notebook Walkthroughs</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/CheatSheet.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../notebooks/notebooks.html" title="API Notebook Walkthroughs"
             >next</a> |</li>
        <li class="right" >
          <a href="../BestPractices.html" title="API Best Practices"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT API Cheat Sheet</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>