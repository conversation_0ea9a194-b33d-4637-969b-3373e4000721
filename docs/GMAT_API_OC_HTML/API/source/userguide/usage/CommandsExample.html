
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Propagation Command &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="Example: MONTE-GMAT Interoperability" href="UseCase2.html" />
    <link rel="prev" title="API Examples in Java" href="JavaExamples.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="UseCase2.html" title="Example: MONTE-GMAT Interoperability"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="JavaExamples.html" title="API Examples in Java"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" accesskey="U">Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Propagation Command</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="propagation-command">
<span id="examples"></span><h1>Propagation Command<a class="headerlink" href="#propagation-command" title="Permalink to this heading">¶</a></h1>
<div class="literal-block-wrapper docutils container" id="id1">
<span id="propagatetoperigee"></span><div class="code-block-caption"><span class="caption-number">Listing 19 </span><span class="caption-text">Setting up and running a Propagate command</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">from</span> <span class="nn">load_gmat</span> <span class="kn">import</span> <span class="n">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Core resources used</span>
<span class="linenos"> 4</span><span class="n">sat</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Spacecraft&quot;</span><span class="p">,</span> <span class="s2">&quot;Sat&quot;</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;DateFormat&quot;</span><span class="p">,</span> <span class="s2">&quot;UTCGregorian&quot;</span><span class="p">)</span>
<span class="linenos"> 6</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;Epoch&quot;</span><span class="p">,</span> <span class="s2">&quot;02 Jan 2023 12:00:00.000&quot;</span><span class="p">)</span>
<span class="linenos"> 7</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;X&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="linenos"> 8</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;Y&quot;</span><span class="p">,</span> <span class="o">-</span><span class="mi">3100</span><span class="p">)</span>
<span class="linenos"> 9</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;Z&quot;</span><span class="p">,</span> <span class="o">-</span><span class="mi">11591</span><span class="p">)</span>
<span class="linenos">10</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;VX&quot;</span><span class="p">,</span> <span class="mf">5.155</span><span class="p">)</span>
<span class="linenos">11</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;VY&quot;</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
<span class="linenos">12</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;VZ&quot;</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
<span class="linenos">13</span>
<span class="linenos">14</span><span class="n">fm</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ForceModel&quot;</span><span class="p">,</span> <span class="s2">&quot;Dynamics&quot;</span><span class="p">)</span>
<span class="linenos">15</span><span class="n">epm</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;EPM&quot;</span><span class="p">)</span>
<span class="linenos">16</span><span class="n">fm</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos">17</span>
<span class="linenos">18</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop&quot;</span><span class="p">)</span>
<span class="linenos">19</span><span class="n">prop</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;FM&quot;</span><span class="p">,</span> <span class="s2">&quot;Dynamics&quot;</span><span class="p">)</span>
<span class="linenos">20</span>
<span class="linenos">21</span><span class="n">propagate</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Propagate&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop(Sat) </span><span class="si">{Sat.Periapsis}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="linenos">22</span>
<span class="linenos">23</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">24</span>
<span class="linenos">25</span><span class="n">gmat</span><span class="o">.</span><span class="n">Execute</span><span class="p">()</span>
</pre></div>
</div>
</div>
</section>
<section id="targeting-command">
<h1>Targeting Command<a class="headerlink" href="#targeting-command" title="Permalink to this heading">¶</a></h1>
<div class="literal-block-wrapper docutils container" id="id2">
<span id="burntargeting"></span><div class="code-block-caption"><span class="caption-number">Listing 20 </span><span class="caption-text">Setting up and running a Propagate command</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">from</span> <span class="nn">load_gmat</span> <span class="kn">import</span> <span class="n">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Core resources used</span>
<span class="linenos"> 4</span><span class="n">sat</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Spacecraft&quot;</span><span class="p">,</span> <span class="s2">&quot;Sat&quot;</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;DateFormat&quot;</span><span class="p">,</span> <span class="s2">&quot;UTCGregorian&quot;</span><span class="p">)</span>
<span class="linenos"> 6</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;Epoch&quot;</span><span class="p">,</span> <span class="s2">&quot;10 Feb 2023 12:00:00.000&quot;</span><span class="p">)</span>
<span class="linenos"> 7</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;DispayStateType&quot;</span><span class="p">,</span> <span class="s2">&quot;Keplerian&quot;</span><span class="p">)</span>
<span class="linenos"> 8</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;SMA&quot;</span><span class="p">,</span> <span class="mi">6900</span><span class="p">)</span>
<span class="linenos"> 9</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;ECC&quot;</span><span class="p">,</span> <span class="mf">0.005</span><span class="p">)</span>
<span class="linenos">10</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;INC&quot;</span><span class="p">,</span> <span class="mf">28.5</span><span class="p">)</span>
<span class="linenos">11</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;RAAN&quot;</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>
<span class="linenos">12</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;AOP&quot;</span><span class="p">,</span> <span class="mf">90.0</span><span class="p">)</span>
<span class="linenos">13</span><span class="n">sat</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;TA&quot;</span><span class="p">,</span> <span class="mf">180.0</span><span class="p">)</span>
<span class="linenos">14</span>
<span class="linenos">15</span><span class="n">fm</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ForceModel&quot;</span><span class="p">,</span> <span class="s2">&quot;Dynamics&quot;</span><span class="p">)</span>
<span class="linenos">16</span><span class="n">epm</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;EPM&quot;</span><span class="p">)</span>
<span class="linenos">17</span><span class="n">fm</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos">18</span>
<span class="linenos">19</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop&quot;</span><span class="p">)</span>
<span class="linenos">20</span><span class="n">prop</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;FM&quot;</span><span class="p">,</span> <span class="s2">&quot;Dynamics&quot;</span><span class="p">)</span>
<span class="linenos">21</span>
<span class="linenos">22</span><span class="n">burn</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ImpulsiveBurn&quot;</span><span class="p">,</span> <span class="s2">&quot;Burn&quot;</span><span class="p">)</span>
<span class="linenos">23</span><span class="n">dc</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;DifferentialCorrector&quot;</span><span class="p">,</span> <span class="s2">&quot;DC&quot;</span><span class="p">)</span>
<span class="linenos">24</span>
<span class="linenos">25</span><span class="c1"># First propagate so we start at perigee</span>
<span class="linenos">26</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Propagate&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop(Sat) </span><span class="si">{Sat.Periapsis}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="linenos">27</span>
<span class="linenos">28</span><span class="c1"># Target commands</span>
<span class="linenos">29</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Target&quot;</span><span class="p">,</span> <span class="s2">&quot;DC&quot;</span><span class="p">)</span>
<span class="linenos">30</span><span class="c1"># Note that commands are added to the branch in the Target command</span>
<span class="linenos">31</span><span class="c1"># until the EndTarget command is added</span>
<span class="linenos">32</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Vary&quot;</span><span class="p">,</span> <span class="s2">&quot;DC(Burn.Element1 = 0.5, {Perturbation = 0.0001})&quot;</span><span class="p">)</span>
<span class="linenos">33</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Maneuver&quot;</span><span class="p">,</span> <span class="s2">&quot;Burn(Sat)&quot;</span><span class="p">)</span>
<span class="linenos">34</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Propagate&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop(Sat) </span><span class="si">{Sat.Apoapsis}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="linenos">35</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Achieve&quot;</span><span class="p">,</span> <span class="s2">&quot;DC(Sat.Earth.RMAG = 42165.0, {Tolerance = 0.1})&quot;</span><span class="p">)</span>
<span class="linenos">36</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;EndTarget&quot;</span><span class="p">)</span>
<span class="linenos">37</span>
<span class="linenos">38</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">39</span>
<span class="linenos">40</span><span class="c1"># Execute() runs the entire sequence to completion</span>
<span class="linenos">41</span><span class="n">gmat</span><span class="o">.</span><span class="n">Execute</span><span class="p">()</span>
</pre></div>
</div>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Propagation Command</a></li>
<li><a class="reference internal" href="#targeting-command">Targeting Command</a></li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="JavaExamples.html"
                          title="previous chapter">API Examples in Java</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="UseCase2.html"
                          title="next chapter">Example: MONTE-GMAT Interoperability</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/CommandsExample.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="UseCase2.html" title="Example: MONTE-GMAT Interoperability"
             >next</a> |</li>
        <li class="right" >
          <a href="JavaExamples.html" title="API Examples in Java"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" >Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Propagation Command</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>