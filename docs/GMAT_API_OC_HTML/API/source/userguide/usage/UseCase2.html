
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Example: MONTE-GMAT Interoperability &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="API Best Practices" href="../BestPractices.html" />
    <link rel="prev" title="Propagation Command" href="CommandsExample.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../BestPractices.html" title="API Best Practices"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="CommandsExample.html" title="Propagation Command"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" accesskey="U">Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Example: MONTE-GMAT Interoperability</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="example-monte-gmat-interoperability">
<h1>Example: MONTE-GMAT Interoperability<a class="headerlink" href="#example-monte-gmat-interoperability" title="Permalink to this heading">¶</a></h1>
<p>GMAT is able to work with other programs through the API. Examples have been made
between GMAT and MONTE for OSIRIS-REx and LUCY missions. In these examples, data
will be shared between MONTE and GMAT using interfaces built with MONTE’s
native Python framework and GMAT’s API, accessed through Python.</p>
<p>For access to these example scripts contact the GMAT development team.</p>
<section id="ephemeris-sharing">
<h2>Ephemeris Sharing<a class="headerlink" href="#ephemeris-sharing" title="Permalink to this heading">¶</a></h2>
<p>Both GMAT and MONTE have ephemeris reading and writing capabilities.  GMAT
supports four types of spacecraft ephemerides: Goddard specific “Code-500”, STK
time-position-velocity (.e), CCSDS OEM, and SPICE SPK formats.  MONTE supports
SPICE based SPK ephemerides, so that format is used for data interchange between
the systems. Ephemeris sharing between GMAT and MONTE is straightforward:
use the system providing the ephemeris to generate the file, and then import
it into the other system.</p>
</section>
<section id="maneuver-sharing">
<h2>Maneuver Sharing<a class="headerlink" href="#maneuver-sharing" title="Permalink to this heading">¶</a></h2>
<p>Allow maneuver planning for both finite and impulse burns.</p>
</section>
<section id="covariance-sharing">
<h2>Covariance Sharing<a class="headerlink" href="#covariance-sharing" title="Permalink to this heading">¶</a></h2>
<p>Possible to share covariance arrays between MONTE and GMAT. The arrays are in
slightly different formats so some conversion will be necessary between the two
programs.</p>
</section>
<section id="dynamics-sharing">
<h2>Dynamics Sharing<a class="headerlink" href="#dynamics-sharing" title="Permalink to this heading">¶</a></h2>
<p>Dynamics sharing is done through the External Force Model Plugin. (Currently an alpha
feature as of R2022a)</p>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Example: MONTE-GMAT Interoperability</a><ul>
<li><a class="reference internal" href="#ephemeris-sharing">Ephemeris Sharing</a></li>
<li><a class="reference internal" href="#maneuver-sharing">Maneuver Sharing</a></li>
<li><a class="reference internal" href="#covariance-sharing">Covariance Sharing</a></li>
<li><a class="reference internal" href="#dynamics-sharing">Dynamics Sharing</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="CommandsExample.html"
                          title="previous chapter">Propagation Command</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../BestPractices.html"
                          title="next chapter">API Best Practices</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/UseCase2.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../BestPractices.html" title="API Best Practices"
             >next</a> |</li>
        <li class="right" >
          <a href="CommandsExample.html" title="Propagation Command"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Examples.html" >Usage Examples</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Example: MONTE-GMAT Interoperability</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>