
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Usage Examples &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="Time and Coordinate Conversions" href="Conversion.html" />
    <link rel="prev" title="Configuring a Command" href="../commands/SingleCommands.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="Conversion.html" title="Time and Coordinate Conversions"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../commands/SingleCommands.html" title="Configuring a Command"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Usage Examples</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="usage-examples">
<h1>Usage Examples<a class="headerlink" href="#usage-examples" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="Conversion.html">Time and Coordinate Conversions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="Conversion.html#time-system-conversion">Time System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="Conversion.html#coordinate-system-conversion">Coordinate System Conversion</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="PythonExamples.html">API Examples</a><ul>
<li class="toctree-l2"><a class="reference internal" href="PythonExamples.html#case-1-time-system-conversion">Case 1: Time System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="PythonExamples.html#case-2-coordinate-system-conversion">Case 2: Coordinate System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="PythonExamples.html#cases-3-and-4-force-modeling-and-propagation">Cases 3 and 4: Force Modeling and Propagation</a></li>
<li class="toctree-l2"><a class="reference internal" href="PythonExamples.html#cases-5-working-with-a-gmat-script">Cases 5: Working with a GMAT Script</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="JavaExamples.html">API Examples in Java</a><ul>
<li class="toctree-l2"><a class="reference internal" href="JavaExamples.html#time-system-conversion">Time System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="JavaExamples.html#coordinate-system-conversion">Coordinate System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="JavaExamples.html#force-modeling">Force Modeling</a></li>
<li class="toctree-l2"><a class="reference internal" href="JavaExamples.html#propagation">Propagation</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="CommandsExample.html">Propagation Command</a></li>
<li class="toctree-l1"><a class="reference internal" href="CommandsExample.html#targeting-command">Targeting Command</a></li>
<li class="toctree-l1"><a class="reference internal" href="UseCase2.html">Example: MONTE-GMAT Interoperability</a><ul>
<li class="toctree-l2"><a class="reference internal" href="UseCase2.html#ephemeris-sharing">Ephemeris Sharing</a></li>
<li class="toctree-l2"><a class="reference internal" href="UseCase2.html#maneuver-sharing">Maneuver Sharing</a></li>
<li class="toctree-l2"><a class="reference internal" href="UseCase2.html#covariance-sharing">Covariance Sharing</a></li>
<li class="toctree-l2"><a class="reference internal" href="UseCase2.html#dynamics-sharing">Dynamics Sharing</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../commands/SingleCommands.html"
                          title="previous chapter">Configuring a Command</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="Conversion.html"
                          title="next chapter">Time and Coordinate Conversions</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/usage/Examples.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="Conversion.html" title="Time and Coordinate Conversions"
             >next</a> |</li>
        <li class="right" >
          <a href="../commands/SingleCommands.html" title="Configuring a Command"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Usage Examples</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>