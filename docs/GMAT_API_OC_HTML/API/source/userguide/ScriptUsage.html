
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Script Usage &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="Tutorial: Accessing GMAT Propagation and Navigation Features" href="usage/UseCase1.html" />
    <link rel="prev" title="Functions Used in the GMAT API" href="APIFunctions.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="usage/UseCase1.html" title="Tutorial: Accessing GMAT Propagation and Navigation Features"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="APIFunctions.html" title="Functions Used in the GMAT API"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Script Usage</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="script-usage">
<span id="scriptusage"></span><h1>Script Usage<a class="headerlink" href="#script-usage" title="Permalink to this heading">¶</a></h1>
<p>The GMAT API can be used as a front end for driving the GMAT application in a
“headless” mode.  You might want to do this to run GMAT remotely, to script
product generation, or to perform a large scale run like a Monte-Carlo run or
a scan through a set of parameters.  This section introduces the API features
that make these processes possible.</p>
<section id="api-functions-for-script-users">
<h2>API Functions for Script Users<a class="headerlink" href="#api-functions-for-script-users" title="Permalink to this heading">¶</a></h2>
<p>The GMAT API includes five functions, shown in <a class="reference internal" href="#scriptdrivers"><span class="std std-numref">Table 5</span></a>
specifically designed for script based usage.</p>
<span id="scriptdrivers"></span><table class="docutils align-default" id="id1">
<caption><span class="caption-number">Table 5 </span><span class="caption-text">Functions Used to Run Scripts in the API</span><a class="headerlink" href="#id1" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 18%" />
<col style="width: 20%" />
<col style="width: 15%" />
<col style="width: 47%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Function</p></th>
<th class="head"><p>Example</p></th>
<th class="head"><p>Return Value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>LoadScript</p></td>
<td><p>LoadScript(“script”)</p></td>
<td><p>bool</p></td>
<td><p>Loads a script into the GMAT system</p></td>
</tr>
<tr class="row-odd"><td><p>RunScript</p></td>
<td><p>RunScript()</p></td>
<td><p>bool</p></td>
<td><p>Runs a loaded script</p></td>
</tr>
<tr class="row-even"><td><p>SaveScript</p></td>
<td><p>SaveScript(“script”)</p></td>
<td><p>bool</p></td>
<td><p>Saves the configured objects to a file</p></td>
</tr>
<tr class="row-odd"><td><p>GetRuntimeObject</p></td>
<td><p>GetRuntimeObject (“Sat”)</p></td>
<td><p>GmatBase*</p></td>
<td><p>Retrieves an object from a GMAT run</p></td>
</tr>
<tr class="row-even"><td><p>GetRunSummary</p></td>
<td><p>GetRunSummary()</p></td>
<td><p>String</p></td>
<td><p>Retrieves a listing of the spacecraft data
from a script run for each command in the
script</p></td>
</tr>
</tbody>
</table>
<section id="background-the-gmat-run-script-process">
<h3>Background: The GMAT Run Script Process<a class="headerlink" href="#background-the-gmat-run-script-process" title="Permalink to this heading">¶</a></h3>
<p>In GMAT, when a user runs a script three steps are taken:</p>
<ol class="arabic simple">
<li><p>The script is read and GMAT objects are created that match the objects
described by the script.</p>
<ul class="simple">
<li><p>GMAT Resources are stored in the GMAT Configuration.</p></li>
<li><p>GMAT Commands are connected together to create the Mission Control Sequence.</p></li>
</ul>
</li>
<li><p>The objects from the script are copied (“cloned”) into a memory location, the
Sandbox, used for the run.</p>
<ul class="simple">
<li><p>This creates a new set of objects used for the run.</p></li>
<li><p>After cloning, these run time objects are connected together as needed and
initialized.</p></li>
</ul>
</li>
<li><p>The Mission Control Sequence is executed sequentially, starting from the first
command in the sequence.  Command execution manipulates the run time objects
to simulate the scripted mission.</p></li>
</ol>
<p>API users drive this process using the functions in <a class="reference internal" href="#scriptdrivers"><span class="std std-numref">Table 5</span></a>.</p>
</section>
<section id="driving-a-script-from-the-api">
<h3>Driving a Script From the API<a class="headerlink" href="#driving-a-script-from-the-api" title="Permalink to this heading">¶</a></h3>
<p>API users take the following steps to execute a script:</p>
<ol class="arabic simple">
<li><p>Start the API environment (e.g. Python or MATLAB) from the GMAT bin directory
or start the application and change the current directory to the GMAT bin
directory.</p></li>
<li><p>Load GMAT into the environment.  The MATLAB implementation includes a
scripted function for this process.</p>
<ul class="simple">
<li><p><strong>Python</strong>: import gmatpy</p></li>
<li><p><strong>MATLAB</strong>: load_gmat()</p></li>
</ul>
</li>
<li><p>Read a script into the loaded GMAT engine.</p>
<ul class="simple">
<li><p><strong>Python</strong>: gmat.LoadScript(<em>script_path_and_name</em>)</p></li>
<li><p><strong>MATLAB</strong>: GMATAPI.LoadScript(<em>script_path_and_name</em>)</p></li>
</ul>
</li>
<li><p>Run the script.  This step performs the run time object cloning and
initialization and then executes the Mission Control Sequence.</p>
<ul class="simple">
<li><p><strong>Python</strong>: gmat.RunScript()</p></li>
<li><p><strong>MATLAB</strong>: GMATAPI.RunScript()</p></li>
</ul>
</li>
</ol>
</section>
</section>
<section id="examples">
<h2>Examples<a class="headerlink" href="#examples" title="Permalink to this heading">¶</a></h2>
<p>The sections above describe in general terms how to run GMAT scripts from the
API.  The following sample usage shows these features in Python and MATLAB.</p>
<section id="example-running-a-sample-mission">
<h3>Example: Running a Sample Mission<a class="headerlink" href="#example-running-a-sample-mission" title="Permalink to this heading">¶</a></h3>
<p>The first example shows how to run a sample mission from the API and retrieve
data generated from the run.  The example runs the sample mission
Ex_GEOTransfer.script in the GMAT samples folder, then accesses the targeted
maneuvers from the script and computes the total delta-V needed for the run.</p>
<section id="python">
<h4>Python<a class="headerlink" href="#python" title="Permalink to this heading">¶</a></h4>
<p>Use of the API in Python is performed through direct calls to the functions
described above.</p>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-number">Listing 1 </span><span class="caption-text">Sample Run: Calculating the Delta-V for the GEO Transfer, Run in Python</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-shell notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span>$ python3
<span class="linenos"> 2</span>Python <span class="m">3</span>.6.7 <span class="o">(</span>default, Oct <span class="m">22</span> <span class="m">2018</span>, <span class="m">11</span>:32:17<span class="o">)</span>
<span class="linenos"> 3</span><span class="o">[</span>GCC <span class="m">8</span>.2.0<span class="o">]</span> on linux
<span class="linenos"> 4</span>Type <span class="s2">&quot;help&quot;</span>, <span class="s2">&quot;copyright&quot;</span>, <span class="s2">&quot;credits&quot;</span> or <span class="s2">&quot;license&quot;</span> <span class="k">for</span> more information.
<span class="linenos"> 5</span>&gt;&gt;&gt; import gmatpy as gmat
<span class="linenos"> 6</span>&gt;&gt;&gt; gmat.LoadScript<span class="o">(</span><span class="s2">&quot;../samples/Ex_GEOTransfer.script&quot;</span><span class="o">)</span>
<span class="linenos"> 7</span>True
<span class="linenos"> 8</span>&gt;&gt;&gt; gmat.RunScript<span class="o">()</span>
<span class="linenos"> 9</span>True
<span class="linenos">10</span>&gt;&gt;&gt; <span class="nv">TOI</span> <span class="o">=</span> gmat.GetRuntimeObject<span class="o">(</span><span class="s2">&quot;TOI&quot;</span><span class="o">)</span>
<span class="linenos">11</span>&gt;&gt;&gt; <span class="nv">MCC</span> <span class="o">=</span> gmat.GetRuntimeObject<span class="o">(</span><span class="s2">&quot;MCC&quot;</span><span class="o">)</span>
<span class="linenos">12</span>&gt;&gt;&gt; <span class="nv">MOI</span> <span class="o">=</span> gmat.GetRuntimeObject<span class="o">(</span><span class="s2">&quot;MOI&quot;</span><span class="o">)</span>
<span class="linenos">13</span>&gt;&gt;&gt; <span class="nv">toidv</span> <span class="o">=</span> float<span class="o">(</span>TOI.GetField<span class="o">(</span><span class="s2">&quot;Element1&quot;</span><span class="o">))</span>
<span class="linenos">14</span>&gt;&gt;&gt; <span class="nv">mccdv</span> <span class="o">=</span> <span class="o">(</span>float<span class="o">(</span>MCC.GetField<span class="o">(</span><span class="s2">&quot;Element1&quot;</span><span class="o">))</span>**2+float<span class="o">(</span>MCC.GetField<span class="o">(</span><span class="s2">&quot;Element2&quot;</span><span class="o">))</span>**2<span class="o">)</span>**0.5
<span class="linenos">15</span>&gt;&gt;&gt; <span class="nv">moidv</span> <span class="o">=</span> float<span class="o">(</span>MOI.GetField<span class="o">(</span><span class="s2">&quot;Element1&quot;</span><span class="o">))</span>
<span class="linenos">16</span>&gt;&gt;&gt; <span class="nv">deltaV</span> <span class="o">=</span> abs<span class="o">(</span>toidv<span class="o">)</span>+mccdv+abs<span class="o">(</span>moidv<span class="o">)</span>
<span class="linenos">17</span>&gt;&gt;&gt; print<span class="o">(</span><span class="s2">&quot;Total Delta-V Cost: &quot;</span>, deltaV, <span class="s2">&quot; km/s&quot;</span><span class="o">)</span>
<span class="linenos">18</span>Total Delta-V Cost:  <span class="m">4</span>.394839062410714  km/s
<span class="linenos">19</span>&gt;&gt;&gt; exit<span class="o">()</span>
</pre></div>
</div>
</div>
</section>
<section id="matlab">
<h4>MATLAB<a class="headerlink" href="#matlab" title="Permalink to this heading">¶</a></h4>
<p>Loading the GMAT API in MATLAB is a moderately complicated procedure, so the API
developers have wrapped the load process in a MATLAB function, load_gmat.m.  The
function takes two optional arguments: the name of a script, and the startup
file used to initialize GMAT.  The example shown below leaves both inputs blank
so that it closely matches the Python example, above.</p>
<div class="literal-block-wrapper docutils container" id="id3">
<div class="code-block-caption"><span class="caption-number">Listing 2 </span><span class="caption-text">Sample Run: Calculating the Delta-V for the GEO Transfer, Run in MATLAB</span><a class="headerlink" href="#id3" title="Permalink to this code">¶</a></div>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">load_gmat</span><span class="p">()</span><span class="w"></span>
<span class="linenos"> 2</span><span class="n">Initialize</span><span class="w"> </span><span class="s">Moderator</span><span class="w"> </span><span class="s">Status:</span><span class="w"> </span><span class="s">1</span><span class="w"></span>
<span class="linenos"> 3</span><span class="n">No</span><span class="w"> </span><span class="s">script</span><span class="w"> </span><span class="s">provided</span><span class="w"> </span><span class="s">to</span><span class="w"> </span><span class="s">load.</span><span class="w"></span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="nb">ans</span><span class="w"> </span><span class="p">=</span><span class="w"></span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="n">Instance</span><span class="w"> </span><span class="s">of</span><span class="w"> </span><span class="s">GMAT</span><span class="w"> </span><span class="s">Moderator</span><span class="w"> </span><span class="s">is</span><span class="w"> </span><span class="s">initialized.</span><span class="w"> </span><span class="s">No</span><span class="w"> </span><span class="s">script</span><span class="w"> </span><span class="s">ready</span><span class="w"> </span><span class="s">to</span><span class="w"> </span><span class="s">run.</span><span class="w"></span>
<span class="linenos"> 8</span>
<span class="linenos"> 9</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">GMATAPI</span><span class="p">.</span><span class="n">LoadScript</span><span class="p">(</span><span class="s">&quot;../samples/Ex_GEOTransfer.script&quot;</span><span class="p">)</span><span class="w"></span>
<span class="linenos">10</span>
<span class="linenos">11</span><span class="nb">ans</span><span class="w"> </span><span class="p">=</span><span class="w"></span>
<span class="linenos">12</span>
<span class="linenos">13</span><span class="w">  </span><span class="nb">logical</span><span class="w"></span>
<span class="linenos">14</span>
<span class="linenos">15</span><span class="w">   </span><span class="mi">1</span><span class="w"></span>
<span class="linenos">16</span>
<span class="linenos">17</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">GMATAPI</span><span class="p">.</span><span class="n">RunScript</span><span class="p">()</span><span class="w"></span>
<span class="linenos">18</span>
<span class="linenos">19</span><span class="nb">ans</span><span class="w"> </span><span class="p">=</span><span class="w"></span>
<span class="linenos">20</span>
<span class="linenos">21</span><span class="w">  </span><span class="nb">logical</span><span class="w"></span>
<span class="linenos">22</span>
<span class="linenos">23</span><span class="w">   </span><span class="mi">1</span><span class="w"></span>
<span class="linenos">24</span>
<span class="linenos">25</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">TOI</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">GMATAPI</span><span class="p">.</span><span class="n">GetRuntimeObject</span><span class="p">(</span><span class="s">&quot;TOI&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">26</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">MCC</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">GMATAPI</span><span class="p">.</span><span class="n">GetRuntimeObject</span><span class="p">(</span><span class="s">&quot;MCC&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">27</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">MOI</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">GMATAPI</span><span class="p">.</span><span class="n">GetRuntimeObject</span><span class="p">(</span><span class="s">&quot;MOI&quot;</span><span class="p">);</span><span class="w"></span>
<span class="linenos">28</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">toidv</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">str2num</span><span class="p">(</span><span class="n">TOI</span><span class="p">.</span><span class="n">GetField</span><span class="p">(</span><span class="s">&quot;Element1&quot;</span><span class="p">));</span><span class="w"></span>
<span class="linenos">29</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">mccdv</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">sqrt</span><span class="p">(</span><span class="nb">str2num</span><span class="p">(</span><span class="n">MCC</span><span class="p">.</span><span class="n">GetField</span><span class="p">(</span><span class="s">&quot;Element1&quot;</span><span class="p">))</span>^<span class="mi">2</span><span class="o">+</span><span class="nb">str2num</span><span class="p">(</span><span class="n">MCC</span><span class="p">.</span><span class="n">GetField</span><span class="p">(</span><span class="s">&quot;Element2&quot;</span><span class="p">))</span>^<span class="mi">2</span><span class="p">);</span><span class="w"></span>
<span class="linenos">30</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">moidv</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">str2num</span><span class="p">(</span><span class="n">MOI</span><span class="p">.</span><span class="n">GetField</span><span class="p">(</span><span class="s">&quot;Element1&quot;</span><span class="p">));</span><span class="w"></span>
<span class="linenos">31</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">DeltaV</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">abs</span><span class="p">(</span><span class="n">toidv</span><span class="p">)</span><span class="o">+</span><span class="n">mccdv</span><span class="o">+</span><span class="nb">abs</span><span class="p">(</span><span class="n">moidv</span><span class="p">)</span><span class="w"></span>
<span class="linenos">32</span>
<span class="linenos">33</span><span class="n">DeltaV</span><span class="w"> </span><span class="p">=</span><span class="w"></span>
<span class="linenos">34</span>
<span class="linenos">35</span><span class="w">   </span><span class="mf">4.394839062410714</span><span class="w"></span>
<span class="linenos">36</span>
<span class="linenos">37</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="nb">exit</span><span class="w"></span>
</pre></div>
</div>
</div>
</section>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Script Usage</a><ul>
<li><a class="reference internal" href="#api-functions-for-script-users">API Functions for Script Users</a><ul>
<li><a class="reference internal" href="#background-the-gmat-run-script-process">Background: The GMAT Run Script Process</a></li>
<li><a class="reference internal" href="#driving-a-script-from-the-api">Driving a Script From the API</a></li>
</ul>
</li>
<li><a class="reference internal" href="#examples">Examples</a><ul>
<li><a class="reference internal" href="#example-running-a-sample-mission">Example: Running a Sample Mission</a><ul>
<li><a class="reference internal" href="#python">Python</a></li>
<li><a class="reference internal" href="#matlab">MATLAB</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="APIFunctions.html"
                          title="previous chapter">Functions Used in the GMAT API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="usage/UseCase1.html"
                          title="next chapter">Tutorial: Accessing GMAT Propagation and Navigation Features</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/userguide/ScriptUsage.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="usage/UseCase1.html" title="Tutorial: Accessing GMAT Propagation and Navigation Features"
             >next</a> |</li>
        <li class="right" >
          <a href="APIFunctions.html" title="Functions Used in the GMAT API"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Script Usage</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>