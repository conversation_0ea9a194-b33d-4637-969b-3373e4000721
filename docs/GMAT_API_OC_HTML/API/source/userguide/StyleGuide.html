
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Conventions in the API Examples and Use Cases &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="Object Usage with the GMAT API" href="GettingStarted.html" />
    <link rel="prev" title="Setting up the GMAT API" href="Setup.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="GettingStarted.html" title="Object Usage with the GMAT API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Setup.html" title="Setting up the GMAT API"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" accesskey="U">GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Conventions in the API Examples and Use Cases</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="conventions-in-the-api-examples-and-use-cases">
<h1>Conventions in the API Examples and Use Cases<a class="headerlink" href="#conventions-in-the-api-examples-and-use-cases" title="Permalink to this heading">¶</a></h1>
<p>The usage section of this documentation shows API users how to perform several
common tasks using the GMAT API.</p>
<section id="general-conventions">
<h2>General Conventions<a class="headerlink" href="#general-conventions" title="Permalink to this heading">¶</a></h2>
<ul>
<li><p>Numbers that would normally display as 16 digits are truncated to fit on the
page when necessary.</p></li>
<li><p>Extraneous white space has been removed from some output.</p></li>
<li><p>Interactive and scripted code segments are shown offset in special blocks,
like this:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">import</span> <span class="nn">gmatpy</span> <span class="k">as</span> <span class="nn">gmat</span>
</pre></div>
</div>
<p>Interactive Python blocks, like the one shown above, include Python’s triple
bracket marker.  Blocks from Python script files do not have this marker:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">import</span> <span class="nn">gmatpy</span> <span class="k">as</span> <span class="nn">gmat</span>
</pre></div>
</div>
<p>Interactive MATLAB elements are displayed similarly, with MATLAB’s Command
Window line marker:</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="n">load_gmat</span><span class="w"></span>
</pre></div>
</div>
</li>
</ul>
<p>Note that the typesetting for MATLAB is also different from the Python settings.</p>
</section>
<section id="user-workspace-and-gmat-typography">
<h2>User Workspace and GMAT Typography<a class="headerlink" href="#user-workspace-and-gmat-typography" title="Permalink to this heading">¶</a></h2>
<p>This documentation provides examples in the two application environments
supported by the core GMAT API development team: Python and MATLAB (via Java).
The API is built using SWIG, and can be built for other platforms.  This
documentation does not address other platforms.</p>
<p>Work performed using the API involves interactions with objects created in GMAT
that are accessed in the user’s application environment.  This results in
references in the user’s environment of objects in the GMAT environment.  The
following conventions are observed in this document to help clarify this
component dichotomy:</p>
<ul>
<li><p>Objects created in GMAT use camel-cased names.  User references to those
objects are presented in lowercase.  For instance, the Python script line</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">mysat</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Spacecraft&quot;</span><span class="p">,</span> <span class="s2">&quot;MySat&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>creates a GMAT Spacecraft object named “MySat,” stored in GMAT, that a user
accesses through their mysat environment variable.</p>
</li>
</ul>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Conventions in the API Examples and Use Cases</a><ul>
<li><a class="reference internal" href="#general-conventions">General Conventions</a></li>
<li><a class="reference internal" href="#user-workspace-and-gmat-typography">User Workspace and GMAT Typography</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Setup.html"
                          title="previous chapter">Setting up the GMAT API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="GettingStarted.html"
                          title="next chapter">Object Usage with the GMAT API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/userguide/StyleGuide.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="GettingStarted.html" title="Object Usage with the GMAT API"
             >next</a> |</li>
        <li class="right" >
          <a href="Setup.html" title="Setting up the GMAT API"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" >GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Conventions in the API Examples and Use Cases</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>