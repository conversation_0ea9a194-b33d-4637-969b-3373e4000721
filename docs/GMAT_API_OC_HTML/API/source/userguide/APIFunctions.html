
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Functions Used in the GMAT API &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="Script Usage" href="ScriptUsage.html" />
    <link rel="prev" title="Object Usage with the GMAT API" href="GettingStarted.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="ScriptUsage.html" title="Script Usage"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="GettingStarted.html" title="Object Usage with the GMAT API"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" accesskey="U">GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Functions Used in the GMAT API</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="functions-used-in-the-gmat-api">
<span id="apifunctions"></span><h1>Functions Used in the GMAT API<a class="headerlink" href="#functions-used-in-the-gmat-api" title="Permalink to this heading">¶</a></h1>
<p>The GMAT API provides several functions that simplify GMAT use from Java and
Python.  GMAT’s user classes include member functions, called methods, that
provide interfaces into GMAT objects that help simplify calls into the GMAT
objects.  These features of the API are described in this chapter.  Script users
may also want to refer to the <a class="reference internal" href="ScriptUsage.html#scriptusage"><span class="std std-ref">Script Usage</span></a> chapter for information
about functions tailored to running GMAT scripts through the API.</p>
<section id="general-api-functions">
<h2>General API Functions<a class="headerlink" href="#general-api-functions" title="Permalink to this heading">¶</a></h2>
<p>API specific code in can be broken into three blocks: General API functions used
to interact with the system, functions specific to driving GMAT using script
files, and methods that are implemented on the GMAT classes to simplify object
interactions using the API.</p>
<span id="generalpurposefunctions"></span><table class="docutils align-default" id="id1">
<caption><span class="caption-number">Table 3 </span><span class="caption-text">General Purpose Functions Controlling the API</span><a class="headerlink" href="#id1" title="Permalink to this table">¶</a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>Function</p></th>
<th class="head"><p>Example</p></th>
<th class="head"><p>Return Value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Help</p></td>
<td><p>gmat.Help(“MySat”)</p></td>
<td><p>string</p></td>
<td><p>Returns help for the input item</p></td>
</tr>
<tr class="row-odd"><td><p>Setup</p></td>
<td><p>gmat.Setup(“StartFile.txt”)</p></td>
<td><p>void</p></td>
<td><p>Initializes the GMAT system with a custom
startup file</p></td>
</tr>
<tr class="row-even"><td><p>Initialize</p></td>
<td><p>gmat.Initialize()</p></td>
<td><p>none</p></td>
<td><p>Sets up interconnections between objects and
reports on missing pieces</p></td>
</tr>
<tr class="row-odd"><td><p>ShowObjects</p></td>
<td><p>gmat.ShowObjects()</p></td>
<td><p>string</p></td>
<td><p>Lists the configured objects</p></td>
</tr>
<tr class="row-even"><td><p>ShowClasses</p></td>
<td><p>gmat.ShowClasses(“Burn”)</p></td>
<td><p>string</p></td>
<td><p>Lists the classes available of a given type</p></td>
</tr>
<tr class="row-odd"><td><p>Construct</p></td>
<td><dl class="simple">
<dt>gmat.Construct(</dt><dd><p>“Spacecraft”,”Sat”)</p>
</dd>
</dl>
</td>
<td><p>object</p></td>
<td><p>Creates an instance of a class with a given
name and adds it to the GMAT configuration</p></td>
</tr>
<tr class="row-even"><td><p>Copy</p></td>
<td><p>gmat.Copy(sat, “Sat2”)</p></td>
<td></td>
<td><p>Creates a new object using the settings on
an existing object</p></td>
</tr>
<tr class="row-odd"><td><p>GetObject</p></td>
<td><p>gmat.GetObject(“Sat”)</p></td>
<td><p>object</p></td>
<td><p>Retrieves an object from the configuration</p></td>
</tr>
</tbody>
</table>
</section>
<section id="gmat-object-methods">
<h2>GMAT Object Methods<a class="headerlink" href="#gmat-object-methods" title="Permalink to this heading">¶</a></h2>
<p>GMAT’s user classes have been updated with member functions (or methods) that
facilitate use for the objects by API users.  These methods are shown in
<a class="reference internal" href="#objectmethods"><span class="std std-numref">Table 4</span></a>.</p>
<span id="objectmethods"></span><table class="docutils align-default" id="id2">
<caption><span class="caption-number">Table 4 </span><span class="caption-text">Methods added to GMAT objects for API users</span><a class="headerlink" href="#id2" title="Permalink to this table">¶</a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>Function</p></th>
<th class="head"><p>Example</p></th>
<th class="head"><p>Return Value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Help</p></td>
<td><p>Sat.Help()</p></td>
<td><p>string</p></td>
<td><p>Retrieves help for an object</p></td>
</tr>
<tr class="row-odd"><td><p>SetField</p></td>
<td><p>Sat.SetField(“X”,0.0)</p></td>
<td><p>bool</p></td>
<td><p>Sets a field on an object</p></td>
</tr>
<tr class="row-even"><td><p>GetField</p></td>
<td><p>Sat.GetField(“X”)</p></td>
<td><p>string</p></td>
<td><p>Retrieves the setting for a field as a string</p></td>
</tr>
<tr class="row-odd"><td><p>GetNumber</p></td>
<td><p>Sat.GetNumber(“X”)</p></td>
<td><p>double</p></td>
<td><p>Retrieves the setting for numerical field</p></td>
</tr>
<tr class="row-even"><td><p>GetVector</p></td>
<td><p>Sat.GetVector(“X”)</p></td>
<td><p>vector</p></td>
<td><p>Retrieves the setting for Rvector field</p></td>
</tr>
<tr class="row-odd"><td><p>GetMatrix</p></td>
<td><p>Sat.GetMatrix(“X”)</p></td>
<td><p>matrix</p></td>
<td><p>Retrieves the setting for Rmatrix field</p></td>
</tr>
</tbody>
</table>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Functions Used in the GMAT API</a><ul>
<li><a class="reference internal" href="#general-api-functions">General API Functions</a></li>
<li><a class="reference internal" href="#gmat-object-methods">GMAT Object Methods</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="GettingStarted.html"
                          title="previous chapter">Object Usage with the GMAT API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="ScriptUsage.html"
                          title="next chapter">Script Usage</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/userguide/APIFunctions.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="ScriptUsage.html" title="Script Usage"
             >next</a> |</li>
        <li class="right" >
          <a href="GettingStarted.html" title="Object Usage with the GMAT API"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" >GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Functions Used in the GMAT API</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>