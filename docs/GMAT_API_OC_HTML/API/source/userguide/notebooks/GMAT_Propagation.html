
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Propagation with the GMAT API &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="Bibliography" href="../../Bibliography.html" />
    <link rel="prev" title="State Management with the GMAT API" href="GMAT_States.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../../Bibliography.html" title="Bibliography"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="GMAT_States.html" title="State Management with the GMAT API"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="notebooks.html" accesskey="U">API Notebook Walkthroughs</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Propagation with the GMAT API</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="propagation-with-the-gmat-api">
<h1>Propagation with the GMAT API<a class="headerlink" href="#propagation-with-the-gmat-api" title="Permalink to this heading">¶</a></h1>
<p>This document walks you through the configuration and use of the GMAT
API for propagation.</p>
<section id="prepare-the-gmat-environment">
<h2>Prepare the GMAT Environment<a class="headerlink" href="#prepare-the-gmat-environment" title="Permalink to this heading">¶</a></h2>
<p>Before the API can be used, it needs to be loaded into the Python system
and initialized using a GMAT startup file. This can be done from the
GMAT bin folder by importing the gmatpy module, but using that approach
tends to leave pieces in the bin folder that may annoy other users.
Running from an outside folder takes a few steps, which have been
captured in the run_gmat.py file imported here:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>from run_gmat import *
</pre></div>
</div>
</section>
<section id="configure-a-spacecraft">
<h2>Configure a Spacecraft<a class="headerlink" href="#configure-a-spacecraft" title="Permalink to this heading">¶</a></h2>
<p>We’ll need an object to propagate. Here’s a basic one:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>sat = gmat.Construct(&quot;Spacecraft&quot;,&quot;LeoSat&quot;)

sat.SetField(&quot;DateFormat&quot;, &quot;UTCGregorian&quot;)
sat.SetField(&quot;Epoch&quot;, &quot;27 Sep 2019 15:05:00.000&quot;)
sat.SetField(&quot;CoordinateSystem&quot;, &quot;EarthMJ2000Eq&quot;)
sat.SetField(&quot;DisplayStateType&quot;, &quot;Keplerian&quot;)
sat.SetField(&quot;SMA&quot;, 7005)
sat.SetField(&quot;ECC&quot;, 0.008)
sat.SetField(&quot;INC&quot;, 28.5)
sat.SetField(&quot;RAAN&quot;, 75)
sat.SetField(&quot;AOP&quot;, 90)
sat.SetField(&quot;TA&quot;, 45)

sat.SetField(&quot;DryMass&quot;, 50)
sat.SetField(&quot;Cd&quot;, 2.2)
sat.SetField(&quot;Cr&quot;, 1.8)
sat.SetField(&quot;DragArea&quot;, 1.5)
sat.SetField(&quot;SRPArea&quot;, 1.2)
</pre></div>
</div>
</section>
<section id="configure-the-forces">
<h2>Configure the Forces<a class="headerlink" href="#configure-the-forces" title="Permalink to this heading">¶</a></h2>
<p>Next we’ll set up a force model. For this example, we’ll use an Earth
8x8 potential model, with Sun and Moon point masses and Jacchia-Roberts
drag. In GMAT, forces are collected in the ODEModel class. That class is
scripted as a “ForceModel” in the script language. The API accepts
either. The force model is built and its (empty) contents displayed
using</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>fm = gmat.Construct(&quot;ForceModel&quot;, &quot;TheForces&quot;)
fm.Help()
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">ForceModel</span>  <span class="n">TheForces</span>

   <span class="n">Field</span>                                   <span class="n">Type</span>   <span class="n">Value</span>
   <span class="o">--------------------------------------------------------</span>

   <span class="n">CentralBody</span>                           <span class="n">Object</span>   <span class="n">Earth</span>
   <span class="n">PrimaryBodies</span>                    <span class="n">ObjectArray</span>   <span class="p">{}</span>
   <span class="n">PolyhedralBodies</span>                 <span class="n">ObjectArray</span>   <span class="p">{}</span>
   <span class="n">PointMasses</span>                      <span class="n">ObjectArray</span>   <span class="p">{}</span>
   <span class="n">Drag</span>                                  <span class="n">Object</span>   <span class="kc">None</span>
   <span class="n">SRP</span>                                    <span class="n">OnOff</span>   <span class="n">Off</span>
   <span class="n">RelativisticCorrection</span>                 <span class="n">OnOff</span>   <span class="n">Off</span>
   <span class="n">ErrorControl</span>                            <span class="n">List</span>   <span class="n">RSSStep</span>
   <span class="n">UserDefined</span>                      <span class="n">ObjectArray</span>   <span class="p">{}</span>
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="s1">&#39;&#39;</span>
</pre></div>
</div>
</section>
<section id="add-the-potential-field">
<h2>Add the Potential Field<a class="headerlink" href="#add-the-potential-field" title="Permalink to this heading">¶</a></h2>
<p>In this example, the spacecraft is in Earth orbit. The largest force for
the model is the Earth gravity field. We’ll set it to an 8x8 field and
add it to the force model using the code</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># An 8x8 JGM-3 Gravity Model
earthgrav = gmat.Construct(&quot;GravityField&quot;)
earthgrav.SetField(&quot;BodyName&quot;,&quot;Earth&quot;)
earthgrav.SetField(&quot;Degree&quot;,8)
earthgrav.SetField(&quot;Order&quot;,8)
earthgrav.SetField(&quot;PotentialFile&quot;,&quot;JGM2.cof&quot;)

# Add forces into the ODEModel container
fm.AddForce(earthgrav)
</pre></div>
</div>
</section>
<section id="add-the-other-forces">
<h2>Add the other forces<a class="headerlink" href="#add-the-other-forces" title="Permalink to this heading">¶</a></h2>
<p>Next we’ll build and add the Sun, Moon, and Drag forces, and then show
the completed force model.</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># The Point Masses
moongrav = gmat.Construct(&quot;PointMassForce&quot;)
moongrav.SetField(&quot;BodyName&quot;,&quot;Luna&quot;)
sungrav = gmat.Construct(&quot;PointMassForce&quot;)
sungrav.SetField(&quot;BodyName&quot;,&quot;Sun&quot;)

# Drag using Jacchia-Roberts
jrdrag = gmat.Construct(&quot;DragForce&quot;)
jrdrag.SetField(&quot;AtmosphereModel&quot;,&quot;JacchiaRoberts&quot;)

# Build and set the atmosphere for the model
atmos = gmat.Construct(&quot;JacchiaRoberts&quot;)
jrdrag.SetReference(atmos)

# Add all of the forces into the ODEModel container
fm.AddForce(moongrav)
fm.AddForce(sungrav)
fm.AddForce(jrdrag)

fm.Help()
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">ForceModel</span>  <span class="n">TheForces</span>

   <span class="n">Field</span>                                   <span class="n">Type</span>   <span class="n">Value</span>
   <span class="o">--------------------------------------------------------</span>

   <span class="n">CentralBody</span>                           <span class="n">Object</span>   <span class="n">Earth</span>
   <span class="n">PrimaryBodies</span>                    <span class="n">ObjectArray</span>   <span class="p">{</span><span class="n">Earth</span><span class="p">}</span>
   <span class="n">PolyhedralBodies</span>                 <span class="n">ObjectArray</span>   <span class="p">{}</span>
   <span class="n">PointMasses</span>                      <span class="n">ObjectArray</span>   <span class="p">{</span><span class="n">Luna</span><span class="p">,</span> <span class="n">Sun</span><span class="p">}</span>
   <span class="n">Drag</span>                                  <span class="n">Object</span>   <span class="n">JacchiaRoberts</span>
   <span class="n">SRP</span>                                    <span class="n">OnOff</span>   <span class="n">Off</span>
   <span class="n">RelativisticCorrection</span>                 <span class="n">OnOff</span>   <span class="n">Off</span>
   <span class="n">ErrorControl</span>                            <span class="n">List</span>   <span class="n">RSSStep</span>
   <span class="n">UserDefined</span>                      <span class="n">ObjectArray</span>   <span class="p">{}</span>
</pre></div>
</div>
<p>In GMAT, the force model scripting shows the settings for each force. In
the API, you can examine the settings for the individual forces:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>earthgrav.Help()
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">GravityField</span>

   <span class="n">Field</span>                                   <span class="n">Type</span>   <span class="n">Value</span>
   <span class="o">--------------------------------------------------------</span>

   <span class="n">Degree</span>                               <span class="n">Integer</span>   <span class="mi">8</span>
   <span class="n">Order</span>                                <span class="n">Integer</span>   <span class="mi">8</span>
   <span class="n">StmLimit</span>                             <span class="n">Integer</span>   <span class="mi">100</span>
   <span class="n">PotentialFile</span>                       <span class="n">Filename</span>   <span class="n">JGM2</span><span class="o">.</span><span class="n">cof</span>
   <span class="n">TideFile</span>                            <span class="n">Filename</span>
   <span class="n">TideModel</span>                             <span class="n">String</span>   <span class="kc">None</span>
</pre></div>
</div>
<p>or, with a little work, the scripting for the complete force model:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>print(fm.GetGeneratingString(0))
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span> <span class="n">ForceModel</span> <span class="n">TheForces</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">CentralBody</span> <span class="o">=</span> <span class="n">Earth</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">PrimaryBodies</span> <span class="o">=</span> <span class="p">{</span><span class="n">Earth</span><span class="p">};</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">PointMasses</span> <span class="o">=</span> <span class="p">{</span><span class="n">Luna</span><span class="p">,</span> <span class="n">Sun</span><span class="p">};</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">SRP</span> <span class="o">=</span> <span class="n">Off</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">RelativisticCorrection</span> <span class="o">=</span> <span class="n">Off</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">ErrorControl</span> <span class="o">=</span> <span class="n">RSSStep</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">GravityField</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">Degree</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">GravityField</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">Order</span> <span class="o">=</span> <span class="mi">8</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">GravityField</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">StmLimit</span> <span class="o">=</span> <span class="mi">100</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">GravityField</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">PotentialFile</span> <span class="o">=</span> <span class="s1">&#39;JGM2.cof&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">GravityField</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">TideModel</span> <span class="o">=</span> <span class="s1">&#39;None&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">AtmosphereModel</span> <span class="o">=</span> <span class="n">JacchiaRoberts</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">HistoricWeatherSource</span> <span class="o">=</span> <span class="s1">&#39;ConstantFluxAndGeoMag&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">PredictedWeatherSource</span> <span class="o">=</span> <span class="s1">&#39;ConstantFluxAndGeoMag&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">CSSISpaceWeatherFile</span> <span class="o">=</span> <span class="s1">&#39;SpaceWeather-All-v1.2.txt&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">SchattenFile</span> <span class="o">=</span> <span class="s1">&#39;SchattenPredict.txt&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">F107</span> <span class="o">=</span> <span class="mi">150</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">F107A</span> <span class="o">=</span> <span class="mi">150</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">MagneticIndex</span> <span class="o">=</span> <span class="mi">3</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">SchattenErrorModel</span> <span class="o">=</span> <span class="s1">&#39;Nominal&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">SchattenTimingModel</span> <span class="o">=</span> <span class="s1">&#39;NominalCycle&#39;</span><span class="p">;</span>
<span class="n">GMAT</span> <span class="n">TheForces</span><span class="o">.</span><span class="n">Drag</span><span class="o">.</span><span class="n">DragModel</span> <span class="o">=</span> <span class="s1">&#39;Spherical&#39;</span><span class="p">;</span>
</pre></div>
</div>
</section>
<section id="configure-the-integrator">
<h2>Configure the Integrator<a class="headerlink" href="#configure-the-integrator" title="Permalink to this heading">¶</a></h2>
<p>Finally, in order to propagate, we need an integrator. For this example,
we’ll use a Prince-Dormand 7(8) Runge-Kutta integrator. The propagator
is set using the code</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># Build the propagation container that connect the integrator, force model, and spacecraft together
pdprop = gmat.Construct(&quot;Propagator&quot;,&quot;PDProp&quot;)

# Create and assign a numerical integrator for use in the propagation
gator = gmat.Construct(&quot;PrinceDormand78&quot;, &quot;Gator&quot;)
pdprop.SetReference(gator)

# Set some of the fields for the integration
pdprop.SetField(&quot;InitialStepSize&quot;, 60.0)
pdprop.SetField(&quot;Accuracy&quot;, 1.0e-12)
pdprop.SetField(&quot;MinStep&quot;, 0.0)
</pre></div>
</div>
</section>
<section id="connect-the-objects-together">
<h2>Connect the Objects Together<a class="headerlink" href="#connect-the-objects-together" title="Permalink to this heading">¶</a></h2>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># Assign the force model imported from BasicFM
pdprop.SetReference(fm)

# Setup the spacecraft that is propagated
psm = pdprop.GetPropStateManager()
psm.SetObject(sat)
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kc">True</span>
</pre></div>
</div>
</section>
<section id="initialize-the-system-and-propagate-a-step">
<h2>Initialize the System and Propagate a Step<a class="headerlink" href="#initialize-the-system-and-propagate-a-step" title="Permalink to this heading">¶</a></h2>
<p>Finally, the system can be initialized and fired to see a single
propagation step. Some of the code displayed here will be folded into
the API’s Initialize() function. For now, the steps needed to initialize
the system for a propagation step are:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># Perform top level initialization
gmat.Initialize()

# Refresh the object references from the propagator clones
fm = pdprop.GetODEModel()
gator = pdprop.GetPropagator()

psm.BuildState()

# Pass the state manager to the dynamics model
fm.SetPropStateManager(psm)
fm.SetState(psm.GetState())

# Assemble all of the force model objects together
fm.Initialize()

# Finish the force model setup
fm.BuildModelFromMap()
fm.UpdateInitialData()

# Initialize the Propagator components
pdprop.Initialize()
gator.Initialize()
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="kc">True</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Alternatively, the above code can be replaced with a call to the
PrepareInternals() function on the propagator. This also removes the need to
manually configure the PropagationStateManager. The condensed code can be seen
below:</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># Perform top level initialization
gmat.Initialize()

# Setup the spacecraft that is propagated
pdprop.AddPropObject(sat)
pdprop.PrepareInternals()

# Refresh the object references from the propagator clones
fm = pdprop.GetODEModel()
gator = pdprop.GetPropagator()
</pre></div>
</div>
</div>
<p>and we can then propagate, and start accumulating the data</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span># Take a 60 second step, showing the state before and after, and start buffering
# Buffers for the data
time = []
pos = []
vel = []

gatorstate = gator.GetState()
t = 0.0
r = []
v = []
for j in range(3):
    r.append(gatorstate[j])
    v.append(gatorstate[j+3])
time.append(t)
pos.append(r)
vel.append(v)

print(&quot;Starting state:  &quot;, t, r, v)

# Take a step and buffer it
gator.Step(60.0)
gatorstate = gator.GetState()
t = t + 60.0
r = []
v = []
for j in range(3):
    r.append(gatorstate[j])
    v.append(gatorstate[j+3])
time.append(t)
pos.append(r)
vel.append(v)

print(&quot;Propped state:   &quot;, t, r, v)
</pre></div>
</div>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">Starting</span> <span class="n">state</span><span class="p">:</span>   <span class="mf">0.0</span> <span class="p">[</span><span class="o">-</span><span class="mf">5455.495852919224</span><span class="p">,</span> <span class="o">-</span><span class="mf">3637.0485868833093</span><span class="p">,</span> <span class="mf">2350.0571814448517</span><span class="p">]</span> <span class="p">[</span><span class="mf">3.1318014092807545</span><span class="p">,</span> <span class="o">-</span><span class="mf">6.423940627548709</span><span class="p">,</span> <span class="o">-</span><span class="mf">2.545227573417657</span><span class="p">]</span>
<span class="n">Propped</span> <span class="n">state</span><span class="p">:</span>    <span class="mf">60.0</span> <span class="p">[</span><span class="o">-</span><span class="mf">5256.1378692623475</span><span class="p">,</span> <span class="o">-</span><span class="mf">4014.4902800853415</span><span class="p">,</span> <span class="mf">2192.4488937848546</span><span class="p">]</span> <span class="p">[</span><span class="mf">3.51104729424038</span><span class="p">,</span> <span class="o">-</span><span class="mf">6.153042432990329</span><span class="p">,</span> <span class="o">-</span><span class="mf">2.7064897093764597</span><span class="p">]</span>
</pre></div>
</div>
<p>Finally, we can run for a few orbits and show the results</p>
<div class="highlight-ipython3 notranslate"><div class="highlight"><pre><span></span>for i in range(360):
    # Take a step and buffer it
    gator.Step(60.0)
    gatorstate = gator.GetState()
    t = t + 60.0
    r = []
    v = []
    for j in range(3):
        r.append(gatorstate[j])
        v.append(gatorstate[j+3])
    time.append(t)
    pos.append(r)
    vel.append(v)

import matplotlib.pyplot as plt
plt.rcParams[&#39;figure.figsize&#39;] = (15, 5)
plt.plot(time, pos)
plt.show()
plt.plot(time, vel)
plt.show()
</pre></div>
</div>
<img alt="../../../../_images/GMAT_Propagation_23_0.png" src="../../../../_images/GMAT_Propagation_23_0.png" />
<img alt="../../../../_images/GMAT_Propagation_23_1.png" src="../../../../_images/GMAT_Propagation_23_1.png" />
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Propagation with the GMAT API</a><ul>
<li><a class="reference internal" href="#prepare-the-gmat-environment">Prepare the GMAT Environment</a></li>
<li><a class="reference internal" href="#configure-a-spacecraft">Configure a Spacecraft</a></li>
<li><a class="reference internal" href="#configure-the-forces">Configure the Forces</a></li>
<li><a class="reference internal" href="#add-the-potential-field">Add the Potential Field</a></li>
<li><a class="reference internal" href="#add-the-other-forces">Add the other forces</a></li>
<li><a class="reference internal" href="#configure-the-integrator">Configure the Integrator</a></li>
<li><a class="reference internal" href="#connect-the-objects-together">Connect the Objects Together</a></li>
<li><a class="reference internal" href="#initialize-the-system-and-propagate-a-step">Initialize the System and Propagate a Step</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="GMAT_States.html"
                          title="previous chapter">State Management with the GMAT API</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../../Bibliography.html"
                          title="next chapter">Bibliography</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/notebooks/GMAT_Propagation.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../../Bibliography.html" title="Bibliography"
             >next</a> |</li>
        <li class="right" >
          <a href="GMAT_States.html" title="State Management with the GMAT API"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="notebooks.html" >API Notebook Walkthroughs</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Propagation with the GMAT API</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>