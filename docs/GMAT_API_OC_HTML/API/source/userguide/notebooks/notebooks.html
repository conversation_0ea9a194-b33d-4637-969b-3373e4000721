
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>API Notebook Walkthroughs &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="State Management with the GMAT API" href="GMAT_States.html" />
    <link rel="prev" title="GMAT API Cheat Sheet" href="../usage/CheatSheet.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="GMAT_States.html" title="State Management with the GMAT API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../usage/CheatSheet.html" title="GMAT API Cheat Sheet"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Notebook Walkthroughs</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="api-notebook-walkthroughs">
<h1>API Notebook Walkthroughs<a class="headerlink" href="#api-notebook-walkthroughs" title="Permalink to this heading">¶</a></h1>
<p>API builds include several <a class="reference internal" href="../../Bibliography.html#jupyter"><span class="std std-ref">[Jupyter]</span></a> notebooks illustrating
specific features of the interface.  These notebooks can be found in the
api/Jupyter folder of the GMAT build.  Users with access to the Jupyter system
can run these notebooks interactively.  Static versions of the notebooks are
included in this chapter.</p>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="GMAT_States.html">State Management with the GMAT API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="GMAT_States.html#prepare-the-gmat-environment">Prepare the GMAT Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_States.html#configure-a-spacecraft">Configure a Spacecraft</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_States.html#changing-coordinate-systems">Changing Coordinate Systems</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="GMAT_Propagation.html">Propagation with the GMAT API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#prepare-the-gmat-environment">Prepare the GMAT Environment</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#configure-a-spacecraft">Configure a Spacecraft</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#configure-the-forces">Configure the Forces</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#add-the-potential-field">Add the Potential Field</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#add-the-other-forces">Add the other forces</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#configure-the-integrator">Configure the Integrator</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#connect-the-objects-together">Connect the Objects Together</a></li>
<li class="toctree-l2"><a class="reference internal" href="GMAT_Propagation.html#initialize-the-system-and-propagate-a-step">Initialize the System and Propagate a Step</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../usage/CheatSheet.html"
                          title="previous chapter">GMAT API Cheat Sheet</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="GMAT_States.html"
                          title="next chapter">State Management with the GMAT API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/notebooks/notebooks.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="GMAT_States.html" title="State Management with the GMAT API"
             >next</a> |</li>
        <li class="right" >
          <a href="../usage/CheatSheet.html" title="GMAT API Cheat Sheet"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Notebook Walkthroughs</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>