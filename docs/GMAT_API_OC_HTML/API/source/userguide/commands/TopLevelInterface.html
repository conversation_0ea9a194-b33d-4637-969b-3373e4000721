
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>API Access to GMAT Commands &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../../_static/classic.css" />
    
    <script data-url_root="../../../../" id="documentation_options" src="../../../../_static/documentation_options.js"></script>
    <script src="../../../../_static/jquery.js"></script>
    <script src="../../../../_static/underscore.js"></script>
    <script src="../../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../../genindex.html" />
    <link rel="search" title="Search" href="../../../../search.html" />
    <link rel="next" title="Configuring a Command" href="SingleCommands.html" />
    <link rel="prev" title="Tutorial: Accessing GMAT Propagation and Navigation Features" href="../usage/UseCase1.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="SingleCommands.html" title="Configuring a Command"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../usage/UseCase1.html" title="Tutorial: Accessing GMAT Propagation and Navigation Features"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" accesskey="U">GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Access to GMAT Commands</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="api-access-to-gmat-commands">
<span id="toplevelinterface"></span><h1>API Access to GMAT Commands<a class="headerlink" href="#api-access-to-gmat-commands" title="Permalink to this heading">¶</a></h1>
<p><code class="xref std std-numref docutils literal notranslate"><span class="pre">RunScript</span></code> in the <span class="xref std std-ref">Introduction</span> shows how GMAT responds when a user executes a script.  The GMAT API provides a set of functions, described in this chapter, that reproduce the key features of this set of actions for API users.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>In the text that follows, the word “command” refers to a GmatCommand member of the Mission Control Sequence when referencing the GMAT application, and to a GmatCommand object when referencing the GMAT API.  A set of linked together commands is called a “sequence.”  The term “Mission Control Sequence,” abbreviated as “MCS,” is reserved for the linked list of commands that are executed when the GMAT application runs a script.</p>
</div>
<p>Running in GMAT, the objects created in a user’s script are copied into the GMAT Sandbox through a cloning process.  The scripted objects are preserved as scripted and not changed during the run.  Instead, clones of those objects are manipulated during the run.  This cloning is the action represented in the “Clone Resources” bubble in the diagram.  The Mission Control Sequence (MCS) linked list is not cloned.  The MCS linked list created when the script is parsed is directly assigned to the Sandbox used for the run.  The Sandbox is then initialized, resources first followed by the MCS commands.  This initialization process sets up the references between objects used in the run and sets up internal object data.  Resources are initialized first so that the internal object data manipulated by the control sequence is available when the sequence is initialized.  The Mission Control Sequence is then initialized by walking the initialization process through the linked list, initializing the commands in the same order as is followed when the sequence executes.  Once everything used in the run is initialized, the Mission Control Sequence is executed by walking through the linked list of commands, calling the Execute() method on each command until the end of the list is reached.  To summarize, a GMAT run performs these steps when a user runs a loaded script:</p>
<ol class="arabic simple">
<li><p>Clone resources into a Sandbox</p></li>
<li><p>Assign the first node of the Mission Control Sequence list to the Sandbox</p></li>
<li><p>Initialize the Resource objects in the Sandbox.  (This initialization follows a specific order of object initialization, the details of which are not important here.)</p></li>
<li><p>Initialize the Mission Control Sequence node by node</p></li>
<li><p>Execute the Mission Control Sequence</p></li>
</ol>
<p>There are several differences between running commands in the managed Sandbox environment of GMAT and running commands through the GMAT API.</p>
<ul class="simple">
<li><p>In the GMAT API, runs typically are performed on the configured objects directly.</p></li>
<li><p>Steps 1 and 2 above are not needed.</p></li>
<li><p>The Initialize() command familiar to API users performs step 3, initializing Resources.</p></li>
<li><p>Additional steps are preformed to execute commands</p></li>
</ul>
<p>The functions described below are designed to perform steps 4 and 5, addressing the “additional steps” referenced in the final bullet.</p>
<section id="api-functions-for-the-mcs">
<span id="apicommandfunctions"></span><h2>API Functions for the MCS<a class="headerlink" href="#api-functions-for-the-mcs" title="Permalink to this heading">¶</a></h2>
<p><a class="reference internal" href="#mcsfunctions"><span class="std std-ref">Control Sequence Functions</span></a> lists the core functions used to run commands in the GMAT API.  Information about each function is provided following the table.</p>
<span id="mcsfunctions"></span><table class="docutils align-default" id="id1">
<caption><span class="caption-number">Table 6 </span><span class="caption-text">Control Sequence Functions</span><a class="headerlink" href="#id1" title="Permalink to this table">¶</a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>Function</p></th>
<th class="head"><p>Example</p></th>
<th class="head"><p>Return Value</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Command</p></td>
<td><p>Command(type, desc)</p></td>
<td><p>GmatCommand</p></td>
<td><p>Creates a GmatCommand and adds it to the
end of the current sequence of GMAT Commands</p></td>
</tr>
<tr class="row-odd"><td><p>Execute</p></td>
<td><p>Execute()</p></td>
<td><p>Int</p></td>
<td><p>Runs the sequence of GMATCommands</p></td>
</tr>
<tr class="row-even"><td><p>Delete Command</p></td>
<td><p>DeleteCommand(cmd)</p></td>
<td><p>GmatCommand</p></td>
<td><p>Deletes the command, removing it from the sequence</p></td>
</tr>
</tbody>
</table>
<p><strong>Command</strong> creates a GmatCommand object using the type of command that is built followed by the script syntax used in the command. The code then chains through the current sequence of commands and places the object at the end of the list. For branch commands, the function inserts the command into the branch until the branch terminator is added, and then moves to the next element in the main list.</p>
<p><strong>Execute</strong> runs the current sequence of commands to completion.</p>
<p><strong>Delete Command</strong> Removes a command from the command sequence. If deleting branch command, it will remove and delete all children from the branch</p>
</section>
<section id="example-executing-a-command">
<h2>Example: Executing a Command<a class="headerlink" href="#example-executing-a-command" title="Permalink to this heading">¶</a></h2>
<p><a class="reference internal" href="#basicpropagate"><span class="std std-numref">Listing 3</span></a> sketches out Python scripting for a call to the GMAT Propagate command that moves a spacecraft from an initial state to perigee, scripted in GMAT like this:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">Propagate</span> <span class="n">Prop</span><span class="p">(</span><span class="n">Sat</span><span class="p">)</span> <span class="p">{</span><span class="n">Sat</span><span class="o">.</span><span class="n">Earth</span><span class="o">.</span><span class="n">Periapsis</span><span class="p">}</span>
</pre></div>
</div>
<p>The full listing for this script is given in <a class="reference internal" href="../usage/CommandsExample.html#propagatetoperigee"><span class="std std-numref">Listing 19</span></a>.</p>
<div class="literal-block-wrapper docutils container" id="id2">
<span id="basicpropagate"></span><div class="code-block-caption"><span class="caption-number">Listing 3 </span><span class="caption-text">Setting up and running a Propagate command</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">from</span> <span class="nn">load_gmat</span> <span class="kn">import</span> <span class="n">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Core resources used; Configuration omitted</span>
<span class="linenos"> 4</span><span class="n">sat</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Spacecraft&quot;</span><span class="p">,</span> <span class="s2">&quot;Sat&quot;</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">fm</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ForceModel&quot;</span><span class="p">,</span> <span class="s2">&quot;FM&quot;</span><span class="p">)</span>
<span class="linenos"> 6</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop&quot;</span><span class="p">)</span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="n">propagate</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Propagate&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop(Sat) </span><span class="si">{Sat.Periapsis}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="n">gmat</span><span class="o">.</span><span class="n">Execute</span><span class="p">()</span>
</pre></div>
</div>
</div>
</section>
<section id="example-executing-a-sequence">
<h2>Example: Executing a Sequence<a class="headerlink" href="#example-executing-a-sequence" title="Permalink to this heading">¶</a></h2>
<p>A basic GMAT script block for targeting is shown in <a class="reference internal" href="#atargetercontrolseq"><span class="std std-numref">Listing 4</span></a>.  This scripting targets a burn that moves a spacecraft from an initial orbit to Geosynchronous distance.</p>
<div class="literal-block-wrapper docutils container" id="id3">
<span id="atargetercontrolseq"></span><div class="code-block-caption"><span class="caption-number">Listing 4 </span><span class="caption-text">Setting up and running a Targeter</span><a class="headerlink" href="#id3" title="Permalink to this code">¶</a></div>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="c">% Create resources (configuration omitted)</span><span class="w"></span>
<span class="linenos"> 2</span><span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">Sat</span><span class="w"></span>
<span class="linenos"> 3</span><span class="n">Create</span><span class="w"> </span><span class="s">ForceModel</span><span class="w"> </span><span class="s">FM</span><span class="w"></span>
<span class="linenos"> 4</span><span class="n">Create</span><span class="w"> </span><span class="s">Propagator</span><span class="w"> </span><span class="s">Prop</span><span class="w"></span>
<span class="linenos"> 5</span><span class="n">Create</span><span class="w"> </span><span class="s">ImpulsiveBurn</span><span class="w"> </span><span class="s">Burn1</span><span class="w"></span>
<span class="linenos"> 6</span><span class="n">Create</span><span class="w"> </span><span class="s">DifferentialCorrectorDC</span><span class="w"></span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="n">BeginMissionSequence</span><span class="w"></span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="s">Target</span><span class="w"> </span><span class="s">DC</span><span class="w"></span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="n">Vary</span><span class="w"> </span><span class="s">DC(Burn1.Element1</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.5</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="n">Perturbation</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.0001</span><span class="p">,</span><span class="w"> </span><span class="n">MaxStep</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.2</span><span class="p">})</span><span class="w"></span>
<span class="linenos">13</span><span class="n">Maneuver</span><span class="w"> </span><span class="s">Burn1(Sat)</span><span class="w"></span>
<span class="linenos">14</span><span class="n">Propagate</span><span class="w"> </span><span class="s">Prop(Sat)</span><span class="w"> </span><span class="s">{Sat.Periapsis}</span><span class="w"></span>
<span class="linenos">15</span><span class="n">Achieve</span><span class="w"> </span><span class="s">DC(Sat.RMAG</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">42165.0</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="n">Tolerance</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mf">0.1</span><span class="p">})</span><span class="w"></span>
<span class="linenos">16</span>
<span class="linenos">17</span><span class="n">EndTarget</span><span class="w"></span>
</pre></div>
</div>
</div>
<p>The corresponding API configuration is shown in <a class="reference internal" href="#apitargeting"><span class="std std-numref">Listing 5</span></a>.</p>
<div class="literal-block-wrapper docutils container" id="id4">
<span id="apitargeting"></span><div class="code-block-caption"><span class="caption-number">Listing 5 </span><span class="caption-text">API Calls that Run a Targeter</span><a class="headerlink" href="#id4" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">from</span> <span class="nn">load_gmat</span> <span class="kn">import</span> <span class="n">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Core resources used; Configuration omitted</span>
<span class="linenos"> 4</span><span class="n">sat</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Spacecraft&quot;</span><span class="p">,</span> <span class="s2">&quot;Sat&quot;</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">fm</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ForceModel&quot;</span><span class="p">,</span> <span class="s2">&quot;FM&quot;</span><span class="p">)</span>
<span class="linenos"> 6</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop&quot;</span><span class="p">)</span>
<span class="linenos"> 7</span><span class="n">burn</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ImpulsiveBurn&quot;</span><span class="p">,</span> <span class="s2">&quot;Burn1&quot;</span><span class="p">)</span>
<span class="linenos"> 8</span><span class="n">dc</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;DifferentialCorrector&quot;</span><span class="p">,</span> <span class="s2">&quot;DC&quot;</span><span class="p">)</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="n">tcs</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Target&quot;</span><span class="p">,</span> <span class="s2">&quot;DC&quot;</span><span class="p">)</span>
<span class="linenos">11</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Vary&quot;</span><span class="p">,</span> <span class="s2">&quot;DC(Burn1.Element1 = 0.5, {Perturbation = 0.0001, MaxStep = 0.2})&quot;</span><span class="p">)</span>
<span class="linenos">12</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Maneuver&quot;</span><span class="p">,</span> <span class="s2">&quot;Burn1(Sat)&quot;</span><span class="p">)</span>
<span class="linenos">13</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Propagate&quot;</span><span class="p">,</span> <span class="s2">&quot;Prop(Sat) </span><span class="si">{Sat.Periapsis}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="linenos">14</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;Achieve&quot;</span><span class="p">,</span> <span class="s2">&quot;DC(Sat.RMAG = 42165.0, {Tolerance = -0.1})&quot;</span><span class="p">)</span>
<span class="linenos">15</span><span class="n">gmat</span><span class="o">.</span><span class="n">Command</span><span class="p">(</span><span class="s2">&quot;EndTarget&quot;</span><span class="p">)</span>
<span class="linenos">16</span>
<span class="linenos">17</span><span class="c1"># Setup</span>
<span class="linenos">18</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">19</span>
<span class="linenos">20</span><span class="c1"># and run</span>
<span class="linenos">21</span><span class="n">gmat</span><span class="o">.</span><span class="n">Execute</span><span class="p">()</span>
</pre></div>
</div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">API Access to GMAT Commands</a><ul>
<li><a class="reference internal" href="#api-functions-for-the-mcs">API Functions for the MCS</a></li>
<li><a class="reference internal" href="#example-executing-a-command">Example: Executing a Command</a></li>
<li><a class="reference internal" href="#example-executing-a-sequence">Example: Executing a Sequence</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../usage/UseCase1.html"
                          title="previous chapter">Tutorial: Accessing GMAT Propagation and Navigation Features</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="SingleCommands.html"
                          title="next chapter">Configuring a Command</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../../_sources/API/source/userguide/commands/TopLevelInterface.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="SingleCommands.html" title="Configuring a Command"
             >next</a> |</li>
        <li class="right" >
          <a href="../usage/UseCase1.html" title="Tutorial: Accessing GMAT Propagation and Navigation Features"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">API Access to GMAT Commands</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>