
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Object Usage with the GMAT API &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="Functions Used in the GMAT API" href="APIFunctions.html" />
    <link rel="prev" title="Conventions in the API Examples and Use Cases" href="StyleGuide.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="APIFunctions.html" title="Functions Used in the GMAT API"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="StyleGuide.html" title="Conventions in the API Examples and Use Cases"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" accesskey="U">GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Object Usage with the GMAT API</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="object-usage-with-the-gmat-api">
<h1>Object Usage with the GMAT API<a class="headerlink" href="#object-usage-with-the-gmat-api" title="Permalink to this heading">¶</a></h1>
<p>The goal of this section is to help you start using the GMAT API quickly, while
introducing features of the interface in a natural progression of steps needed
to solve a simple orbital state conversion problem.</p>
</section>
<section id="overview">
<h1>Overview<a class="headerlink" href="#overview" title="Permalink to this heading">¶</a></h1>
<p>The GMAT API is built using the Simplified Wrapper and Interface Generator,
SWIG.  SWIG connects code written in C and C++ with a variety of high level
languages.  The production GMAT API provides Python and Java connections for
GMAT functionality.  C++ programmers can also use the API specific code
interfaces through direct calls into the GMAT libraries.  The following sections
describe the provided interfaces.  Following that, three levels of interface
usage are described:</p>
<ol class="arabic simple">
<li><p>Usage based primarily on GMAT scripting, with API generated component changes.</p></li>
<li><p>High level usage of GMAT components to meet specific user needs.</p></li>
<li><p>Class and object level access to GMAT components for expert users.</p></li>
</ol>
<section id="gmat-api-interfaces">
<h2>GMAT API Interfaces<a class="headerlink" href="#gmat-api-interfaces" title="Permalink to this heading">¶</a></h2>
<p>The production GMAT API is built with Python and Java wrappers.  The Java
wrappers are used to also provide the MATLAB interface into the API.</p>
<section id="the-python-interface">
<h3>The Python Interface<a class="headerlink" href="#the-python-interface" title="Permalink to this heading">¶</a></h3>
<p>The Python wrappers are identified by the suffix “_py.”  Python imports are
provided as .py files, which connect to associated shared library files.  The
Python API is packaged in the gmatpy folder contained in the GMAT bin folder.
The API is loaded by importing that folder into the Python environment.</p>
</section>
<section id="the-java-and-matlab-interface">
<h3>The Java and MATLAB Interface<a class="headerlink" href="#the-java-and-matlab-interface" title="Permalink to this heading">¶</a></h3>
<p>The Java interface is provided in Java Archive (jar) files and associated
libraries.  These files include the GMAT base code API interfaces and the
interfaces to the components used in the navigation subsystem, tabulated below.</p>
<table class="docutils align-default" id="id1">
<caption><span class="caption-number">Table 2 </span><span class="caption-text">Java archives for the GMAT API</span><a class="headerlink" href="#id1" title="Permalink to this table">¶</a></caption>
<thead>
<tr class="row-odd"><th class="head"><p>Java Wrapper</p></th>
<th class="head"><p>Python Wrapper</p></th>
<th class="head"><p>Contents</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>gmat.jar</p></td>
<td><p>gmat_py.py</p></td>
<td><p>GMAT Base and utility code</p></td>
</tr>
<tr class="row-odd"><td><p>station.jar</p></td>
<td><p>station_py.py</p></td>
<td><p>Groundstation components</p></td>
</tr>
<tr class="row-even"><td><p>navigation.jar</p></td>
<td><p>navigation_py.py</p></td>
<td><p>Orbit determination components</p></td>
</tr>
</tbody>
</table>
<p>MATLAB users load the GMAT API by calling the load_gmat.m MATLAB script in the
GMAT bin folder.</p>
</section>
</section>
<section id="interface-complexity">
<h2>Interface Complexity<a class="headerlink" href="#interface-complexity" title="Permalink to this heading">¶</a></h2>
<p>The GMAT API is designed for three different styles of usage: Users working with
configurations based on GMAT scripting, users that work with GMAT objects
through API calls, and users that work at a low level with GMAT objects
directly.</p>
<section id="gmat-script-drivers">
<h3>GMAT Script Drivers<a class="headerlink" href="#gmat-script-drivers" title="Permalink to this heading">¶</a></h3>
<p>One use for the GMAT API is to act as a front end for GMAT mission runs.  In
this context, the user starts a runtime environment for the controlling
language (e.g. a Python session or MATLAB), loads and initializes GMAT using the
API, and then loads a GMAT script into the running environment.  At this point
the user might manipulate setting on the GMAT objects used in the script to
tailor the run.  Once the configuration is ready, the API is used to run the
script.</p>
<p>More details can be found in the <a class="reference internal" href="ScriptUsage.html#scriptusage"><span class="std std-ref">Script Usage</span></a> chapter.</p>
</section>
<section id="high-level-access">
<h3>High Level Access<a class="headerlink" href="#high-level-access" title="Permalink to this heading">¶</a></h3>
<p>A second use of the GMAT API is as a tool for using intermediate level GMAT
objects to model portions of an analysis problem, and to feed the modeled
results back to the driving system for further analysis.  When used this way,
the GMAT API provides proven and tested components used to meet the user’s needs
for building blocks for a problem running outside of GMAT.  Examples of this
type of usage include</p>
<ul class="simple">
<li><p>Converting state data from one coordinate system into another</p></li>
<li><p>Accessing the GMAT force models for accelerations and Jacobians of a given
state.</p></li>
<li><p>Accessing the Navigation measurement models for retrieve calculated
measurement values.</p></li>
</ul>
<p>The walk-through provided in the <a class="reference internal" href="usage/UseCase1.html#usecase1"><span class="std std-ref">Tutorial: Accessing GMAT Propagation and Navigation Features</span></a> chapter covers the techniques
needed for high level object access and usage.</p>
</section>
<section id="component-level-access">
<h3>Component Level Access<a class="headerlink" href="#component-level-access" title="Permalink to this heading">¶</a></h3>
<p>Some API users need access to the details of GMAT’s components in order to
control them at a fine-grained level during use, to extend them with new
computed data during use, or to monitor and report their state during use.
These users may want to configure the objects by hand, and may want to
manipulate the objects differently from the ways anticipated by the GMAT
developers.  The GMAT API allows for this level of access to GMAT’s components.
The <a class="reference internal" href="../Bibliography.html#doxygen"><span class="std std-ref">Doxygen</span></a> generated object level documentation provides a
guide to this type of usage.</p>
</section>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Object Usage with the GMAT API</a></li>
<li><a class="reference internal" href="#overview">Overview</a><ul>
<li><a class="reference internal" href="#gmat-api-interfaces">GMAT API Interfaces</a><ul>
<li><a class="reference internal" href="#the-python-interface">The Python Interface</a></li>
<li><a class="reference internal" href="#the-java-and-matlab-interface">The Java and MATLAB Interface</a></li>
</ul>
</li>
<li><a class="reference internal" href="#interface-complexity">Interface Complexity</a><ul>
<li><a class="reference internal" href="#gmat-script-drivers">GMAT Script Drivers</a></li>
<li><a class="reference internal" href="#high-level-access">High Level Access</a></li>
<li><a class="reference internal" href="#component-level-access">Component Level Access</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="StyleGuide.html"
                          title="previous chapter">Conventions in the API Examples and Use Cases</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="APIFunctions.html"
                          title="next chapter">Functions Used in the GMAT API</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/userguide/GettingStarted.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="APIFunctions.html" title="Functions Used in the GMAT API"
             >next</a> |</li>
        <li class="right" >
          <a href="StyleGuide.html" title="Conventions in the API Examples and Use Cases"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="UsersGuide.html" >GMAT API User’s Guide</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Object Usage with the GMAT API</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>