
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>To Be Removed: Review and Prototype Information &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">To Be Removed: Review and Prototype Information</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="to-be-removed-review-and-prototype-information">
<h1>To Be Removed: Review and Prototype Information<a class="headerlink" href="#to-be-removed-review-and-prototype-information" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="OpenIssues.html">Open issues</a></li>
<li class="toctree-l1"><a class="reference internal" href="ReviewActions.html">Review Responses</a><ul>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-01-unsafe-c-api">API RFA 01 - Unsafe C++ API</a></li>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-02-python-version-compatibility">API RFA 02 - Python version compatibility</a></li>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-03-handling-of-persistent-objects-in-gmat-memory-when-using-the-api">API RFA 03 - Handling of persistent objects in GMAT memory when using the API</a></li>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-04-complexity-of-create-interface">API RFA 04 - Complexity of “Create” Interface</a></li>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-05-rename-and-simplify-the-setup-command">API RFA 05 - Rename and simplify the Setup() command</a></li>
<li class="toctree-l2"><a class="reference internal" href="ReviewActions.html#api-rfa-06-api-style-guide">API RFA 06 - API Style Guide</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="PrototypeProductionComparison.html">Comparison of the SWIG Prototype with the Production API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="PrototypeProductionComparison.html#time-system-conversion">Time System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="PrototypeProductionComparison.html#coordinate-system-conversion">Coordinate System Conversion</a></li>
<li class="toctree-l2"><a class="reference internal" href="PrototypeProductionComparison.html#force-modeling">Force Modeling</a></li>
<li class="toctree-l2"><a class="reference internal" href="PrototypeProductionComparison.html#force-modeling-and-propagation">Force Modeling and Propagation</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="LessonsLearned.html">GMAT API Prototypes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="LessonsLearned.html#a-short-history-of-the-gmat-api">A short history of the GMAT API</a></li>
<li class="toctree-l2"><a class="reference internal" href="LessonsLearned.html#user-experiences">User Experiences</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="Stuff.html">GMAT Code Updates</a><ul>
<li class="toctree-l2"><a class="reference internal" href="Stuff.html#setfield-and-getfield">SetField() and GetField()</a></li>
<li class="toctree-l2"><a class="reference internal" href="Stuff.html#object-help">Object help</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="Stuff.html#api-support-functions">API Support Functions</a></li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/appendix/ReviewInfo.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">To Be Removed: Review and Prototype Information</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>