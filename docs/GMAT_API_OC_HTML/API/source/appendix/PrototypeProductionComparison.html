
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Comparison of the SWIG Prototype with the Production API &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Comparison of the SWIG Prototype with the Production API</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="comparison-of-the-swig-prototype-with-the-production-api">
<span id="swigoldandnew"></span><h1>Comparison of the SWIG Prototype with the Production API<a class="headerlink" href="#comparison-of-the-swig-prototype-with-the-production-api" title="Permalink to this heading">¶</a></h1>
<p>The prototype SWIG generated API developed in 2016 can be used with GMAT R2018.
This appendix shows the differences between that version of the SWIG generated
API and the production API presented in this document for the three examples
described in <span class="xref std std-ref">DesignExamples</span>.</p>
<section id="time-system-conversion">
<h2>Time System Conversion<a class="headerlink" href="#time-system-conversion" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id1">
<div class="code-block-caption"><span class="caption-text">Time System Conversion using GMAT R2018a SWIG Configuration</span><a class="headerlink" href="#id1" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Initializing the Moderator configures the converter</span>
<span class="linenos"> 4</span><span class="n">gmat</span><span class="o">.</span><span class="n">Moderator</span><span class="o">.</span><span class="n">Instance</span><span class="p">()</span><span class="o">.</span><span class="n">Initialize</span><span class="p">(</span><span class="s1">&#39;gmat_startup_file.txt&#39;</span><span class="p">)</span>
<span class="linenos"> 5</span>
<span class="linenos"> 6</span><span class="c1"># Get the converter</span>
<span class="linenos"> 7</span><span class="n">timeConverter</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">TimeSystemConverter</span><span class="o">.</span><span class="n">Instance</span><span class="p">()</span>
<span class="linenos"> 8</span>
<span class="linenos"> 9</span><span class="c1"># Convert an epoch</span>
<span class="linenos">10</span><span class="n">UTCEpoch</span> <span class="o">=</span> <span class="mf">21738.22145</span>
<span class="linenos">11</span><span class="n">TAIepoch</span> <span class="o">=</span> <span class="n">timeConverter</span><span class="o">.</span><span class="n">Convert</span><span class="p">(</span><span class="n">UTCepoch</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id2">
<div class="code-block-caption"><span class="caption-text">Time System Conversion using the Production API</span><a class="headerlink" href="#id2" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos">1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos">2</span>
<span class="linenos">3</span><span class="c1"># Get the converter</span>
<span class="linenos">4</span><span class="n">timeConverter</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">theTimeSystemConverter</span>
<span class="linenos">5</span>
<span class="linenos">6</span><span class="c1"># Convert an epoch</span>
<span class="linenos">7</span><span class="n">UTCEpoch</span> <span class="o">=</span> <span class="mf">21738.22145</span>
<span class="linenos">8</span><span class="n">TAIepoch</span> <span class="o">=</span> <span class="n">timeConverter</span><span class="o">.</span><span class="n">Convert</span><span class="p">(</span><span class="n">UTCepoch</span><span class="p">,</span> <span class="n">UTC</span><span class="p">,</span> <span class="n">TAI</span><span class="p">)</span>
</pre></div>
</div>
</div>
</section>
<section id="coordinate-system-conversion">
<h2>Coordinate System Conversion<a class="headerlink" href="#coordinate-system-conversion" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id3">
<div class="code-block-caption"><span class="caption-text">Coordinate System Conversion using GMAT R2018a SWIG Configuration</span><a class="headerlink" href="#id3" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Initialize to set default objects needed to configure the converter</span>
<span class="linenos"> 4</span><span class="n">mod</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Moderator</span><span class="o">.</span><span class="n">Instance</span><span class="p">()</span>
<span class="linenos"> 5</span><span class="n">mod</span><span class="o">.</span><span class="n">Initialize</span><span class="p">(</span><span class="s1">&#39;gmat_startup_file.txt&#39;</span><span class="p">)</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="c1"># Setup the GMAT data structures for the conversion</span>
<span class="linenos"> 8</span><span class="n">mjd</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">A1Mjd</span><span class="p">(</span><span class="mf">22326.977184</span><span class="p">)</span>
<span class="linenos"> 9</span><span class="n">rvIn</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">6988.426918</span><span class="p">,</span> <span class="mf">1073.884261</span><span class="p">,</span> <span class="mf">2247.332981</span><span class="p">,</span> <span class="mf">0.019982</span><span class="p">,</span> <span class="mf">7.226988</span><span class="p">,</span> <span class="o">-</span><span class="mf">1.554962</span><span class="p">)</span>
<span class="linenos">10</span><span class="n">rvOut</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="c1"># Create the converter</span>
<span class="linenos">13</span><span class="n">csConverter</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateConverter</span><span class="p">()</span>
<span class="linenos">14</span>
<span class="linenos">15</span><span class="c1"># Get the solar system and central body</span>
<span class="linenos">16</span><span class="n">ss</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">GetSolarSystemInUse</span><span class="p">()</span>
<span class="linenos">17</span><span class="n">earth</span> <span class="o">=</span> <span class="n">ss</span><span class="o">.</span><span class="n">GetBody</span><span class="p">(</span><span class="s2">&quot;Earth&quot;</span><span class="p">)</span>
<span class="linenos">18</span>
<span class="linenos">19</span><span class="c1"># Create the input and output coordinate systems</span>
<span class="linenos">20</span><span class="n">eci</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateSystem</span><span class="o">.</span><span class="n">CreateLocalCoordinateSystem</span><span class="p">(</span>
<span class="linenos">21</span>    <span class="s2">&quot;ECI&quot;</span><span class="p">,</span> <span class="s2">&quot;MJ2000Eq&quot;</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="n">ss</span><span class="p">)</span>
<span class="linenos">22</span><span class="n">ecef</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateSystem</span><span class="o">.</span><span class="n">CreateLocalCoordinateSystem</span><span class="p">(</span>
<span class="linenos">23</span>    <span class="s2">&quot;ECEF&quot;</span><span class="p">,</span> <span class="s2">&quot;BodyFixed&quot;</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="n">ss</span><span class="p">)</span>
<span class="linenos">24</span>
<span class="linenos">25</span><span class="n">csConverter</span><span class="o">.</span><span class="n">Convert</span><span class="p">(</span><span class="n">UTCepoch</span><span class="p">,</span> <span class="n">rvIn</span><span class="p">,</span> <span class="n">eci</span><span class="p">,</span> <span class="n">rvOut</span><span class="p">,</span> <span class="n">ecef</span><span class="p">)</span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id4">
<div class="code-block-caption"><span class="caption-text">Coordinate System Conversion using the Production API</span><a class="headerlink" href="#id4" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Setup the GMAT data structures for the conversion</span>
<span class="linenos"> 4</span><span class="n">mjd</span>   <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">A1Mjd</span><span class="p">(</span><span class="mf">22326.977184</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">rvIn</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">6988.426918</span><span class="p">,</span> <span class="mf">1073.884261</span><span class="p">,</span> <span class="mf">2247.332981</span><span class="p">,</span> <span class="mf">0.019982</span><span class="p">,</span> <span class="mf">7.226988</span><span class="p">,</span> <span class="o">-</span><span class="mf">1.554962</span><span class="p">)</span>
<span class="linenos"> 6</span><span class="n">rvOut</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Rvector6</span><span class="p">(</span><span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="c1"># Create the converter</span>
<span class="linenos"> 9</span><span class="n">csConverter</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateConverter</span><span class="p">()</span>
<span class="linenos">10</span>
<span class="linenos">11</span><span class="c1"># Create the input and output coordinate systems</span>
<span class="linenos">12</span><span class="n">eci</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;CoordinateSystem&quot;</span><span class="p">,</span>
<span class="linenos">13</span>    <span class="s2">&quot;ECI&quot;</span><span class="p">,</span> <span class="s2">&quot;Earth&quot;</span><span class="p">,</span> <span class="s2">&quot;MJ2000Eq&quot;</span><span class="p">)</span>
<span class="linenos">14</span><span class="n">ecef</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;CoordinateSystem&quot;</span><span class="p">,</span>
<span class="linenos">15</span>    <span class="s2">&quot;ECEF&quot;</span><span class="p">,</span> <span class="s2">&quot;Earth&quot;</span><span class="p">,</span> <span class="s2">&quot;BodyFixed&quot;</span><span class="p">)</span>
<span class="linenos">16</span>
<span class="linenos">17</span><span class="n">csConverter</span><span class="o">.</span><span class="n">Convert</span><span class="p">(</span><span class="n">UTCepoch</span><span class="p">,</span> <span class="n">rvIn</span><span class="p">,</span> <span class="n">eci</span><span class="p">,</span> <span class="n">rvOut</span><span class="p">,</span> <span class="n">ecef</span><span class="p">)</span>
</pre></div>
</div>
</div>
</section>
<section id="force-modeling">
<h2>Force Modeling<a class="headerlink" href="#force-modeling" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id5">
<div class="code-block-caption"><span class="caption-text">Force modeling using GMAT R2018a SWIG Configuration</span><a class="headerlink" href="#id5" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span><span class="n">mod</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Moderator</span><span class="o">.</span><span class="n">Instance</span><span class="p">()</span>
<span class="linenos"> 3</span><span class="n">mod</span><span class="o">.</span><span class="n">Initialize</span><span class="p">(</span><span class="s1">&#39;gmat_startup_file.txt&#39;</span><span class="p">)</span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="c1"># Spacecraft setup</span>
<span class="linenos"> 6</span><span class="n">sc</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Spacecraft</span><span class="p">(</span><span class="s2">&quot;sc&quot;</span><span class="p">)</span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="c1"># Evaluating only the 6 element Cartesian state</span>
<span class="linenos"> 9</span><span class="n">state</span> <span class="o">=</span> <span class="p">[</span><span class="mf">7000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">1000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">8.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">0.25</span><span class="p">]</span>
<span class="linenos">10</span><span class="n">pstate</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">new_doubleArray</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span>
<span class="linenos">11</span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">state</span><span class="p">)):</span>
<span class="linenos">12</span>    <span class="n">gmat</span><span class="o">.</span><span class="n">doubleArray_setitem</span><span class="p">(</span><span class="n">pstate</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">state</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
<span class="linenos">13</span>
<span class="linenos">14</span><span class="c1"># Internal required objects</span>
<span class="linenos">15</span><span class="n">ss</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">GetDefaultSolarSystem</span><span class="p">()</span>
<span class="linenos">16</span><span class="n">earth</span> <span class="o">=</span> <span class="n">ss</span><span class="o">.</span><span class="n">GetBody</span><span class="p">(</span><span class="s2">&quot;Earth&quot;</span><span class="p">)</span>
<span class="linenos">17</span><span class="n">eci</span>  <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">CoordinateSystem</span><span class="o">.</span><span class="n">CreateLocalCoordinateSystem</span><span class="p">(</span>
<span class="linenos">18</span>        <span class="s2">&quot;ECI&quot;</span><span class="p">,</span> <span class="s2">&quot;MJ2000Eq&quot;</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span><span class="p">,</span> <span class="n">earth</span><span class="p">,</span> <span class="n">ss</span><span class="p">)</span>
<span class="linenos">19</span>
<span class="linenos">20</span><span class="c1"># The state manager</span>
<span class="linenos">21</span><span class="n">psm</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PropagationStateManager</span><span class="p">()</span>
<span class="linenos">22</span><span class="n">psm</span><span class="o">.</span><span class="n">SetObject</span><span class="p">(</span><span class="n">sc</span><span class="p">)</span>
<span class="linenos">23</span><span class="n">psm</span><span class="o">.</span><span class="n">BuildState</span><span class="p">()</span>
<span class="linenos">24</span><span class="n">psm</span><span class="o">.</span><span class="n">MapObjectsToVector</span><span class="p">()</span>
<span class="linenos">25</span>
<span class="linenos">26</span><span class="c1"># The force model</span>
<span class="linenos">27</span><span class="n">dynamics</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">ODEModel</span><span class="p">(</span><span class="s2">&quot;EPointMassDynamics&quot;</span><span class="p">)</span>
<span class="linenos">28</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetForceOrigin</span><span class="p">(</span><span class="n">earth</span><span class="p">)</span>
<span class="linenos">29</span>
<span class="linenos">30</span><span class="n">epm</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PointMassForce</span><span class="p">(</span><span class="s2">&quot;EarthPointMass&quot;</span><span class="p">)</span>
<span class="linenos">31</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos">32</span>
<span class="linenos">33</span><span class="c1"># Manage memory - dynamics now owns the epm, so Python should not delete it</span>
<span class="linenos">34</span><span class="n">epm</span><span class="o">.</span><span class="n">thisown</span> <span class="o">=</span> <span class="mi">0</span>
<span class="linenos">35</span>
<span class="linenos">36</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetPropStateManager</span><span class="p">(</span><span class="n">psm</span><span class="p">)</span>
<span class="linenos">37</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetSolarSystem</span><span class="p">(</span><span class="n">ss</span><span class="p">)</span>
<span class="linenos">38</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetInternalCoordSystem</span><span class="p">(</span><span class="n">eci</span><span class="p">)</span>
<span class="linenos">39</span><span class="n">dynamics</span><span class="o">.</span><span class="n">BufferState</span><span class="p">()</span>
<span class="linenos">40</span><span class="n">dynamics</span><span class="o">.</span><span class="n">BuildModelFromMap</span><span class="p">()</span>
<span class="linenos">41</span>
<span class="linenos">42</span><span class="n">dynamics</span><span class="o">.</span><span class="n">UpdateInitialData</span><span class="p">()</span>
<span class="linenos">43</span><span class="n">dynamics</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">44</span>
<span class="linenos">45</span><span class="n">pderiv</span> <span class="o">=</span> <span class="n">dynamics</span><span class="o">.</span><span class="n">GetDerivativeArray</span><span class="p">()</span>
<span class="linenos">46</span><span class="n">dynamics</span><span class="o">.</span><span class="n">GetDerivatives</span><span class="p">(</span><span class="n">pstate</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id6">
<div class="code-block-caption"><span class="caption-text">Force Modeling using the Production API</span><a class="headerlink" href="#id6" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="n">dynamics</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ODEModel&quot;</span><span class="p">,</span> <span class="s2">&quot;EPointMassDynamics&quot;</span><span class="p">)</span>
<span class="linenos"> 4</span><span class="n">epm</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PointMassForce</span><span class="p">(</span><span class="s2">&quot;EarthPointMass&quot;</span><span class="p">)</span>
<span class="linenos"> 5</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos"> 6</span>
<span class="linenos"> 7</span><span class="c1"># Evaluating only the 6 element Cartesian state</span>
<span class="linenos"> 8</span><span class="n">pstate</span> <span class="o">=</span> <span class="p">[</span><span class="mf">7000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">1000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">8.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">0.25</span><span class="p">]</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="n">pderiv</span> <span class="o">=</span> <span class="n">dynamics</span><span class="o">.</span><span class="n">GetDerivativeArray</span><span class="p">()</span>
<span class="linenos">13</span><span class="n">dynamics</span><span class="o">.</span><span class="n">GetDerivatives</span><span class="p">(</span><span class="n">pstate</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">)</span>
</pre></div>
</div>
</div>
</section>
<section id="force-modeling-and-propagation">
<h2>Force Modeling and Propagation<a class="headerlink" href="#force-modeling-and-propagation" title="Permalink to this heading">¶</a></h2>
<div class="literal-block-wrapper docutils container" id="id7">
<div class="code-block-caption"><span class="caption-text">Propagation using GMAT R2018a SWIG Configuration</span><a class="headerlink" href="#id7" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span><span class="n">mod</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Moderator</span><span class="o">.</span><span class="n">Instance</span><span class="p">()</span>
<span class="linenos"> 3</span><span class="n">mod</span><span class="o">.</span><span class="n">Initialize</span><span class="p">(</span><span class="s1">&#39;gmat_startup_file.txt&#39;</span><span class="p">)</span>
<span class="linenos"> 4</span>
<span class="linenos"> 5</span><span class="c1"># Setup the state for propagation</span>
<span class="linenos"> 6</span><span class="n">state</span> <span class="o">=</span> <span class="p">[</span><span class="mf">7000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">1000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">8.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">0.25</span><span class="p">]</span>
<span class="linenos"> 7</span>
<span class="linenos"> 8</span><span class="c1"># Load some pieces we need to configure the system</span>
<span class="linenos"> 9</span><span class="n">ss</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">GetDefaultSolarSystem</span><span class="p">()</span>
<span class="linenos">10</span><span class="n">sc</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Spacecraft</span><span class="p">(</span><span class="s2">&quot;sc&quot;</span><span class="p">)</span>
<span class="linenos">11</span>
<span class="linenos">12</span><span class="n">psm</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PropagationStateManager</span><span class="p">()</span>
<span class="linenos">13</span><span class="n">psm</span><span class="o">.</span><span class="n">SetObject</span><span class="p">(</span><span class="n">sc</span><span class="p">)</span>
<span class="linenos">14</span><span class="n">psm</span><span class="o">.</span><span class="n">BuildState</span><span class="p">()</span>
<span class="linenos">15</span><span class="n">psm</span><span class="o">.</span><span class="n">MapObjectsToVector</span><span class="p">()</span>
<span class="linenos">16</span>
<span class="linenos">17</span><span class="c1"># Setup a Earth/Sun/Moon force model</span>
<span class="linenos">18</span><span class="c1"># note: Use Moderator for the forces and Python memory management won&#39;t segfault</span>
<span class="linenos">19</span><span class="n">dynamics</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">ODEModel</span><span class="p">(</span><span class="s2">&quot;Forces&quot;</span><span class="p">)</span>
<span class="linenos">20</span>
<span class="linenos">21</span><span class="n">epm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">CreatePhysicalModel</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;EarthPointMass&quot;</span><span class="p">)</span>
<span class="linenos">22</span><span class="n">spm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">CreatePhysicalModel</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;SunPointMass&quot;</span><span class="p">)</span>
<span class="linenos">23</span><span class="n">mpm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">CreatePhysicalModel</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;MoonPointMass&quot;</span><span class="p">)</span>
<span class="linenos">24</span><span class="n">spm</span><span class="o">.</span><span class="n">SetStringParameter</span><span class="p">(</span><span class="s2">&quot;BodyName&quot;</span><span class="p">,</span> <span class="s2">&quot;Sun&quot;</span><span class="p">)</span>
<span class="linenos">25</span><span class="n">mpm</span><span class="o">.</span><span class="n">SetStringParameter</span><span class="p">(</span><span class="s2">&quot;BodyName&quot;</span><span class="p">,</span> <span class="s2">&quot;Luna&quot;</span><span class="p">)</span>
<span class="linenos">26</span>
<span class="linenos">27</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos">28</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">spm</span><span class="p">)</span>
<span class="linenos">29</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">mpm</span><span class="p">)</span>
<span class="linenos">30</span>
<span class="linenos">31</span><span class="c1"># Manage memory</span>
<span class="linenos">32</span><span class="n">epm</span><span class="o">.</span><span class="n">thisown</span> <span class="o">=</span> <span class="mi">0</span>
<span class="linenos">33</span><span class="n">spm</span><span class="o">.</span><span class="n">thisown</span> <span class="o">=</span> <span class="mi">0</span>
<span class="linenos">34</span><span class="n">mpm</span><span class="o">.</span><span class="n">thisown</span> <span class="o">=</span> <span class="mi">0</span>
<span class="linenos">35</span>
<span class="linenos">36</span><span class="c1"># Reference object setup</span>
<span class="linenos">37</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetPropStateManager</span><span class="p">(</span><span class="n">psm</span><span class="p">)</span>
<span class="linenos">38</span><span class="n">dynamics</span><span class="o">.</span><span class="n">SetSolarSystem</span><span class="p">(</span><span class="n">ss</span><span class="p">)</span>
<span class="linenos">39</span>
<span class="linenos">40</span><span class="c1"># ODE model initialization</span>
<span class="linenos">41</span><span class="n">dynamics</span><span class="o">.</span><span class="n">BufferState</span><span class="p">()</span>
<span class="linenos">42</span><span class="n">dynamics</span><span class="o">.</span><span class="n">BuildModelFromMap</span><span class="p">()</span>
<span class="linenos">43</span><span class="n">dynamics</span><span class="o">.</span><span class="n">UpdateInitialData</span><span class="p">()</span>
<span class="linenos">44</span><span class="n">rv</span> <span class="o">=</span> <span class="n">dynamics</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">45</span>
<span class="linenos">46</span><span class="c1"># Propagator configuration</span>
<span class="linenos">47</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PrinceDormand78</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">)</span>
<span class="linenos">48</span><span class="n">prop</span><span class="o">.</span><span class="n">SetSolarSystem</span><span class="p">(</span><span class="n">ss</span><span class="p">)</span>
<span class="linenos">49</span><span class="n">prop</span><span class="o">.</span><span class="n">SetPropStateManager</span><span class="p">(</span><span class="n">psm</span><span class="p">)</span>
<span class="linenos">50</span><span class="n">prop</span><span class="o">.</span><span class="n">SetPhysicalModel</span><span class="p">(</span><span class="n">dynamics</span><span class="p">)</span>
<span class="linenos">51</span><span class="n">prop</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">52</span>
<span class="linenos">53</span><span class="c1"># Set the propagation state</span>
<span class="linenos">54</span><span class="n">pstate</span> <span class="o">=</span> <span class="n">dynamics</span><span class="o">.</span><span class="n">GetState</span><span class="p">()</span>
<span class="linenos">55</span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">state</span><span class="p">)):</span>
<span class="linenos">56</span>    <span class="n">gmat</span><span class="o">.</span><span class="n">doubleArray_setitem</span><span class="p">(</span><span class="n">pstate</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">state</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
<span class="linenos">57</span>
<span class="linenos">58</span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">count</span><span class="p">):</span>
<span class="linenos">59</span>    <span class="n">prop</span><span class="o">.</span><span class="n">Step</span><span class="p">(</span><span class="mf">60.0</span><span class="p">)</span>
</pre></div>
</div>
</div>
<div class="literal-block-wrapper docutils container" id="id8">
<div class="code-block-caption"><span class="caption-text">Propagation using the Production API</span><a class="headerlink" href="#id8" title="Permalink to this code">¶</a></div>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="linenos"> 1</span><span class="kn">import</span> <span class="nn">gmat_py</span> <span class="k">as</span> <span class="nn">gmat</span>
<span class="linenos"> 2</span>
<span class="linenos"> 3</span><span class="c1"># Setup the state for propagation</span>
<span class="linenos"> 4</span><span class="n">state</span> <span class="o">=</span> <span class="p">[</span><span class="mf">7000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">1000.0</span><span class="p">,</span> <span class="mf">0.0</span><span class="p">,</span> <span class="mf">8.0</span><span class="p">,</span> <span class="o">-</span><span class="mf">0.25</span><span class="p">]</span>
<span class="linenos"> 5</span>
<span class="linenos"> 6</span><span class="c1"># Setup a Earth/Sun/Moon force model</span>
<span class="linenos"> 7</span><span class="c1"># note: Use Moderator for the forces and Python memory management won&#39;t segfault</span>
<span class="linenos"> 8</span><span class="n">dynamics</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">Construct</span><span class="p">(</span><span class="s2">&quot;ODEModel&quot;</span><span class="p">,</span> <span class="s2">&quot;Forces&quot;</span><span class="p">)</span>
<span class="linenos"> 9</span>
<span class="linenos">10</span><span class="n">epm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">Create</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;EarthPointMass&quot;</span><span class="p">)</span>
<span class="linenos">11</span><span class="n">spm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">Create</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;SunPointMass&quot;</span><span class="p">)</span>
<span class="linenos">12</span><span class="n">mpm</span> <span class="o">=</span> <span class="n">mod</span><span class="o">.</span><span class="n">Create</span><span class="p">(</span><span class="s2">&quot;PointMassForce&quot;</span><span class="p">,</span> <span class="s2">&quot;MoonPointMass&quot;</span><span class="p">)</span>
<span class="linenos">13</span><span class="n">spm</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;BodyName&quot;</span><span class="p">,</span> <span class="s2">&quot;Sun&quot;</span><span class="p">)</span>
<span class="linenos">14</span><span class="n">mpm</span><span class="o">.</span><span class="n">SetField</span><span class="p">(</span><span class="s2">&quot;BodyName&quot;</span><span class="p">,</span> <span class="s2">&quot;Luna&quot;</span><span class="p">)</span>
<span class="linenos">15</span>
<span class="linenos">16</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">epm</span><span class="p">)</span>
<span class="linenos">17</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">spm</span><span class="p">)</span>
<span class="linenos">18</span><span class="n">dynamics</span><span class="o">.</span><span class="n">AddForce</span><span class="p">(</span><span class="n">mpm</span><span class="p">)</span>
<span class="linenos">19</span>
<span class="linenos">20</span><span class="c1"># Propagator configuration</span>
<span class="linenos">21</span><span class="n">prop</span> <span class="o">=</span> <span class="n">gmat</span><span class="o">.</span><span class="n">PrinceDormand78</span><span class="p">(</span><span class="s2">&quot;Propagator&quot;</span><span class="p">)</span>
<span class="linenos">22</span><span class="n">prop</span><span class="o">.</span><span class="n">SetPhysicalModel</span><span class="p">(</span><span class="n">dynamics</span><span class="p">)</span>
<span class="linenos">23</span>
<span class="linenos">24</span><span class="n">gmat</span><span class="o">.</span><span class="n">Initialize</span><span class="p">()</span>
<span class="linenos">25</span>
<span class="linenos">26</span><span class="c1"># Set the propagation state</span>
<span class="linenos">27</span><span class="n">pstate</span> <span class="o">=</span> <span class="n">dynamics</span><span class="o">.</span><span class="n">GetState</span><span class="p">()</span>
<span class="linenos">28</span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="nb">len</span><span class="p">(</span><span class="n">state</span><span class="p">)):</span>
<span class="linenos">29</span>    <span class="n">gmat</span><span class="o">.</span><span class="n">doubleArray_setitem</span><span class="p">(</span><span class="n">pstate</span><span class="p">,</span> <span class="n">i</span><span class="p">,</span> <span class="n">state</span><span class="p">[</span><span class="n">i</span><span class="p">])</span>
<span class="linenos">30</span>
<span class="linenos">31</span><span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="n">count</span><span class="p">):</span>
<span class="linenos">32</span>    <span class="n">prop</span><span class="o">.</span><span class="n">Step</span><span class="p">(</span><span class="mf">60.0</span><span class="p">)</span>
</pre></div>
</div>
</div>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">Comparison of the SWIG Prototype with the Production API</a><ul>
<li><a class="reference internal" href="#time-system-conversion">Time System Conversion</a></li>
<li><a class="reference internal" href="#coordinate-system-conversion">Coordinate System Conversion</a></li>
<li><a class="reference internal" href="#force-modeling">Force Modeling</a></li>
<li><a class="reference internal" href="#force-modeling-and-propagation">Force Modeling and Propagation</a></li>
</ul>
</li>
</ul>

  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/appendix/PrototypeProductionComparison.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Comparison of the SWIG Prototype with the Production API</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>