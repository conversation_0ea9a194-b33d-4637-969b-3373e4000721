
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>The API Design &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../../_static/classic.css" />
    
    <script data-url_root="../../../" id="documentation_options" src="../../../_static/documentation_options.js"></script>
    <script src="../../../_static/jquery.js"></script>
    <script src="../../../_static/underscore.js"></script>
    <script src="../../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../../genindex.html" />
    <link rel="search" title="Search" href="../../../search.html" />
    <link rel="next" title="GMAT API User’s Guide" href="../userguide/UsersGuide.html" />
    <link rel="prev" title="GMAT Architectural Components" href="Architecture.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="../userguide/UsersGuide.html" title="GMAT API User’s Guide"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="Architecture.html" title="GMAT Architectural Components"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Design.html" accesskey="U">System Design</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The API Design</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="the-api-design">
<h1>The API Design<a class="headerlink" href="#the-api-design" title="Permalink to this heading">¶</a></h1>
<p><a class="reference internal" href="Architecture.html#gmatstack"><span class="std std-numref">Fig. 2</span></a> shows an overview of the GMAT component stack.  The stack
for the GMAT API, shown in <a class="reference internal" href="#apistack"><span class="std std-numref">Fig. 3</span></a>, has a similar appearance.  Users
interact with the GMAT API through an interface layer built using the Simplified
Wrapper and Interface Generator, SWIG.  SWIG generates interfaces and shared
libraries for Python and Java, and can generate similar interface code for other
languages when needed.  Classes in GMAT’s code base are exposed through this
interface using language specific wrappers.  Users interact with the GMAT
classes through these wrappers.</p>
<figure class="align-center" id="id1">
<span id="apistack"></span><a class="reference internal image-reference" href="../../../_images/GMATAPIStack.png"><img alt="../../../_images/GMATAPIStack.png" src="../../../_images/GMATAPIStack.png" style="width: 464.09999999999997px; height: 364.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 3 </span><span class="caption-text">The GMAT API stack.</span><a class="headerlink" href="#id1" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>Using the SWIG interface code, users can work directly with GMAT classes on a
class by class/object by object level.  Users that work this way need a pretty
complete understanding of object linkages and interactions in GMAT.  Using that
expertise, they either imitate many of the steps that are performed by the GMAT
engine when GMAT is run or make calls to the components of the engine to perform
the required actions.</p>
<p>Most users would rather work at a less detailed level than this object by
object interaction.  There are two groups of users in this category: those that are
familiar with GMAT and want to use the API to run GMAT scripts, making API calls
to adapt their scripts along the way, and those that want to use capabilities
provided by GMAT inside of models that they are running in a tool like MATLAB or
Python, or in a compiled application written in a language like Java.  The API
provides a set of helper functions that encapsulate the GMAT engine behind calls
that simplify the management tasks of the GMAT engine for these users.  These
API helpers are exposed through the SWIG interface layer for use by these API
users.</p>
<p>A driving feature of the GMAT API is the incorporation of usability features for
the API user community.  During the prototyping exercise for the API, the
development team found that the SWIG system provides a simple mechanism for
exposing GMAT components in the Python, Java, and MATLAB environments.  However,
users working in those systems still found it difficult to use the prototype API
because of a lack of on line documentation and apparent inconsistencies in the
methods in GMAT.  The production API addresses the first of these issues through
the incorporation of class and object level help functions for classes that are
identified as “API ready.”  Interface inconsistencies are addressed through the
addition of methods to the source code that simplify the class and object
interfaces, leaving in place where necessary the interfaces that appear to API
users to be inconsistent because of internal code needs in the GMAT system.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="Architecture.html"
                          title="previous chapter">GMAT Architectural Components</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="../userguide/UsersGuide.html"
                          title="next chapter">GMAT API User’s Guide</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../../_sources/API/source/design/APIDesign.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="../userguide/UsersGuide.html" title="GMAT API User’s Guide"
             >next</a> |</li>
        <li class="right" >
          <a href="Architecture.html" title="GMAT Architectural Components"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="../apiIndex.html" >GMAT API Design and User’s Guide</a> &#187;</li>
          <li class="nav-item nav-item-2"><a href="Design.html" >System Design</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">The API Design</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>