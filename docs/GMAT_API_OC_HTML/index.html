
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>GMAT Tools and Extensions &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="_static/classic.css" />
    
    <script data-url_root="./" id="documentation_options" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="GMAT API Design and User’s Guide" href="API/source/apiIndex.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="API/source/apiIndex.html" title="GMAT API Design and User’s Guide"
             accesskey="N">next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT Tools and Extensions</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="gmat-tools-and-extensions">
<h1>GMAT Tools and Extensions<a class="headerlink" href="#gmat-tools-and-extensions" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="API/source/apiIndex.html">GMAT API Design and User’s Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="API/source/intro/Overview.html">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/design/Design.html">System Design</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/UsersGuide.html">GMAT API User’s Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/ScriptUsage.html">Script Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/usage/UseCase1.html">Tutorial: Accessing GMAT Propagation and Navigation Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/commands/TopLevelInterface.html">API Access to GMAT Commands</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/commands/SingleCommands.html">Configuring a Command</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/usage/Examples.html">Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/BestPractices.html">API Best Practices</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/usage/CheatSheet.html">GMAT API Cheat Sheet</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/userguide/notebooks/notebooks.html">API Notebook Walkthroughs</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/Bibliography.html">Bibliography</a></li>
<li class="toctree-l2"><a class="reference internal" href="API/source/ChangeHistory.html">Change History</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="CSALT/source/csaltIndex.html">GMAT Optimal Control</a><ul>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-overview.html">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-SoftwareOrganizationAndCompilation.html">Software Organization and Compilation</a></li>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-ConceptsAndAlgorithms.html">Concepts and Algorithms</a></li>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-UserGuide.html">User Guide</a></li>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-UserInterfaceSpec-smallTables.html">User Interface Specification</a></li>
<li class="toctree-l2"><a class="reference internal" href="CSALT/source/csalt-EMTGSpacecraftFileSpec-smallTables.html">EMTG Spacecraft File Specification</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="API/source/apiIndex.html"
                          title="next chapter">GMAT API Design and User’s Guide</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="API/source/apiIndex.html" title="GMAT API Design and User’s Guide"
             >next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT Tools and Extensions</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>