
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>Overview &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Software Organization and Compilation" href="csalt-SoftwareOrganizationAndCompilation.html" />
    <link rel="prev" title="GMAT Optimal Control" href="csaltIndex.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="csalt-SoftwareOrganizationAndCompilation.html" title="Software Organization and Compilation"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="csaltIndex.html" title="GMAT Optimal Control"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" accesskey="U">GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Overview</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="overview">
<h1>Overview<a class="headerlink" href="#overview" title="Permalink to this heading">¶</a></h1>
<p>The Collocation Stand Alone Library and Toolkit (CSALT) is a C++ library that employs collocation to solve the optimal control problem. The library contains approximately 22,000 Source Lines of Code (SLOC) for the CSALT library and about 17,000 SLOC reused from utilities in the General Mission Analysis Tool (GMAT).  The system has (loose) dependencies on the Boost C++ library for sparse matrix arithmetic and SNOPT for nonlinear programming. (See <a class="reference internal" href="csalt-SoftwareOrganizationAndCompilation.html#sec-gmatoc-installation"><span class="std std-ref">Installation</span></a> for more details on how to install CSALT.) The software was developed in collaboration between GSFC engineers and software developers, GSFC support contractors, the Korea Aerospace Research Institute (KARI), and Yonsei University.  CSALT is licensed using the Apache License 2.0.</p>
<p>The CSALT library is integrated into GMAT in the GMAT Optimal Control subsystem plugin.  This document contains the user guide and the high-level software design documentation for the CSALT and the GMAT Optimal Control plugin. The first few chapters discuss the software contents (<a class="reference internal" href="csalt-SoftwareOrganizationAndCompilation.html#sec-gmatoc-organization"><span class="std std-ref">Software Organization and Compilation</span></a>), the optimal control problem, and CSALT algorithms (<a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#sec-gmatoc-concepts"><span class="std std-ref">Concepts and Algorithms</span></a>) to a level appropriate to provide context for the software user guide and design sections. The <a class="reference internal" href="csalt-UserGuide.html#sec-gmatoc-userguide"><span class="std std-ref">User Guide</span></a> presents the user interfaces for CSALT (which are C++ interfaces) and for the GMAT script interfaces.  The documentation includes an overview of components used to solve optimal control problems, a tutorial for CSALT and GMAT applications, and extensive examples (<a class="reference internal" href="csalt-UserGuide.html#sec-gmatoc-userguide"><span class="std std-ref">User Guide</span></a>).  Reference material illustrates detailed user class/Resource interfaces. The reference material also incudes a complete user interface specification for the GMAT Optimal Control plugin (<a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#sec-gmatoc-uispec"><span class="std std-ref">User Interface Specification</span></a>) and a file specification for Evolutionary Mission Trajectory Generator (EMTG) spacecraft files (<a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#emtgspacecraftfilespec-label"><span class="std std-ref">EMTG Spacecraft File Specification</span></a>), which are used to specify spacecraft propulsion system properties when using the GMAT Optimal Control plugin.</p>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="csaltIndex.html"
                          title="previous chapter">GMAT Optimal Control</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="csalt-SoftwareOrganizationAndCompilation.html"
                          title="next chapter">Software Organization and Compilation</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/CSALT/source/csalt-overview.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="csalt-SoftwareOrganizationAndCompilation.html" title="Software Organization and Compilation"
             >next</a> |</li>
        <li class="right" >
          <a href="csaltIndex.html" title="GMAT Optimal Control"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" >GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Overview</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>