
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>EMTG Spacecraft File Specification &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="prev" title="User Interface Specification" href="csalt-UserInterfaceSpec-smallTables.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="csalt-UserInterfaceSpec-smallTables.html" title="User Interface Specification"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" accesskey="U">GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">EMTG Spacecraft File Specification</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="emtg-spacecraft-file-specification">
<span id="emtgspacecraftfilespec-label"></span><h1>EMTG Spacecraft File Specification<a class="headerlink" href="#emtg-spacecraft-file-specification" title="Permalink to this heading">¶</a></h1>
<p>This section describes the contents of an EMTG spacecraft file. The descriptions are given in the tables in this section and are organized by the spacecraft file block, the line number within a block, and the variable number within a line. It is highly recommended that this file specification be used in conjunction with <a class="reference internal" href="csalt-UserGuide.html#sec-gmatoc-formatofemtgspacecraftfile"><span class="std std-ref">Format of EMTG Spacecraft File</span></a> and the example .emtg_spacecraft files provided in gmat/data/emtg/.</p>
<p>Several symbols and acronyms are used in the tables in this section: <img class="math" src="../../_images/math/c2aa3dff9bffb099e9dff196fd36aed56ec16baf.png" alt="P"/> is power; <img class="math" src="../../_images/math/a2a080be6dfb0788804710115faf8088371f00f0.png" alt="P_0"/> is base power delivered by a solar array; <img class="math" src="../../_images/math/8c841a4fa812c0e6775d8dc9252d7c8021b599b1.png" alt="r_s"/> is the distance from the spacecraft to the Sun; Isp is specific impulse; CSI is constant specific impulse; and VSI is variable specific impulse.</p>
<section id="spacecraft-block">
<h2>Spacecraft Block<a class="headerlink" href="#spacecraft-block" title="Permalink to this heading">¶</a></h2>
<p>This section describes the contents of the Spacecraft Block of an EMTG spacecraft file.</p>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line1-entry1"></span><table class="docutils align-default" id="id3">
<caption><span class="caption-number">Table 96 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 1, entry 1.</span><a class="headerlink" href="#id3" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>name</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line2-entry1"></span><table class="docutils align-default" id="id4">
<caption><span class="caption-number">Table 97 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 2, entry 1.</span><a class="headerlink" href="#id4" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableGlobalElectricPropellantTankConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line3-entry1"></span><table class="docutils align-default" id="id5">
<caption><span class="caption-number">Table 98 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 3, entry 1.</span><a class="headerlink" href="#id5" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableGlobalChemicalPropellantTankConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line4-entry1"></span><table class="docutils align-default" id="id6">
<caption><span class="caption-number">Table 99 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 4, entry 1.</span><a class="headerlink" href="#id6" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableGlobalDryMassConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line5-entry1"></span><table class="docutils align-default" id="id7">
<caption><span class="caption-number">Table 100 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 5, entry 1.</span><a class="headerlink" href="#id7" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>GlobalElectricPropellantTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line6-entry1"></span><table class="docutils align-default" id="id8">
<caption><span class="caption-number">Table 101 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 6, entry 1.</span><a class="headerlink" href="#id8" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>6</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>GlobalFuelTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line7-entry1"></span><table class="docutils align-default" id="id9">
<caption><span class="caption-number">Table 102 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 7, entry 1.</span><a class="headerlink" href="#id9" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>GlobalOxidizerTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line8-entry1"></span><table class="docutils align-default" id="id10">
<caption><span class="caption-number">Table 103 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 8, entry 1.</span><a class="headerlink" href="#id10" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>GlobalDryMassBounds</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Lower bound on global dry mass</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/>; <img class="math" src="../../_images/math/8a062baade83eb0aaa8c5b00f6fce6124847bd68.png" alt="\leq"/> GlobalDryMassBounds[2]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-spacecraft-line8-entry2"></span><table class="docutils align-default" id="id11">
<caption><span class="caption-number">Table 104 </span><span class="caption-text">EMTG spacecraft file specification for Spacecraft Block, line 8, entry 2.</span><a class="headerlink" href="#id11" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>GlobalDryMassBounds</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Upper bound on global dry mass</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/5029c2c60b99e78ec2ab8c273ff9aa8fdb7edac8.png" alt="\geq"/> GlobalDryMassBounds[1]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="stage-block">
<h2>Stage Block<a class="headerlink" href="#stage-block" title="Permalink to this heading">¶</a></h2>
<p>This section describes the contents of the Stage Block of an EMTG spacecraft file.</p>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line1-entry1"></span><table class="docutils align-default" id="id12">
<caption><span class="caption-number">Table 105 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 1, entry 1.</span><a class="headerlink" href="#id12" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>name</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Name of the stage</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line2-entry1"></span><table class="docutils align-default" id="id13">
<caption><span class="caption-number">Table 106 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 2, entry 1.</span><a class="headerlink" href="#id13" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>BaseDryMass</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Dry mass of the propulsion system base</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line3-entry1"></span><table class="docutils align-default" id="id14">
<caption><span class="caption-number">Table 107 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 3, entry 1.</span><a class="headerlink" href="#id14" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>AdapterMass</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Mass of the spacecraft-to-power-system adapter</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line4-entry1"></span><table class="docutils align-default" id="id15">
<caption><span class="caption-number">Table 108 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 4, entry 1.</span><a class="headerlink" href="#id15" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableElectricPropellantTankConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Is the constraint on the electric propellant tank capacity enabled?</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line5-entry1"></span><table class="docutils align-default" id="id16">
<caption><span class="caption-number">Table 109 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 5, entry 1.</span><a class="headerlink" href="#id16" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableChemicalPropellantTankConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Is the constraint on the chemical propellant tank capacity enabled?</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line6-entry1"></span><table class="docutils align-default" id="id17">
<caption><span class="caption-number">Table 110 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 6, entry 1.</span><a class="headerlink" href="#id17" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>6</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>EnableDryMassConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Is the constraint on dry mass enabled?</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>0 or 1</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>boolean integer</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line7-entry1"></span><table class="docutils align-default" id="id18">
<caption><span class="caption-number">Table 111 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 7, entry 1.</span><a class="headerlink" href="#id18" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ElectricPropellantTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Maximum electric propellant mass</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Only enforced if EnableElectricPropellantTankConstraint == 1</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line8-entry1"></span><table class="docutils align-default" id="id19">
<caption><span class="caption-number">Table 112 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 8, entry 1.</span><a class="headerlink" href="#id19" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ChemicalFuelTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Maximum chemical fuel mass</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Only enforced if EnableChemicalPropellantTankConstraint == 1</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line9-entry1"></span><table class="docutils align-default" id="id20">
<caption><span class="caption-number">Table 113 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 9, entry 1.</span><a class="headerlink" href="#id20" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>9</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ChemicalOxidizerTankCapacity</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Maximum oxidizer mass</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Only enforced if EnableChemicalPropellantTankConstraint == 1</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line10-entry1"></span><table class="docutils align-default" id="id21">
<caption><span class="caption-number">Table 114 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 10, entry 1.</span><a class="headerlink" href="#id21" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>10</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ThrottleLogic</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>How should the number of thrusters be selected?</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>1 or 2</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>1: minimum number of thrusters</p>
<p>2: maximum number of thrusters</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line11-entry1"></span><table class="docutils align-default" id="id22">
<caption><span class="caption-number">Table 115 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 11, entry 1.</span><a class="headerlink" href="#id22" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>11</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ThrottleSharpness</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Determines how smooth or sharp the transition between throttle levels is</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/5d74586ac5e280e37f097dbfeace9bc66cda9330.png" alt="&gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>The Heaviside step function is analytically approximated using <img class="math" src="../../_images/math/c6efaa20aedf4f4d1f2daf5714a842e974b91adb.png" alt="H = \frac{1}{1 - \exp(-2 k x)}"/>, <img class="math" src="../../_images/math/2c8bd61fdb0d3cce98cb0bf4a0f6cefc81917749.png" alt="k ="/> ThrottleSharpness.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line12-entry1"></span><table class="docutils align-default" id="id23">
<caption><span class="caption-number">Table 116 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 12, entry 1.</span><a class="headerlink" href="#id23" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>12</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>PowerSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Name of power system for stage</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>Must be a valid name in the stage’s Power Library block</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line13-entry1"></span><table class="docutils align-default" id="id24">
<caption><span class="caption-number">Table 117 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 13, entry 1.</span><a class="headerlink" href="#id24" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>13</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ElectricPropulsionSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Name of electric propulsion system for stage</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>Must be a valid name in the stage’s Propulsion Library block</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-stage-line14-entry1"></span><table class="docutils align-default" id="id25">
<caption><span class="caption-number">Table 118 </span><span class="caption-text">EMTG spacecraft file specification for Stage Block, line 14, entry 1.</span><a class="headerlink" href="#id25" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>14</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>ChemicalPropulsionSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Name of chemical propulsion system for stage</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>Must be a valid name in the stage’s Propulsion Library block</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="power-library-block">
<h2>Power Library Block<a class="headerlink" href="#power-library-block" title="Permalink to this heading">¶</a></h2>
<p>This section describes the contents of the Power Library Block of an EMTG spacecraft file.</p>
<p>Multiple power systems may be added to the power library by adding additional lines that contain all elements specified in line 1.</p>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry1"></span><table class="docutils align-default" id="id26">
<caption><span class="caption-number">Table 119 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 1.</span><a class="headerlink" href="#id26" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>name for this power system being specified by that line</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry2"></span><table class="docutils align-default" id="id27">
<caption><span class="caption-number">Table 120 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 2.</span><a class="headerlink" href="#id27" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>power supply type</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>[0, 1]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>0: constant; 1: solar</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry3"></span><table class="docutils align-default" id="id28">
<caption><span class="caption-number">Table 121 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 3.</span><a class="headerlink" href="#id28" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>power supply curve type</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>[0, 1]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>0: Sauer: <img class="math" src="../../_images/math/71bd24f6a8f3f42bfce480ea411fdd7a4d481f24.png" alt="P_{Generated} = \frac{P_0}{r_s^2} \left( \frac{\gamma_0 + \gamma_1 / r_s + \gamma_2 / r_s^2}{1 + \gamma_3 r_s + \gamma_4 r_s^2} \right)"/></p>
<blockquote>
<div><p>1: polynomial: <img class="math" src="../../_images/math/0142a0ab70236bc2425dbdf94e997617322bf432.png" alt="P_{Generated} = \frac{P_0}{r_s^2} \sum_{i=0}^6 r_s^i"/></p>
</div></blockquote>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry4"></span><table class="docutils align-default" id="id29">
<caption><span class="caption-number">Table 122 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 4.</span><a class="headerlink" href="#id29" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>bus power type</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>[0, 1]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>0: type A quadratic: P_Bus = bus_power[0] + bus_power[1] / r_s^1 + bus_power[2] / r_s^2</p>
<p>1: type B conditional: If P_Provided &gt; bus_power[0], then P_Bus = bus_power[0]. Else, P_Bus = bus_power[0] + bus_power[1] * (bus_power[2] - P_provided)</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry5"></span><table class="docutils align-default" id="id30">
<caption><span class="caption-number">Table 123 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 5.</span><a class="headerlink" href="#id30" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>P0</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Base power</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry6"></span><table class="docutils align-default" id="id31">
<caption><span class="caption-number">Table 124 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 6.</span><a class="headerlink" href="#id31" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>6</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass per kW</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry7"></span><table class="docutils align-default" id="id32">
<caption><span class="caption-number">Table 125 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 7.</span><a class="headerlink" href="#id32" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>decay rate</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>1/years</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>rate of power decay over time: <img class="math" src="../../_images/math/0f461b30a23430bd4c073db0989daac13aff10ae.png" alt="P_{generated} = P_{generated} \exp(-\texttt{decay\_rate}  \cdot t)"/>, with t in years</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry8"></span><table class="docutils align-default" id="id33">
<caption><span class="caption-number">Table 126 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 8.</span><a class="headerlink" href="#id33" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[0]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in km and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 0: numerator coefficient of r_s^0.</p>
<p>If power supply curve type == 1: coefficient of r_s^{-2}</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry9"></span><table class="docutils align-default" id="id34">
<caption><span class="caption-number">Table 127 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 9.</span><a class="headerlink" href="#id34" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>9</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[1]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 0: numerator coefficient of r_s^1.</p>
<p>If power supply curve type == 1: coefficient of r_s^{-1}</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry10"></span><table class="docutils align-default" id="id35">
<caption><span class="caption-number">Table 128 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 10.</span><a class="headerlink" href="#id35" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>10</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[2]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 0: numerator coefficient of r_s^2.</p>
<p>If power supply curve type == 1: coefficient of r_s^0</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry11"></span><table class="docutils align-default" id="id36">
<caption><span class="caption-number">Table 129 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 11.</span><a class="headerlink" href="#id36" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>11</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[3]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 0: denominator coefficient of r_s^1.</p>
<p>If power supply curve type == 1: coefficient of r_s^1</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry12"></span><table class="docutils align-default" id="id37">
<caption><span class="caption-number">Table 130 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 12.</span><a class="headerlink" href="#id37" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>12</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[4]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 0: denominator coefficient of r_s^2.</p>
<p>If power supply curve type == 1: coefficient of r_s^2</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry13"></span><table class="docutils align-default" id="id38">
<caption><span class="caption-number">Table 131 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 13.</span><a class="headerlink" href="#id38" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>13</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[5]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array.</p>
<p>If power supply curve type == 1: coefficient of r_s^3</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry14"></span><table class="docutils align-default" id="id39">
<caption><span class="caption-number">Table 132 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 14.</span><a class="headerlink" href="#id39" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>14</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>gamma[6]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes r_s is in au and P is in kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Power delivered by solar array. If power supply curve type == 1: coefficient of r_s^4</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry15"></span><table class="docutils align-default" id="id40">
<caption><span class="caption-number">Table 133 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 15.</span><a class="headerlink" href="#id40" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>15</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>bus power[0]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If bus power type == 0: Power required by spacecraft bus: coefficient of r_s^0.</p>
<p>If bus power type == 1, see note on bus power type.</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry16"></span><table class="docutils align-default" id="id41">
<caption><span class="caption-number">Table 134 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 16.</span><a class="headerlink" href="#id41" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>16</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>bus power[1]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If bus power type == 0: Power required by spacecraft bus: coefficient of r_s^1.</p>
<p>If bus power type == 1, see note on bus power type.</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-power-line1-entry17"></span><table class="docutils align-default" id="id42">
<caption><span class="caption-number">Table 135 </span><span class="caption-text">EMTG spacecraft file specification for Power Library Block, line 1, entry 17.</span><a class="headerlink" href="#id42" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>17</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>bus power[2]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If bus power type == 0: Power required by spacecraft bus: coefficient of r_s^2.</p>
<p>If bus power type == 1, see note on bus power type.</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="propulsion-library-block">
<h2>Propulsion Library Block<a class="headerlink" href="#propulsion-library-block" title="Permalink to this heading">¶</a></h2>
<p>This section describes the contents of the Propulsion Library Block of an EMTG spacecraft file.</p>
<p>Multiple propulsion systems may be added to the propulsion library by adding additional lines that contain all elements specified in line 1.</p>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry1"></span><table class="docutils align-default" id="id43">
<caption><span class="caption-number">Table 136 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 1.</span><a class="headerlink" href="#id43" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>name for this power system being specified by that line</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>none</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>N/A</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry2"></span><table class="docutils align-default" id="id44">
<caption><span class="caption-number">Table 137 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 2.</span><a class="headerlink" href="#id44" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>2</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thruster mode</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>[0,1,3,4,5]</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>0: constant thrust and Isp</p>
<p>1: fixed efficiency CSI</p>
<p>3: polynomial 1D</p>
<p>4: stepped H thrust 1D</p>
<p>5: stepped L mdot 1D.</p>
</td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry3"></span><table class="docutils align-default" id="id45">
<caption><span class="caption-number">Table 138 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 3.</span><a class="headerlink" href="#id45" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>throttle table file</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>string</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>“none” or  path to external <a href="#id1"><span class="problematic" id="id2">*</span></a>.ThrottleTable file, relative to GMAT executable location</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If an external throttle table is to be used, the relevant file is specified here. Otherwise, use “none”. If an external table is provided, it overrides local thrust coefficients and power coefficients</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry4"></span><table class="docutils align-default" id="id46">
<caption><span class="caption-number">Table 139 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 4.</span><a class="headerlink" href="#id46" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass per string</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kg</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>mass of a single thruster string</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry5"></span><table class="docutils align-default" id="id47">
<caption><span class="caption-number">Table 140 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 5.</span><a class="headerlink" href="#id47" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>5</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>number of strings</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>integer</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/bdb7a78ea7aa5eef15afe3cec22a20fa0b6a7c08.png" alt="\geq 1"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>the number of thruster strings in operation</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry6"></span><table class="docutils align-default" id="id48">
<caption><span class="caption-number">Table 141 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 6.</span><a class="headerlink" href="#id48" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>6</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Minimum power</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Minimum power to thruster</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry7"></span><table class="docutils align-default" id="id49">
<caption><span class="caption-number">Table 142 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 7.</span><a class="headerlink" href="#id49" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>Maximum power</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>kW</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/5029c2c60b99e78ec2ab8c273ff9aa8fdb7edac8.png" alt="\geq"/> Minimum power (variable 6)</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Maximum power to thruster</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry8"></span><table class="docutils align-default" id="id50">
<caption><span class="caption-number">Table 143 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 8.</span><a class="headerlink" href="#id50" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>8</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>constrant thrust</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If a constant thrust is desired, its value is set here</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry9"></span><table class="docutils align-default" id="id51">
<caption><span class="caption-number">Table 144 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 9.</span><a class="headerlink" href="#id51" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>9</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>constant Isp</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>If a constant Isp is desired, its value is set here</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry10"></span><table class="docutils align-default" id="id52">
<caption><span class="caption-number">Table 145 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 10.</span><a class="headerlink" href="#id52" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>10</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>minimum Isp / monoprop Isp</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Isp for a monoprop system, if used</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry11"></span><table class="docutils align-default" id="id53">
<caption><span class="caption-number">Table 146 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 11.</span><a class="headerlink" href="#id53" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>11</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>fixed efficiency</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/639929c7967e1ffb01190f899f80cd9722f70fa6.png" alt="0 \leq \texttt{real} \leq 1"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Fixed efficiency if operating in fixed efficiency mode</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry12"></span><table class="docutils align-default" id="id54">
<caption><span class="caption-number">Table 147 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 12.</span><a class="headerlink" href="#id54" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>12</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mixture ratio</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/639929c7967e1ffb01190f899f80cd9722f70fa6.png" alt="0 \leq \texttt{real} \leq 1"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mixture ratio for chemical thruster</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry13"></span><table class="docutils align-default" id="id55">
<caption><span class="caption-number">Table 148 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 13.</span><a class="headerlink" href="#id55" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>13</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust scale factor</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p><img class="math" src="../../_images/math/c7e8fee54f825ced9b447c0e26342b8c8f20ea50.png" alt="\geq 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Constant factor by which to scale thrust</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry14"></span><table class="docutils align-default" id="id56">
<caption><span class="caption-number">Table 149 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 14.</span><a class="headerlink" href="#id56" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>14</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[0]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^0. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry15"></span><table class="docutils align-default" id="id57">
<caption><span class="caption-number">Table 150 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 15.</span><a class="headerlink" href="#id57" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>15</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[1]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^1. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry16"></span><table class="docutils align-default" id="id58">
<caption><span class="caption-number">Table 151 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 16.</span><a class="headerlink" href="#id58" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>16</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[2]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^2. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry17"></span><table class="docutils align-default" id="id59">
<caption><span class="caption-number">Table 152 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 17.</span><a class="headerlink" href="#id59" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>17</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[3]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^3. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry18"></span><table class="docutils align-default" id="id60">
<caption><span class="caption-number">Table 153 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 18.</span><a class="headerlink" href="#id60" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>18</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[4]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^4. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry19"></span><table class="docutils align-default" id="id61">
<caption><span class="caption-number">Table 154 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 19.</span><a class="headerlink" href="#id61" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>19</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[5]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^5. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry20"></span><table class="docutils align-default" id="id62">
<caption><span class="caption-number">Table 155 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 20.</span><a class="headerlink" href="#id62" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>20</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>thrust coefficient[6]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>assumes thrust is in mN</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Thrust coefficient of P^6. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry21"></span><table class="docutils align-default" id="id63">
<caption><span class="caption-number">Table 156 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 21.</span><a class="headerlink" href="#id63" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>21</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[0]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^0. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry22"></span><table class="docutils align-default" id="id64">
<caption><span class="caption-number">Table 157 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 22.</span><a class="headerlink" href="#id64" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>22</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[1]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^1. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry23"></span><table class="docutils align-default" id="id65">
<caption><span class="caption-number">Table 158 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 23.</span><a class="headerlink" href="#id65" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>23</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[2]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^2. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry24"></span><table class="docutils align-default" id="id66">
<caption><span class="caption-number">Table 159 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 24.</span><a class="headerlink" href="#id66" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>24</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[3]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^3. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry25"></span><table class="docutils align-default" id="id67">
<caption><span class="caption-number">Table 160 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 25.</span><a class="headerlink" href="#id67" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>25</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[4]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^4. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry26"></span><table class="docutils align-default" id="id68">
<caption><span class="caption-number">Table 161 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 26.</span><a class="headerlink" href="#id68" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>26</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[5]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^5. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-emtgspacecraftfilespec-block-propulsion-line1-entry27"></span><table class="docutils align-default" id="id69">
<caption><span class="caption-number">Table 162 </span><span class="caption-text">EMTG spacecraft file specification for Propulsion Library Block, line 1, entry 27.</span><a class="headerlink" href="#id69" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Line number</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-even"><td><p>Line name</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Variable number</p></td>
<td><p>27</p></td>
</tr>
<tr class="row-even"><td><p>Variable description</p></td>
<td><p>mass flow coefficient[6]</p></td>
</tr>
<tr class="row-odd"><td><p>Data type</p></td>
<td><p>real</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>asumes mass flow is in mg/s</p></td>
</tr>
<tr class="row-odd"><td><p>Value restrictions</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Other</p></td>
<td><p>Mass flow coefficient of P^6. Only used if throttle table file is “none”.</p></td>
</tr>
</tbody>
</table>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">EMTG Spacecraft File Specification</a><ul>
<li><a class="reference internal" href="#spacecraft-block">Spacecraft Block</a></li>
<li><a class="reference internal" href="#stage-block">Stage Block</a></li>
<li><a class="reference internal" href="#power-library-block">Power Library Block</a></li>
<li><a class="reference internal" href="#propulsion-library-block">Propulsion Library Block</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="csalt-UserInterfaceSpec-smallTables.html"
                          title="previous chapter">User Interface Specification</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/CSALT/source/csalt-EMTGSpacecraftFileSpec-smallTables.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="csalt-UserInterfaceSpec-smallTables.html" title="User Interface Specification"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" >GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">EMTG Spacecraft File Specification</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>