
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>GMAT Optimal Control &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="Overview" href="csalt-overview.html" />
    <link rel="prev" title="Change History" href="../../API/source/ChangeHistory.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="csalt-overview.html" title="Overview"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="../../API/source/ChangeHistory.html" title="Change History"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT Optimal Control</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="gmat-optimal-control">
<h1>GMAT Optimal Control<a class="headerlink" href="#gmat-optimal-control" title="Permalink to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="csalt-overview.html">Overview</a></li>
<li class="toctree-l1"><a class="reference internal" href="csalt-SoftwareOrganizationAndCompilation.html">Software Organization and Compilation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="csalt-SoftwareOrganizationAndCompilation.html#installation">Installation</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html">Concepts and Algorithms</a><ul>
<li class="toctree-l2"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#the-optimal-control-problem">The Optimal Control Problem</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#csalt-algorithms">CSALT Algorithms</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#derivatives-and-sparsity-determination">Derivatives and Sparsity Determination</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#mesh-refinement">Mesh Refinement</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-ConceptsAndAlgorithms.html#a-note-on-optimization">A Note on Optimization</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="csalt-UserGuide.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#tutorial-setting-up-an-optimal-control-problem-in-csalt">Tutorial: Setting Up an Optimal Control Problem in CSALT</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#tutorial-setting-up-an-optimal-control-problem-in-matlab">Tutorial: Setting up an Optimal Control Problem in MATLAB</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#tutorial-setting-up-an-optimal-control-problem-in-gmat">Tutorial: Setting up an Optimal Control Problem in GMAT</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#trajectory-reference">Trajectory Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#phase-reference">Phase Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#dynamics-configuration-reference">Dynamics Configuration Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#optimal-control-guess-reference">Optimal Control Guess Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#emtg-spacecraft-reference">EMTG Spacecraft Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#boundary-functions-reference">Boundary Functions Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#path-functions-reference">Path Functions Reference</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserGuide.html#executioninterface-reference">ExecutionInterface Reference</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html">User Interface Specification</a><ul>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#trajectory">Trajectory</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#phase">Phase</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#dynamicsconfiguration">DynamicsConfiguration</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolguess">OptimalControlGuess</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#customlinkageconstraint">CustomLinkageConstraint</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#emtgspacecraft">EMTGSpacecraft</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolfunction">OptimalControlFunction</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolfunction-for-function-field-expression">OptimalControlFunction for Function Field = Expression</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolfunction-for-function-field-patchedconiclaunch">OptimalControlFunction for Function Field = PatchedConicLaunch</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolfunction-for-function-field-celestialbodyrendezvous">OptimalControlFunction for Function Field = CelestialBodyRendezvous</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#optimalcontrolfunction-for-function-field-integratedflyby">OptimalControlFunction for Function Field = IntegratedFlyby</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html">EMTG Spacecraft File Specification</a><ul>
<li class="toctree-l2"><a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#spacecraft-block">Spacecraft Block</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#stage-block">Stage Block</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#power-library-block">Power Library Block</a></li>
<li class="toctree-l2"><a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#propulsion-library-block">Propulsion Library Block</a></li>
</ul>
</li>
</ul>
</div>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="../../API/source/ChangeHistory.html"
                          title="previous chapter">Change History</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="csalt-overview.html"
                          title="next chapter">Overview</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/CSALT/source/csaltIndex.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="csalt-overview.html" title="Overview"
             >next</a> |</li>
        <li class="right" >
          <a href="../../API/source/ChangeHistory.html" title="Change History"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">GMAT Optimal Control</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>