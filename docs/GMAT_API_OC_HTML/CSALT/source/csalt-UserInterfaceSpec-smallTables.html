
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>User Interface Specification &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="EMTG Spacecraft File Specification" href="csalt-EMTGSpacecraftFileSpec-smallTables.html" />
    <link rel="prev" title="User Guide" href="csalt-UserGuide.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="csalt-EMTGSpacecraftFileSpec-smallTables.html" title="EMTG Spacecraft File Specification"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="csalt-UserGuide.html" title="User Guide"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" accesskey="U">GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Interface Specification</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="user-interface-specification">
<span id="sec-gmatoc-uispec"></span><h1>User Interface Specification<a class="headerlink" href="#user-interface-specification" title="Permalink to this heading">¶</a></h1>
<p>This section describes the GMAT Optimal Control user interface. The user interface specification is given in the tables in this section and are organized by Resource name.</p>
<section id="trajectory">
<h2>Trajectory<a class="headerlink" href="#trajectory" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-trajectory-guesssource"></span><table class="docutils align-default" id="id3">
<caption><span class="caption-number">Table 15 </span><span class="caption-text">User interface specification for Trajectory.GuessSource.</span><a class="headerlink" href="#id3" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>GuessSource</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>OptimalControlGuess Resource</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Guess Resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The Guess source for the trajectory. If no guess is provided on a Phase, then the GuessSource from Trajectory is used. For more information see the OptimalControlGuess Resource documentation.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Phase.GuessSource</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-majoroptimalitytolerances"></span><table class="docutils align-default" id="id4">
<caption><span class="caption-number">Table 16 </span><span class="caption-text">User interface specification for Trajectory.MajorOptimalityTolerances.</span><a class="headerlink" href="#id4" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MajorOptimalityTolerances</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real Array</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[1e-4]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>All elements must be greater than 0.0</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>An array of optimality tolerances for optimizer termination during mesh refinement. For iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, the optimization tolerance is MajorOptimalityTolerances(<img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>). If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (MajorOptimalityTolerances) &lt; mesh refinement iteration number, then the optimality tolerance is <img class="math" src="../../_images/math/39f0717917ced2c52227d7255df708067e214574.png" alt="\texttt{last}"/> (MajorOptimalityTolerances).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Caution: If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (MajorOptimalityTolerances) &gt; MaxMeshRefinementIterations, the last elements of MajorOptimalityTolerances will not be used.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>The units are defined by the user’s problem scaling.</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>MaxRelativeErrorTolerance</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-majoriterationslimits"></span><table class="docutils align-default" id="id5">
<caption><span class="caption-number">Table 17 </span><span class="caption-text">User interface specification for Trajectory.MajorIterationsLimits.</span><a class="headerlink" href="#id5" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MajorIterationsLimits</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer Array</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[1000]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>All elements must be greater than or equal to 0.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>An array of major iterations limits for optimizer termination during mesh refinement iteration. For iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, the major iterations limit is MajorIterationsLimits(<img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>). If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (MajorIterationsLimits) &lt; mesh refinement iteration number, then the major iteration limit is <img class="math" src="../../_images/math/39f0717917ced2c52227d7255df708067e214574.png" alt="\texttt{last}"/> (MajorIterationsLimits).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Caution: If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (MajorIterationsLimits) &gt; MaxMeshRefinementIterations, the last elements of MajorIterationsLimits will not be used.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>dimensionless</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>MaxMeshRefinementIterations</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-totaliterationslimits"></span><table class="docutils align-default" id="id6">
<caption><span class="caption-number">Table 18 </span><span class="caption-text">User interface specification for Trajectory.TotalIterationsLimits.</span><a class="headerlink" href="#id6" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>TotalIterationsLimits</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer Array</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[20000]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>All elements must be greater than or equal to 0.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>An array of total iterations limits for optimizer termination during mesh refinement iteration. For iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, the total iterations limit is TotalIterationsLimits(<img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>). If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (TotalIterationsLimits) &lt; mesh refinement iteration number, then the total iteration limit is <img class="math" src="../../_images/math/39f0717917ced2c52227d7255df708067e214574.png" alt="\texttt{last}"/> (TotalIterationsLimits). Note, for SNOPT, the total iterations is the sum of the Major (SQP) and minor (QP) iterations.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Caution: If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (TotalIterationsLimits) &gt; MaxMeshRefinementIterations, the last elements of TotalIterationsLimits will not be used.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>dimensionless</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>MaxMeshRefinementIterations</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-feasibilitytolerances"></span><table class="docutils align-default" id="id7">
<caption><span class="caption-number">Table 19 </span><span class="caption-text">User interface specification for Trajectory.FeasibilityTolerances.</span><a class="headerlink" href="#id7" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FeasibilityTolerances</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real Array</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[1e-6]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>All elements must be greater than 0.0</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>An array of feasibility tolerances for optimizer termination during mesh refinement iteration. For iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, the feasibility tolerance is FeasibilityTolerances(<img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>). If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (FeasibilityTolerances) &lt; mesh refinement iteration number, then the feasibility tolerance is <img class="math" src="../../_images/math/39f0717917ced2c52227d7255df708067e214574.png" alt="\texttt{last}"/> (FeasibilityTolerances).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Caution: If <img class="math" src="../../_images/math/93291000acc73967a13c830615940ee21da191fe.png" alt="\texttt{length}"/> (FeasibilityTolerances) &gt; MaxMeshRefinementIterations, the last elements of FeasibilityTolerances will not be used.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>The units are defined by the user’s problem scaling.</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>This field interacts with Phase.MaxRelativeErrorTol.  When trying to achieve a specified MaxRelativeErrorTol for a phase, if the feasiblity tolerance is set too tightly, then the mesh refinement algorithm may not converge.</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-maxmeshrefinementiterations"></span><table class="docutils align-default" id="id8">
<caption><span class="caption-number">Table 20 </span><span class="caption-text">User interface specification for Trajectory.MaxMeshRefinementIterations.</span><a class="headerlink" href="#id8" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MaxMeshRefinementIterations</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/9e08ab0aa5e8084c8c25c6f7fc3b253dfdac3b95.png" alt="0 &lt; \texttt{Integer} &lt; \texttt{Inf}"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Maximum number of mesh refinement iterations</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>MajorIterationsLimits</p>
<p>TotalIterationsLimits</p>
<p>FeasibilityTolerances</p>
<p>MajorOptimalityTolerances</p>
</td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-phaselist"></span><table class="docutils align-default" id="id9">
<caption><span class="caption-number">Table 21 </span><span class="caption-text">User interface specification for Trajectory.PhaseList.</span><a class="headerlink" href="#id9" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PhaseList</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Phase Resources</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty List</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>A list of user-defined Phase Resources.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>A list of user-defined Phase Resources. These phases are included in the optimzation.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>None.</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-snoptoutputfile"></span><table class="docutils align-default" id="id10">
<caption><span class="caption-number">Table 22 </span><span class="caption-text">User interface specification for Trajectory.SNOPTOutputFile.</span><a class="headerlink" href="#id10" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SNOPTOutputFile</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>FileName</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>SNOPTOutputFile.txt</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid file name</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>File containing SNOPT output data.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-addsimplelinkagechain"></span><table class="docutils align-default" id="id11">
<caption><span class="caption-number">Table 23 </span><span class="caption-text">User interface specification for Trajectory.AddSimpleLinkageChain.</span><a class="headerlink" href="#id11" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>AddSimpleLinkageChain</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Phase Resources</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty List</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>A list of user-defined Phase Resources. The length of the list be <img class="math" src="../../_images/math/52cdde092e466f300cc6a50e1d3b8627d194152e.png" alt="\geq 2"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Adds a list of phases to be linked with full time and state continuity. Including multiple lines with “AddSimpleLinkageChain” adds multiple simple linkage chain configurations. For example</p>
<p><img class="math" src="../../_images/math/5f0b9c2869dab298f99db826e71cf37cd14338b3.png" alt="\texttt{myTrajectory.AddSimpleLinkageChain} = \\ \left\{\texttt{Phase1}, \texttt{Phase2}\right\}"/></p>
<p><img class="math" src="../../_images/math/281c1fe9eaafeba0a45b166936308985f1d13463.png" alt="\texttt{myTrajectory.AddSimpleLinkageChain} = \\ \left\{\texttt{Phase3}, \texttt{Phase4}, \texttt{Phase5} \right\}"/></p>
<p>results in full time/state continuity between the end <img class="math" src="../../_images/math/122f6b2e242d47cf5ea6ff4f9dd2897d1e5cd2f0.png" alt="\texttt{Phase1}"/> and the start of <img class="math" src="../../_images/math/5f49268002187b362775fdddf08723bcb7f37ad2.png" alt="\texttt{Phase2}"/>, and full time/state continuity between end of <img class="math" src="../../_images/math/ce2c7b1026615e546919b17a8a4b79f155c2da16.png" alt="\texttt{Phase3}"/> and the start of <img class="math" src="../../_images/math/50f8834d3ad0ff14ed30af716047fe1be8d5f5b8.png" alt="\texttt{Phase4}"/>, and full time/state continuity between end of <img class="math" src="../../_images/math/50f8834d3ad0ff14ed30af716047fe1be8d5f5b8.png" alt="\texttt{Phase4}"/> and the start of <img class="math" src="../../_images/math/40fe12d87dca0dc8dfde6d259f0872e7a76ccd51.png" alt="\texttt{Phase5}"/>.</p>
</td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Trajectory.PhaseList. Phases in AddSimpleLinkageChain must also be in PhaseList.</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-customlinkages"></span><table class="docutils align-default" id="id12">
<caption><span class="caption-number">Table 24 </span><span class="caption-text">User interface specification for Trajectory.CustomLinkages.</span><a class="headerlink" href="#id12" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>CustomLinkages</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of CustomLinkageCosntraints</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty List</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Any CustomLinkageConstraint Resources created.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Adds custom linkage constraints to the trajectory as opposed to the full state/time constraints from the AddSimpleLinkageChain field. For example, a CustomLinkageConstraint object can be added to only restrict the position state. In that case, this field is used to add that constraint to the trajectory.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-outputcoordinatesystem"></span><table class="docutils align-default" id="id13">
<caption><span class="caption-number">Table 25 </span><span class="caption-text">User interface specification for Trajectory.OutputCoordinateSystem.</span><a class="headerlink" href="#id13" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>OutputCoordinateSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Coordinate System</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>UsePhaseCoordinateSystems</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Any created coordinate system object or the keyword “UsePhaseCoordinateSystems”</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the coordinate system for data written to the output optimal control history file. If “UsePhaseCoordinateSystems” is used, the data from each phase is printed in their respective coordinate systems used in their force models.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-solutionfile"></span><table class="docutils align-default" id="id14">
<caption><span class="caption-number">Table 26 </span><span class="caption-text">User interface specification for Trajectory.SolutionFile.</span><a class="headerlink" href="#id14" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SolutionFile</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>&lt;TrajectoryResourceName&gt;Solution.och</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>File name that the output data will be sent to in the GMAT output folder</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Used to set the output Optimal Control History file name sent to the GMAT output folder. A new file is created if the current file name is not found</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-allowfailedmeshoptimizations"></span><table class="docutils align-default" id="id15">
<caption><span class="caption-number">Table 27 </span><span class="caption-text">User interface specification for Trajectory.AllowFailedMeshOptimizations.</span><a class="headerlink" href="#id15" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>AllowFailedMeshOptimizations</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Boolean</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>false</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>true, false</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets whether the optimization should be stopped if convergence is not achieved in any mesh refinement iteration (<img class="math" src="../../_images/math/020a6873314031f6ed640b8e1e6779aaa83a68d4.png" alt="\texttt{FALSE}"/>) or if mesh refinement should be continued regardless of the final result of the previous optimization attempt (<img class="math" src="../../_images/math/4a2dfbadf386ea053a3f7f4c97d7cb666e9d22c1.png" alt="\texttt{TRUE}"/>).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-statescalemode"></span><table class="docutils align-default" id="id16">
<caption><span class="caption-number">Table 28 </span><span class="caption-text">User interface specification for Trajectory.StateScaleMode.</span><a class="headerlink" href="#id16" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>StateScaleMode</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Canonical</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Canonical</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The scaling mode for the orbit state. If the Sun is the central body, then the distance unit is an astronomical unit and the GM of the Sun is 1. If the Earth is the central body, then the distance unit is 42000 km and the GM of the Earth is 1. Otherwise, the radius of the central body is the distance unit and the GM of the central body is 1.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-massscalefactor"></span><table class="docutils align-default" id="id17">
<caption><span class="caption-number">Table 29 </span><span class="caption-text">User interface specification for Trajectory.MassScaleFactor.</span><a class="headerlink" href="#id17" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MassScaleFactor</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/6f814c9db1724efe93f9868708dcfd1739d5234c.png" alt="\texttt{REAL\_MIN} &lt; \texttt{Real} &lt; \texttt{REAL\_MAX}"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The mass scale factor (non-dimensional mass is mass/MassScaleFactor). A best-practice is to set the mass scale factor to be approximately the initial total mass of the spacecraft to result in an initial non-dimensionalized mass of approximately 1.0. Note <img class="math" src="../../_images/math/2d58009c037dd47638c00a070783dc2382dc243d.png" alt="\texttt{REAL\_MIN}"/> is currently set to 2.2250738585072014e-308.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-addboundaryfunction"></span><table class="docutils align-default" id="id18">
<caption><span class="caption-number">Table 30 </span><span class="caption-text">User interface specification for Trajectory.AddBoundaryFunction.</span><a class="headerlink" href="#id18" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>AddBoundaryFunction</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of OptimalControlFunction resources</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty List</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Any created OptimalControlFunction resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>A list of user-defined OptimalControlFunction resources to be included in the optmization problem.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-publishupdaterate"></span><table class="docutils align-default" id="id19">
<caption><span class="caption-number">Table 31 </span><span class="caption-text">User interface specification for Trajectory.PublishUpdateRate.</span><a class="headerlink" href="#id19" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PublishUpdateRate</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/826ed6988ee4a23017e57f7e4e7478240b7fcec4.png" alt="\texttt{Integer} &gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Rate at which trajectory state is output to the publisher. Trajectory data is sent to the publisher (which updates plots and reports) every PublishUpdateRate calls to the cost/constraint functions. The output rate to the Optimal Control History file is independent of PublishUpdateRate.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-optimizationmode"></span><table class="docutils align-default" id="id20">
<caption><span class="caption-number">Table 32 </span><span class="caption-text">User interface specification for Trajectory.OptimizationMode.</span><a class="headerlink" href="#id20" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>OptimizationMode</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of strings</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>{Minimize}</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Feasible point, Minimize, Maximize</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The optimization mode (meaning to miminize, maximize, or find a feasible solution). For mesh-refinement iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, the optimization mode is OptimizationMode(<img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>). If length(OptimizationMode) &lt; mesh refinement iteration number, then the optimization mode is last(OptimizationMode).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-meshrefinementguessmode"></span><table class="docutils align-default" id="id21">
<caption><span class="caption-number">Table 33 </span><span class="caption-text">User interface specification for Trajectory.MeshRefinementGuessMode.</span><a class="headerlink" href="#id21" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MeshRefinementGuessMode</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>LastSolutionMostRecentMesh</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>LastSolutionMostRecentMesh, BestSolutionMostRecentMesh, BestSolutionAnyMesh</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The initial-guess source for mesh refinement iterations after the first mesh refinement iteration. LastSolutionMostRecentMesh will use the solution attained by the previous mesh refinement iteration – whether that iteration converged to a feasible solution or not. BestSolutionMostRecentMesh uses the “best” previous solution from only the one immediately previous mesh refinement iteration as the initial guess for the current mesh refinement iteration. BestSolutionAnyMesh uses the “best” previous solution from any previous mesh refinement iteration as an initial guess for the current mesh refinement iteration. The following conditions are used to define “best”: The following conditions are used to define “best”: (1) If no feasible solutions have been found, then “best” refers to the previous solution with the smallest infeasibility. (2) If one feasible solution has been found, then that solution is “best,” regardless of its optimality. (3) If multiple feasible solutions have been found, then the feasible solution with the smallest value of the merit function (i.e., the most optimal solution) is “best.”</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-statedimension"></span><table class="docutils align-default" id="id22">
<caption><span class="caption-number">Table 34 </span><span class="caption-text">User interface specification for Trajectory.StateDimension.</span><a class="headerlink" href="#id22" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>StateDimension</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/826ed6988ee4a23017e57f7e4e7478240b7fcec4.png" alt="\texttt{Integer} &gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The number of states defining the problem for the trajectory.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Default value of 7 is selected because of 6 states defining position and velocity of point mass and 1 state defining mass of point mass. All Phases of a Trajectory default to Trajectory.StateDimension. However, if Phase.StateDimension is set, then the Phase value overrides the Trajectory value for that Phase. Not currently exposed to users because the default value is currently the only acceptable value.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Phase.StateDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-trajectory-controldimension"></span><table class="docutils align-default" id="id23">
<caption><span class="caption-number">Table 35 </span><span class="caption-text">User interface specification for Trajectory.ControlDimension.</span><a class="headerlink" href="#id23" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Trajectory</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ControlDimension</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/826ed6988ee4a23017e57f7e4e7478240b7fcec4.png" alt="\texttt{Integer} &gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The number of controls defining the problem for the trajectory.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Default value of 3 is selected for 3D control vector. All Phases of a Trajectory default to Trajectory.ControlDimension. However, if Phase.ControlDimension is set, then the Phase value overrides the Trajectory value for that Phase. Not currently exposed to users because the default value is currently the only acceptable value.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Phase.ControlDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="phase">
<h2>Phase<a class="headerlink" href="#phase" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-phase-type"></span><table class="docutils align-default" id="id24">
<caption><span class="caption-number">Table 36 </span><span class="caption-text">User interface specification for Phase.Type.</span><a class="headerlink" href="#id24" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>RadauPseudospectral</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>RadauPseudospectral, HermiteSimpson, ImplicitRKOrder4, ImplicitRKOrder6, ImplicitRKOrder8</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The transcription algorithm for the phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>GMAT instantiates a CSALT phase according to the type chosen here by the user.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>PointsPerSubPhase, SubPhaseBoundaries</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-thrustmode"></span><table class="docutils align-default" id="id25">
<caption><span class="caption-number">Table 37 </span><span class="caption-text">User interface specification for Phase.ThrustMode.</span><a class="headerlink" href="#id25" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ThrustMode</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Thrust</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Thrust, Coast</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Flag to model the Phase as a thrust or coast phase. In a coast phase, the control magnitude must be zero at all times. In a thrust phase, the control magnitude is allowed to vary. In other words, unless subject to additional constraints, a thrust phase may contain coasting segments.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-maxrelativeerrortolerance"></span><table class="docutils align-default" id="id26">
<caption><span class="caption-number">Table 38 </span><span class="caption-text">User interface specification for Phase.MaxRelativeErrorTolerance.</span><a class="headerlink" href="#id26" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MaxRelativeErrorTolerance</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1e-05</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/e66c181d5861aaa8b7031b5486116e13e9e0abd9.png" alt="\texttt{Real} &gt; 0.0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The maximum allowable relative error in a mesh interval. The mesh refinement algorithm will continue to iterate until all mesh intervals in the phase have a relative error less than MaxRelativeErrorTolerance. The mesh refinment algorithm is different for different transcription types.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-subphaseboundaries"></span><table class="docutils align-default" id="id27">
<caption><span class="caption-number">Table 39 </span><span class="caption-text">User interface specification for Phase.SubPhaseBoundaries.</span><a class="headerlink" href="#id27" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SubPhaseBoundaries</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[-1 1]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of real numbers that is monotonically increasing. Allowed values depend on the Type field; see Description for more details.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Defines sub-phases within a phase used as an initial guess for mesh refinement and to support different quadrature orders within a given phase to capture slow and fast dynamics within a single phase. When Type is set to RadauPseudospectral, SubPhaseBoundaries must start with -1 and end with 1, with internal values increasing monotonically. For other transcriptions,  SubPhaseBoundaries must start with 0 and end with 1, with internal values increasing monotonically. The default value assumes the default Type = RadauPseudospectral.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT MeshIntervalFractions</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type, PointsPerSubPhase</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-pointspersubphase"></span><table class="docutils align-default" id="id28">
<caption><span class="caption-number">Table 40 </span><span class="caption-text">User interface specification for Phase.PointsPerSubPhase.</span><a class="headerlink" href="#id28" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PointsPerSubPhase</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Integers</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[5]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of integers that has one less entry than SubPhaseBoundaries.  Allowed values depend on the Type field; see Desciption for more details.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Models sub-phases within a phase. For RadauPseudospectral, this allows for different quadrature orders for each sub-phase. The first entry in PointsPerSubPhase is the number of points for the first sub-phase in SubPhaseBoundaries. For example, if SubPhaseBoundaries = [-1 0.5 1], and PointsPerSubPhase = [5 3], then the phase is modeled using two sub-phases. The first sub-phase is from non-dimensional time -1 to 0.5 and is modeled with 5 points, and the second sub-phase is from non-dimensional time 0.5 to 1.0 and is modeled with 3 points. Note: Mesh refinement will change these values; the values provided are guesses for the discretization. In general, set the number of points as low as possible to obtain convergence on the initial mesh and allow the mesh refinement algorithm to determine a finer mesh. On the other hand, for an ImplictRKOrder* method, the order of the quadrature is fixed. In this case, the value of PointsPerSubPhase sets the number of implicit integration steps. Using the earlier example, if Phase.Type = ImplicitRKOrder6, there would be 5 IRK steps in the first subphase, each of order 6, and 3 IRK steps in the second subphase, each of order 6. The default value assumes the default Type = RadauPseudospectral.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT MeshIntervalNumPoints</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type, SubPhaseBoundaries</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-guesssource"></span><table class="docutils align-default" id="id29">
<caption><span class="caption-number">Table 41 </span><span class="caption-text">User interface specification for Phase.GuessSource.</span><a class="headerlink" href="#id29" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>GuessSource</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Guess Resource</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>OptimalControlGuess Resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The Guess source for the phase. If no guess is provided, the Trajectory’s Guess resource is used.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Trajectory.Guess</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-statelowerbound"></span><table class="docutils align-default" id="id30">
<caption><span class="caption-number">Table 42 </span><span class="caption-text">User interface specification for Phase.StateLowerBound.</span><a class="headerlink" href="#id30" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>StateLowerBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of real numbers with same length as number of state decision parameters (StateDimension)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The lower bound on state variables at all mesh and stage points within a phase.</p>
<p>Bounds must be consistent with the state type</p>
<p>and should be specified with respect to the central body of the DynamicsConfiguration with</p>
<p><img class="math" src="../../_images/math/75d6e1a990add2dacd6837ae216c940b7dbf43b4.png" alt="\texttt{J2000Eq}"/> axes.</p>
</td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Units of the State Vector. Typically [km km km km/s km/s km/s and kg] if using Cartesian state, and modified accordingly for other state types.</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>StateDimension, Trajectory.StateDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-stateupperbound"></span><table class="docutils align-default" id="id31">
<caption><span class="caption-number">Table 43 </span><span class="caption-text">User interface specification for Phase.StateUpperBound.</span><a class="headerlink" href="#id31" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>StateUpperBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of real numbers with same length as number of state decision parameters (StateDimension)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The upper bound on state variables at all mesh and stage points within a phase.</p>
<p>Bounds must be consistent with the state type</p>
<p>and should be specified with respect to the central body of the DynamicsConfiguration with</p>
<p><img class="math" src="../../_images/math/75d6e1a990add2dacd6837ae216c940b7dbf43b4.png" alt="\texttt{J2000Eq}"/> axes.</p>
</td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Units of the State Vector. Typically [km km km km/s km/s km/s and kg] if using Cartesian state, and modified accordingly for other state types.</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>StateDimension, Trajectory.StateDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-controllowerbound"></span><table class="docutils align-default" id="id32">
<caption><span class="caption-number">Table 44 </span><span class="caption-text">User interface specification for Phase.ControlLowerBound.</span><a class="headerlink" href="#id32" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ControlLowerBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of real numbers with same length as number of control decision parameters (ControlDimension)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The non-dimensional lower bound on control variables at all mesh and stage points within a phase. In standard non-dimensionalization, 0, -1 is minimum control, and +1 is maximum control.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>non-dimensional</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ControlDimension, Trajectory.ControlDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-controlupperbound"></span><table class="docutils align-default" id="id33">
<caption><span class="caption-number">Table 45 </span><span class="caption-text">User interface specification for Phase.ControlUpperBound.</span><a class="headerlink" href="#id33" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ControlUpperBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Array of real numbers with same length as number of control decision parameters (ControlDimension)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The non-dimensional upper bound on control variables at all mesh and stage points within a phase. In standard non-dimensionalization, 0, -1 is minimum control, and +1 is maximum control.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Passed through to CSALT</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>non-dimensional</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ControlDimension, Trajectory.ControlDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-epochformat"></span><table class="docutils align-default" id="id34">
<caption><span class="caption-number">Table 46 </span><span class="caption-text">User interface specification for Phase.EpochFormat.</span><a class="headerlink" href="#id34" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>EpochFormat</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>UTCGregorian</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid GMAT epoch type. E.g., UTCModJulian, TDBGregorian</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The epoch format used to specify all epoch fields for the phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>InitialEpoch, FinalEpoch, EpochLowerBound, EpochUpperBound</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-initialepoch"></span><table class="docutils align-default" id="id35">
<caption><span class="caption-number">Table 47 </span><span class="caption-text">User interface specification for Phase.InitialEpoch.</span><a class="headerlink" href="#id35" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>InitialEpoch</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Initial guess for the initial epoch of the phase.  This value is modified during optimization.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Time.</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>EpochFormat</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-finalepoch"></span><table class="docutils align-default" id="id36">
<caption><span class="caption-number">Table 48 </span><span class="caption-text">User interface specification for Phase.FinalEpoch.</span><a class="headerlink" href="#id36" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FinalEpoch</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Initial guess for the final epoch of the phase.  This value is modified during optimization.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Time</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>EpochFormat</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-epochlowerbound"></span><table class="docutils align-default" id="id37">
<caption><span class="caption-number">Table 49 </span><span class="caption-text">User interface specification for Phase.EpochLowerBound.</span><a class="headerlink" href="#id37" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>EpochLowerBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The lower bound on time variables at all mesh and stage points within a phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Time</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>EpochFormat</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-epochupperbound"></span><table class="docutils align-default" id="id38">
<caption><span class="caption-number">Table 50 </span><span class="caption-text">User interface specification for Phase.EpochUpperBound.</span><a class="headerlink" href="#id38" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>EpochUpperBound</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid GMAT Epoch</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The upper bound on time variables at all mesh and stage points within a phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Time</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>EpochFormat</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-dynamicsconfiguration"></span><table class="docutils align-default" id="id39">
<caption><span class="caption-number">Table 51 </span><span class="caption-text">User interface specification for Phase.DynamicsConfiguration.</span><a class="headerlink" href="#id39" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>DynamicsConfiguration Resource</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid DynamicsConfiguration Resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The grouping of spacecraft, force models, and maneuver models to be used for the phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>StateLowerBound,StateUpperBound</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-maxcontrolmagnitude"></span><table class="docutils align-default" id="id40">
<caption><span class="caption-number">Table 52 </span><span class="caption-text">User interface specification for Phase.MaxControlMagnitude.</span><a class="headerlink" href="#id40" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MaxControlMagnitude</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/0ab5aeee3ddceabfe1d3ed8062a38beb3900964e.png" alt="0 &lt; \texttt{Real} &lt; \texttt{Inf}"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The maximum magnitude of the non-dimensional control vector. Warning: Setting this value to greater than 1.0 corresponds to control authority greater than what the controller can physically provide and can result in non-physical thrust profiles and trajectories. The upper bound should be set to <img class="math" src="../../_images/math/1237dffe831bfc3548386dca265d5d8160d736dc.png" alt="\leq 1"/> except for when using homotopy or troubleshooting.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Dimensionless</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-mincontrolmagnitude"></span><table class="docutils align-default" id="id41">
<caption><span class="caption-number">Table 53 </span><span class="caption-text">User interface specification for Phase.MinControlMagnitude.</span><a class="headerlink" href="#id41" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>MinControlMagnitude</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>0</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/0ab5aeee3ddceabfe1d3ed8062a38beb3900964e.png" alt="0 &lt; \texttt{Real} &lt; \texttt{Inf}"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The minimum magnitude of the non-dimensional control vector. Warning: Setting this value to greater than 1.0 corresponds to control authority greater than what the controller can physically provide and can result in non-physical thrust profiles and trajectories. The upper bound should be set to <img class="math" src="../../_images/math/1237dffe831bfc3548386dca265d5d8160d736dc.png" alt="\leq 1"/> except for when using homotopy or troubleshooting.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Dimensionless</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-overridecoloringraphics"></span><table class="docutils align-default" id="id42">
<caption><span class="caption-number">Table 54 </span><span class="caption-text">User interface specification for Phase.OverrideColorInGraphics.</span><a class="headerlink" href="#id42" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>OverrideColorInGraphics</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Boolean</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>false</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>true, false</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Flag to override the default color defined on the spacecraft and to use the color defined by OrbitColor in graphics during optimization. This field allows phases to appear using different colors in the graphics.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>OrbitColor</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-builtincost"></span><table class="docutils align-default" id="id43">
<caption><span class="caption-number">Table 55 </span><span class="caption-text">User interface specification for Phase.BuiltInCost.</span><a class="headerlink" href="#id43" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>BuiltInCost</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>RMAGFinal, TotalMassFinal, AbsoluteEpochFinal</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The cost function.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Trajectory.OptimizationMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-builtinboundaryconstraints"></span><table class="docutils align-default" id="id44">
<caption><span class="caption-number">Table 56 </span><span class="caption-text">User interface specification for Phase.BuiltInBoundaryConstraints.</span><a class="headerlink" href="#id44" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>BuiltInBoundaryConstraints</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>StringArray</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Boundary constraints for the phase. These boundary constraints do not support analytic Jacobians.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>This interface is intended to support rapid implementation but is being replaced with assignment-based constraints. As of this writing, there are no constraints implemented via this interface.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-orbitcolor"></span><table class="docutils align-default" id="id45">
<caption><span class="caption-number">Table 57 </span><span class="caption-text">User interface specification for Phase.OrbitColor.</span><a class="headerlink" href="#id45" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>OrbitColor</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>ColorType</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Red</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Valid predefined color name or RGB triplet value between 0 and 255</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The color for the phase in graphics if OverrideColorInGraphics is true.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>OverrideColorInGraphics</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-statedimension"></span><table class="docutils align-default" id="id46">
<caption><span class="caption-number">Table 58 </span><span class="caption-text">User interface specification for Phase.StateDimension.</span><a class="headerlink" href="#id46" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>StateDimension</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>7</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/826ed6988ee4a23017e57f7e4e7478240b7fcec4.png" alt="\texttt{Integer} &gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The number of states defining the problem for the phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Default value of 7 is selected because of 6 states defining position and velocity of point mass and 1 state defining mass of point mass. All Phases of a Trajectory default to Trajectory.StateDimension. However, if Phase.StateDimension is set, then the Phase value overrides the Trajectory value for that Phase. Not currently exposed to users because the default value is currently the only acceptable value.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Trajectory.StateDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-phase-controldimension"></span><table class="docutils align-default" id="id47">
<caption><span class="caption-number">Table 59 </span><span class="caption-text">User interface specification for Phase.ControlDimension.</span><a class="headerlink" href="#id47" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>Phase</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ControlDimension</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Integer</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>3</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/826ed6988ee4a23017e57f7e4e7478240b7fcec4.png" alt="\texttt{Integer} &gt; 0"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The number of control variables defining the problem for the phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Default value of 3 is selected for 3D control vector. All Phases of a Trajectory default to Trajectory.ControlDimension. However, if Phase.ControlDimension is set, then the Phase value overrides the Trajectory value for that Phase. Not currently exposed to users because the default value is currently the only acceptable value.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Trajectory.ControlDimension</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="dynamicsconfiguration">
<h2>DynamicsConfiguration<a class="headerlink" href="#dynamicsconfiguration" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-dynamicsconfiguration-forcemodels"></span><table class="docutils align-default" id="id48">
<caption><span class="caption-number">Table 60 </span><span class="caption-text">User interface specification for DynamicsConfiguration.ForceModels.</span><a class="headerlink" href="#id48" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ForceModels</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Force Model Resources</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List of Valid Force Model Resources</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lists the force model for each spacecraft. Currently, only a single spacecraft is supported, so only a single force model should be provided.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Spacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-dynamicsconfiguration-spacecraft"></span><table class="docutils align-default" id="id49">
<caption><span class="caption-number">Table 61 </span><span class="caption-text">User interface specification for DynamicsConfiguration.Spacecraft.</span><a class="headerlink" href="#id49" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Spacecraft</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Spacecraft Resources</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List of Valid Spacecraft Resources</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lists the spacecraft that are modeled.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Currently, only one spacecraft per phase is supported. If there are multiple spacecraft, then the ith spacecraft in the list is the spacecraft to which the ith elements in the DynamicsModels, FiniteBurns, and ImpulsiveBurns lists apply.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ForceModels, FiniteBurns, ImpulsiveBurns</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-dynamicsconfiguration-finiteburns"></span><table class="docutils align-default" id="id50">
<caption><span class="caption-number">Table 62 </span><span class="caption-text">User interface specification for DynamicsConfiguration.FiniteBurns.</span><a class="headerlink" href="#id50" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FiniteBurns</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Finite Burns</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List of Finite Burns (including EMTG models, if used)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lists the finite burn resources for each spacecraft.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Currently, finite burns MUST be EmtgInterface resources</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Spacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-dynamicsconfiguration-impulsiveburns"></span><table class="docutils align-default" id="id51">
<caption><span class="caption-number">Table 63 </span><span class="caption-text">User interface specification for DynamicsConfiguration.ImpulsiveBurns.</span><a class="headerlink" href="#id51" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ImpulsiveBurns</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Impulsive Burns</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List of Impulsive Burns</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lists the impulsive burn resources for each spacecraft.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>Not currently implemented.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Spacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-dynamicsconfiguration-emtgtankconfig"></span><table class="docutils align-default" id="id52">
<caption><span class="caption-number">Table 64 </span><span class="caption-text">User interface specification for DynamicsConfiguration.EMTGTankConfig.</span><a class="headerlink" href="#id52" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>DynamicsConfiguration</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>EMTGTankConfig</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>List of Fuel Tank Hardware</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List of valid Fuel Tank Hardware resources</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lists the fuel tanks that are used by the selected spacecraft to represent the depletion of fuel mass along a trajectory on the GMAT side of the GMAT/CSALT interface. The tank(s) selected must be attached to the spacecraft in use and the total mass of the spacecraft must be at least as much as the upper bound on mass in the phase objects using this spacecraft. This field is only required when the DynamicsConfiguration is using an EMTGSpacecraft for a Phase that is not a coast phase.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolguess">
<h2>OptimalControlGuess<a class="headerlink" href="#optimalcontrolguess" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolguess-type"></span><table class="docutils align-default" id="id53">
<caption><span class="caption-number">Table 65 </span><span class="caption-text">User interface specification for OptimalControlGuess.Type.</span><a class="headerlink" href="#id53" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlGuess</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>GMATArray, CollocationGuessFile</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Defines the array guess type: GMATArray-based or file-based.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolguess-timesystem"></span><table class="docutils align-default" id="id54">
<caption><span class="caption-number">Table 66 </span><span class="caption-text">User interface specification for OptimalControlGuess.TimeSystem.</span><a class="headerlink" href="#id54" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlGuess</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>TimeSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Any GMAT Modified Julian Time System</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The time system used in the data array specified by ArrayName. Only used if Type = GMATArray.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolguess-coordinatesystem"></span><table class="docutils align-default" id="id55">
<caption><span class="caption-number">Table 67 </span><span class="caption-text">User interface specification for OptimalControlGuess.CoordinateSystem.</span><a class="headerlink" href="#id55" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlGuess</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>CoordinateSystem</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>CoordinateSystemResource</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>EarthMJ2000Eq</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>CoordinateSystem Resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The coordinate system used in the data array specified by ArrayName. Only used if Type = GMATArray.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolguess-guessarray"></span><table class="docutils align-default" id="id56">
<caption><span class="caption-number">Table 68 </span><span class="caption-text">User interface specification for OptimalControlGuess.GuessArray.</span><a class="headerlink" href="#id56" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlGuess</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>GuessArray</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Array of Reals</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Each element of the array must be <img class="math" src="../../_images/math/253953bf1fad8ee516113a905880a2893bb8026d.png" alt="-\texttt{Inf} &lt; \texttt{Real} &lt; \texttt{Inf}"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The array containing the guess data.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>The columns of the array are: time, states, controls. The rows are the different values of those variables. The times must increase monotonically as the row number increases. The array must have at least 5 rows.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolguess-filename"></span><table class="docutils align-default" id="id57">
<caption><span class="caption-number">Table 69 </span><span class="caption-text">User interface specification for OptimalControlGuess.FileName.</span><a class="headerlink" href="#id57" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlGuess</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FileName</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>File name and path.</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>File consistent with Type defined in Type field.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The file containing the guess data, formatted as an Optimal Control History file, relative to the directory in which the GMAT executable is located.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="customlinkageconstraint">
<h2>CustomLinkageConstraint<a class="headerlink" href="#customlinkageconstraint" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-customlinkageconstraint-constraintmode"></span><table class="docutils align-default" id="id58">
<caption><span class="caption-number">Table 70 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.ConstraintMode.</span><a class="headerlink" href="#id58" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Difference, Absolute</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Applies a constraint on the difference between parameters (specifed in using SetModelParameter) for two phases (Difference mode) or applies an absolute constraint on quantities for a single phase specified in the InitialPhase field (Absolute mode).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-customlinkageconstraint-initialphase"></span><table class="docutils align-default" id="id59">
<caption><span class="caption-number">Table 71 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.InitialPhase.</span><a class="headerlink" href="#id59" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>InitialPhase</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Phase resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The first phase in the linkage for a Difference-mode constraint. The only phase in the linkage for an Absolute-mode constraint.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-customlinkageconstraint-initialphaseboundarytype"></span><table class="docutils align-default" id="id60">
<caption><span class="caption-number">Table 72 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.InitialPhaseBoundaryType.</span><a class="headerlink" href="#id60" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>InitialPhaseBoundaryType</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Start, End</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The boundary of InitialPhase at which the constraint is calculated.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-customlinkageconstraint-finalphase"></span><table class="docutils align-default" id="id61">
<caption><span class="caption-number">Table 73 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.FinalPhase.</span><a class="headerlink" href="#id61" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FinalPhase</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Phase resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The second phase in the linkage for a Difference-mode constraint.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-customlinkageconstraint-finalphaseboundarytype"></span><table class="docutils align-default" id="id62">
<caption><span class="caption-number">Table 74 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.FinalPhaseBoundaryType.</span><a class="headerlink" href="#id62" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>FinalPhaseBoundaryType</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Start, End</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The boundary of FinalPhase at which the constraint is calculated.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-customlinkageconstraint-setmodelparameter"></span><table class="docutils align-default" id="id63">
<caption><span class="caption-number">Table 75 </span><span class="caption-text">User interface specification for CustomLinkageConstraint.SetModelParameter().</span><a class="headerlink" href="#id63" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>CustomLinkageConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter()</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>See <a class="reference internal" href="csalt-UserGuide.html#csalt-constrainttypes"><span class="std std-numref">Table 12</span></a></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>SetModelParameter() is an overloaded function for setting model data. In general, the argument is a tuple. The first element of the tuple is a string describing the parameter to be set, and the second element of the tuple is the value of the parameter.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>None</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>See <a class="reference internal" href="csalt-UserGuide.html#csalt-constrainttypes"><span class="std std-numref">Table 12</span></a></p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>ConstraintMode</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="emtgspacecraft">
<h2>EMTGSpacecraft<a class="headerlink" href="#emtgspacecraft" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-emtgspacecraft-spacecraftfile"></span><table class="docutils align-default" id="id64">
<caption><span class="caption-number">Table 76 </span><span class="caption-text">User interface specification for EMTGSpacecraft.SpacecraftFile.</span><a class="headerlink" href="#id64" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>EMTGSpacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SpacecraftFile</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Full file path (can be relative to ../data/emtg) AND name of <a href="#id1"><span class="problematic" id="id2">*</span></a>.emtg_spacecraftopt file</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>A valid EMTG spacecraft file</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-emtgspacecraft-spacecraftstage"></span><table class="docutils align-default" id="id65">
<caption><span class="caption-number">Table 77 </span><span class="caption-text">User interface specification for EMTGSpacecraft.SpacecraftStage.</span><a class="headerlink" href="#id65" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>EMTGSpacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SpacecraftStage</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>IntegerArray</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>[ 1 ]</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Integers that are less than or equal to the number of stages contained in the EMTG options file that is set using the “EMTGSpacecraftFile” field on EMTGInterface Resource</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>The EMTG spacecraft stage for each phase, where each position in the array corresponds to the phase index on the trajectory. (I.e., the second element in this array corresponds to the second phase’s stage). If there are more phases than listed stages that use this EmtgInterface object, the phases after the last stage will continue to use the last stage in the list. The elements of EMTGSpacecraftStage are 1-indexed.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>The array of stages does NOT skip coast phases. I.e., if the phase structure is coast-thrust-thrust, then [2,1] will result in stage 2 applying to the coast phase (and not being used) and stage 1 being applied to both thrust phases. [2,2,1] results in the first thrust phase using stage 2 and the second thrust phase using stage 1. If the length of the array is longer than the number of phases, then trailing elements of the array are not used. I.e., if there are i phases, then only the first i elements of EMTGSpacecraftStage are used.</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-emtgspacecraft-dutycycle"></span><table class="docutils align-default" id="id66">
<caption><span class="caption-number">Table 78 </span><span class="caption-text">User interface specification for EMTGSpacecraft.DutyCycle.</span><a class="headerlink" href="#id66" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>EMTGSpacecraft</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>DutyCycle</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Real</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p><img class="math" src="../../_images/math/cd3ebaedfedcf65cbfd99e17dd00c7f9ac72c873.png" alt="0 \leq \texttt{Real} \leq 1"/></p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Field to set the averaged duty cycle factor of the propulsion system.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolfunction">
<h2>OptimalControlFunction<a class="headerlink" href="#optimalcontrolfunction" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolfunction-function"></span><table class="docutils align-default" id="id67">
<caption><span class="caption-number">Table 79 </span><span class="caption-text">User interface specification for OptimalControlFunction.Function.</span><a class="headerlink" href="#id67" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Function</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Expression</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>“Expression”, “PatchedConicLaunch”, “IntegratedFlyby”, “PatchedConicFlyby”, “CelestialBodyRendezvous”</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets type of function of the OptimalControlFunction</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolfunction-for-function-field-expression">
<h2>OptimalControlFunction for Function Field = Expression<a class="headerlink" href="#optimalcontrolfunction-for-function-field-expression" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolfunction-expression-type"></span><table class="docutils align-default" id="id68">
<caption><span class="caption-number">Table 80 </span><span class="caption-text">User interface specification for OptimalControlFunction.Type for OptimalControlFunction.Function = Expression.</span><a class="headerlink" href="#id68" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:Expression</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the “meaning” of the OptimalControlFunction. I.e., is the function to be interpreted as a constraint or a cost function, etc.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-expression-phaselist"></span><table class="docutils align-default" id="id69">
<caption><span class="caption-number">Table 81 </span><span class="caption-text">User interface specification for OptimalControlFunction.PhaseList for OptimalControlFunction.Function = Expression.</span><a class="headerlink" href="#id69" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:Expression</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PhaseList</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT List</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Name of a single existing Phase resource and either Initial or Final. E.g., <img class="math" src="../../_images/math/240a1ee644ab12a1d810d566fe48711cfd11031f.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase1.Initial}\right\}"/> or <img class="math" src="../../_images/math/b575e8b2bea792cebe76fb0b2dbc1c9ee435f551.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase2.Final}\right\}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets on which phase and at which boundary of that phase the OptimalControlFunction Expression is to be evaluated.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-expression-setmodelparameter"></span><table class="docutils align-default" id="id70">
<caption><span class="caption-number">Table 82 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter for OptimalControlFunction.Function = Expression.</span><a class="headerlink" href="#id70" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:Expression</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>No default</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>(‘LowerBounds’, &lt;Real&gt;); (‘UpperBounds’, &lt;Real&gt;); (‘ScaleFactor’, &lt;Real&gt;); (‘Expression’, &lt;GMAT Equation as a string&gt;)</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Used to set parameters of an OptimalControlFunction. If OptimalControlFunction.Type = AlgebraicConstraint, then the tuple can be used to set the bounds on the constraint (‘LowerBounds’ or ‘UpperBounds’); to set the scale factor for the constraint (‘ScaleFactor’); and to set the constrained expression itself (‘Expression’).</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>Default GMAT units for the expression being set</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Type, Function</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolfunction-for-function-field-patchedconiclaunch">
<h2>OptimalControlFunction for Function Field = PatchedConicLaunch<a class="headerlink" href="#optimalcontrolfunction-for-function-field-patchedconiclaunch" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolfunction-patchedconiclaunch-type"></span><table class="docutils align-default" id="id71">
<caption><span class="caption-number">Table 83 </span><span class="caption-text">User interface specification for OptimalControlFunction.Type for OptimalControlFunction.Function = PatchedConicLaunch.</span><a class="headerlink" href="#id71" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:PatchedConicLaunch</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>When the user instantiates a PatchedConicLaunch OptimalControlFunction, it must be of type AlgebraicConstraint.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>Function</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-patchedconiclaunch-phaselist"></span><table class="docutils align-default" id="id72">
<caption><span class="caption-number">Table 84 </span><span class="caption-text">User interface specification for OptimalControlFunction.PhaseList for OptimalControlFunction.Function = PatchedConicLaunch.</span><a class="headerlink" href="#id72" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:PatchedConicLaunch</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PhaseList</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT List</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty list</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Name of a single existing Phase resource and either Initial or Final. E.g., <img class="math" src="../../_images/math/240a1ee644ab12a1d810d566fe48711cfd11031f.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase1.Initial}\right\}"/> or <img class="math" src="../../_images/math/b575e8b2bea792cebe76fb0b2dbc1c9ee435f551.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase2.Final}\right\}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets on which phase and at which boundary of that phase the PatchedConicLaunch is to be evaluated.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-patchedconiclaunch-setmodelparameter-centralbody-body-name"></span><table class="docutils align-default" id="id73">
<caption><span class="caption-number">Table 85 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘CentralBody’, ‘body name’) for OptimalControlFunction.Function = PatchedConicLaunch.</span><a class="headerlink" href="#id73" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:PatchedConicLaunch</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘CentralBody’, ‘body name’)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>The first element of the tuple must be a string: CentralBody. The second element of the tuple is a string whose value is a celestial body that an be interpreted by GMAT and whose ephemeris is accessible.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the central body of the patched-conic launch.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-patchedconiclaunch-setmodelparameter-emtglaunchvehicleoptionsfile-launch-vehicle-options-file"></span><table class="docutils align-default" id="id74">
<caption><span class="caption-number">Table 86 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘EMTGLaunchVehicleOptionsFile’, ‘launch vehicle options file’) for OptimalControlFunction.Function = PatchedConicLaunch.</span><a class="headerlink" href="#id74" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:PatchedConicLaunch</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘EMTGLaunchVehicleOptionsFile’, ‘launch vehicle options file’)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>The first element of the tuple must be a string: EMTGLaunchVehicleOptionsFile. The second element of the tuple is a string containing the name (including path relative to the GMAT executable directory) of an EMTG launch vehicle options file.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the EMTG launch vehicle file in which the launch vehicle characteristics are described.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-patchedconiclaunch-setmodelparameter-vehiclename-name-of-launch-vehicle"></span><table class="docutils align-default" id="id75">
<caption><span class="caption-number">Table 87 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘VehicleName’, ‘name of launch vehicle’) for OptimalControlFunction.Function = PatchedConicLaunch.</span><a class="headerlink" href="#id75" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:PatchedConicLaunch</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘VehicleName’, ‘name of launch vehicle’)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>The first element of the tuple must be a string: VehicleName. The second element of the tuple is the name of a launch vehicle described in the specified EMTG launch vehicle options file.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the specific name of the launch vehicle within the provided EMTG launch vehicle options file.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>SetModelParameter(‘EMTGLaunchVehicleOptionsFile’, ‘launch vehicle options file’)</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolfunction-for-function-field-celestialbodyrendezvous">
<h2>OptimalControlFunction for Function Field = CelestialBodyRendezvous<a class="headerlink" href="#optimalcontrolfunction-for-function-field-celestialbodyrendezvous" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolfunction-celestialbodyrendezvous-type"></span><table class="docutils align-default" id="id76">
<caption><span class="caption-number">Table 88 </span><span class="caption-text">User interface specification for OptimalControlFunction.Type for OptimalControlFunction.Function = CelestialBodyRendezvous.</span><a class="headerlink" href="#id76" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:CelestialBodyRendezvous</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>When the user instantiates a CelestialBodyRendezvous OptimalControlFunction, it must be of type AlgebraicConstraint.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-celestialbodyrendezvous-phaselist"></span><table class="docutils align-default" id="id77">
<caption><span class="caption-number">Table 89 </span><span class="caption-text">User interface specification for OptimalControlFunction.PhaseList for OptimalControlFunction.Function = CelestialBodyRendezvous.</span><a class="headerlink" href="#id77" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:CelestialBodyRendezvous</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PhaseList</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT List</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>Empty list</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>Name of a single existing Phase resource and either Initial or Final. E.g., <img class="math" src="../../_images/math/240a1ee644ab12a1d810d566fe48711cfd11031f.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase1.Initial}\right\}"/> or <img class="math" src="../../_images/math/b575e8b2bea792cebe76fb0b2dbc1c9ee435f551.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase2.Final}\right\}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets on which phase and at which boundary of that phase the CelestialBodyRendezvous is to be evaluated.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-celestialbodyrendezvous-setmodelparameter-celestialbody-body-name"></span><table class="docutils align-default" id="id78">
<caption><span class="caption-number">Table 90 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘CelestialBody’, ‘body name’) for OptimalControlFunction.Function = CelestialBodyRendezvous.</span><a class="headerlink" href="#id78" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:CelestialBodyRendezvous</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘CelestialBody’, ‘body name’)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>The first element of the tuple must be a string: CelestialBody. The second element of the tuple is a string whose value is a celestial body that an be interpreted by GMAT and whose ephemeris is accessible.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the celestial body with which the spacecraft is to rendezvous.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
</section>
<section id="optimalcontrolfunction-for-function-field-integratedflyby">
<h2>OptimalControlFunction for Function Field = IntegratedFlyby<a class="headerlink" href="#optimalcontrolfunction-for-function-field-integratedflyby" title="Permalink to this heading">¶</a></h2>
<span id="gmatoc-uispec-optimalcontrolfunction-integratedflyby-type"></span><table class="docutils align-default" id="id79">
<caption><span class="caption-number">Table 91 </span><span class="caption-text">User interface specification for OptimalControlFunction.Type for OptimalControlFunction.Function = IntegratedFlyby.</span><a class="headerlink" href="#id79" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:IntegratedFlyby</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>Type</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>String</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>AlgebraicConstraint</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>When the user instantiates an IntegratedFlyby OptimalControlFunction, it must be of type AlgebraicConstraint.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-integratedflyby-phaselist"></span><table class="docutils align-default" id="id80">
<caption><span class="caption-number">Table 92 </span><span class="caption-text">User interface specification for OptimalControlFunction.PhaseList for OptimalControlFunction.Function = IntegratedFlyby.</span><a class="headerlink" href="#id80" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:IntegratedFlyby</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>PhaseList</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>GMAT List</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>empty list</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>List must consist of exactly 2 existing Phase resources and either Initial and Final on both. E.g., <img class="math" src="../../_images/math/7de2af504a801fadc9314e72a6b507df75dbdffa.png" alt="\texttt{PhaseList} = \left\{\texttt{Phase1.Final, Phase2.Initial}\right\}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the phase preceeding the flyby event and the phase following the flyby event.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-integratedflyby-setmodelparameter-celestialbody-body-name"></span><table class="docutils align-default" id="id81">
<caption><span class="caption-number">Table 93 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘CelestialBody’, ‘body name’) for OptimalControlFunction.Function = IntegratedFlyby.</span><a class="headerlink" href="#id81" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:IntegratedFlyby</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘CelestialBody’, ‘body name’)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>The first element of the tuple must be a string: CelestialBody. The second element of the tuple is a string whose value is a celestial body that an be interpreted by GMAT and whose ephemeris is accessible.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Sets the celestial body that is the central body of the integrated flyby.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-integratedflyby-setmodelparameter-periapsisradiuslowerbound-value"></span><table class="docutils align-default" id="id82">
<caption><span class="caption-number">Table 94 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘PeriapsisRadiusLowerBound’, value) for OptimalControlFunction.Function = IntegratedFlyby.</span><a class="headerlink" href="#id82" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:IntegratedFlyby</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘PeriapsisRadiusLowerBound’, value)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>6978</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>First element of tuple must be a string: PeriapsisRadiusLowerBound. The second element of the tuple is a <img class="math" src="../../_images/math/0ab5aeee3ddceabfe1d3ed8062a38beb3900964e.png" alt="0 &lt; \texttt{Real} &lt; \texttt{Inf}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Lower bound of periapse of flyby with respect to the central body’s center of mass.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>km</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
<div class="line-block">
<div class="line"><br /></div>
</div>
<span id="gmatoc-uispec-optimalcontrolfunction-integratedflyby-setmodelparameter-periapsisradiusupperbound-value"></span><table class="docutils align-default" id="id83">
<caption><span class="caption-number">Table 95 </span><span class="caption-text">User interface specification for OptimalControlFunction.SetModelParameter(‘PeriapsisRadiusUpperBound’, value) for OptimalControlFunction.Function = IntegratedFlyby.</span><a class="headerlink" href="#id83" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 50%" />
<col style="width: 50%" />
</colgroup>
<tbody>
<tr class="row-odd"><td><p>Resource/Command Name</p></td>
<td><p>OptimalControlFunction:IntegratedFlyby</p></td>
</tr>
<tr class="row-even"><td><p>Field Name</p></td>
<td><p>SetModelParameter(‘PeriapsisRadiusUpperBound’, value)</p></td>
</tr>
<tr class="row-odd"><td><p>Data Type</p></td>
<td><p>Tuple</p></td>
</tr>
<tr class="row-even"><td><p>Default Value</p></td>
<td><p>1000000</p></td>
</tr>
<tr class="row-odd"><td><p>Allowed Values</p></td>
<td><p>First element of tuple must be a string: PeriapsisRadiusUpperBound. The second element of the tuple is a <img class="math" src="../../_images/math/0ab5aeee3ddceabfe1d3ed8062a38beb3900964e.png" alt="0 &lt; \texttt{Real} &lt; \texttt{Inf}"/>.</p></td>
</tr>
<tr class="row-even"><td><p>Description</p></td>
<td><p>Upper bound of periapse of flyby with respect to the central body’s center of mass.</p></td>
</tr>
<tr class="row-odd"><td><p>Notes</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Units</p></td>
<td><p>km</p></td>
</tr>
<tr class="row-odd"><td><p>Field Couplings</p></td>
<td><p>N/A</p></td>
</tr>
<tr class="row-even"><td><p>Access</p></td>
<td><p>set</p></td>
</tr>
<tr class="row-odd"><td><p>Interfaces</p></td>
<td><p>script</p></td>
</tr>
</tbody>
</table>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">User Interface Specification</a><ul>
<li><a class="reference internal" href="#trajectory">Trajectory</a></li>
<li><a class="reference internal" href="#phase">Phase</a></li>
<li><a class="reference internal" href="#dynamicsconfiguration">DynamicsConfiguration</a></li>
<li><a class="reference internal" href="#optimalcontrolguess">OptimalControlGuess</a></li>
<li><a class="reference internal" href="#customlinkageconstraint">CustomLinkageConstraint</a></li>
<li><a class="reference internal" href="#emtgspacecraft">EMTGSpacecraft</a></li>
<li><a class="reference internal" href="#optimalcontrolfunction">OptimalControlFunction</a></li>
<li><a class="reference internal" href="#optimalcontrolfunction-for-function-field-expression">OptimalControlFunction for Function Field = Expression</a></li>
<li><a class="reference internal" href="#optimalcontrolfunction-for-function-field-patchedconiclaunch">OptimalControlFunction for Function Field = PatchedConicLaunch</a></li>
<li><a class="reference internal" href="#optimalcontrolfunction-for-function-field-celestialbodyrendezvous">OptimalControlFunction for Function Field = CelestialBodyRendezvous</a></li>
<li><a class="reference internal" href="#optimalcontrolfunction-for-function-field-integratedflyby">OptimalControlFunction for Function Field = IntegratedFlyby</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="csalt-UserGuide.html"
                          title="previous chapter">User Guide</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="csalt-EMTGSpacecraftFileSpec-smallTables.html"
                          title="next chapter">EMTG Spacecraft File Specification</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/CSALT/source/csalt-UserInterfaceSpec-smallTables.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="csalt-EMTGSpacecraftFileSpec-smallTables.html" title="EMTG Spacecraft File Specification"
             >next</a> |</li>
        <li class="right" >
          <a href="csalt-UserGuide.html" title="User Guide"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" >GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Interface Specification</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>