
<!DOCTYPE html>

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><meta name="generator" content="Docutils 0.18.1: http://docutils.sourceforge.net/" />

    <title>User Guide &#8212; GMAT Tools and Extensions R2025a documentation</title>
    <link rel="stylesheet" type="text/css" href="../../_static/pygments.css" />
    <link rel="stylesheet" type="text/css" href="../../_static/classic.css" />
    
    <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
    <script src="../../_static/jquery.js"></script>
    <script src="../../_static/underscore.js"></script>
    <script src="../../_static/_sphinx_javascript_frameworks_compat.js"></script>
    <script src="../../_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="User Interface Specification" href="csalt-UserInterfaceSpec-smallTables.html" />
    <link rel="prev" title="Concepts and Algorithms" href="csalt-ConceptsAndAlgorithms.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="csalt-UserInterfaceSpec-smallTables.html" title="User Interface Specification"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="csalt-ConceptsAndAlgorithms.html" title="Concepts and Algorithms"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" accesskey="U">GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Guide</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <section id="user-guide">
<span id="sec-gmatoc-userguide"></span><h1>User Guide<a class="headerlink" href="#user-guide" title="Permalink to this heading">¶</a></h1>
<p>This section contains the user guide for CSALT and the GMAT Optimal Control subsystem that is built upon CSALT. To provide context, the high-level architecture of CSALT and the GMAT Optimal Control subsystem is shown in <a class="reference internal" href="#csalt-architecture"><span class="std std-numref">Fig. 4</span></a>.  The core of the system is the CSALT API. The GMAT Optimal Control plugin is built upon the CSALT API and is integrated into GMAT using the GMAT plugin interface.</p>
<figure class="align-center" id="id70">
<span id="csalt-architecture"></span><a class="reference internal image-reference" href="../../_images/CSALT_Architecture.png"><img alt="../../_images/CSALT_Architecture.png" src="../../_images/CSALT_Architecture.png" style="width: 800.0px; height: 600.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 4 </span><span class="caption-text">High-level architecture of CSALT and GMAT Optimal Control.</span><a class="headerlink" href="#id70" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>This section contains two tutorials and extensive reference material for CSALT and GMAT Optimal Control components. Tutorials include how to use CSALT as a stand-alone C++ library, and how to use the GMAT Optimal Control subsystem. For each interface, we include a high-level overview of the classes/Resources used to specify optimal control problems and intermediate-level details for key classes. Additional detailed reference material is provided in <a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#sec-gmatoc-uispec"><span class="std std-ref">User Interface Specification</span></a> and <a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#emtgspacecraftfilespec-label"><span class="std std-ref">EMTG Spacecraft File Specification</span></a> and via Doxygen output and extensive examples that are included in the CSALT distribution.</p>
<section id="tutorial-setting-up-an-optimal-control-problem-in-csalt">
<h2>Tutorial: Setting Up an Optimal Control Problem in CSALT<a class="headerlink" href="#tutorial-setting-up-an-optimal-control-problem-in-csalt" title="Permalink to this heading">¶</a></h2>
<p>In this section, we describe how to set up an optimal control problem using the Brachistichrone problem as an example.  We begin by providing an overview of the problem setup procedures for a general optimal control problem, then present the Brachistichrone problem statement, followed by the C++ source code for the Brachistichrone problem.</p>
<section id="csalt-c-user-interface-component-overview">
<h3>CSALT C++ User Interface Component Overview<a class="headerlink" href="#csalt-c-user-interface-component-overview" title="Permalink to this heading">¶</a></h3>
<p>The CSALT API is composed of several classes shown in <a class="reference internal" href="#csalt-cpp-classes"><span class="std std-numref">Fig. 5</span></a>.  The user classes that define the API are Trajectory, Phase, UserPathFunction, UserPointFunction, OptimalControlFunction, and Publisher.  Note that UserFunction is an abstract base class that contains commonality between path and point functions.</p>
<figure class="align-center" id="id71">
<span id="csalt-cpp-classes"></span><a class="reference internal image-reference" href="../../_images/CSALT_CPP_Classes.png"><img alt="../../_images/CSALT_CPP_Classes.png" src="../../_images/CSALT_CPP_Classes.png" style="width: 841.5px; height: 585.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 5 </span><span class="caption-text">CSALT C++ user classes.</span><a class="headerlink" href="#id71" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>The responsibilities of classes in the CSALT API are highlighted in <a class="reference internal" href="#csalt-classes"><span class="std std-numref">Table 9</span></a>.</p>
<span id="csalt-classes"></span><table class="docutils align-default" id="id72">
<caption><span class="caption-number">Table 9 </span><span class="caption-text">CSALT API classes.</span><a class="headerlink" href="#id72" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 15%" />
<col style="width: 60%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Class</p></th>
<th class="head"><p>Required?</p></th>
<th class="head"><p>Responsibility</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Trajectory</p></td>
<td><p>Yes</p></td>
<td><p>Trajectory is the container for all phases in the problem and for the UserPointFunction and UserPathFunction objects.  Internally, the Trajectory concatenates functions and Jacobians provided on different phases and from UserPointFunction.</p></td>
</tr>
<tr class="row-odd"><td><p>Phase</p></td>
<td><p>Yes</p></td>
<td><p>Phase defines the transcription employed and manages/concatenates the defect constraints, integral cost, and algebraic path functions.</p></td>
</tr>
<tr class="row-even"><td><p>UserPathFunction</p></td>
<td><p>Yes</p></td>
<td><p>Computes path function values (dynamics, integral cost, and algebraic path constraints) and, optionally, analytic Jacobians of path functions.  This class provides the generic interface for those computations. The user provides problem-specific functions by deriving a path function class from the UserPathFunction base class.</p></td>
</tr>
<tr class="row-odd"><td><p>UserPointFunction</p></td>
<td><p>Yes</p></td>
<td><p>Computes boundary functions (cost and algebraic point constraints) and, optionally, analytic boundary Jacobians. This class provides the generic interface for those computations. The user provides problem-specific functions by deriving a point function class from the UserPointFunction base class.</p></td>
</tr>
<tr class="row-even"><td><p>OptimalControlFunction</p></td>
<td><p>No</p></td>
<td><p>A helper class for optimal control functions and Jacobians.  This class provides a generic interface for optimal control functions and helps modularize problems that have large numbers of boundary constraints and allows the user to re-use functions across different problems without duplicating code.</p></td>
</tr>
<tr class="row-odd"><td><p>Publisher</p></td>
<td><p>No</p></td>
<td><p>A helper class to publish data before, during, or after optimization.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="configuring-files-for-tutorial">
<h3>Configuring Files for Tutorial<a class="headerlink" href="#configuring-files-for-tutorial" title="Permalink to this heading">¶</a></h3>
<p>Configuring and executing files for the tutorial is done by adding the example problem to the already existing example problems.</p>
<p>Six C++ files must be created: a driver .cpp file, a path function .cpp file, a point function .cpp, and header .hpp files for each .cpp file. The driver class derives from the <img class="math" src="../../_images/math/081ec909861c6834361af0ff5aad40fd076daa10.png" alt="\texttt{CsaltTestDriver}"/> class; the path function class derives from the <img class="math" src="../../_images/math/c42f62196250d9fba7b7d5a1d0c36625cb36dde3.png" alt="\texttt{UserPathFunction class}"/>; and the point function class derives from the <img class="math" src="../../_images/math/87f28ac87eb9b0e56f69840aca2652d87b44f6d9.png" alt="\texttt{UserPointFunction}"/> class. Driver .cpp and .hpp files are placed in the gmat/src/csaltTester/src/TestOptCtrl/drivers directory. Path function and point function .cpp and .hpp files are placed in the gmat/src/csaltTester/src/TestOptCtrl/src/pointpath directory. Depending on the method of compilation used, paths to .cpp files may need to be added to the list of <img class="math" src="../../_images/math/e2f7f41952159f1e112f27be44cbedae6aabe442.png" alt="\texttt{CSALT\_TEST\_SRCS}"/> in gmat/src/csaltTester/CMakeLists.txt.</p>
<p>Once the driver class, point function class, and path function class are created, the problem is added to the list of test problems in TestOptCtrl.cpp. Within TestOptCtrl.cpp, do the following:</p>
<ol class="arabic simple">
<li><p>Find text “<img class="math" src="../../_images/math/619ac5f25ae3069e9c1c8eaf4359e066a6ab44a6.png" alt="\texttt{*driver21 = new HohmannTransferDriver()}"/>”. At the end of the list of driver objects, add your own in the same style as the others. Note that this code block occurs in two places in the file, and both locations should be updated.</p></li>
<li><p>Directly below the previously referenced list of <img class="math" src="../../_images/math/7050f278a486a1db9bc7de6175880dfa5327321b.png" alt="\texttt{driver}"/> objects, add a call to the <img class="math" src="../../_images/math/9d40500b97e3139b00783ec05ebfc9a055bc35b3.png" alt="\texttt{Run}"/> method of your driver to the end of the list of <img class="math" src="../../_images/math/9d40500b97e3139b00783ec05ebfc9a055bc35b3.png" alt="\texttt{Run}"/> methods already in the file. Note that this code block occurs in two places in the file, and both locations should be updated.</p></li>
<li><p>Delete your driver object. (Add to list of delete calls directly below list of Run calls.) Note that this code block occurs in two places in the file, and both locations should be updated.</p></li>
<li><p>Find text “<img class="math" src="../../_images/math/c91896af2078dc2ea58517d7b67745abe8938257.png" alt="\texttt{else if (test == &quot;HohmannTransfer&quot;)}"/>”. At the end of the list of <img class="math" src="../../_images/math/d5dc8629762706eb1f05e380fca2eebd42941e06.png" alt="\texttt{else if}"/> blocks, add a new <img class="math" src="../../_images/math/d5dc8629762706eb1f05e380fca2eebd42941e06.png" alt="\texttt{else if}"/> block for your own CSALT driver. Note that this code block occurs in two places in the file, and both locations should be updated.</p></li>
<li><p>Find text “<img class="math" src="../../_images/math/b1b75b6b4a4d19b6b0007091bec4134be5938909.png" alt="\texttt{msg += &quot;   HohmannTransfer&quot;}"/>”. At the end of the list of additions to the <img class="math" src="../../_images/math/f99922ef05e0ce6259b5c32abac8b1e089fb78d7.png" alt="\texttt{msg}"/> variable – but prior to the new line before the <img class="math" src="../../_images/math/9410828c54b69d173e68a0ca2f3545d04bc27a5f.png" alt="\texttt{&quot;exit&quot;}"/> line – add to <img class="math" src="../../_images/math/f99922ef05e0ce6259b5c32abac8b1e089fb78d7.png" alt="\texttt{msg}"/> the name of your example, modeled after the problems already listed in the file. Note that this code block occurs in two places in the file, and both locations should be updated.</p></li>
<li><p>Find text “<img class="math" src="../../_images/math/619ac5f25ae3069e9c1c8eaf4359e066a6ab44a6.png" alt="\texttt{*driver21 = new HohmannTransferDriver()}"/>”. Add a line creating your driver with an unused pointer name at the end of the block of pointer declarations. Scroll down to the next blocks of code, and add a <img class="math" src="../../_images/math/7e8a81c2b20714bff4d05301d6be0eb3e1bd923b.png" alt="\texttt{Run()}"/> command and <img class="math" src="../../_images/math/92633b873e521b94d12491b92fed90242f1cb132.png" alt="\texttt{Delete}"/> command for the pointer to your driver class.</p></li>
<li><p>Find text “<img class="math" src="../../_images/math/c8f5bab86271c6408fae326a507ded16aebb82b4.png" alt="\texttt{test = &quot;HohmannTransferDriver&quot;}"/>”. Add a case block for your driver to the bottom of the already existing list of case blocks.</p></li>
</ol>
</section>
<section id="overview-of-csalt-configuration-process">
<h3>Overview of CSALT Configuration Process<a class="headerlink" href="#overview-of-csalt-configuration-process" title="Permalink to this heading">¶</a></h3>
<p>The key steps in setting up an optimization problem in CSALT are:</p>
<ol class="arabic simple">
<li><p>Configure the Path Functions</p></li>
<li><p>Configure the Boundary Functions</p></li>
<li><p>Configure the Phases and Transcription</p></li>
<li><p>Configure the Trajectory</p></li>
<li><p>Solve the problem and examine results</p></li>
</ol>
<p>To solve the Brachistichrone problem using CSALT,  let’s begin with the Brachistichrone problem statement:</p>
<p>Minimize the final time</p>
<div class="math">
<p><img src="../../_images/math/0a3cba5ae4eaea5833f5b1648b61891c52cce47e.png" alt="\textrm{Minimize } J = t_f"/></p>
</div><p>Subject to the dynamics</p>
<div class="math">
<p><img src="../../_images/math/17b7ab04e05b91960fc0411846341ceb85c40985.png" alt="\dot{x} &amp; = v \sin u \\
\dot{y} &amp; = v \cos u \\
\dot{v} &amp; = g_0 \cos u"/></p>
</div><p>the initial boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/7a40a97f4d65fa00a40883c535f3846f6e4dc878.png" alt="t_0 &amp; = 0 \\
x \left( t_0 \right) &amp; = 0 \\
y \left( t_0 \right) &amp; = 0 \\
v \left( t_0 \right) &amp; = 0"/></p>
</div><p>and the final boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/4f146728930f1f46298077a3c9459aeaa8b19193.png" alt="0 \leq t_f &amp; \leq 10 \\
x \left( t_f \right) &amp; = 1 \\
-10 \leq y \left( t_f \right) &amp; \leq 10 \\
-10 \leq v \left( t_f \right) &amp; \leq 0"/></p>
</div></section>
<section id="step-1-configure-the-path-functions">
<h3>Step 1: Configure the Path Functions<a class="headerlink" href="#step-1-configure-the-path-functions" title="Permalink to this heading">¶</a></h3>
<p>Path functions, including dynamics, algebraic path constraints, and, if applicable, an integral cost function, are implemented by deriving a class from the CSALT UserPathFunction base class and implementing the EvaluateFunctions() and EvaluateJacobians() methods. The EvaluateFunctions() method is required. The EvaluateJacobians() method is required but may be empty. If EvaluateJacobians() does not set a particular Jacobian, CSALT will finite-difference that Jacobian. For the Brachistichrone example, we have provided all path function Jacobians analytically in the source code.</p>
<p>The dynamics model for the Brachistichrone problem is:</p>
<div class="math">
<p><img src="../../_images/math/17b7ab04e05b91960fc0411846341ceb85c40985.png" alt="\dot{x} &amp; = v \sin u \\
\dot{y} &amp; = v \cos u \\
\dot{v} &amp; = g_0 \cos u"/></p>
</div><p>The EvaluateFunctions() method that implements those dynamics is shown below. For this example, the Path Function class is named BrachistichronePathObject.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Class constructor</span>
<span class="n">BrachistichronePathObject</span><span class="o">::</span><span class="n">BrachistichronePathObject</span><span class="p">()</span><span class="w"> </span><span class="o">:</span><span class="w"></span>
<span class="n">UserPathFunction</span><span class="p">(),</span><span class="w"></span>
<span class="n">gravity</span><span class="p">(</span><span class="mf">-32.174</span><span class="p">)</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>

<span class="c1">// The EvaluateFunctions method</span>
<span class="kt">void</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="o">::</span><span class="n">EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Compute the ODEs</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">xdot</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">ydot</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">vdot</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gravity</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the ODEs by calling SetFunctions with DYNAMICS enum value</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="nf">dynFunctions</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="n">xdot</span><span class="p">,</span><span class="w"> </span><span class="n">ydot</span><span class="p">,</span><span class="w"> </span><span class="n">vdot</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">dynFunctions</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="o">::</span><span class="n">EvaluateJacobians</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Get state and control</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics state Jacobian</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dxdot_dv</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dydot_dv</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="nf">dynState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="n">dxdot_dv</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="n">dydot_dv</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics control Jacobian</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dxdot_du</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dydot_du</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">-</span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dvdot_du</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">-</span><span class="n">gravity</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="nf">dynControl</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">dxdot_du</span><span class="p">,</span><span class="w"> </span><span class="n">dydot_du</span><span class="p">,</span><span class="w"> </span><span class="n">dvdot_du</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics time Jacobian</span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="nf">dynTime</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the Jacobians</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">STATE</span><span class="p">,</span><span class="w"> </span><span class="n">dynState</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">CONTROL</span><span class="p">,</span><span class="w"> </span><span class="n">dynControl</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">TIME</span><span class="p">,</span><span class="w"> </span><span class="n">dynTime</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Note that CSALT uses GMAT variable types like Real, Rvector, and Rmatrix.</p>
</section>
<section id="step-2-configure-the-boundary-functions">
<h3>Step 2: Configure the Boundary Functions<a class="headerlink" href="#step-2-configure-the-boundary-functions" title="Permalink to this heading">¶</a></h3>
<p>Boundary functions, including algebraic constraints and cost function, are implemented by deriving a class from the CSALT UserPointFunction base class and implementing the EvaluateFunctions() method. The EvaluateFunctions() method is required.</p>
<p>The boundary functions for the Brachistochrone problem are</p>
<div class="math">
<p><img src="../../_images/math/0a3cba5ae4eaea5833f5b1648b61891c52cce47e.png" alt="\textrm{Minimize } J = t_f"/></p>
</div><p>subject to the initial boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/7a40a97f4d65fa00a40883c535f3846f6e4dc878.png" alt="t_0 &amp; = 0 \\
x \left( t_0 \right) &amp; = 0 \\
y \left( t_0 \right) &amp; = 0 \\
v \left( t_0 \right) &amp; = 0"/></p>
</div><p>and the final boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/4f146728930f1f46298077a3c9459aeaa8b19193.png" alt="0 \leq t_f &amp; \leq 10 \\
x \left( t_f \right) &amp; = 1 \\
-10 \leq y \left( t_f \right) &amp; \leq 10 \\
-10 \leq v \left( t_f \right) &amp; \leq 0"/></p>
</div><p>The EvaluateFunctions() method that implements those boundary functions is shown below. For this example, the Point Function class is named BrachistichronePointObject.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">//-------------------------------------------------------------------------</span>
<span class="c1">// void EvaluateFunctions()</span>
<span class="c1">//-------------------------------------------------------------------------</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">BrachistichronePointObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetInitialStateVector</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetFinalStateVector</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">tInit</span><span class="o">=</span><span class="w"> </span><span class="n">GetInitialTime</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">tFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetFinalTime</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">xInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">yInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">vInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">xFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">yFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">vFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Dimension the function array and bounds arrays</span>
<span class="w">    </span><span class="n">Integer</span><span class="w"> </span><span class="n">numFunctions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">8</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">funcValues</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">lowerBounds</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">upperBounds</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Initial Time Constraint: t_0 = 0;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">tInit</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint on initial x value: x(0) = 0.0;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">xInit</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint on initial y value : y(0) = 0.0;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">yInit</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint on initial v value : v(0) = 0.0;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">vInit</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Final time constraint : 0.0 &lt;= t_F = 10;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">tFinal</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">10.00</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint on final x value : x(t_f) = 1.0;</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">xFinal</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint on final y value; -10 &lt;= y(t_f) &lt;= 10</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">yFinal</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Constraint of final v value: -10 &lt;= v(t_f) &lt;= 0.0</span>
<span class="w">    </span><span class="n">funcValues</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">vFinal</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the cost and constraint functions</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">costFunc</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">tFinal</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">costFunc</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">funcValues</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">LOWER</span><span class="p">,</span><span class="w"> </span><span class="n">lowerBounds</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">UPPER</span><span class="p">,</span><span class="w"> </span><span class="n">upperBounds</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Note that interfaces for configuring problems with large numbers of boundary constraints and/or analytic Jacobians are explained in <a class="reference internal" href="#sec-gmatoc-boundaryfunctionsreference"><span class="std std-ref">Boundary Functions Reference</span></a>.</p>
</section>
<section id="step-3-configure-the-phases-and-transcription">
<h3>Step 3: Configure the Phases and Transcription<a class="headerlink" href="#step-3-configure-the-phases-and-transcription" title="Permalink to this heading">¶</a></h3>
<p>The next step in configuring the Brachistochrone problem is to set up a Phase. (This example is a single-phase problem.)  The Phase object contains the configuration for the selected transcription, the bounds on optimization parameters (e.g., time, state, and control), and the initial guesses for those parameters.  In this example, the phase is configured to use Radau orthogonal collocation with two equally spaced mesh intervals, each containing five points.  (The mesh interval configuration and initial-guess options are presented in more detail in <a class="reference internal" href="#sec-gmatoc-phasereference"><span class="std std-ref">Phase Reference</span></a>).</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create a phase and set transcription configuration</span>
<span class="n">RadauPhase</span><span class="w"> </span><span class="o">*</span><span class="n">phase1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">();</span><span class="w"></span>
<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">initialGuessMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;LinearNoControl&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">meshIntervalFractions</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mf">-1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>
<span class="n">IntegerArray</span><span class="w"> </span><span class="n">meshIntervalNumPoints</span><span class="p">;</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Set time properties</span>
<span class="n">Real</span><span class="w"> </span><span class="n">timeLowerBound</span><span class="w">   </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">timeUpperBound</span><span class="w">   </span><span class="o">=</span><span class="w"> </span><span class="mf">100.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">initialGuessTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">finalGuessTime</span><span class="w">   </span><span class="o">=</span><span class="w"> </span><span class="mf">.3</span><span class="p">;</span><span class="w"></span>

<span class="c1">// Set state properties</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numStateVars</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">stateLowerBound</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">,</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">,</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">stateUpperBound</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w">  </span><span class="mf">10.0</span><span class="p">,</span><span class="w">   </span><span class="mf">0.0</span><span class="p">,</span><span class="w">   </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">initialGuessState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w">   </span><span class="mf">0.0</span><span class="p">,</span><span class="w">   </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">finalGuessState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w">   </span><span class="mf">2.0</span><span class="p">,</span><span class="w">  </span><span class="mf">-1.0</span><span class="p">,</span><span class="w">  </span><span class="mf">-1.0</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Set control properties</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numControlVars</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">controlUpperBound</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w">  </span><span class="mf">10.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">controlLowerBound</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Set the phase configuration</span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessMode</span><span class="p">(</span><span class="n">initialGuessMode</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessArrays</span><span class="p">(</span><span class="n">timeArray</span><span class="p">,</span><span class="w"> </span><span class="n">stateArray</span><span class="p">,</span><span class="w"> </span><span class="n">controlArray</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumStateVars</span><span class="p">(</span><span class="n">numStateVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumControlVars</span><span class="p">(</span><span class="n">numControlVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalFractions</span><span class="p">(</span><span class="n">meshIntervalFractions</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalNumPoints</span><span class="p">(</span><span class="n">meshIntervalNumPoints</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateLowerBound</span><span class="p">(</span><span class="n">stateLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateUpperBound</span><span class="p">(</span><span class="n">stateUpperBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateInitialGuess</span><span class="p">(</span><span class="n">initialGuessState</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateFinalGuess</span><span class="p">(</span><span class="n">finalGuessState</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeLowerBound</span><span class="p">(</span><span class="n">timeLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeUpperBound</span><span class="p">(</span><span class="n">timeUpperBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeInitialGuess</span><span class="p">(</span><span class="n">initialGuessTime</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeFinalGuess</span><span class="p">(</span><span class="n">finalGuessTime</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetControlLowerBound</span><span class="p">(</span><span class="n">controlLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetControlUpperBound</span><span class="p">(</span><span class="n">controlUpperBound</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="step-4-configure-the-trajectory">
<h3>Step 4: Configure the Trajectory<a class="headerlink" href="#step-4-configure-the-trajectory" title="Permalink to this heading">¶</a></h3>
<p>The next step is to add the path function, boundary function, and phase objects to a Trajectory.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create a trajectory object</span>
<span class="c1">// To illustrate the high level approach, default tolerances are used.</span>
<span class="n">traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>

<span class="c1">// Create the path and point function objects and add to Trajectory</span>
<span class="n">pathObject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="p">();</span><span class="w"></span>
<span class="n">pointObject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetUserPathFunction</span><span class="p">(</span><span class="n">pathObject</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetUserPointFunction</span><span class="p">(</span><span class="n">pointObject</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Add the Phase to the Trajectory</span>
<span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">Phase</span><span class="o">*&gt;</span><span class="w"> </span><span class="n">phaseList</span><span class="p">;</span><span class="w"></span>
<span class="n">phaseList</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="n">phase1</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetPhaseList</span><span class="p">(</span><span class="n">phaseList</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="step-5-run-the-optimizer-and-examine-the-solution">
<h3>Step 5: Run the Optimizer and Examine the Solution<a class="headerlink" href="#step-5-run-the-optimizer-and-examine-the-solution" title="Permalink to this heading">¶</a></h3>
<p>Now that the path, boundary, phase, and trajectory objects are configured, we are ready to optimize the problem and write the solution to a file.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Optimize the problem</span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">Optimize</span><span class="p">();</span><span class="w"></span>

<span class="c1">// Write the solution to file</span>
<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">solutionFile</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;DocTestFile.och&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">WriteToFile</span><span class="p">(</span><span class="n">solutionFile</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>The console output for the Brachistichrone problem solved using SNOPT is shown in <a class="reference internal" href="#csalt-snopt-output"><span class="std std-numref">Fig. 6</span></a>.</p>
<figure class="align-center" id="id73">
<span id="csalt-snopt-output"></span><a class="reference internal image-reference" href="../../_images/CSALT_SNOPT_Output.png"><img alt="../../_images/CSALT_SNOPT_Output.png" src="../../_images/CSALT_SNOPT_Output.png" style="width: 513.0px; height: 422.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 6 </span><span class="caption-text">Brachistichrone problem optimization console output.</span><a class="headerlink" href="#id73" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>In the example above, we called WriteToFile() to write the state and control history to a text file. The contents of the file are shown in <a class="reference internal" href="#csalt-och-output"><span class="std std-numref">Fig. 7</span></a>.</p>
<figure class="align-center" id="id74">
<span id="csalt-och-output"></span><a class="reference internal image-reference" href="../../_images/CSALT_OCH_Output.png"><img alt="../../_images/CSALT_OCH_Output.png" src="../../_images/CSALT_OCH_Output.png" style="width: 951.0px; height: 372.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 7 </span><span class="caption-text">Brachistichrone problem file output.</span><a class="headerlink" href="#id74" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
</section>
<section id="additional-tutorials">
<h3>Additional Tutorials<a class="headerlink" href="#additional-tutorials" title="Permalink to this heading">¶</a></h3>
<p>The C++ version of CSALT is distributed with many example problems. The examples are located in the folder gmat/src/csaltTester/src/TestOptCtrl/src.  The subfolders “drivers” and “pointpath” contain the test drivers and point and path objects, respectively.</p>
</section>
</section>
<section id="tutorial-setting-up-an-optimal-control-problem-in-matlab">
<h2>Tutorial: Setting up an Optimal Control Problem in MATLAB<a class="headerlink" href="#tutorial-setting-up-an-optimal-control-problem-in-matlab" title="Permalink to this heading">¶</a></h2>
<p>In this section, we describe how to set up an optimal control problem using the Brachistichrone problem as an example.  We begin by providing an overview of the problem setup procedures for a general optimal control problem, then present the Brachistichrone problem statement, followed by the MATLAB source code for the Brachistichrone problem.</p>
<section id="matlab-user-interface-component-overview">
<h3>MATLAB User Interface Component Overview<a class="headerlink" href="#matlab-user-interface-component-overview" title="Permalink to this heading">¶</a></h3>
<p>The MATLAB API is composed of several classes shown in <a class="reference internal" href="#csalt-matlab-classes"><span class="std std-numref">Fig. 8</span></a>.  The user classes that define the API are Trajectory, Phase, UserPathFunction, and UserPointFunction.  Note that UserFunction is an abstract base class that contains commonality between path and point functions. The user implements path and boundary functions for a problem by deriving from the UserPathFunction and UserPointFunction base classes as shown in <a class="reference internal" href="#csalt-matlab-classes"><span class="std std-numref">Fig. 8</span></a>.</p>
<figure class="align-center" id="id75">
<span id="csalt-matlab-classes"></span><a class="reference internal image-reference" href="../../_images/CSALT_matlab_Classes.png"><img alt="../../_images/CSALT_matlab_Classes.png" src="../../_images/CSALT_matlab_Classes.png" style="width: 606.0px; height: 306.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 8 </span><span class="caption-text">CSALT MATLAB user classes.</span><a class="headerlink" href="#id75" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>The responsibilities of classes in the CSALT API are highlighted in <a class="reference internal" href="#csalt-matlabclasses"><span class="std std-numref">Table 10</span></a>.</p>
<span id="csalt-matlabclasses"></span><table class="docutils align-default" id="id76">
<caption><span class="caption-number">Table 10 </span><span class="caption-text">CSALT MATLAB API classes.</span><a class="headerlink" href="#id76" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 15%" />
<col style="width: 60%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Class</p></th>
<th class="head"><p>Required?</p></th>
<th class="head"><p>Responsibility</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Trajectory</p></td>
<td><p>Yes</p></td>
<td><p>Trajectory is the container for all phases in the problem and for the UserPointFunction and UserPathFunction objects.  Internally, the Trajectory concatenates functions and Jacobians provided on different phases and from UserPointFunction.</p></td>
</tr>
<tr class="row-odd"><td><p>Phase</p></td>
<td><p>Yes</p></td>
<td><p>Phase defines the transcription employed and manages/concatenates the defect constraints, integral cost, and algebraic path functions.</p></td>
</tr>
<tr class="row-even"><td><p>UserPathFunction</p></td>
<td><p>Yes</p></td>
<td><p>Computes path function values (dynamics, integral cost, and algebraic path constraints) and, optionally, analytic Jacobians of path functions.  This class provides the generic interface for those computations. The user provides problem-specific functions by deriving a path function class from the UserPathFunction base class.</p></td>
</tr>
<tr class="row-odd"><td><p>UserPointFunction</p></td>
<td><p>Yes</p></td>
<td><p>Computes boundary functions (cost and algebraic point constraints) and, optionally, analytic boundary Jacobians. This class provides the generic interface for those computations. The user provides problem-specific functions by deriving a point function class from the UserPointFunction base class.</p></td>
</tr>
</tbody>
</table>
</section>
<section id="id1">
<h3>Overview of CSALT Configuration Process<a class="headerlink" href="#id1" title="Permalink to this heading">¶</a></h3>
<p>The key steps in setting up an optimization problem in CSALT are:</p>
<ol class="arabic simple">
<li><p>Configure the Path Functions</p></li>
<li><p>Configure the Boundary Functions</p></li>
<li><p>Configure the Phases and Transcription</p></li>
<li><p>Configure the Trajectory</p></li>
<li><p>Solve the problem and examine results</p></li>
</ol>
<p>When the tutorial is complete, the user will have three files: a path function object, a point function object, and a driver script that contains the contents of steps 3-4.  The user runs the tutorial by executing the driver script.</p>
<p>To solve the Brachistichrone problem using CSALT,  let’s begin with the Brachistichrone problem statement:</p>
<p>Minimize the final time</p>
<div class="math">
<p><img src="../../_images/math/0a3cba5ae4eaea5833f5b1648b61891c52cce47e.png" alt="\textrm{Minimize } J = t_f"/></p>
</div><p>Subject to the dynamics</p>
<div class="math">
<p><img src="../../_images/math/17b7ab04e05b91960fc0411846341ceb85c40985.png" alt="\dot{x} &amp; = v \sin u \\
\dot{y} &amp; = v \cos u \\
\dot{v} &amp; = g_0 \cos u"/></p>
</div><p>the initial boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/7a40a97f4d65fa00a40883c535f3846f6e4dc878.png" alt="t_0 &amp; = 0 \\
x \left( t_0 \right) &amp; = 0 \\
y \left( t_0 \right) &amp; = 0 \\
v \left( t_0 \right) &amp; = 0"/></p>
</div><p>the final boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/4f146728930f1f46298077a3c9459aeaa8b19193.png" alt="0 \leq t_f &amp; \leq 10 \\
x \left( t_f \right) &amp; = 1 \\
-10 \leq y \left( t_f \right) &amp; \leq 10 \\
-10 \leq v \left( t_f \right) &amp; \leq 0"/></p>
</div><p>and the following bound constraints on the state and control at any time:</p>
<div class="math">
<p><img src="../../_images/math/608a135d715cb417d0e4a62fe9235c5a41ba84fd.png" alt="-10 \leq x (t) &amp; \leq 10 \\
-10 \leq y (t) &amp; \leq 10 \\
-10 \leq v (t) &amp; \leq 10 \\
-10 \leq u (t) &amp; \leq 10"/></p>
</div></section>
<section id="configuring-the-csalt-path">
<h3>Configuring the CSALT Path<a class="headerlink" href="#configuring-the-csalt-path" title="Permalink to this heading">¶</a></h3>
<p>CSALT MATLAB is distributed with two folders as shown in <a class="reference internal" href="#csalt-matlab-directories"><span class="std std-numref">Fig. 9</span></a>.</p>
<figure class="align-center" id="id77">
<span id="csalt-matlab-directories"></span><a class="reference internal image-reference" href="../../_images/CSALT_matlab_Directories.png"><img alt="../../_images/CSALT_matlab_Directories.png" src="../../_images/CSALT_matlab_Directories.png" style="width: 147.0px; height: 88.5px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 9 </span><span class="caption-text">Directories in which CSALT MATLAB is distributed.</span><a class="headerlink" href="#id77" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>The code for CSALT and tutorial problems is contained in the MATLAB folder, and third-party files such as SNOPT are contained in the ThirdParty folder.  All files in both directories must be added to the user’s MATLAB path.</p>
</section>
<section id="id2">
<h3>Step 1: Configure the Path Functions<a class="headerlink" href="#id2" title="Permalink to this heading">¶</a></h3>
<p>Path functions, including dynamics, algebraic path constraints, and, if applicable, an integral cost function, are implemented by deriving a class from the CSALT UserPathFunction base class and implementing the EvaluateFunctions() and EvaluateJacobians() methods.   The EvaluateFunctions() method is required.  The EvaluateJacobians() method is required but may be empty.  If EvaluateJacobians() does not set a particular Jacobian, CSALT will finite-difference that Jacobian.</p>
<p>The dynamics model for the Brachistichrone problem is:</p>
<div class="math">
<p><img src="../../_images/math/17b7ab04e05b91960fc0411846341ceb85c40985.png" alt="\dot{x} &amp; = v \sin u \\
\dot{y} &amp; = v \cos u \\
\dot{v} &amp; = g_0 \cos u"/></p>
</div><p>The EvaluateFunctions() method that implements those dynamics is shown below. For this example, the Path Function class is named BrachistichronePathObject.  Create a file named BrachistichronePathObject.m and configure the contents as shown below.  Note in MATLAB the name of a class .m file must match the name of the class.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="k">classdef</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">userfunutils</span><span class="p">.</span><span class="n">UserPathFunction</span><span class="w"></span>
<span class="w">    </span><span class="c">% Path functions for Brachistichrone problem.</span><span class="w"></span>

<span class="w">    </span><span class="k">properties</span><span class="w"></span>
<span class="w">    </span><span class="c">% Set the gravity coefficient</span><span class="w"></span>
<span class="w">    </span><span class="n">gravity</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mf">32.174</span><span class="w"></span>
<span class="w">    </span><span class="k">end</span><span class="w"></span>

<span class="w">    </span><span class="k">methods</span><span class="w"></span>

<span class="w">    </span><span class="k">function</span><span class="w"> </span><span class="nf">EvaluateFunctions</span><span class="p">(</span>obj<span class="p">)</span><span class="w"></span>

<span class="w">        </span><span class="c">%  Get the state and control</span><span class="w"></span>
<span class="w">        </span><span class="n">stateVec</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetStateVec</span><span class="p">();</span><span class="w"></span>
<span class="w">        </span><span class="n">controlVec</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetControlVec</span><span class="p">();</span><span class="w"></span>
<span class="w">        </span><span class="n">v</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">3</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="n">u</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>

<span class="w">        </span><span class="c">%  Set the dynamics functions</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetDynFunctions</span><span class="p">([</span><span class="n">v</span><span class="o">*</span><span class="nb">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="n">v</span><span class="o">*</span><span class="nb">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="n">obj</span><span class="p">.</span><span class="n">gravity</span><span class="o">*</span><span class="nb">cos</span><span class="p">(</span><span class="n">u</span><span class="p">)]);</span><span class="w"></span>

<span class="w">    </span><span class="k">end</span><span class="w"></span>

<span class="w">    </span><span class="k">function</span><span class="w"> </span><span class="nf">EvaluateJacobians</span><span class="p">(</span>obj<span class="p">)</span><span class="w"></span>

<span class="w">        </span><span class="c">% Get the state and control</span><span class="w"></span>
<span class="w">        </span><span class="n">stateVec</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetStateVec</span><span class="p">();</span><span class="w"></span>
<span class="w">        </span><span class="n">controlVec</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetControlVec</span><span class="p">();</span><span class="w"></span>
<span class="w">        </span><span class="n">v</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">3</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="n">u</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>

<span class="w">        </span><span class="c">% Set the Jacobian of the dynamics w/r/t state</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetDynStateJac</span><span class="p">([</span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="nb">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="n">0</span><span class="w"> </span><span class="s">0</span><span class="w"> </span><span class="s">cos(u)</span><span class="p">;</span><span class="w"></span>
<span class="w">            </span><span class="n">0</span><span class="w"> </span><span class="s">0</span><span class="w"> </span><span class="s">0])</span><span class="p">;</span><span class="w"></span>

<span class="w">        </span><span class="c">% Set the Jacobian of the dynamics w/r/t control</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetDynControlJac</span><span class="p">([</span><span class="n">v</span><span class="o">*</span><span class="nb">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="o">-</span><span class="n">v</span><span class="o">*</span><span class="nb">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="o">-</span><span class="n">obj</span><span class="p">.</span><span class="n">gravity</span><span class="o">*</span><span class="nb">sin</span><span class="p">(</span><span class="n">u</span><span class="p">)]);</span><span class="w"></span>

<span class="w">        </span><span class="c">% Set the Jacobian of dynamics w/r/t time</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetDynTimeJac</span><span class="p">([</span><span class="mi">0</span><span class="p">;</span><span class="mi">0</span><span class="p">;</span><span class="mi">0</span><span class="p">]);</span><span class="w"></span>

<span class="w">    </span><span class="k">end</span><span class="w"></span>
<span class="w">    </span><span class="k">end</span><span class="w"></span>
<span class="k">end</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id3">
<h3>Step 2: Configure the Boundary Functions<a class="headerlink" href="#id3" title="Permalink to this heading">¶</a></h3>
<p>Boundary functions, including algebraic constraints and cost function, are implemented by deriving a class from the CSALT UserPointFunction base class and implementing the EvaluateFunctions() method.   The EvaluateFunctions() method is required.   Note the MATLAB version of CSALT does not currently support analytic Jacobians of boundary functions. The EvaluateJacobians() method should still be provided but left empty.</p>
<p>The boundary functions for the Brachistochrone problem are</p>
<div class="math">
<p><img src="../../_images/math/0a3cba5ae4eaea5833f5b1648b61891c52cce47e.png" alt="\textrm{Minimize } J = t_f"/></p>
</div><p>subject to the initial boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/7a40a97f4d65fa00a40883c535f3846f6e4dc878.png" alt="t_0 &amp; = 0 \\
x \left( t_0 \right) &amp; = 0 \\
y \left( t_0 \right) &amp; = 0 \\
v \left( t_0 \right) &amp; = 0"/></p>
</div><p>and the final boundary conditions</p>
<div class="math">
<p><img src="../../_images/math/4f146728930f1f46298077a3c9459aeaa8b19193.png" alt="0 \leq t_f &amp; \leq 10 \\
x \left( t_f \right) &amp; = 1 \\
-10 \leq y \left( t_f \right) &amp; \leq 10 \\
-10 \leq v \left( t_f \right) &amp; \leq 0"/></p>
</div><p>The EvaluateFunctions() method that implements those boundary functions is shown below. For this example, the Point Function class is named BrachistichronePointObject.  Create a file named BrachistichronePointObject.m and configure the contents as shown below.  Note in MATLAB the name of a class .m file must match the name of the class.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="k">classdef</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">userfunutils</span><span class="p">.</span><span class="n">UserPointFunction</span><span class="w"></span>
<span class="w">    </span><span class="c">% Boundary functions for Brachistichrone problem.</span><span class="w"></span>

<span class="w">    </span><span class="k">methods</span><span class="w"></span>

<span class="w">    </span><span class="k">function</span><span class="w"> </span><span class="nf">EvaluateFunctions</span><span class="p">(</span>obj<span class="p">)</span><span class="w"></span>

<span class="w">        </span><span class="c">%  Extract the initial and final time and state for</span><span class="w"></span>
<span class="w">        </span><span class="c">%  phase 1 (the only phase in the problem)</span><span class="w"></span>
<span class="w">        </span><span class="n">initTime</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetInitialTime</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="n">finalTime</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetFinalTime</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="n">initState</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetInitialStateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="n">finalState</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">GetFinalStateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>

<span class="w">        </span><span class="c">% Set the cost function</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetCostFunction</span><span class="p">(</span><span class="n">finalTime</span><span class="p">);</span><span class="w"></span>

<span class="w">        </span><span class="c">% Set the boundary functions</span><span class="w"></span>
<span class="w">        </span><span class="n">obj</span><span class="p">.</span><span class="n">SetAlgFunctions</span><span class="p">([</span><span class="n">initTime</span><span class="p">;</span><span class="w"></span>
<span class="w">            </span><span class="n">finalTime</span><span class="p">;</span><span class="k">...</span><span class="w"></span>
<span class="w">            </span><span class="n">initState</span><span class="p">;</span><span class="w"></span>
<span class="w">            </span><span class="n">finalState</span><span class="p">]);</span><span class="w"></span>

<span class="w">        </span><span class="c">% If initializing, set the bounds using the same order</span><span class="w"></span>
<span class="w">        </span><span class="c">% as in the call to SetAlgFunctions()</span><span class="w"></span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="n">obj</span><span class="p">.</span><span class="n">IsInitializing</span><span class="w"></span>
<span class="w">            </span><span class="n">obj</span><span class="p">.</span><span class="n">SetAlgFuncLowerBounds</span><span class="p">([</span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w">   </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w">  </span><span class="mi">0</span><span class="w">  </span><span class="mi">1</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">]</span><span class="o">&#39;</span><span class="p">);</span><span class="w"></span>
<span class="w">            </span><span class="n">obj</span><span class="p">.</span><span class="n">SetAlgFuncUpperBounds</span><span class="p">([</span><span class="mi">0</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w">  </span><span class="mi">0</span><span class="w">  </span><span class="mi">1</span><span class="w"> </span><span class="mi">10</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="p">]</span><span class="o">&#39;</span><span class="p">);</span><span class="w"></span>
<span class="w">        </span><span class="k">end</span><span class="w"></span>

<span class="w">    </span><span class="k">end</span><span class="w"></span>

<span class="w">    </span><span class="k">function</span><span class="w"> </span><span class="nf">EvaluateJacobians</span><span class="p">(</span>~<span class="p">)</span><span class="w"></span>

<span class="w">    </span><span class="k">end</span><span class="w"></span>

<span class="w">    </span><span class="k">end</span><span class="w"></span>
<span class="k">end</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id4">
<h3>Step 3: Configure the Phases and Transcription<a class="headerlink" href="#id4" title="Permalink to this heading">¶</a></h3>
<p>The next step in configuring the Brachistichrone problem is to set up a Phase. (This example is a single-phase problem.)  The Phase object contains the configuration for the selected transcription, the bounds on optimization parameters (e.g., time, state, and control), and the initial guesses for those parameters.  In this example, the phase is configured to use Radau orthogonal collocation with two equally spaced mesh intervals, each containing five points.  (The mesh interval configuration and initial-guess options are presented in more detail in <a class="reference internal" href="#sec-gmatoc-phasereference"><span class="std std-ref">Phase Reference</span></a>).  Create a file named Brachistichrone_Driver.m and configure the contents as shown below.  Code written in steps 3-5 should be added to the driver script file.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">%% ===== Initializations</span><span class="w"></span>
<span class="nb">clear</span><span class="w"> </span><span class="nb">all</span><span class="w"></span>
<span class="k">global</span><span class="w"> </span><span class="n">traj</span><span class="w">  </span><span class="c">% Required for SNOPT function wrapper. Can&#39;t pass a Trajectory to SNOPT.</span><span class="w"></span>

<span class="c">% Import CSALT classes</span><span class="w"></span>
<span class="nb">import</span><span class="w"> </span><span class="n">exec</span><span class="p">.</span><span class="n">Trajectory</span><span class="w"></span>
<span class="nb">import</span><span class="w"> </span><span class="n">exec</span><span class="p">.</span><span class="n">RadauPhase</span><span class="w"></span>

<span class="c">%% =====  Define Properties for the Phase</span><span class="w"></span>

<span class="c">% Create a Radau phase and set preliminary mesh configuration</span><span class="w"></span>
<span class="n">phase1</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">initialGuessMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;LinearNoControl&#39;</span><span class="p">;</span><span class="w"></span>
<span class="n">meshIntervalFractions</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">1</span><span class="p">];</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">5</span><span class="w"> </span><span class="mi">5</span><span class="p">];</span><span class="w"></span>

<span class="c">%  Set time bounds and guesses</span><span class="w"></span>
<span class="n">initialGuessTime</span><span class="w">  </span><span class="p">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">finalGuessTime</span><span class="w">      </span><span class="p">=</span><span class="w"> </span><span class="mf">.3</span><span class="p">;</span><span class="w"></span>
<span class="n">timeLowerBound</span><span class="w">      </span><span class="p">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">timeUpperBound</span><span class="w">      </span><span class="p">=</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span><span class="w"></span>

<span class="c">%  Set state bounds and guesses</span><span class="w"></span>
<span class="n">numStateVars</span><span class="w">        </span><span class="p">=</span><span class="w"> </span><span class="mi">3</span><span class="p">;</span><span class="w"></span>
<span class="n">stateLowerBound</span><span class="w">   </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">]</span><span class="o">&#39;</span><span class="p">;</span><span class="w"></span>
<span class="n">initialGuessState</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="p">]</span><span class="o">&#39;</span><span class="p">;</span><span class="w"> </span><span class="c">% [x0 y0 v0]</span><span class="w"></span>
<span class="n">finalGuessState</span><span class="w">   </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mf">.4</span><span class="w"> </span><span class="o">-</span><span class="mf">.5</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="o">&#39;</span><span class="p">;</span><span class="w"> </span><span class="c">% [xf yf vf]</span><span class="w"></span>
<span class="n">stateUpperBound</span><span class="w">   </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">10</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="p">]</span><span class="o">&#39;</span><span class="p">;</span><span class="w"></span>

<span class="c">%  Set control bounds (guess is set to 1s)</span><span class="w"></span>
<span class="n">numControlVars</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span><span class="w"></span>
<span class="n">controlUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span><span class="w"></span>
<span class="n">controlLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">;</span><span class="w"></span>

<span class="c">% Call set methods to configure the data</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetNumStateVars</span><span class="p">(</span><span class="n">numStateVars</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetNumControlVars</span><span class="p">(</span><span class="n">numControlVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetMeshIntervalFractions</span><span class="p">(</span><span class="n">meshIntervalFractions</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetMeshIntervalNumPoints</span><span class="p">(</span><span class="n">meshIntervalNumPoints</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateLowerBound</span><span class="p">(</span><span class="n">stateLowerBound</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateUpperBound</span><span class="p">(</span><span class="n">stateUpperBound</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateInitialGuess</span><span class="p">(</span><span class="n">initialGuessState</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateFinalGuess</span><span class="p">(</span><span class="n">finalGuessState</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeLowerBound</span><span class="p">(</span><span class="n">timeLowerBound</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeUpperBound</span><span class="p">(</span><span class="n">timeUpperBound</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeInitialGuess</span><span class="p">(</span><span class="n">initialGuessTime</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeFinalGuess</span><span class="p">(</span><span class="n">finalGuessTime</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetControlLowerBound</span><span class="p">(</span><span class="n">controlLowerBound</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetControlUpperBound</span><span class="p">(</span><span class="n">controlUpperBound</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id5">
<h3>Step 4: Configure the Trajectory<a class="headerlink" href="#id5" title="Permalink to this heading">¶</a></h3>
<p>The next step is to add the path function, boundary function, and phase objects to a Trajectory.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">%% =====  Define Properties for the Trajectory</span><span class="w"></span>

<span class="c">%  Create trajectory and configure user function names</span><span class="w"></span>
<span class="n">traj</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>

<span class="c">% Add the path, boundary function, and phases to the trajectory</span><span class="w"></span>
<span class="nb">import</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="w"></span>
<span class="nb">import</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">pathFunctionObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">pointFunctionObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">phaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">};</span><span class="w"></span>

<span class="c">% Set other settings</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">showPlot</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">false</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">plotUpdateRate</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">100</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">costLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="nb">Inf</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">costUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">Inf</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">maxMeshRefinementCount</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id6">
<h3>Step 5: Run the Optimizer and Examine the Solution<a class="headerlink" href="#id6" title="Permalink to this heading">¶</a></h3>
<p>Now that the path, boundary, phase, and trajectory objects are configured, we are ready to optimize the problem and write the solution to a file.  Add the following lines to your driver script, and then run the script in MATLAB.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">%% =====  Run The Optimizer and plot the solution</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">Initialize</span><span class="p">();</span><span class="w"></span>
<span class="p">[</span><span class="n">z</span><span class="p">,</span><span class="n">info</span><span class="p">]</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">traj</span><span class="p">.</span><span class="n">Optimize</span><span class="p">();</span><span class="w"></span>

<span class="n">stateSol</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">traj</span><span class="p">.</span><span class="n">GetStateArray</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="nb">plot</span><span class="p">(</span><span class="n">stateSol</span><span class="p">(:,</span><span class="mi">1</span><span class="p">),</span><span class="n">stateSol</span><span class="p">(:,</span><span class="mi">2</span><span class="p">))</span><span class="w"></span>
<span class="nb">grid</span><span class="w"> </span><span class="n">on</span><span class="w"></span>
</pre></div>
</div>
<p>The console output for the Brachistichrone problem solved using SNOPT is shown in <a class="reference internal" href="#csalt-matlab-console-output"><span class="std std-numref">Fig. 10</span></a>.</p>
<figure class="align-center" id="id78">
<span id="csalt-matlab-console-output"></span><a class="reference internal image-reference" href="../../_images/CSALT_matlab_console_Output.png"><img alt="../../_images/CSALT_matlab_console_Output.png" src="../../_images/CSALT_matlab_console_Output.png" style="width: 574.5px; height: 420.75px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 10 </span><span class="caption-text">Brachistichrone problem optimization MATLAB console output.</span><a class="headerlink" href="#id78" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
</section>
<section id="id7">
<h3>Additional Tutorials<a class="headerlink" href="#id7" title="Permalink to this heading">¶</a></h3>
<p>The MATLAB version of CSALT is distributed with many tutorials in the lowthrustMatlabTestProblems folder.</p>
</section>
</section>
<section id="tutorial-setting-up-an-optimal-control-problem-in-gmat">
<h2>Tutorial: Setting up an Optimal Control Problem in GMAT<a class="headerlink" href="#tutorial-setting-up-an-optimal-control-problem-in-gmat" title="Permalink to this heading">¶</a></h2>
<p>In this section, we describe how to set up an optimal control problem in GMAT using a simple, single-phase, low-thrust optimization problem.   We begin by providing an overview of the GMAT components used to solve optimal control problems, then present an overview of the steps to set up a problem, and finally walk through the GMAT script for the problem configuration.</p>
<section id="gmat-script-user-interface-component-overview">
<h3>GMAT Script User Interface Component Overview<a class="headerlink" href="#gmat-script-user-interface-component-overview" title="Permalink to this heading">¶</a></h3>
<p>The GMAT Optimal Control subsystem is composed of several Resources shown in <a class="reference internal" href="#csalt-gmat-interface"><span class="std std-numref">Fig. 11</span></a>. The user Resources are Trajectory, Phase, OptimalControlFunction, OptimalControlGuess, CustomLinkageConstraint, DynamicsConfiguration, EMTGSpacecraft, Spacecraft, ForceModel, ChemicalTank, and ElectricTank.  The diagram uses the UML “has-a” relationship that employs a  diamond to indicate an “owned” object.  In this case, for example, a Trajectory Resource “has a” list of OptimalControlFunctions, a list of Phases, and an OptimalControlGuess.  Bulleted lists in the diagram indicate key local data.  For example, convergence tolerances are set on the Trajectory Resource.</p>
<figure class="align-center" id="id79">
<span id="csalt-gmat-interface"></span><a class="reference internal image-reference" href="../../_images/CSALT_GMAT_Interface_2020a_Release.png"><img alt="../../_images/CSALT_GMAT_Interface_2020a_Release.png" src="../../_images/CSALT_GMAT_Interface_2020a_Release.png" style="width: 862.1999999999999px; height: 450.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 11 </span><span class="caption-text">GMAT Optimal Control interface.</span><a class="headerlink" href="#id79" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>A brief description of the new Resources and Command introduced in GMAT Optimal Control is contained in <a class="reference internal" href="#csalt-gmatresources"><span class="std std-numref">Table 11</span></a>.</p>
<span id="csalt-gmatresources"></span><table class="docutils align-default" id="id80">
<caption><span class="caption-number">Table 11 </span><span class="caption-text">GMAT Optimal Control resources and command.</span><a class="headerlink" href="#id80" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 75%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Resource</p></th>
<th class="head"><p>Responsibility</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Trajectory</p></td>
<td><p>The container for all phases and optimal control functions in the optimization problem and configuration for settings that apply to the whole problem (such as convergence tolerances and iterations limits).</p></td>
</tr>
<tr class="row-odd"><td><p>Phase</p></td>
<td><p>A phase is used to model a segment of a trajectory that can be modeled using a single dynamics model. (If the dynamics must change, the problem requires a different phase for each subsection of the trajectory).</p></td>
</tr>
<tr class="row-even"><td><p>OptimalControlGuess</p></td>
<td><p>An object that provides a guess to the optimal control subsystem. The OptimalControlGuess supports file-based and GMAT-Array-based guess data. Guess information can be provided on a Phase or Trajectory.</p></td>
</tr>
<tr class="row-odd"><td><p>CustomLinkageConstraint</p></td>
<td><p>A constraint that defines custom time and state linkages (i.e., not full state linkage) between phases or the start and end of a single phase.  For example, if a phase’s time duration must be less than some known duration, a custom linkage constraint is used.  Other examples of custom linkage constraints include constraining a phase boundary to a known initial or final state and constraining mass change between different points in a trajectory.</p></td>
</tr>
<tr class="row-even"><td><p>DynamicsConfiguration</p></td>
<td><p>A configuration of a spacecraft, force model (orbital dynamics), and thruster model, and fuel tank used to model the spacecraft dynamics.</p></td>
</tr>
<tr class="row-odd"><td><p>OptimalControlFunction</p></td>
<td><p>An optimal control function models algebraic constraints or cost functions (must be scalar in the case of a cost function).  The OptimalControlFunction type field sets the function type, e.g., IntegratedFlyby, PatchedConicLaunch, Expression, etc.</p></td>
</tr>
<tr class="row-even"><td><p>EMTGSpacecraft</p></td>
<td><p>An interface to EMTG thruster models.</p></td>
</tr>
<tr class="row-odd"><td><p>Collocate</p></td>
<td><p>The command to solve the optimal control problem</p></td>
</tr>
</tbody>
</table>
</section>
<section id="overview-of-gmat-script-configuration-process">
<h3>Overview of GMAT Script Configuration Process<a class="headerlink" href="#overview-of-gmat-script-configuration-process" title="Permalink to this heading">¶</a></h3>
<p>The basic steps to set up an optimal control problem in GMAT are:</p>
<ol class="arabic">
<li><p>Configure path functions and dynamics</p>
<blockquote>
<div><ol class="loweralpha simple">
<li><p>Configure spacecraft</p></li>
<li><p>Configure fuel tank</p></li>
<li><p>Configure orbital dynamics and thruster models</p></li>
<li><p>Configure the DynamicsConfiguration</p></li>
</ol>
</div></blockquote>
</li>
<li><p>Configure the OptimalControlGuess</p></li>
<li><p>Configure the boundary functions</p></li>
<li><p>Configure the phases</p></li>
<li><p>Configure the trajectory</p></li>
<li><p>Run the optimizer and examine the solution</p></li>
</ol>
<p>Note that the GMAT script parser is a two-pass parser, and the items above need not be implemented in the order shown except that the last step (executing the optimizer) must be performed last. The sections below walk through a script configuration for a simple optimal control problem. The GMAT tutorial problem statement is:</p>
<p>Maximize the final distance from the Sun, i.e.,</p>
<div class="math">
<p><img src="../../_images/math/cf7e29d8c7a1ca06b7158c127a4bc41953ffebdb.png" alt="\textrm{Minimize } J = - \sqrt{x \left(t_f \right)^2 + y \left(t_f \right)^2 + z \left(t_f \right)^2}"/></p>
</div><p>subject to the initial boundary conditions (Sun-centered MJ2000Eq reference frame)</p>
<div class="math">
<p><img src="../../_images/math/1e643b46f108c5fbf61544474a89dc6c822b3d4c.png" alt="t_0 &amp; = 30542 \textrm{ A1MJD} \\
x \left(t_0 \right) &amp; = 125291184 \textrm{ km} \\
y \left(t_0 \right) &amp; = -75613036 \textrm{ km} \\
z \left(t_0 \right) &amp; = -32788527 \textrm{ km} \\
\dot{x} \left(t_0 \right) &amp; = 13.438 \textrm{ km/s} \\
\dot{y} \left(t_0 \right) &amp; = 25.234 \textrm{ km/s} \\
\dot{z} \left(t_0 \right) &amp; = 10.903 \textrm{ km/s} \\
m \left(t_0 \right) &amp; = 3930 \textrm{ kg}"/></p>
</div><p>and the final boundary condition (a time of flight of 250 days)</p>
<div class="math">
<p><img src="../../_images/math/3e3a0b7fb97bdf73aea399128263a8b9e7a17aeb.png" alt="t_f = 30792 \textrm{ A1MJD}"/></p>
</div><p>The initial conditions above start at about 1 AU from the Sun in an Earth-like orbit.  We will configure GMAT to use a Sun-centered dynamics model and determine the thrust history to maximize the spacecraft’s position from the Sun after 250 days.</p>
<p>Now let’s walk through the GMAT script configuration for this problem.</p>
</section>
<section id="step-1-configure-path-functions-and-dynamics">
<h3>Step 1: Configure Path Functions and Dynamics<a class="headerlink" href="#step-1-configure-path-functions-and-dynamics" title="Permalink to this heading">¶</a></h3>
<p>The Resource that defines the dynamics for a phase is the DynamicsConfiguration Resource.  To set up a DynamicsConfiguration, attach pre-configured Spacecraft, ForceModel, Tank, and EMTGSpacecraft Resources.  The example below illustrates how to configure those models.</p>
<section id="configure-the-spacecraft">
<h4>Configure the Spacecraft<a class="headerlink" href="#configure-the-spacecraft" title="Permalink to this heading">¶</a></h4>
<p>Configuring a spacecraft for this example is relatively simple.  When we configure the guess data later in the tutorial, which includes spacecraft state and mass, those settings on the spacecraft will be set according to the guess, so setting state and epoch information is not required here.  However, the total mass of the spacecraft must be set along with fuel tank masses so that they can be updated by CSALT during optimization.  Fuel tanks are required whenever a burn is being used along the trajectory.  Note that the total mass of the spacecraft must at least be the upper bound of the mass constraint set later in phases, but the fuel tanks themselves will be internally set to allow negative mass values to allow CSALT to attempt to reach convergence.  We need to create a spacecraft and its fuel tank.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a spacecraft named aSat.  Guess is set later.</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">aSat</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">aSat.DryMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1000</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">aSat.Tanks</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemTank</span><span class="p">}</span><span class="w"></span>

<span class="c">% Create a chemical tank named ChemTank.</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ChemicalTank</span><span class="w"> </span><span class="s">ChemTank</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">ChemTank.FuelMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">4000</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">ChemTank.Volume</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">4</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">ChemTank.FuelDensity</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1260</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="configure-orbital-dynamics-models">
<h4>Configure Orbital Dynamics Models<a class="headerlink" href="#configure-orbital-dynamics-models" title="Permalink to this heading">¶</a></h4>
<p>Below is the script configuration for a simple Sun-centered dynamics model with Earth, Sun, and Moon point masses included in the model.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create an orbit dynamics model with Earth, Sun, Moon point mass</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ForceModel</span><span class="w"> </span><span class="s">DeepSpaceForceModel</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.CentralBody</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Sun</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.PointMasses</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">Sun</span><span class="p">,</span><span class="n">Earth</span><span class="p">,</span><span class="n">Luna</span><span class="p">};</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.Drag</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">None</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.SRP</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">On</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="configure-the-thruster-models-emtgspacecraft">
<h4>Configure the Thruster Models (EMTGSpacecraft)<a class="headerlink" href="#configure-the-thruster-models-emtgspacecraft" title="Permalink to this heading">¶</a></h4>
<p>The thrust model is configured by setting up EMTG options files and setting those files on an EMTGSpacecraft Resource.  The EMTG options files contain the configuration for the thrust model. See <a class="reference internal" href="#sec-gmatoc-emtgspacecraftreference"><span class="std std-ref">EMTG Spacecraft Reference</span></a> for more information.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create an EMTGSpacecraft Resource</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">emtgThrustModel</span><span class="p">;</span><span class="w"></span>
<span class="n">emtgThrustModel</span><span class="p">.</span><span class="n">SpacecraftFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">SpacecraftModel</span><span class="p">.</span><span class="n">emtg_spacecraftopt</span><span class="w"></span>
<span class="n">emtgThrustModel</span><span class="p">.</span><span class="n">DutyCycle</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.9</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This EMTG interface is alpha, and will be replaced by a new interface to EMTG models that is production quality.</p>
</div>
<p>Note that the EMTGSpacecraft SpacecraftFile field specifies the path relative to the GMAT /data/emtg directory.</p>
</section>
<section id="configure-the-dynamics-configuration">
<h4>Configure the Dynamics Configuration<a class="headerlink" href="#configure-the-dynamics-configuration" title="Permalink to this heading">¶</a></h4>
<p>Now that the Spacecraft, ChemicalTank,  ForceModel, and EMTGSpacecraft Resources are configured, they are added to a DynamicsConfiguration Resource.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a DynamicsConfiguration object and add Spacecraft,</span><span class="w"></span>
<span class="c">% ForceModel, and Thrust model</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">SunThrustDynConfig</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">ForceModels</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">DeepSpaceForceModel</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">Spacecraft</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">aSat</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">FiniteBurns</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">emtgThrustModel</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">EMTGTankConfig</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemTank</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>These Resources are added to the Phase Resources later in the tutorial.</p>
</section>
</section>
<section id="step-2-configure-the-guess">
<span id="sec-gmatoc-gmattutorial-configuretheguess"></span><h3>Step 2: Configure the Guess<a class="headerlink" href="#step-2-configure-the-guess" title="Permalink to this heading">¶</a></h3>
<p>One significant difference between GMAT parameter optimization interfaces (Vary, Nonlinear Constraint, Minimize, etc. ) and the optimal control subsystem is that a guess for time, state, and control over the whole trajectory is required to use the optimal control subsystem.  Guesses can be generated from a GMAT script and propagation, external tools, or quasi-random number generation processes.</p>
<p>The OptimalControlGuess Resource supports several guess data types, including a file-based guess and GMAT-Array-based guesses.  Here, we use a file-based guess using an Optimal Control History (.och) file.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a guess object that uses a file-based guess source</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlGuess</span><span class="w"> </span><span class="s">trajectoryGuess</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">CollocationGuessFile</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">FileName</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">.</span><span class="o">./</span><span class="n">data</span><span class="o">/</span><span class="n">misc</span><span class="o">/</span><span class="n">GuessWithUnityControl</span><span class="p">.</span><span class="n">och</span><span class="w"></span>
</pre></div>
</div>
<p>The guess file contains a header with metadata that describes the data content.  Three rows of data are shown below, with line wrapping to show all of the data contained in the rows.  The rows contain time, Cartesian state, mass, and control. Currently EME2000 is the only supported reference frame, and A1ModJulian is the only supported time system when using a file-based guess.  If the guess file comes from a previous run’s output file, the file will also contain the exit condition data from the run.  It is safe to leave this data in the file when used as a guess file, as it will be ignored by CSALT when collecting the guess data.</p>
<p>The guess source may include data at times that are beyond the initial guesses for the initial and final epochs of the problem. However, because interpolation is used to obtain guess values at times between or beyond the times listed in the guess, it is strongly recommended to provide a guess whose timespan completely encompasses the initial guess for the initial and final epochs.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">META_START</span><span class="w"></span>
<span class="w">    </span><span class="s">CENTRAL_BODY</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Sun</span><span class="w"></span>
<span class="w">    </span><span class="n">REF_FRAME</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">EME2000</span><span class="w"></span>
<span class="w">    </span><span class="n">TIME_SYSTEM</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">A1ModJulian</span><span class="w"></span>
<span class="w">    </span><span class="n">NUM_STATES</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">7</span><span class="w"></span>
<span class="w">    </span><span class="n">NUM_CONTROLS</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">3</span><span class="w"></span>
<span class="w">    </span><span class="n">NUM_INTEGRALS</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">0</span><span class="w"></span>
<span class="n">META_STOP</span><span class="w"></span>

<span class="s">DATA_START</span><span class="w"></span>
<span class="mf">30537.58130403295</span><span class="w">           </span><span class="mf">120045407.6646147</span><span class="w">       </span><span class="o">-</span><span class="mf">84769659.07938783</span><span class="w">      </span><span class="o">-</span><span class="mf">36758301.62764073</span><span class="w">      </span><span class="mf">13.68515738249333</span><span class="w">       </span><span class="mf">26.23213523803535</span><span class="w">       </span><span class="mf">10.71343366494688</span><span class="w">       </span><span class="mf">3928.8979</span><span class="w">               </span><span class="mi">1</span><span class="w">                       </span><span class="mi">0</span><span class="w">                       </span><span class="mi">0</span><span class="w"></span>
<span class="mf">30537.68130403294</span><span class="w">           </span><span class="mf">120166876.6672654</span><span class="w">       </span><span class="o">-</span><span class="mf">84550219.83076148</span><span class="w">      </span><span class="o">-</span><span class="mf">36666345.46750365</span><span class="w">      </span><span class="mf">14.29149185537749</span><span class="w">       </span><span class="mf">24.96927614272317</span><span class="w">       </span><span class="mf">10.58473151780541</span><span class="w">       </span><span class="mf">3928.8979</span><span class="w">               </span><span class="mi">1</span><span class="w">                       </span><span class="mi">0</span><span class="w">                       </span><span class="mi">0</span><span class="w"></span>
<span class="mf">30537.78130403294</span><span class="w">           </span><span class="mf">120291284.2924708</span><span class="w">       </span><span class="o">-</span><span class="mf">84335930.88030888</span><span class="w">      </span><span class="o">-</span><span class="mf">36575177.55088668</span><span class="w">      </span><span class="mf">14.47799877262803</span><span class="w">       </span><span class="mf">24.68188855218928</span><span class="w">       </span><span class="mf">10.5266647230637</span><span class="w">        </span><span class="mf">3928.8979</span><span class="w">               </span><span class="mi">1</span><span class="w">                       </span><span class="mi">0</span><span class="w">                       </span><span class="mi">0</span><span class="w"></span>
<span class="p">.</span><span class="w"></span>
<span class="p">.</span><span class="w"></span>
<span class="p">.</span><span class="w"></span>
<span class="n">DATA_STOP</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="step-3-configure-the-phases">
<h3>Step 3: Configure the Phases<a class="headerlink" href="#step-3-configure-the-phases" title="Permalink to this heading">¶</a></h3>
<p>The Phase Resource includes settings for the transcription, bounds on optimization parameters during the phase, bounds on the phase initial and final epoch, the guess for the initial and final epoch, and the OptimalControlGuess Resource that contains the guess for the phase.  The description for some of these settings is beyond the scope of the Tutorial.  See <a class="reference internal" href="#sec-gmatoc-phasereference"><span class="std std-ref">Phase Reference</span></a> for more details.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">%  Create a phase and set transcription and parameter bounds</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Phase</span><span class="w"> </span><span class="s">thePhase</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">RadauPseudospectral</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ThrustMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Thrust</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">DynamicsConfiguration</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">SunThrustDynConfig</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">SubPhaseBoundaries</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="w">   </span><span class="o">-</span><span class="mf">0.75</span><span class="w"> </span><span class="o">-</span><span class="mf">0.5</span><span class="w"> </span><span class="o">-</span><span class="mf">0.25</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mf">0.25</span><span class="w"> </span><span class="mf">.5</span><span class="w"> </span><span class="mf">0.75</span><span class="w"> </span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">PointsPerSubPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="w"> </span><span class="mi">7</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">GuessSource</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">trajectoryGuess</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochFormat</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">A1ModJulian</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">30500.0</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">31000.0</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">InitialEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">30542.0</span><span class="w"> </span><span class="c">% initial guess</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">FinalEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">30792.0</span><span class="w"> </span><span class="c">% initial guess</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">StateLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="o">-</span><span class="mf">1.49598e+09</span><span class="w"> </span><span class="o">-</span><span class="mf">1.49598e+09</span><span class="w"> </span><span class="o">-</span><span class="mf">1.49598e+09</span><span class="w"> </span><span class="k">...</span><span class="w"></span>
<span class="w">                            </span><span class="o">-</span><span class="mi">100</span><span class="w"> </span><span class="o">-</span><span class="mi">100</span><span class="w"> </span><span class="o">-</span><span class="mi">100</span><span class="w"> </span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">StateUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="mf">1.49598e+09</span><span class="w">  </span><span class="mf">1.49598e+09</span><span class="w"> </span><span class="mf">1.49598e+09</span><span class="w"> </span><span class="k">...</span><span class="w"></span>
<span class="w">                            </span><span class="mi">100</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="mi">100</span><span class="w"> </span><span class="mi">5000</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ControlLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="o">-</span><span class="mi">1</span><span class="w">  </span><span class="o">-</span><span class="mi">1</span><span class="w">  </span><span class="o">-</span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ControlUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="mi">1</span><span class="w">  </span><span class="mi">1</span><span class="w">  </span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="step-4-configure-the-boundary-functions">
<h3>Step 4: Configure the Boundary Functions<a class="headerlink" href="#step-4-configure-the-boundary-functions" title="Permalink to this heading">¶</a></h3>
<p>The tutorial problem has boundary conditions on the initial state and a phase-duration constraint, which are configured using CustomLinkageConstraint Resources illustrated in the next two subsections.</p>
<section id="set-the-initial-conditions">
<h4>Set the Initial Conditions<a class="headerlink" href="#set-the-initial-conditions" title="Permalink to this heading">¶</a></h4>
<p>For convenience, we repeat the initial boundary conditions for the tutorial:</p>
<div class="math">
<p><img src="../../_images/math/1e643b46f108c5fbf61544474a89dc6c822b3d4c.png" alt="t_0 &amp; = 30542 \textrm{ A1MJD} \\
x \left(t_0 \right) &amp; = 125291184 \textrm{ km} \\
y \left(t_0 \right) &amp; = -75613036 \textrm{ km} \\
z \left(t_0 \right) &amp; = -32788527 \textrm{ km} \\
\dot{x} \left(t_0 \right) &amp; = 13.438 \textrm{ km/s} \\
\dot{y} \left(t_0 \right) &amp; = 25.234 \textrm{ km/s} \\
\dot{z} \left(t_0 \right) &amp; = 10.903 \textrm{ km/s} \\
m \left(t_0 \right) &amp; = 3930 \textrm{ kg}"/></p>
</div><p>To apply these constraints, configure a CustomLinkageConstraint Resource as shown below.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create constraint that applies the initial conditions</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">CustomLinkageConstraint</span><span class="w"> </span><span class="s">initialConditions</span><span class="p">;</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">ConstraintMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Absolute</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">InitialPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thePhase</span><span class="p">;</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">InitialPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Start</span><span class="p">;</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="n">A1ModJulian</span><span class="p">,</span><span class="w"> </span><span class="mi">30542</span><span class="p">)</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="n">A1ModJulian</span><span class="p">,</span><span class="w"> </span><span class="mi">30542</span><span class="p">)</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;PositionLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mi">125291184</span><span class="w"> </span><span class="o">-</span><span class="mi">75613036</span><span class="w"> </span><span class="o">-</span><span class="mi">32788527</span><span class="p">])</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;PositionUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mi">125291184</span><span class="w"> </span><span class="o">-</span><span class="mi">75613036</span><span class="w"> </span><span class="o">-</span><span class="mi">32788527</span><span class="p">])</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;VelocityLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">13.438</span><span class="w"> </span><span class="mf">25.234</span><span class="w"> </span><span class="mf">10.903</span><span class="p">])</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;VelocityUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">13.438</span><span class="w"> </span><span class="mf">25.234</span><span class="w"> </span><span class="mf">10.903</span><span class="p">])</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;MassLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">3930</span><span class="p">)</span><span class="w"></span>
<span class="n">initialConditions</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;MassUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">3930</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="configure-the-phase-duration-constraint">
<h4>Configure the phase duration constraint<a class="headerlink" href="#configure-the-phase-duration-constraint" title="Permalink to this heading">¶</a></h4>
<p>For convenience, we repeat the phase duration constraint for the tutorial:</p>
<div class="math">
<p><img src="../../_images/math/3e3a0b7fb97bdf73aea399128263a8b9e7a17aeb.png" alt="t_f = 30792 \textrm{ A1MJD}"/></p>
</div><p>This final time constraint corresponds to 250 days past the initial time. To apply this constraint, configure a CustomLinkageConstraint Resource as shown below.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a constraint on Phase duration. Duration must be 250 days</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">CustomLinkageConstraint</span><span class="w"> </span><span class="s">duration_Thrust</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">ConstraintMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Difference</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">InitialPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thePhase</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">InitialPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Start</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">FinalPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thePhase</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">FinalPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">End</span><span class="p">;</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="s">&#39;ElapsedDays&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">250</span><span class="p">)</span><span class="w"></span>
<span class="n">duration_Thrust</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="s">&#39;ElapsedDays&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">250</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-cost-function">
<h4>Setting the Cost Function<a class="headerlink" href="#setting-the-cost-function" title="Permalink to this heading">¶</a></h4>
<p>The cost function for this tutorial is:</p>
<div class="math">
<p><img src="../../_images/math/cf7e29d8c7a1ca06b7158c127a4bc41953ffebdb.png" alt="\textrm{Minimize } J = - \sqrt{x \left(t_f \right)^2 + y \left(t_f \right)^2 + z \left(t_f \right)^2}"/></p>
</div><p>This specific cost function is built into GMAT. To configure the cost function, use the following script.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">thePhase</span><span class="p">.</span><span class="n">BuiltInCost</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;RMAGFinal&#39;</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="step-5-configure-the-trajectory">
<h3>Step 5: Configure the Trajectory<a class="headerlink" href="#step-5-configure-the-trajectory" title="Permalink to this heading">¶</a></h3>
<p>The last configuration step is to create a Trajectory Resource and add the guess, phase, and constraint Resources and include a few extra settings. Note that there are significantly more Trajectory fields that may be set (see <a class="reference internal" href="#sec-gmatoc-trajectoryreference"><span class="std std-ref">Trajectory Reference</span></a>), but, for a minimal example such as this tutorial, it is sufficient to set the fields shown below.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">Trajectory</span><span class="w"> </span><span class="s">traj</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">thePhase</span><span class="p">}</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">GuessSource</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">trajectoryGuess</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SolutionFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;Solution.och&#39;</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">StateScaleMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Canonical</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">MassScaleFactor</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">4000</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">MaxMeshRefinementIterations</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">5</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">CustomLinkages</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">initialConditions</span><span class="p">,</span><span class="w"> </span><span class="n">duration_Thrust</span><span class="p">}</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">MajorIterationsLimits</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">1000</span><span class="p">]</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">TotalIterationsLimits</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">20000</span><span class="p">]</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">FeasibilityTolerances</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mf">1e-7</span><span class="p">]</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">MajorOptimalityTolerances</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mf">1e-4</span><span class="p">]</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">OptimizationMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">Maximize</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="step-6-run-the-optimizer-and-examine-the-solution">
<h3>Step 6: Run the Optimizer and Examine the Solution<a class="headerlink" href="#step-6-run-the-optimizer-and-examine-the-solution" title="Permalink to this heading">¶</a></h3>
<p>The Collocate command is used to run the problem.  To optimize a problem, use the Collocate command including the name of the Trajectory to be optimized. Note that the Collocate command is placed after a BeginMissionSequence line in a GMAT script.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Collocate</span><span class="w"> </span><span class="s">traj</span><span class="w"></span>
</pre></div>
</div>
<p>The solution may be examined in several ways. First, the GMAT console displays information about the optimization process, including SNOPT iteration-by-iteration output and a problem summary once the optimization is completed. The summary contains information on optimizer status and constraint violations. A snippet of the console output for this example is shown below. (The output was reformatted slightly to fit on the page.)</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="o">==========================================================================</span><span class="p">=</span><span class="w"></span>
<span class="o">====</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Summary</span><span class="w">                                       </span><span class="o">====</span><span class="w"></span>
<span class="o">==========================================================================</span><span class="p">=</span><span class="w"></span>

<span class="n">Max</span><span class="p">.</span><span class="w"> </span><span class="n">Simple</span><span class="w"> </span><span class="n">Linkage</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Error</span><span class="w"> </span><span class="p">:</span><span class="w">   </span><span class="mf">0.000000000000e+00</span><span class="w"></span>
<span class="n">Max</span><span class="p">.</span><span class="w"> </span><span class="n">Custom</span><span class="w"> </span><span class="n">Linkage</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Violation</span><span class="w"> </span><span class="p">:</span><span class="w">   </span><span class="mf">2.883374691010e-06</span><span class="w"></span>
<span class="n">Max</span><span class="p">.</span><span class="w"> </span><span class="n">Custom</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="p">:</span><span class="w">   </span><span class="mf">1.252911840000e+08</span><span class="w"></span>
<span class="n">Max</span><span class="p">.</span><span class="w"> </span><span class="n">Defect</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="p">:</span><span class="w">   </span><span class="mf">2.220179595724e-11</span><span class="w"></span>
<span class="n">Cost</span><span class="w"> </span><span class="s">Function</span><span class="w"> </span><span class="s">Value</span><span class="w"> </span><span class="s">:</span><span class="w">   </span><span class="s">1.554290155413e+00</span><span class="w"></span>
<span class="n">Convergence</span><span class="w"> </span><span class="s">Status</span><span class="w"> </span><span class="s">:</span><span class="w"> </span><span class="s">Optimizer</span><span class="w"> </span><span class="s">converged</span><span class="w"></span>

…<span class="w"></span>
…<span class="w"></span>

<span class="o">==========================================================================</span><span class="p">=</span><span class="w"></span>
<span class="o">====</span><span class="w"> </span><span class="n">Custom</span><span class="w"> </span><span class="n">Linkage</span><span class="w"> </span><span class="n">Constraints</span><span class="w">                                   </span><span class="o">====</span><span class="w"></span>
<span class="o">==========================================================================</span><span class="p">=</span><span class="w"></span>

<span class="n">Constrained</span><span class="w"> </span><span class="s">Phases:</span><span class="w"> </span><span class="s">thePhase.Start</span><span class="w"></span>

<span class="w">                </span><span class="n">Lower</span><span class="w"> </span><span class="n">Bounds</span><span class="w">  </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Value</span><span class="w">  </span><span class="n">Upper</span><span class="w"> </span><span class="n">Bounds</span><span class="w"></span>
<span class="n">Time</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">3.054200000000e+04</span><span class="w">  </span><span class="mf">3.054200000000e+04</span><span class="w">  </span><span class="mf">3.054200000000e+04</span><span class="w"></span>
<span class="n">X</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1.252911840000e+08</span><span class="w">  </span><span class="mf">1.252911840000e+08</span><span class="w">  </span><span class="mf">1.252911840000e+08</span><span class="w"></span>
<span class="n">Y</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mf">7.561303600000e+07</span><span class="w">  </span><span class="o">-</span><span class="mf">7.561303600000e+07</span><span class="w">  </span><span class="o">-</span><span class="mf">7.561303600000e+07</span><span class="w"></span>
<span class="n">Z</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mf">3.278852700000e+07</span><span class="w">  </span><span class="o">-</span><span class="mf">3.278852700000e+07</span><span class="w">  </span><span class="o">-</span><span class="mf">3.278852700000e+07</span><span class="w"></span>
<span class="n">VX</span><span class="w"> </span><span class="p">=</span><span class="w">  </span><span class="mf">1.343800000000e+01</span><span class="w">  </span><span class="mf">1.343800000000e+01</span><span class="w">  </span><span class="mf">1.343800000000e+01</span><span class="w"></span>
<span class="n">VY</span><span class="w"> </span><span class="p">=</span><span class="w">  </span><span class="mf">2.523400000000e+01</span><span class="w">  </span><span class="mf">2.523400000000e+01</span><span class="w">  </span><span class="mf">2.523400000000e+01</span><span class="w"></span>
<span class="n">VZ</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1.090300000000e+01</span><span class="w">  </span><span class="mf">1.090300000000e+01</span><span class="w">  </span><span class="mf">1.090300000000e+01</span><span class="w"></span>
<span class="n">Mass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">3.930000000000e+03</span><span class="w">  </span><span class="mf">3.930000000000e+03</span><span class="w">   </span><span class="mf">3.930000000000e+03</span><span class="w"></span>

<span class="n">Constrained</span><span class="w"> </span><span class="s">Phases:</span><span class="w"> </span><span class="s">thePhase.Start</span><span class="w"> </span><span class="s">to</span><span class="w"> </span><span class="s">thePhase.End</span><span class="w"></span>

<span class="w">                </span><span class="n">Lower</span><span class="w"> </span><span class="n">Bounds</span><span class="w">  </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Value</span><span class="w">  </span><span class="n">Upper</span><span class="w"> </span><span class="n">Bounds</span><span class="w"></span>
<span class="n">Time</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">2.500000000000e+02</span><span class="w">  </span><span class="mf">2.500000000000e+02</span><span class="w">  </span><span class="mf">2.500000000000e+02</span><span class="w"></span>

<span class="n">Max</span><span class="p">.</span><span class="w"> </span><span class="n">Custom</span><span class="w"> </span><span class="n">Linkage</span><span class="w"> </span><span class="n">Constraint</span><span class="w"> </span><span class="n">Violation</span><span class="p">:</span><span class="w">   </span><span class="mf">2.883374691010e-06</span><span class="w"></span>
</pre></div>
</div>
<p>State and control history of either the most feasible or best feasible solution is output in an optimal control history (.och) file. In the case where the optimizer converged, the converged solution is printed. If the optimizer did not converge, but one or more feasible solutions were found in the final mesh iteration, the feasible solution with the minimum cost function value is printed. Finally, if no feasible solutions were found during the last mesh iteration, the solution closest to achieving feasibility is printed.</p>
<p>The format of a .och file is described in <a class="reference internal" href="#sec-gmatoc-gmattutorial-configuretheguess"><span class="std std-ref">Step 2: Configure the Guess</span></a>. The only difference from the guess format is that here, exit conditions are provided. The exit conditions contain various optimizer metrics for the solution printed, including whether the solution was a converged, feasible but not optimal, or infeasible solution. For this example, the exit conditions and the first and last lines of the .och data block are:</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">30542</span><span class="w">                      </span><span class="s">125291184.00000001</span><span class="w">         </span><span class="o">-</span><span class="mi">75613036</span><span class="w">                  </span><span class="o">-</span><span class="mf">32788526.999997117</span><span class="w">        </span><span class="mf">13.438000000000237</span><span class="w">         </span><span class="mf">25.233999999999995</span><span class="w">         </span><span class="mf">10.903</span><span class="w">                     </span><span class="mi">3930</span><span class="w">                       </span><span class="mf">0.2065315270878699</span><span class="w">         </span><span class="mf">0.89816159643679194</span><span class="w">        </span><span class="mf">0.38813718716109147</span><span class="w"></span>
…<span class="w"></span>
…<span class="w"></span>
<span class="mf">30791.999999999993</span><span class="w">         </span><span class="o">-</span><span class="mf">167583548.9232918</span><span class="w">         </span><span class="o">-</span><span class="mf">75171810.936078712</span><span class="w">        </span><span class="o">-</span><span class="mf">32391739.681220464</span><span class="w">        </span><span class="mf">9.6520747091844967</span><span class="w">         </span><span class="o">-</span><span class="mf">20.867812869758279</span><span class="w">        </span><span class="o">-</span><span class="mf">9.0315868085278339</span><span class="w">        </span><span class="mf">3822.5243683003659</span><span class="w">         </span><span class="o">-</span><span class="mf">0.89871563615191197</span><span class="w">       </span><span class="o">-</span><span class="mf">0.40299676014008162</span><span class="w">       </span><span class="o">-</span><span class="mf">0.17294310281002576</span><span class="w"></span>
</pre></div>
</div>
<p>Traditional GMAT Reports and Plots may also be used to examine the solution. A plot of the optimized solution is shown in <a class="reference internal" href="#csalt-gmat-plot"><span class="std std-numref">Fig. 12</span></a>.</p>
<figure class="align-center" id="id81">
<span id="csalt-gmat-plot"></span><a class="reference internal image-reference" href="../../_images/CSALT_GMAT_Plot.png"><img alt="../../_images/CSALT_GMAT_Plot.png" src="../../_images/CSALT_GMAT_Plot.png" style="width: 817.8px; height: 429.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 12 </span><span class="caption-text">GMAT plot generated from GMAT Optimal Control.</span><a class="headerlink" href="#id81" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
</section>
</section>
<section id="trajectory-reference">
<span id="sec-gmatoc-trajectoryreference"></span><h2>Trajectory Reference<a class="headerlink" href="#trajectory-reference" title="Permalink to this heading">¶</a></h2>
<p>A Trajectory is the primary manager class for optimal control problems and coordinates and concatenates data provided by phases and point functions. There are some differences between the implementation of Trajectory <em>resource</em> in GMAT and the Trajectory <em>class</em> in CSALT. In GMAT, much of the configuration is performed automatically by the system, and GMAT supports built-in cost and constraint functions. On the other hand, CSALT is a low-level library and does not contain built-in models.</p>
<p>In both GMAT and CSALT, the following parameters are set on the Trajectory:</p>
<ul class="simple">
<li><p>Phases to be included in the problem</p></li>
<li><p>Feasibility tolerances</p></li>
<li><p>Optimality tolerances</p></li>
<li><p>Mesh refinement iteration limits</p></li>
<li><p>Optimization iteration limits</p></li>
<li><p>Cost function bounds</p></li>
<li><p>Optimization mode (i.e., maximize, minimize, or feasible point)</p></li>
<li><p>Guess data</p></li>
</ul>
<p>In the GMAT interface, the Trajectory resource additionally supports:</p>
<ul class="simple">
<li><p>Constraints to be included in the solution, including:</p>
<ul>
<li><p>Full state linkage constraints</p></li>
<li><p>Custom linkage constraints</p></li>
<li><p>General optimal control constraints</p></li>
</ul>
</li>
<li><p>Scaling configuration</p></li>
<li><p>Output file and publisher configurations</p></li>
</ul>
<p>In CSALT interfaces, the Trajectory class additionally supports:</p>
<ul class="simple">
<li><p>Point function pointer (class used to define optimal control point functions)</p></li>
<li><p>Path function pointer (class used to define optimal control path functions)</p></li>
</ul>
<p>This reference material discusses how to configure a Trajectory in CSALT (C++ and MATLAB) and GMAT.   The first sections contain interfaces common to both GMAT and CSALT.  The subsequent sections contain information relevant only to GMAT or CSALT. In</p>
<section id="setting-the-phase-objects">
<h3>Setting the Phase Objects<a class="headerlink" href="#setting-the-phase-objects" title="Permalink to this heading">¶</a></h3>
<p>Phases are included in an optimization by adding them to the Trajectory. The examples below show how to include phases in C++ and in a GMAT script. The Phase object is described in detail in <a class="reference internal" href="#sec-gmatoc-phasereference"><span class="std std-ref">Phase Reference</span></a>.</p>
<section id="gmat-script-example">
<h4>GMAT Script Example<a class="headerlink" href="#gmat-script-example" title="Permalink to this heading">¶</a></h4>
<p>One or more Phase resources may be provided to a Trajectory in one script line.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">,</span><span class="n">phase2</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="c-example">
<h4>C++ Example<a class="headerlink" href="#c-example" title="Permalink to this heading">¶</a></h4>
<p>The Phase objects are added to the Trajectory as a std::vector of phase pointers. In the example below, we assume the Phase objects have been created and configured and illustrate how to provide the phases to the Trajectory object.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetPhaseList(std::vector&lt;Phase*&gt; pList);</span>

<span class="c1">// Example:</span>
<span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">Phase</span><span class="o">*&gt;</span><span class="w"> </span><span class="n">phaseList</span><span class="p">;</span><span class="w"></span>
<span class="n">phaseList</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="n">phase1</span><span class="p">);</span><span class="w"></span>
<span class="n">phaseList</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="n">phase2</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetPhaseList</span><span class="p">(</span><span class="n">phaseList</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="matlab-example">
<h4>MATLAB Example<a class="headerlink" href="#matlab-example" title="Permalink to this heading">¶</a></h4>
<p>One or more Phase objects may be provided to a Trajectory in one script line.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">phaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">,</span><span class="n">phase2</span><span class="p">};</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-cost-function-bounds">
<h3>Setting Cost Function Bounds<a class="headerlink" href="#setting-cost-function-bounds" title="Permalink to this heading">¶</a></h3>
<p>Cost function bounds set minimum and/or maximum values on the cost function.</p>
<section id="id8">
<h4>GMAT Script Example<a class="headerlink" href="#id8" title="Permalink to this heading">¶</a></h4>
<p>In GMAT, the cost function bounds are always +/-inf and the user does not explicitly set them.</p>
</section>
<section id="id9">
<h4>C++ Example<a class="headerlink" href="#id9" title="Permalink to this heading">¶</a></h4>
<p>Cost function bounds are set using the SetCostLowerBound and SetCostLowerBound
methods. The code snippet below shows how to set the cost function bounds to -10 and 10, respectively.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetCostLowerBound(Real costLower);</span>
<span class="c1">// virtual void SetCostUpperBound(Real costUpper);</span>

<span class="c1">// Example:</span>
<span class="n">Real</span><span class="w"> </span><span class="n">lowerBound</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">-10</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">upperBound</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SetCostLowerBound</span><span class="p">(</span><span class="n">lowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SetCostUpperBound</span><span class="p">(</span><span class="n">upperBound</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id10">
<h4>MATLAB Example<a class="headerlink" href="#id10" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">costLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">costUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">10</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-optimization-convergence-tolerances">
<h3>Setting Optimization Convergence Tolerances<a class="headerlink" href="#setting-optimization-convergence-tolerances" title="Permalink to this heading">¶</a></h3>
<p>CSALT supports several convergence tolerance settings, including feasibility tolerance and optimality tolerance. Examples are shown below for each setting. Optimization tolerances are vectors of real numbers, where entry <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/> in the vector corresponds to the tolerance for mesh iteration <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>. This allows for rapid, approximate solutions (i.e., coarse tolerances) during early mesh iterations and more stringent tolerances in later mesh iterations. During early mesh refinement iterations, the mesh is often too coarse to allow an accurate solution to the optimal control problem, so tight optimization tolerances waste computational time.</p>
<p>The following example for GMAT and C++ starts with a tolerance of 1e-4 for the first mesh refinement iteration and lowers the tolerance by an order of magnitude per iteration until 1e-7. Then, for all iterations where <img class="math" src="../../_images/math/baedb0b76556e19ddb600e5c5d47ccdc55de25ab.png" alt="i &gt; 2"/> (zero-indexed), the feasibility tolerance is 1e-7.</p>
<section id="id11">
<h4>GMAT Script Example<a class="headerlink" href="#id11" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">FeasibilityTolerances</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mf">1e-4</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-5</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-6</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-7</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id12">
<h4>C++ Example<a class="headerlink" href="#id12" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetFeasibilityTolerances(const Rvector &amp;tol);</span>

<span class="c1">// Example:</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">feasTol</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-4</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-5</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-6</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-7</span><span class="p">);</span><span class="w"></span>
<span class="n">myTraj</span><span class="p">.</span><span class="n">SetFeasiblityTolerances</span><span class="p">(</span><span class="n">feasTol</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id13">
<h4>MATLAB Example<a class="headerlink" href="#id13" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Note the MATLAB interface for setting tolerances is different than</span><span class="w"></span>
<span class="c">% GMAT interface and CSALT C++ interface.</span><span class="w"></span>
<span class="c">% Tolerances are set in Optimizer.m</span><span class="w"></span>
<span class="n">snset</span><span class="p">(</span><span class="s">&#39;Major feasibility tolerance 5e-7&#39;</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<p>The example below performs the first two mesh iterations with a major optimality tolerance of 1e-2 for GMAT and C++. Then, all subsequent mesh iterations use a tolerance of 1e-5.</p>
</section>
<section id="id14">
<h4>GMAT Script Example<a class="headerlink" href="#id14" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">MajorOptimalityTolerances</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mf">1e-2</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-2</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-5</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id15">
<h4>C++ Example<a class="headerlink" href="#id15" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetOptimalityTolerances(const Rvector &amp;tol);</span>

<span class="c1">// Example:</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">optTol</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-2</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-2</span><span class="p">,</span><span class="w"> </span><span class="mf">1e-5</span><span class="p">);</span><span class="w"></span>
<span class="n">myTraj</span><span class="p">.</span><span class="n">SetOptimalityTolerances</span><span class="p">(</span><span class="n">optTol</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id16">
<h4>MATLAB Example<a class="headerlink" href="#id16" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Note the MATLAB interface for setting tolerances is different than</span><span class="w"></span>
<span class="c">% GMAT interface and CSALT C++ interface.</span><span class="w"></span>
<span class="c">% Tolerances are set in Optimizer.m</span><span class="w"></span>
<span class="n">snset</span><span class="p">(</span><span class="s">&#39;Major feasibility tolerance 5e-7&#39;</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-optimization-iterations-limits">
<h3>Setting Optimization Iterations Limits<a class="headerlink" href="#setting-optimization-iterations-limits" title="Permalink to this heading">¶</a></h3>
<p>CSALT supports several optimization iterations limits, including the total optimization iterations, major optimization iterations, and mesh refinement iterations. Examples are shown below for each setting.</p>
<p>The example below configures CSALT for GMAT and C++ to only allow 50, 100, 500, and 1000 optimizer major iterations for the first four mesh refinement iterations, respectively. The final value in the vector passed to SetMajorIterationsLimit (in this case, 1000) is then used for all subsequent mesh iterations.</p>
<section id="id17">
<h4>GMAT Script Example<a class="headerlink" href="#id17" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">MajorIterationsLimits</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">50</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span><span class="w"> </span><span class="mi">1000</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id18">
<h4>C++ Example<a class="headerlink" href="#id18" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetMajorIterationsLimit(const IntegerArray &amp;iter);</span>

<span class="c1">// Example:</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">iterLimit</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">50</span><span class="p">,</span><span class="w"> </span><span class="mi">100</span><span class="p">,</span><span class="w"> </span><span class="mi">500</span><span class="p">,</span><span class="w"> </span><span class="mi">1000</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SetMajorIterationsLimit</span><span class="p">(</span><span class="n">iterLimit</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id19">
<h4>MATLAB Example<a class="headerlink" href="#id19" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Note the MATLAB interface for setting tolerances is different than</span><span class="w"></span>
<span class="c">% GMAT interface and CSALT C++ interface.</span><span class="w"></span>
<span class="c">% Tolerances are set in Optimizer.m</span><span class="w"></span>
<span class="n">snseti</span><span class="p">(</span><span class="s">&#39;Major iterations limit&#39;</span><span class="p">,</span><span class="mi">75</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>The example below illustrates how to set the total iterations limit to be 1000 and 2000 for the first two iterations, respectively, and to 10000 for all subsequent iterations.</p>
</section>
<section id="id20">
<h4>GMAT Script Example<a class="headerlink" href="#id20" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">TotalIterationsLimits</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">1000</span><span class="p">,</span><span class="w"> </span><span class="mi">2000</span><span class="p">,</span><span class="w"> </span><span class="mi">10000</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id21">
<h4>C++ Example<a class="headerlink" href="#id21" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetTotalIterationsLimit(const IntegerArray &amp;iter);</span>

<span class="c1">// Example:</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">iterLimit</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1000</span><span class="p">,</span><span class="w"> </span><span class="mi">2000</span><span class="p">,</span><span class="w"> </span><span class="mi">10000</span><span class="p">);</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SetTotalIterationsLimit</span><span class="p">(</span><span class="n">iterLimit</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id22">
<h4>MATLAB Example<a class="headerlink" href="#id22" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Note the MATLAB interface for setting tolerances is different than</span><span class="w"></span>
<span class="c">% GMAT interface and CSALT C++ interface.</span><span class="w"></span>
<span class="c">% Tolerances are set in Optimizer.m</span><span class="w"></span>
<span class="n">snseti</span><span class="p">(</span><span class="s">&#39;Iterations limit&#39;</span><span class="p">,</span><span class="mi">3000</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-mesh-refinement-iterations">
<h3>Setting Mesh Refinement Iterations<a class="headerlink" href="#setting-mesh-refinement-iterations" title="Permalink to this heading">¶</a></h3>
<p>The example below shows how to set the maximum number of mesh refinement iterations to 7.</p>
<section id="id23">
<h4>GMAT Script Example<a class="headerlink" href="#id23" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">MaxMeshRefinementIterations</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">7</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id24">
<h4>C++ Example<a class="headerlink" href="#id24" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetMaxMeshRefinementCount(Integer toCount);</span>

<span class="c1">// Example:</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">iterLimit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">7</span><span class="p">;</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">SetMaxMeshRefinementCount</span><span class="p">(</span><span class="n">iterLimit</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id25">
<h4>MATLAB Example<a class="headerlink" href="#id25" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">maxMeshRefinementCount</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">7</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-optimization-and-mesh-modes">
<h3>Setting Optimization and Mesh Modes<a class="headerlink" href="#setting-optimization-and-mesh-modes" title="Permalink to this heading">¶</a></h3>
<p>CSALT supports several settings that determine optimization modes and behavior. These settings allow the user to tell CSALT to find a feasible or optimal solution, whether to allow mesh refinement to continue if an optimization fails, and to choose between guess options to use for new mesh refinement iterations.  Supported optimization modes are “Minimize”, “Feasible point”, and “Maximize”.  The examples below illustrate how to configure CSALT to find a feasible solution for the first two mesh iterations, and to find a minimum point for all subsequent mesh iterations.</p>
<section id="id26">
<h4>GMAT Script Example<a class="headerlink" href="#id26" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">OptimizationMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">Feasible</span><span class="w"> </span><span class="n">point</span><span class="p">,</span><span class="w"> </span><span class="n">Feasible</span><span class="w"> </span><span class="n">point</span><span class="p">,</span><span class="w"> </span><span class="n">Minimize</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id27">
<h4>C++ Example<a class="headerlink" href="#id27" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetOptimizationMode(const StringArray &amp;optMode);</span>

<span class="c1">// Example:</span>
<span class="n">optMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">StringArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Feasible point&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Feasible point&quot;</span><span class="p">,</span><span class="w"> </span><span class="s">&quot;Minimize&quot;</span><span class="p">);</span><span class="w"></span>
<span class="n">SetOptimizationMode</span><span class="p">(</span><span class="n">optMode</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>The user can optionally allow mesh iterations to continue in the event an optimization fails to converge. Often, if the optimizer fails but is “reasonably close” to a solution, subsequent mesh iterations will converge. The example below shows how to configure CSALT to continue mesh refinement iterations even if an optimization (i.e., mesh refinement iteration) fails to converge.</p>
</section>
<section id="id28">
<h4>GMAT Script Example<a class="headerlink" href="#id28" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">AllowFailedMeshOptimizations</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">true</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id29">
<h4>C++ Example<a class="headerlink" href="#id29" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetFailedMeshOptimizationAllowance(bool toAllowance);</span>

<span class="c1">// Example:</span>
<span class="n">SetFailedMeshOptimizationAllowance</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id30">
<h4>MATLAB Example<a class="headerlink" href="#id30" title="Permalink to this heading">¶</a></h4>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>AllowFailedMeshOptimizations is not supported in CSALT MATLAB.</p>
</div>
<p>When performing mesh iterations after a failed mesh refinement iteration (due to reaching the maximum number of iterations or encountering numerical issues in the previous mesh refinement iteration), it is often advantageous to use the “best” previous solution as the initial guess for the next mesh refinement iteration rather than the unconverged final decision variable values from the most recent mesh refinement iteration. This behavior is especially useful when an optimization run is performing reasonably well and then a mesh refinement iteration unexpectedly fails to converge.</p>
<p>CSALT allows the user to specify the previous solution used to generate the initial guess for the next mesh refinement iteration. Supported modes are “LastSolutionMostRecentMesh”, “BestSolutionMostRecentMesh” and “BestSolutionAnyMesh”. “Last” refers to the last solution obtained during a mesh refinement iteration. The following conditions are used to define “best”: (1) If no feasible solutions have been found, then “best” refers to the previous solution with the smallest infeasibility. (2) If one feasible solution has been found, then that solution is “best,” regardless of its optimality. (3) If multiple feasible solutions have been found, then the feasible solution with the smallest value of the merit function (i.e., the most optimal solution) is “best.” “MostRecentMesh” means a guess for the new mesh will only be taken from the previous mesh refinement iteration, while “AnyMesh” means the guess can come from any previous mesh refinement iteration.</p>
<p>The example below shows how to configure CSALT to use either the most recent mesh refinement iteration or any mesh refinement iteration to obtain the initial guess for a given subsequent mesh refinement iteration.</p>
</section>
<section id="id31">
<h4>GMAT Script Example<a class="headerlink" href="#id31" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">MeshRefinementGuessMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span>‘<span class="n">BestSolutionAnyMesh</span>’<span class="w"></span>
</pre></div>
</div>
</section>
<section id="id32">
<h4>C++ Example<a class="headerlink" href="#id32" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetMeshRefinementGuessMode(const std::string &amp;toGuessMode);</span>

<span class="c1">// Example:</span>
<span class="n">SetMeshRefinementGuessMode</span><span class="p">(</span><span class="s">&quot;BestSolutionAnyMesh&quot;</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-snopt-output-data-file">
<h3>Setting SNOPT Output Data File<a class="headerlink" href="#setting-snopt-output-data-file" title="Permalink to this heading">¶</a></h3>
<p>SNOPT writes data to an output file during optimization.  The examples below show how to set the filename for the SNOPT output file.</p>
<section id="id33">
<h4>GMAT Script Example<a class="headerlink" href="#id33" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">SNOPTOutputFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">fileName</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id34">
<h4>C++ Example<a class="headerlink" href="#id34" title="Permalink to this heading">¶</a></h4>
<p>The Trajectory object’s Optimize method takes as an argument a string for the name of the optimizer output file. This string is passed to the Trajectory’s SnoptOptimizer object.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// function prototype for setting the SNOPT output file</span>
<span class="c1">// void SnoptOptimizer::SetOptimizerOutputFile(const std::string &amp;optFile)</span>

<span class="c1">// function prototype for Trajectory::Optimize (Note that Optimize has multiple overloaded definitions.)</span>
<span class="c1">// void Trajectory::Optimize(const std::string &amp;optFile)</span>

<span class="c1">// Example:</span>
<span class="n">Optimize</span><span class="p">(</span><span class="s">&quot;/home/<USER>/file.txt&quot;</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-snopt-print-to-console-verbosity">
<h3>Setting SNOPT Print-To-Console Verbosity<a class="headerlink" href="#setting-snopt-print-to-console-verbosity" title="Permalink to this heading">¶</a></h3>
<p>By default, SNOPT writes output to the console during execution. In some circumstances, this behavior may be undesirable. The example below shows how to quiet SNOPT output. Specifically, the following SNOPT options are set:</p>
<ul class="simple">
<li><p>Major Print Level = 0</p></li>
<li><p>Minor Print Level = 0</p></li>
<li><p>Print No = 0</p></li>
<li><p>Summary file = 0</p></li>
<li><p>Suppress parameters</p></li>
</ul>
<p>By default, the default values are used for all of the above settings.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>This option is only available in CSALT C++.</p>
</div>
<section id="id35">
<h4>C++ Example<a class="headerlink" href="#id35" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">Trajectory</span><span class="o">*</span><span class="w"> </span><span class="n">traj</span><span class="p">;</span><span class="w"> </span><span class="c1">// create pointer to Trajectory object</span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetSnoptConsoleOutputLevel</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"> </span><span class="c1">// 0: quiet output; all other integers (and default): default output</span>
</pre></div>
</div>
</section>
</section>
<section id="scaling-the-optimal-control-problem">
<h3>Scaling the Optimal Control Problem<a class="headerlink" href="#scaling-the-optimal-control-problem" title="Permalink to this heading">¶</a></h3>
<p>GMAT currently supports one option for scaling optimal control problems using canonical units, which are defined as follows: If the Sun is the central body, then the distance unit is an astronomical unit and the GM of the Sun is 1. If the Earth is the central body, then the distance unit is 42000 km and the GM of the Earth is 1. Otherwise, the radius of the central body is the distance unit and the GM of the central body is 1.</p>
<p>Mass values can change significantly from one application to the next and the user can set the mass scale factor as shown below.  It is a best practice to choose a mass scale factor such that the total mass is approximately 1.0 at the beginning of the simulation period.</p>
<section id="id36">
<h4>GMAT Script Example<a class="headerlink" href="#id36" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">StateScaleMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Canonical</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">MassScaleFactor</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1000</span><span class="w"> </span><span class="c">% in Kg.</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>CSALT CURRENTLY REQUIRES USERS TO PROVIDE DATA IN NON-DIMENSIONAL UNITS.  BUILT-IN SCALING IS IN PROGRESS BUT NOT COMPLETE.</p>
</div>
</section>
</section>
<section id="setting-the-solution-output-configuration-in-gmat">
<h3>Setting the Solution Output Configuration in GMAT<a class="headerlink" href="#setting-the-solution-output-configuration-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>GMAT can optionally write solution data to an optimal control history (.och) file. The user may select the output coordinate system by providing the name of a configured coordinate system. Alternatively, the user can use the keyword “UsePhaseCoordinateSystems” and the solution for each Phase will be reported in the Phase’s coordinate system.</p>
<section id="id37">
<h4>GMAT Script Example<a class="headerlink" href="#id37" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">SolutionFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">PATH_AND_FILENAME</span><span class="p">.</span><span class="n">EXTENSTION</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">OutputCoordinateSystem</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">UsePhaseCoordinateSystems</span><span class="w"></span>

<span class="c">% Or to write the data in EarthMJ2000Eq, for example</span><span class="w"></span>

<span class="n">traj</span><span class="p">.</span><span class="n">OutputCoordinateSystem</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">EarthMJ2000Eq</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-simple-linkage-constraints-in-gmat">
<h3>Setting Simple Linkage Constraints in GMAT<a class="headerlink" href="#setting-simple-linkage-constraints-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>A simple linkage constraint requires that the full state (and time) of a phase at one endpoint be equal to the full state (and time) of another phase at one endpoint. In order for a simple linkage constraint to be enforced, a list of linked phases must be added to a trajectory.</p>
<section id="id38">
<h4>GMAT Script Example<a class="headerlink" href="#id38" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">AddSimpleLinkageChain</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">,</span><span class="w"> </span><span class="n">phase2</span><span class="p">,</span><span class="w"> </span><span class="n">phase3</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-guess-source-in-gmat">
<h3>Setting the Guess Source in GMAT<a class="headerlink" href="#setting-the-guess-source-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>GMAT requires an initial guess for the trajectory states and controls. The guess source may be set at the Trajectory level and/or at the Phase level; if a guess resource is set for a Phase, then that guess source overrides the Trajectory-level guess source for that Phase.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlGuess</span><span class="w"> </span><span class="s">trajectoryGuess</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">CollocationGuessFile</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">FileName</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">.</span><span class="o">./</span><span class="n">data</span><span class="o">/</span><span class="n">misc</span><span class="o">/</span><span class="n">GuessWithUnityControl</span><span class="p">.</span><span class="n">och</span><span class="w"></span>

<span class="n">traj</span><span class="p">.</span><span class="n">GuessSource</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">trajectoryGuess</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-publisher-update-rate-in-gmat">
<h3>Setting the Publisher Update Rate in GMAT<a class="headerlink" href="#setting-the-publisher-update-rate-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>GMAT publishes state data during optimization to subscribers such as plots, graphic windows, reports, and ephemeris  files.  You can control how much data is sent to subscribers by setting the PublishUpdateRate field.   For example, if PublishUpdateRate is set to 10, then data is only sent to subscribers every 10 cost/constraint function evaluations.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">traj</span><span class="p">.</span><span class="n">PublishUpdateRate</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">10</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-path-functions-in-csalt">
<h3>Setting Path Functions in CSALT<a class="headerlink" href="#setting-path-functions-in-csalt" title="Permalink to this heading">¶</a></h3>
<p>The path functions include dynamics models, algebraic path functions, and integral cost functions. The examples below show how to include Path Function objects in an optimization problem. Configuring those objects is discussed in <a class="reference internal" href="#sec-gmatoc-pathfunctionsreference"><span class="std std-ref">Path Functions Reference</span></a>.</p>
<section id="id39">
<h4>GMAT Script Example<a class="headerlink" href="#id39" title="Permalink to this heading">¶</a></h4>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Path functions are NOT accessed directly by users in GMAT scripts.</p>
</div>
</section>
<section id="id40">
<h4>C++ Example<a class="headerlink" href="#id40" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Here, BrachistichronePathObject is derived from UserPathObject</span>

<span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetUserPathFunction(UserPathFunction *func);</span>

<span class="c1">// Example:</span>
<span class="n">Traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>
<span class="n">pathObject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachistichronePathObject</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetUserPathFunction</span><span class="p">(</span><span class="n">pathObject</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id41">
<h4>MATLAB Example<a class="headerlink" href="#id41" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Here MyPathObject is a class derived from UserPathFunction.</span><span class="w"></span>
<span class="c">% This creates an object of type MyPathObject.</span><span class="w"></span>
<span class="n">thePathObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">MyPathObject</span><span class="p">();</span><span class="w"></span>

<span class="c">% Now add the object to the trajectory</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">pathFunctionObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thePathObject</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-boundary-functions-in-csalt">
<h3>Setting Boundary Functions in CSALT<a class="headerlink" href="#setting-boundary-functions-in-csalt" title="Permalink to this heading">¶</a></h3>
<p>Boundary functions include algebraic boundary constraints and algebraic cost function terms that depend on only static variables and parameters at phase boundaries. Boundary Function objects are created and then added to a Trajectory object. If a Boundary Function is not added to a Trajectory object, then the boundary function will not be applied during optimization.</p>
<section id="id42">
<h4>C++ Example<a class="headerlink" href="#id42" title="Permalink to this heading">¶</a></h4>
<p>CSALT supports two interfaces for providing Boundary Functions to a Trajectory: the UserPointFunction class and the OptimalControlFunction utility class. Those classes are described in detail in <a class="reference internal" href="#sec-gmatoc-boundaryfunctionsreference"><span class="std std-ref">Boundary Functions Reference</span></a>. The examples below show how to include Boundary Functions in an optimization by adding them to the Trajectory.</p>
<p>A Trajectory can only have one UserPointFunction object. To include a UserPointFunction object:</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Here, BrachistichronePointObject is derived from UserPointObject</span>

<span class="c1">// Function Prototype:</span>
<span class="c1">// virtual void SetUserPointFunction(UserPointFunction *func);</span>

<span class="c1">// Example:</span>
<span class="n">Traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>
<span class="n">pointObject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetUserPathFunction</span><span class="p">(</span><span class="n">pointObject</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id43">
<h4>C++ Example<a class="headerlink" href="#id43" title="Permalink to this heading">¶</a></h4>
<p>A Trajectory can have a list of OptimalControlFunction objects.  Optimal control functions allow re-use of models, cost, and constraint functions between different problems and applications.  The example below contains the function prototype to add an OptimalControlFunction object to a Trajectory.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototpye:</span>
<span class="kt">void</span><span class="w"> </span><span class="n">UserPointFunction</span><span class="o">::</span><span class="n">AddFunctions</span><span class="p">(</span><span class="w"></span>
<span class="w">    </span><span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">OptimalControlFunction</span><span class="o">*&gt;</span><span class="w"> </span><span class="n">funcList</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id44">
<h4>MATLAB Example<a class="headerlink" href="#id44" title="Permalink to this heading">¶</a></h4>
<p>Like in C++, a MATLAB Trajectory object can only have one UserPointFunction object.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Here MyBoundaryObject is a class derived from UserPointFunction.</span><span class="w"></span>
<span class="c">% This creates an object of type MyBoundaryObject.</span><span class="w"></span>
<span class="n">theBoundaryObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">MyBoundaryObject</span><span class="p">();</span><span class="w"></span>

<span class="c">% Now add the object to the trajectory</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">pointFunctionObject</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">theBoundaryObject</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-executioninterface-object-in-csalt">
<h3>Setting the ExecutionInterface Object in CSALT<a class="headerlink" href="#setting-the-executioninterface-object-in-csalt" title="Permalink to this heading">¶</a></h3>
<p>The ExecutionInterface object is an optional referenced object that CSALT calls during optimization to provide CSALT state data during execution.  The ExecutionInterface is described in detail in <a class="reference internal" href="#sec-gmatoc-executioninterfacereference"><span class="std std-ref">ExecutionInterface Reference</span></a>. In the example below, we assume the ExecutionInterface object has been created and configured and illustrate how to provide the ExecutionInterface to the Trajectory object.</p>
<section id="id45">
<h4>GMAT Script Example<a class="headerlink" href="#id45" title="Permalink to this heading">¶</a></h4>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>This interface is not applicable to GMAT script users. Publishing in GMAT is handled via the GMAT publisher using GMAT publish/subscribe components that are transparent to GMAT users.</p>
</div>
</section>
<section id="id46">
<h4>C++ Example<a class="headerlink" href="#id46" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Function Prototpye:</span>
<span class="c1">// void GmatTrajectory::SetExecutionInterface(CsaltExecutionInterface *intf)</span>

<span class="c1">// Example:</span>
<span class="n">csaltInterface</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">CsaltExecutionInterface</span><span class="p">(</span><span class="k">this</span><span class="p">);</span><span class="w"></span>
<span class="n">execInterface</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Publisher</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>
<span class="n">Trajectory</span><span class="o">::</span><span class="n">SetExecutionInterface</span><span class="p">(</span><span class="n">execInterface</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="phase-reference">
<span id="sec-gmatoc-phasereference"></span><h2>Phase Reference<a class="headerlink" href="#phase-reference" title="Permalink to this heading">¶</a></h2>
<p>A Phase is a segment of a Trajectory (a Trajectory must have a least one Phase).  Phases  are often used to differentiate portions of a trajectory that require different dynamics models or to break a problem down into segments to reduce the non-linearity of the problem and therefore improve sensitivity to poor guesses.</p>
<p>In GMAT, the Phase Resource is where the transcription and dynamics model are configured (different phases can use different transcriptions and dynamics models).  Other settings configured on the phase object include bounds on state, control, time, and static parameters, and the initial guess for the phase.  Examples for each of these settings are shown in the subsections below.</p>
<p>Note that GMAT supports pre-defined dynamics models while the C++ and MATLAB systems are low level APIs where you must code your own dynamics models.  There are also differences in how guesses are provided in GMAT and CSALT. Below we present an overview of state and control representations, how to select and configure a transcription, and then provide code examples that illustrate how to configure a Phase in GMAT, CSALT C++ and CSALT MATLAB.</p>
<section id="state-and-control-representations-in-gmat">
<h3>State and Control Representations in GMAT<a class="headerlink" href="#state-and-control-representations-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>The default state representation in GMAT is the Cartesian state and total mass (7 components) and the default control representation is “up-to-unit-vector” control (three components).  The coordinate system for a phase uses MJ2000Eq axes and the central body of the Force Model selected in the DynamicsConfiguration for the phase.  State bounds are defined with respect to that coordinate system.</p>
<p>The default control representation is “up-to-unit-vector” control.  When using up-to-unit vector control, the control decision vector at a given discretization point has three elements that define the direction and magnitude of the thrust.  The applied thrust in the equations of motion is equal to the maximum available thrust from the propulsion system, multiplied by the control decision vector components.   If the control decision vector has magnitude 1.0, then the applied thrust is the maximum thrust available from the system.  If the control decision vector has magnitude 0.0, then no thrust is applied.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>GMAT supports setting the maximum control magnitude, MaxControlMagnitude,  to be greater than 1.0.   This allows the use of homotopy when starting from a poor guess or for troubleshooting issues in a problem’s configuration.  It is critical to understand that solutions found when MaxControlMagnitude &gt; 1.0 are non-physical solutions that use MORE thrust than the chosen thrust system can produce.</p>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>It is a best practice to set the lower bounds on mass to be positive and near, but not equal, to zero.  If mass is zero, then a singularity in the dynamics occurs because mass appears in the denominator for the thrust acceleration term in the equations of motion.</p>
</div>
</section>
<section id="setting-the-transcription">
<h3>Setting the Transcription<a class="headerlink" href="#setting-the-transcription" title="Permalink to this heading">¶</a></h3>
<p>Configuring a transcription requires several steps including:</p>
<ul class="simple">
<li><p>Selecting a transcription algorithm</p></li>
<li><p>Setting the discretization properties (how many points/steps, and the relative location of the points/steps)</p></li>
<li><p>Setting the tolerance on the discretization accuracy</p></li>
</ul>
<p>In this section, we discuss details of those setting and then show examples in GMAT, CSALT C++, and CSALT MATLAB.</p>
<p>CSALT supports several transcription types including Radau pseudospectral and implicit Runge Kutta.  The system supports several implicit Runge-Kutta transcriptions including Hermite Simpson and several Lobatto IIIA types among others.  To select a transcription algorithm, use the Type field on the Phase Resource.</p>
<p>The discretization configuration defines the number and relative location of discretization points within a Phase. In GMAT, the discretization points/steps are defined using the fields SubPhaseBoundaries and PointsPerSubPhase fields.  In CSALT, the discretization points/steps are defined using the functions SetMeshIntervalFractions() and SetMeshIntervalNumPoints(). (SubPhaseBoundaries corresponds to SetMeshIntervalFractions() and PointsPerSubPhase corresponds to SetMeshIntervalNumPoints().) In brief, SubPhaseBoundaries defines the number of sub-phases within a phase and the span of the independent variable of each sub-phase in the initial mesh, while PointsPerSubPhase sets the number of collocation points within each sub-phase in the initial mesh. See the discussions in <a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#gmatoc-uispec-phase-subphaseboundaries"><span class="std std-numref">Table 39</span></a> and <a class="reference internal" href="csalt-UserInterfaceSpec-smallTables.html#gmatoc-uispec-phase-pointspersubphase"><span class="std std-numref">Table 40</span></a> for a more detailed description of SubPhaseBoundaries and PointsPerSubPhase, respectively.</p>
<p>Different transcription types require different discretization configurations. Radau phases are non-dimensionalized on the independent variable (e.g., time) from -1.0 to 1.0. SubPhaseBoundaries (in GMAT) and MeshIntervalFractions (in CSALT) are arrays of real numbers that, for Radau, must start with -1.0 and end with 1.0, with intermediate values (increasing monotonically) representing the relative spacing of the subphases within a phase. On the other hand, Runge-Kutta phases are non-dimensionalized on the independent variable from 0.0 to 1.0.</p>
<p>When dynamics are more rapid, more collocation points are required for accurate modeling. For example, if it is known that there is a relatively highly dynamic region in the first half of a phase, and relatively slow dynamic region in the second half of the phase, it is natural to place more mesh points in the first half of the phase. Mesh refinement refines the initial mesh, potentially increasing the number of subphases and/or the number of points in individual subphases to model the dynamics with enough accuracy to meet the user’s mesh refinement tolerance.</p>
<p>Defining discretization points in GMAT or CSALT defines a guess for the discretization and the mesh refinement algorithm will refine the discretization to meet the requested accuracy.  The MaxRelativeErrorTolerance field defined the maximum relative error allowed in a phase.</p>
<section id="id47">
<h4>GMAT Script Example<a class="headerlink" href="#id47" title="Permalink to this heading">¶</a></h4>
<p>The following example configures a Phase to use RadauPseudospectral transcription, with 5 subphases each with a third order polynomial.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">Phase</span><span class="w"> </span><span class="s">thePhase</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">RadauPseudospectral</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">SubPhaseBoundaries</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="o">-</span><span class="mf">1.0</span><span class="p">,</span><span class="o">-</span><span class="mf">0.75</span><span class="p">,</span><span class="o">-</span><span class="mf">0.5</span><span class="p">,</span><span class="o">-</span><span class="mf">0.25</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">PointsPerSubPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="p">]</span><span class="w"></span>
<span class="n">thephase</span><span class="p">.</span><span class="n">MaxRelativeErrorTolerance</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1e-5</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id48">
<h4>C++ Example<a class="headerlink" href="#id48" title="Permalink to this heading">¶</a></h4>
<p>In the example below, we place 4 equally spaced mesh intervals in the first half of the phase, and one mesh interval in the second half of the phase where all mesh intervals have 4 points.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create a phase pointer to a Radau phase.</span>
<span class="n">Phase</span><span class="w"> </span><span class="o">*</span><span class="n">phase1</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">();</span><span class="w"></span>

<span class="c1">// Set the mesh interval fractions and number of points in each mesh interval. Here we have one mesh interval with 5 points in it.</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">meshIntervalFractions</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="mf">-1.0</span><span class="p">,</span><span class="mf">-0.75</span><span class="p">,</span><span class="mf">-0.5</span><span class="p">,</span><span class="mf">-0.25</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>
<span class="n">IntegerArray</span><span class="w"> </span><span class="n">meshIntervalNumPoints</span><span class="p">;</span><span class="w"> </span><span class="c1">// an std::vector of Integer variables</span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">5</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">4</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalFractions</span><span class="p">(</span><span class="n">meshIntervalFractions</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalNumPoints</span><span class="p">(</span><span class="n">meshIntervalNumPoints</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Set the mesh refinement relative error tolerance to 1e-5</span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetRelativeErrorTol</span><span class="p">(</span><span class="mf">1e-5</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id49">
<h4>MATLAB Example<a class="headerlink" href="#id49" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">phase1</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetMeshIntervalFractions</span><span class="p">([</span><span class="o">-</span><span class="mf">1.0</span><span class="p">,</span><span class="o">-</span><span class="mf">0.75</span><span class="p">,</span><span class="o">-</span><span class="mf">0.5</span><span class="p">,</span><span class="o">-</span><span class="mf">0.25</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">]);</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetMeshIntervalNumPoints</span><span class="p">([</span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="w"> </span><span class="mi">4</span><span class="p">]);</span><span class="w"></span>
</pre></div>
</div>
<p>The example below illustrates how to configure a phase to use an ImplicitRungeKutta phase.   There are several Implicit Runge-Kutta transcription types supported as shown below.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create an implicit Runge Kutta phase of order 8</span>
<span class="n">ImplicitRKPhase</span><span class="w"> </span><span class="o">*</span><span class="n">phase1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">ImplicitRKPhase</span><span class="p">();</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTranscription</span><span class="p">(</span><span class="s">&quot;RungeKutta8&quot;</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Set it to have one mesh interval with 20 steps</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">meshIntervalFractions</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>
<span class="n">IntegerArray</span><span class="w"> </span><span class="n">meshIntervalNumPoints</span><span class="p">;</span><span class="w"></span>
<span class="n">meshIntervalNumPoints</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="mi">20</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalFractions</span><span class="p">(</span><span class="n">meshIntervalFractions</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetMeshIntervalNumPoints</span><span class="p">(</span><span class="n">meshIntervalNumPoints</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-decision-vector-bounds-and-dimensions">
<h3>Setting Decision Vector Bounds and Dimensions<a class="headerlink" href="#setting-decision-vector-bounds-and-dimensions" title="Permalink to this heading">¶</a></h3>
<p>Examples below show how to set bounds on state, control, independent variable (e.g., time), and static variables.  There are some differences in interfaces between GMAT and CSALT related to time, and setting parameter dimensions such as the number of states and controls.  In CSALT, the user must explicitly set the number of states, controls, etc.  In GMAT, the number of states and the number of controls are not currently user-settable: the number of states is seven (position vector, velocity vector, mass), and the number of controls is three (Cartesian control vector).</p>
<section id="id50">
<h4>GMAT Script Example<a class="headerlink" href="#id50" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a phase</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Phase</span><span class="w"> </span><span class="s">thePhase</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">RadauPseudospectral</span><span class="w"></span>

<span class="c">% Set lower bounds on Cartesian position (default state type) to</span><span class="w"></span>
<span class="c">% -10000 km, bounds on Cartesian velocity to -10 km/s and mass</span><span class="w"></span>
<span class="c">% to 0.01 kg.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">StateLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w"> </span><span class="o">-</span><span class="mi">10000</span><span class="w"> </span><span class="o">-</span><span class="mi">10000</span><span class="w"> </span><span class="o">-</span><span class="mi">10000</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="mf">0.01</span><span class="p">]</span><span class="w"></span>

<span class="c">% Set upper bounds on Cartesian position (default state type) to</span><span class="w"></span>
<span class="c">% 10000 km, bounds on Cartesian velocity to 10 km/s and mass</span><span class="w"></span>
<span class="c">% to 3000 kg.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">StateUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="w">  </span><span class="mi">10000</span><span class="w">  </span><span class="mi">10000</span><span class="w">  </span><span class="mi">10000</span><span class="w">  </span><span class="mi">10</span><span class="w">  </span><span class="mi">10</span><span class="w">  </span><span class="mi">10</span><span class="w"> </span><span class="mi">3000</span><span class="p">]</span><span class="w"></span>

<span class="c">% Set bounds on the phase epochs.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochFormat</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">TAIModJulian</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">32402</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">EpochUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">32405</span><span class="w"></span>

<span class="c">% Set the bounds on control to -2 and 2 respectively.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ControlLowerBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="o">-</span><span class="mi">2</span><span class="w"> </span><span class="o">-</span><span class="mi">2</span><span class="w"> </span><span class="o">-</span><span class="mi">2</span><span class="p">]</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ControlUpperBound</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">2</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="mi">2</span><span class="p">]</span><span class="w"></span>

<span class="c">% Set the bounds on the control vector magnitude.</span><span class="w"></span>
<span class="c">% WARNING. SETTING MaxControlMagnitude &gt; 1.0 SHOULD BE USED WITH CARE</span><span class="w"></span>
<span class="c">% (FOR HOMOTOPY OR TROUBLESHOOTING) AND WILL RESULT IN NON-PHYSICAL SOLUTIONS.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">MinControlMagnitude</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.0</span><span class="w"></span>
<span class="n">thephase</span><span class="p">.</span><span class="n">MaxControlMagnitude</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1.0</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id51">
<h4>C++ Example<a class="headerlink" href="#id51" title="Permalink to this heading">¶</a></h4>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create a phase</span>
<span class="n">Phase</span><span class="w"> </span><span class="o">*</span><span class="n">phase1</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">();</span><span class="w"></span>

<span class="c1">// Configure problem with two state variables with lower and upper</span>
<span class="c1">// bounds of 0.0 and 2.0, respectively, for both state variables.</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numStateVars</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">stateLowerBound</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">stateUpperBound</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mf">2.0</span><span class="p">,</span><span class="w"> </span><span class="mf">2.0</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumStateVars</span><span class="p">(</span><span class="n">numStateVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateLowerBound</span><span class="p">(</span><span class="n">stateLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateUpperBound</span><span class="p">(</span><span class="n">stateUpperBound</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Configure problem with one control variable, with lower and upper</span>
<span class="c1">// bounds of -10.0 and 10.0 respectively.</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numControlVars</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">controlUpperBound</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mf">10.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">controlLowerBound</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mf">-10.0</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumControlVars</span><span class="p">(</span><span class="n">numControlVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetControlLowerBound</span><span class="p">(</span><span class="n">controlLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetControlUpperBound</span><span class="p">(</span><span class="n">controlUpperBound</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Configure problem with three static variables with lower and upper</span>
<span class="c1">// bounds to 0.0 and 20.0, respectively, for all three variables.</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numStaticVars</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">staticUpperBound</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">20.0</span><span class="p">,</span><span class="w"> </span><span class="mf">20.0</span><span class="p">,</span><span class="w"> </span><span class="mf">20.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">staticLowerBound</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumStaticVars</span><span class="p">(</span><span class="n">numStaticVars</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStaticLowerBound</span><span class="p">(</span><span class="n">staticLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStaticUpperBound</span><span class="p">(</span><span class="n">staticUpperBound</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Configure lower and upper bounds on time (or independent variable)</span>
<span class="c1">// to 0.0 and 100.0 respectively.</span>
<span class="n">Real</span><span class="w"> </span><span class="n">timeLowerBound</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">timeUpperBound</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100.0</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeLowerBound</span><span class="p">(</span><span class="n">timeLowerBound</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeUpperBound</span><span class="p">(</span><span class="n">timeUpperBound</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id52">
<h4>MATLAB Example<a class="headerlink" href="#id52" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Configure problem with three state variables with lower and upper</span><span class="w"></span>
<span class="c">% bounds of -10 and 10, respectively, for all state variables.</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetNumStateVars</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateLowerBound</span><span class="p">([</span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="w"> </span><span class="o">-</span><span class="mi">10</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateUpperBound</span><span class="p">([</span><span class="mi">10</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>

<span class="c">% Configure problem with one control variable with lower and upper</span><span class="w"></span>
<span class="c">% bounds of -10 and 10, respectively.</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetNumControlVars</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetControlLowerBound</span><span class="p">([</span><span class="o">-</span><span class="mi">10</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetControlUpperBound</span><span class="p">([</span><span class="mi">10</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>

<span class="c">% Configure lower and upper bounds on time (or independent variable)</span><span class="w"></span>
<span class="c">% to 0.0 and 100.0 respectively.</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeLowerBound</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeUpperBound</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-initial-guess-in-gmat">
<h3>Setting the Initial Guess in GMAT<a class="headerlink" href="#setting-the-initial-guess-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>In GMAT, state and control guesses are defined by an OptimalControlGuess Resource and Phase time guesses are defined via fields on the Phase.  An OptimalControlGuess Resource supports text-based and array-based guess data to easily create randomized guesses, import guesses from GMAT utility scripts, call MATLAB or Python for guess data, or use data from an external tool such as EMTG, ATD, or MALTO among others.  See the OptimalControlGuess documentation for further information on how to define the values of state and control guesses.</p>
<p>You can provide an OptimalControlGuess on a Phase and/or a Trajectory. If the Phase is provided with an OptimalControlGuess, that data is used as the guess for a phase, otherwise the guess on the Trajectory is used. If there is no guess on a Phase or Trajectory, an error is issued. The script example below shows how to define the OptimalControlGuess Resource (state and control) for a Phase, and the initial guess for phase epochs.</p>
<section id="id53">
<h4>GMAT Script Example<a class="headerlink" href="#id53" title="Permalink to this heading">¶</a></h4>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Use a pre-configured OptimalControlGuess Resource</span><span class="w"></span>
<span class="c">% called “thePhaseGuess” as the guess for state and control</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">GuessSource</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thePhaseGuess</span><span class="w"></span>

<span class="c">% Define the initial and final epochs using TAI modified Julian date</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">EpochFormat</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">TAIModJulian</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">InitialEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">34050.99962787928</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">FinalEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">34513.39999998195</span><span class="w"></span>

<span class="c">% Alternatively, define the initial and final epochs using the</span><span class="w"></span>
<span class="c">% UTC Gregorian date</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">EpochFormat</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">UTCGregorian</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">InitialEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">29</span><span class="w"> </span><span class="n">Mar</span><span class="w"> </span><span class="mi">2034</span><span class="w"> </span><span class="mi">11</span><span class="p">:</span><span class="mi">58</span><span class="p">:</span><span class="mf">52.849</span><span class="w"></span>
<span class="n">phase</span><span class="p">.</span><span class="n">FinalEpoch</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">04</span><span class="w"> </span><span class="n">Jul</span><span class="w"> </span><span class="mi">2035</span><span class="w"> </span><span class="mi">21</span><span class="p">:</span><span class="mi">35</span><span class="p">:</span><span class="mf">24.998</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-initial-guess-in-csalt-c-and-matlab">
<h3>Setting the Initial Guess in CSALT C++ and MATLAB<a class="headerlink" href="#setting-the-initial-guess-in-csalt-c-and-matlab" title="Permalink to this heading">¶</a></h3>
<p>CSALT supports several interfaces for setting guesses including a linear model  for state  and control, array based and file based guesses.   The sections below contain examples for setting guesses for time (or independent variable), state, control, and static parameters using the various interfaces supported for initial guesses.</p>
<section id="setting-the-initial-guess-for-time-or-independent-variable">
<h4>Setting the Initial Guess for Time (or Independent Variable)<a class="headerlink" href="#setting-the-initial-guess-for-time-or-independent-variable" title="Permalink to this heading">¶</a></h4>
<section id="id54">
<h5>C++ Example<a class="headerlink" href="#id54" title="Permalink to this heading">¶</a></h5>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Set the initial and final time guess to 0.0 and 0.3 respectively.</span>
<span class="n">Real</span><span class="w"> </span><span class="n">initialGuessTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">finalGuessTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.3</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeInitialGuess</span><span class="p">(</span><span class="n">initialGuessTime</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetTimeFinalGuess</span><span class="p">(</span><span class="n">finalGuessTime</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id55">
<h5>MATLAB Example<a class="headerlink" href="#id55" title="Permalink to this heading">¶</a></h5>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">%Set the initial and final time guess to 0.0 and 0.3 respectively.</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeInitialGuess</span><span class="p">(</span><span class="mf">0.0</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetTimeFinalGuess</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-initial-guess-for-state-and-control-using-linear-guess-model">
<h4>Setting the Initial Guess for State and Control using Linear Guess Model<a class="headerlink" href="#setting-the-initial-guess-for-state-and-control-using-linear-guess-model" title="Permalink to this heading">¶</a></h4>
<p>CSALT supports several methods for defining the initial guess for state and control, including several array-based approaches and a file-based approach.</p>
<p>The simplest guess modes are called “LinearUnityControl” and “LinearZeroControl”.  These two modes construct a straight-line guess (in the coordinates of the problem) and set control to all ones in the case of “LinearUnityControl” and all zeros in the case of “LinearZeroControl”.  State guesses are linearly interpolated to the time/independent variable values in the discretization.</p>
<section id="id56">
<h5>C++ Example<a class="headerlink" href="#id56" title="Permalink to this heading">¶</a></h5>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Set the state initial guess to vary linearly from 0.0 to 1.0 for</span>
<span class="c1">// all three state variables.  Control decision variable are</span>
<span class="c1">// all set to 1.0.</span>
<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">initialGuessMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;LinearUnityControl&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">initialGuessState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">finalGuessState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessMode</span><span class="p">(</span><span class="n">initialGuessMode</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateInitialGuess</span><span class="p">(</span><span class="n">initialGuessState</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStateFinalGuess</span><span class="p">(</span><span class="n">finalGuessState</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id57">
<h5>MATLAB Example<a class="headerlink" href="#id57" title="Permalink to this heading">¶</a></h5>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Set the state initial guess to vary linearly from 0.0 to 1.0 for</span><span class="w"></span>
<span class="c">% all three state variables.  Control decision variable are</span><span class="w"></span>
<span class="c">% all set to 0.0.</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">initialGuessMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;LinearNoControl&#39;</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateInitialGuess</span><span class="p">([</span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="w"> </span><span class="mi">0</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>
<span class="n">phase1</span><span class="p">.</span><span class="n">SetStateFinalGuess</span><span class="p">([</span><span class="mf">1.0</span><span class="w"> </span><span class="mf">1.0</span><span class="w"> </span><span class="mf">1.0</span><span class="p">]</span><span class="o">&#39;</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-state-and-control-guesses-in-csalt-c-using-arrays-of-data">
<h4>Setting State and Control Guesses in CSALT C++ using Arrays of Data<a class="headerlink" href="#setting-state-and-control-guesses-in-csalt-c-using-arrays-of-data" title="Permalink to this heading">¶</a></h4>
<p>The CSALT C++ interface allows you to provide arrays of time, state, and control, and CSALT interpolates the guess data to the discretization times of the phase.   The example below shows how to provide guess data arrays for state and control.</p>
<section id="id58">
<h5>C++ Example<a class="headerlink" href="#id58" title="Permalink to this heading">¶</a></h5>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">Rvector</span><span class="w"> </span><span class="nf">timeArray</span><span class="p">(</span><span class="mi">11</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mi">0</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.021836090339203817</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.065059858061501996</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.11298609481175435</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.14731810202287049</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.15624006535589879</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.1780761556951026</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.22129992341740082</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.26922616016765311</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.30355816737876928</span><span class="p">,</span><span class="w"></span>
<span class="w">    </span><span class="mf">0.31248013071179759</span><span class="p">);</span><span class="w"></span>

<span class="n">Rmatrix</span><span class="w"> </span><span class="n">stateArray</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="mi">11</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">);</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.000558</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.0076368</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.70114</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.014536</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.065705</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-2.0561</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.072888</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.1842</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-3.4429</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.15442</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.2898</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-4.3183</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.18169</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.31831</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-4.5258</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.25921</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.38763</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-4.9943</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.4556</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.51198</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-5.7398</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.72747</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.607</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-6.2497</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.94294</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.63533</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-6.394</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">1</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.63662</span><span class="p">;</span><span class="w"></span>
<span class="n">stateArray</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-6.4004</span><span class="p">;</span><span class="w"></span>

<span class="n">Rmatrix</span><span class="w"> </span><span class="n">controlArray</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="mi">11</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.10977</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.32704</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.56797</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.74055</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.78541</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-0.89516</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.1125</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.3534</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">9</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.5259</span><span class="p">;</span><span class="w"></span>
<span class="n">controlArray</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.5705</span><span class="p">;</span><span class="w"></span>

<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">initialGuessMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;GuessArrays&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessMode</span><span class="p">(</span><span class="n">initialGuessMode</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessArrays</span><span class="p">(</span><span class="n">timeArray</span><span class="p">,</span><span class="w"> </span><span class="n">stateArray</span><span class="p">,</span><span class="w"> </span><span class="n">controlArray</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-state-and-control-guesses-in-csalt-c-using-an-och-guess-file">
<h4>Setting State and Control Guesses in CSALT C++ using an OCH Guess File<a class="headerlink" href="#setting-state-and-control-guesses-in-csalt-c-using-an-och-guess-file" title="Permalink to this heading">¶</a></h4>
<p>The CSALT C++ version supports file-based guesses for state and control using an Optimal Control History (OCH) file. The OCH file specification is located in the OptimalControlGuess documentation.  The example below shows how to configure CSALT C++ to use an OCH file for the source of data for state and control guesses.</p>
<section id="id59">
<h5>C++ Example<a class="headerlink" href="#id59" title="Permalink to this heading">¶</a></h5>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Create a phase and set the guess mode to OCHFile</span>
<span class="n">RadauPhase</span><span class="w"> </span><span class="o">*</span><span class="n">phase1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">RadauPhase</span><span class="p">();</span><span class="w"></span>
<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">initialGuessMode</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;OCHFile&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetInitialGuessMode</span><span class="p">(</span><span class="n">initialGuessMode</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Define the path to the guess file</span>
<span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">initialGuessFile</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s">&quot;PATH_TO_GUESS_FILE/GUESS_FILE_NAME.och&quot;</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetGuessFileName</span><span class="p">(</span><span class="n">initialGuessFile</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-state-and-control-guesses-in-csalt-matlab-using-a-matlab-function">
<h4>Setting State and Control Guesses in CSALT MATLAB using a MATLAB Function<a class="headerlink" href="#setting-state-and-control-guesses-in-csalt-matlab-using-a-matlab-function" title="Permalink to this heading">¶</a></h4>
<p>The CSALT MATLAB interface supports a function-based guess source.   You can define a MATLAB function that provides state and control guess data.  The example below shows how to configure a function-based guess in CSALT MATLAB, and the function prototype for MATLAB guess functions.</p>
<section id="id60">
<h5>MATLAB Example<a class="headerlink" href="#id60" title="Permalink to this heading">¶</a></h5>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Set the guess mode to UserGuessFunction for a Phase named thePhase</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">initialGuessMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;UserGuessFunction&#39;</span><span class="p">;</span><span class="w"></span>

<span class="c">% Set the name of the guess function on the Trajectory</span><span class="w"></span>
<span class="n">theTrajectory</span><span class="p">.</span><span class="n">guessFunctionName</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;MyGuessFunction&#39;</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
<p>A MATLAB guess function must return arrays of state and control guess data interpolated to the phase discretization points.  Inputs to a guess function are:</p>
<ul class="simple">
<li><p>A vector of discretization times/independent variable for phase.</p></li>
<li><p>An array of integers describing the discretization point types.   1 means a discretization point needs state guess only, 2 means a discretization point needs state and control guess data.</p></li>
<li><p>A handle to the phase Object for obtaining phase information such as number of states and controls for the phase.</p></li>
</ul>
<p>Outputs from a MATLAB guess function are:</p>
<ul class="simple">
<li><p>State guess array with guesses for state as rows in the array.</p></li>
<li><p>Control guess array with guesses for control as rows in the array.</p></li>
</ul>
</section>
<section id="matlab-function-prototype-for-guess-functions">
<h5>MATLAB Function Prototype for Guess Functions<a class="headerlink" href="#matlab-function-prototype-for-guess-functions" title="Permalink to this heading">¶</a></h5>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="k">function</span><span class="w"> </span>[stateGuess,controlGuess]<span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nf">MyGuessFunc</span><span class="p">(</span>timeVector,timeVectorType,phaseObj<span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-the-initial-guess-for-static-variables">
<h4>Setting the Initial Guess for Static Variables<a class="headerlink" href="#setting-the-initial-guess-for-static-variables" title="Permalink to this heading">¶</a></h4>
<p>Static variables are defined on a per-phase basis and a phase need not have static variables. The example below shows how to set the guess for static variables.</p>
<section id="id61">
<h5>C++ Example<a class="headerlink" href="#id61" title="Permalink to this heading">¶</a></h5>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Configure guess for three static variables set to 1.0, 2.0, and</span>
<span class="c1">// 3.0 respectively.</span>
<span class="n">Integer</span><span class="w"> </span><span class="n">numStaticVars</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">3</span><span class="p">;</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetNumStaticVars</span><span class="p">(</span><span class="n">numStaticVars</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">staticGuess</span><span class="p">(</span><span class="n">numStaticVars</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">2.0</span><span class="p">,</span><span class="w"> </span><span class="mf">3.0</span><span class="p">);</span><span class="w"></span>
<span class="n">phase1</span><span class="o">-&gt;</span><span class="n">SetStaticGuess</span><span class="p">(</span><span class="n">staticGuess</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id62">
<h5>MATLAB Example<a class="headerlink" href="#id62" title="Permalink to this heading">¶</a></h5>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The MATLAB version of CSALT does not support static variables.</p>
</div>
</section>
</section>
</section>
<section id="setting-the-dynamics-configuration-in-gmat">
<h3>Setting the Dynamics Configuration in GMAT<a class="headerlink" href="#setting-the-dynamics-configuration-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>Phases in GMAT use a DynamicsConfiguration Resource to define the Spacecraft,  Force Model, and propulsion model for the phase.  To configure the phase dynamics you define the DynamicsConfiguration for a Phase, and the ThrustMode.  The example below shows how to configure that data first by discussing the fields on the Phase Resource, and then on how to set up a DynamicsConfiguration Resource including the Spacecraft, Force Model, and propulsion model.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a phase.  Set it to</span><span class="w"></span>
<span class="c">% use the DeepSpaceDynConfig DynamicsConfiguration.</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Phase</span><span class="w"> </span><span class="s">thePhase</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">DynamicsConfiguration</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">DeepSpaceDynConfig</span><span class="w"></span>

<span class="c">% Set the Phase to a Coast.  (thrust is not applied)</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ThrustMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Coast</span><span class="w"></span>

<span class="c">% Alternatively, set the Phase to a Thrust.  (thrust is applied)</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">ThrustMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Thrust</span><span class="w"></span>
</pre></div>
</div>
<p>The example above assumes a DynamicsConfiguration named DeepSpaceDynConfig is configured.  Below we illustrate how to set up a that Resource.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a chemical tank</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ChemicalTank</span><span class="w"> </span><span class="s">ChemicalTank1</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">AllowNegativeFuelMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">false</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">FuelMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">4150</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Pressure</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1500</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Temperature</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">20</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">RefTemperature</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">20</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Volume</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">3.5</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">FuelDensity</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1260</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">PressureModel</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">PressureRegulated</span><span class="p">;</span><span class="w"></span>

<span class="c">% Create a Spacecraft</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">mySat</span><span class="w"></span>
<span class="n">mySat</span><span class="p">.</span><span class="n">Tanks</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemicalTank1</span><span class="p">};</span><span class="w"></span>

<span class="c">% Create an orbit dynamics model with Earth, Sun, Moon point mass and SRP</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ForceModel</span><span class="w"> </span><span class="s">DeepSpaceForceModel</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.CentralBody</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Sun</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.PointMasses</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">Sun</span><span class="p">,</span><span class="n">Earth</span><span class="p">,</span><span class="n">Luna</span><span class="p">};</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.Drag</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">None</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.SRP</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">On</span><span class="p">;</span><span class="w"></span>

<span class="c">% Create a spacecraft thrust model</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ThrustModel</span><span class="p">;</span><span class="w"></span>
<span class="n">ThrustModel</span><span class="p">.</span><span class="n">SpacecraftFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">FixedThrustAndIsp_Model1</span><span class="p">.</span><span class="n">emtg_spacecraftopt</span><span class="w"></span>
<span class="n">ThrustModel</span><span class="p">.</span><span class="n">DutyCycle</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>

<span class="c">% Create a DynamicsConfiguration and configure the Spacecraft,</span><span class="w"></span>
<span class="c">% ForceModel, and Thrust model.</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">DeepSpaceDynConfig</span><span class="w"></span>
<span class="n">DeepSpaceDynConfig</span><span class="p">.</span><span class="n">ForceModels</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">DeepSpaceForceModel</span><span class="p">};</span><span class="w"></span>
<span class="n">DeepSpaceDynConfig</span><span class="p">.</span><span class="n">Spacecraft</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">mySat</span><span class="p">}</span><span class="w"></span>
<span class="n">DeepSpaceDynConfig</span><span class="p">.</span><span class="n">FiniteBurns</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ThrustModel</span><span class="p">}</span><span class="w"></span>
<span class="n">DeepSpaceDynConfig</span><span class="p">.</span><span class="n">EMTGTankConfig</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemicalTank1</span><span class="p">};</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-phase-graphics-configuration-in-gmat">
<h3>Setting Phase Graphics Configuration in GMAT<a class="headerlink" href="#setting-phase-graphics-configuration-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>The Phase Resource supports options that can optionally override a spacecraft’s default graphics configuration so that different phases are displayed using different colors in graphics windows, even if the phases use the same spacecraft.   There are two fields, one to optionally override the spacecraft’s color settings, and the other to specify the desired color for the phase (which is not used if OverrideColorInGraphics = false) .  The example below shows how to set the Phase to be drawn using green in the graphics windows.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">thePhase</span><span class="p">.</span><span class="n">OverrideColorInGraphics</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">true</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">OrbitColor</span><span class="w"> </span><span class="p">=</span><span class="w"> </span>‘<span class="n">Green</span>’<span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-phase-cost-and-constraint-functions-in-gmat">
<h3>Setting Phase Cost and Constraint Functions in GMAT<a class="headerlink" href="#setting-phase-cost-and-constraint-functions-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>The Phase Resource has fields to set cost and constraint functions implemented in GMAT.  These fields are documented in the BoundaryFunctions section to keep documentation on setting cost and constraints in a single location.   The fields to set “built-in” cost and constraint functions are called BuiltInCost and BuiltInBoundaryConstraints.  See the Boundary Functions Chapter for more details on supported built-in cost and constraint functions.</p>
</section>
</section>
<section id="dynamics-configuration-reference">
<h2>Dynamics Configuration Reference<a class="headerlink" href="#dynamics-configuration-reference" title="Permalink to this heading">¶</a></h2>
<p>The DynamicsConfiguration resource is used to set the natural and artificial forces acting on spacecraft. Each Phase of a CSALT Trajectory has one DynamicsConfiguration. In a GMAT script, the dynamics configuration for a phase is created and set using:</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">ExampleDynamicsConfiguration</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleDynamicsConfiguration</span><span class="o">&gt;</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Phase</span><span class="w"> </span><span class="s">ExamplePhase</span><span class="w"></span>
<span class="n">ExamplePhase</span><span class="p">.</span><span class="n">DynamicsConfiguration</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">ExampleDynamicsConfiguration</span><span class="w"></span>
</pre></div>
</div>
<section id="setting-the-force-model">
<h3>Setting the Force Model<a class="headerlink" href="#setting-the-force-model" title="Permalink to this heading">¶</a></h3>
<p>A DynamicsConfiguration resource requires a list of ForceModel resources, which describe the non-control forces acting on the spacecraft. <strong>Important note: Currently, CSALT only supports a single spacecraft, so the length of the list must be one.</strong> In a GMAT script, the ForceModel resource is set using the following script block.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">ForceModel</span><span class="w"> </span><span class="s">ExampleForceModel1</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleForceModel1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">ExampleDynamicsConfiguration</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">ForceModels</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleForceModel1</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-spacecraft">
<h3>Setting the Spacecraft<a class="headerlink" href="#setting-the-spacecraft" title="Permalink to this heading">¶</a></h3>
<p>A DynamicsConfiguration resource requires a list of Spacecraft resources, which describe the parameters of the modeled spacecraft. <strong>Important note: Currently, CSALT only supports a single spacecraft, so the length of the list must be one.</strong> In a GMAT script, the Spacecraft resource is set using the following script block.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">ExampleSpacecraft1</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleSpacecraft1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">ExampleDynamicsConfiguration</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">Spacecraft</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleSpacecraft1</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-finite-burn">
<h3>Setting the Finite Burn<a class="headerlink" href="#setting-the-finite-burn" title="Permalink to this heading">¶</a></h3>
<p>A DynamicsConfiguration resource <strong>that is used by a Thrust phase</strong> requires a list of FiniteBurn resources, which describe the control parameters of the modeled spacecraft. If a DynamicsConfiguration resource is only supplied to phases whose ThrustMode = Coast (via the phases’ DynamicsConfiguration fields), then the user does not need to set the FiniteBurns field for that particular DynamicsConfiguration resource.</p>
<p><strong>Important note: Currently, all FiniteBurn resources for a CSALT DynamicsConfiguration must be EMTGSpacecraft resources. GMAT FiniteBurn and ImpulsiveBurn resources are not valid FiniteBurn resources for a CSALT DynamicsConfiguration.</strong></p>
<p><strong>Important note: Currently, CSALT only supports a single spacecraft, so the length of the list must be one.</strong></p>
<p>In a GMAT script, the FiniteBurn resource is set using the following script block.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleEMTGSpacecraft1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">ExampleDynamicsConfiguration</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">FiniteBurns</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleEMTGSpacecraft1</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-emtg-tank-configuration">
<h3>Setting the EMTG Tank Configuration<a class="headerlink" href="#setting-the-emtg-tank-configuration" title="Permalink to this heading">¶</a></h3>
<p>If a DynamicsConfiguration resource contains at least one FiniteBurn, at least one tank must be added to the EMTG tank configuration.  Multiple tanks can be used; the fuel will be depleted in the order the tanks are listed in this field. If the fuel tank object does not allow negative masses, this will be changed internally to be allowed so that the optimizer can test any mass values as it attempts to reach convergence.  New point functions and bounds are created automatically to attempt to only use the amount of fuel available.  These point functions can be viewed at the end of a run in the GMAT Optimal Control solution report.</p>
<p>Additionally, he mass of the Spacecraft resource and the mass used in the optimal control problem are related. The sum of the dry mass of the Spacecraft set on a Phase’s DynamicsConfiguration field and the starting fuel masses of all FuelTank resources attached to that Spacecraft must be equal to that Phase’s StateUpperBound element corresponding to mass, or else an error is thrown.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The chemical tank must be attached to the spacecraft being used.</p>
</div>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">ExampleSpacecraft1</span><span class="w"></span>
<span class="n">ExampleSpacecraft1</span><span class="p">.</span><span class="n">Tanks</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemicalTank1</span><span class="p">};</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleSpacecraft1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">ChemicalTank</span><span class="w"> </span><span class="s">ExampleChemTank1</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleChemTank1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
<span class="o">&lt;</span><span class="n">Set</span><span class="w"> </span><span class="n">up</span><span class="w"> </span><span class="n">ExampleEMTGSpacecraft1</span><span class="o">&gt;</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">ExampleDynamicsConfiguration</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">Spacecraft</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleSpacecraft1</span><span class="p">}</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">FiniteBurns</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleEMTGSpacecraft1</span><span class="p">}</span><span class="w"></span>
<span class="n">ExampleDynamicsConfiguration</span><span class="p">.</span><span class="n">EMTGTankConfig</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ExampleChemTank1</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="optimal-control-guess-reference">
<h2>Optimal Control Guess Reference<a class="headerlink" href="#optimal-control-guess-reference" title="Permalink to this heading">¶</a></h2>
<p>A collocation optimizer like CSALT requires an initial guess in order to execute. The guess must contain values of the state and control at all times between the initial time and the final time at which the dynamics are evaluated.</p>
<p>In GMAT, the OptimalControlGuess resource is used to provide a guess for the trajectory. The Type field sets how the guess is set. If Type is set to CollocationGuessFile, then the guess is obtained from a user-specified .och-formatted file. In this case, the file name is provided by setting the field FileName. If the time at which states and controls are required is between two times that are present in the guess file, then CSALT internally performs interpolation to obtain state and control values at the intermediate time.</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The guess file must have a minimum of five rows of data. If fewer than five rows are provided, an exception will occur because the polynomial interpolation used to obtain state and control values at intermediate times fails.</p>
</div>
<p>An example of creating an OptimalControlGuess GMAT resource and setting the guess resource to use a specified .och file for the initial guess is given below.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlGuess</span><span class="w"> </span><span class="s">trajectoryGuess</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">CollocationGuessFile</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">FileName</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">.</span><span class="o">./</span><span class="n">data</span><span class="o">/</span><span class="n">misc</span><span class="o">/</span><span class="n">GuessWithUnityControl</span><span class="p">.</span><span class="n">och</span><span class="w"></span>
</pre></div>
</div>
<p>Alternatively, Type may be set to GMATArray. As the name suggests, in this case, the guess is set my placing guess values in a GMAT Array and passing the array to the OptimalControlGuess resource. The input Array must be organized such that the first column is the independent variable (i.e., time), the next set of columns holds the states, and the final set of columns holds the controls. For example, a typical spacecraft trajectory optimization problem might have a guess array with 11 columns: [Time, Rx, Ry, Rz, Vx, Vy, Vz, Mass, Ux, Uy, Uz], where R is the position vector, V is the velocity vector, and U is the control vector. The subscripts x, y, z indicate vector components. (In fact, this 11-column format is currently required in GMAT.) Each row of the array corresponds to a different instant in time. (The Time column must increase monotonically as the row number increases.)</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Like the guess file, the guess array must have a minimum of five rows of data. If fewer than five rows are provided, an exception will occur because the polynomial interpolation used to obtain state and control values at intermediate times fails.</p>
</div>
<p>The following presents a simple example.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">Array</span><span class="w"> </span><span class="s">guess[200,11]</span><span class="w"></span>
<span class="n">guess</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">0</span><span class="w"></span>
<span class="n">guess</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">5.5</span><span class="w"></span>
<span class="c">% … continuing setting the rest of the elements in guess</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">CoordinateSystem</span><span class="w"> </span><span class="s">EarthICRFExample</span><span class="w"></span>
<span class="n">EarthICRFExample</span><span class="p">.</span><span class="n">Origin</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Earth</span><span class="w"></span>
<span class="n">EarthICRFExample</span><span class="p">.</span><span class="n">Axes</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">ICRF</span><span class="w"></span>

<span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlGuess</span><span class="w"> </span><span class="s">trajectoryGuess</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">GMATArray</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">TimeSystem</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">TDBModJulian</span><span class="w"></span>
<span class="n">trajectoryGuess</span><span class="p">.</span><span class="n">CoordinateSystem</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">EarthICRFExample</span><span class="w"></span>
</pre></div>
</div>
<p>As shown in the above example, when the OptimalControlGuess Type is set to GMATArray, the field TimeSystem is used to specify the time system in which the guess is specified, and the field CoordinateSystem is used to specify the coordinate system in which the guess is specified. TimeSystem must be a GMAT-supported modified-Julian-date time system, and CoordinateSystem must be an existing CoordinateSystem resource. TimeSystem and CoordinateSystem are not used if the OptimalControlGuess Type is set to CollocationGuessFile. In that case, the time system and coordinate system are read directly from the collocation guess file meta data.</p>
</section>
<section id="emtg-spacecraft-reference">
<span id="sec-gmatoc-emtgspacecraftreference"></span><h2>EMTG Spacecraft Reference<a class="headerlink" href="#emtg-spacecraft-reference" title="Permalink to this heading">¶</a></h2>
<p>The Evolutionary Mission Trajectory Generator (EMTG) is software developed at GSFC to optimize interplanetary space mission trajectories. CSALT uses an EMTG spacecraft file to define the capabilities of an in-space propulsion system.</p>
<section id="creating-an-emtg-spacecraft-resource">
<h3>Creating an EMTG Spacecraft Resource<a class="headerlink" href="#creating-an-emtg-spacecraft-resource" title="Permalink to this heading">¶</a></h3>
<p>In a GMAT script, an EMTGSpacecraft resource is created using:</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-emtg-spacecraft-file">
<h3>Setting the EMTG Spacecraft File<a class="headerlink" href="#setting-the-emtg-spacecraft-file" title="Permalink to this heading">¶</a></h3>
<p>EMTG spacecraft files are text files, typically with the extension emtg_spacecraftopt. The following GMAT script excerpt sets the EMTG spacecraft file to be used.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
<span class="n">ExampleEMTGSpacecraft1</span><span class="p">.</span><span class="n">SpacecraftFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">ExampleEMTGSpacecraftFile</span><span class="p">.</span><span class="n">emtg_spacecraft</span><span class="w"></span>
</pre></div>
</div>
<p>The SpacecraftFile field may be an absolute path or a path relative to gmat/data/emtg/.</p>
</section>
<section id="setting-the-emtg-spacecraft-stage">
<h3>Setting the EMTG Spacecraft Stage<a class="headerlink" href="#setting-the-emtg-spacecraft-stage" title="Permalink to this heading">¶</a></h3>
<p>A single EMTG spacecraft file may contain multiple spacecraft stages. Each stage has unique properties, such as its power system, its propulsion system, its dry mass, etc. A unique stage is set for each phase of a CSALT trajectory by providing EMTGSpacecraftStage with an integer array, as shown in the following example. The GMAT script excerpt sets the CSALT trajectory to use EMTG spacecraft stage 2 for the first phase, stage 3 for the second phase, and stage 1 for all subsequent phases. (EMTG spacecraft stage numbering is one-indexed and is the order in which the stage blocks appear in the EMTG spacecraft file.) Note that coast phases, in addition to thrust phases, are considered in this counting. In other words, if, in the below example, the first phase were a coast phase, then stage 2 would not be used. Stage 3 would still be used for the second phase, and stage 1 would still be used for the third phase and all subsequent phases.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
<span class="n">ExampleEMTGSpacecraft1</span><span class="p">.</span><span class="n">SpacecraftStage</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-the-propulsion-system-duty-cycle">
<h3>Setting the Propulsion System Duty Cycle<a class="headerlink" href="#setting-the-propulsion-system-duty-cycle" title="Permalink to this heading">¶</a></h3>
<p>CSALT approximates a duty cycle by multiplying the calculated thrust magnitude by a constant factor between 0 and 1, inclusive. A value of 1 effectively means that no duty cycle is applied, and a value of 0 means that there is no thrust. The GMAT script excerpt below sets the duty cycle for an EMTGSpacecraft.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">ExampleEMTGSpacecraft1</span><span class="w"></span>
<span class="n">ExampleEMTGSpacecraft1</span><span class="p">.</span><span class="n">DutyCycle</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">0.9</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="format-of-emtg-spacecraft-file">
<span id="sec-gmatoc-formatofemtgspacecraftfile"></span><h3>Format of EMTG Spacecraft File<a class="headerlink" href="#format-of-emtg-spacecraft-file" title="Permalink to this heading">¶</a></h3>
<p>An EMTG spacecraft file is a text file of data, typically with the extension emtg_spacecraft. The file consists of space-delimited lines of data. Lines of text that begin with a “#” are comment lines.</p>
<p>The file is separated into two types of “blocks”:</p>
<ol class="arabic simple">
<li><p>Spacecraft block</p></li>
<li><p>One or more stage blocks</p></li>
</ol>
<p>Each EMTG spacecraft file contains one spacecraft block, which contains “global” data that applies to all stage blocks. On the other hand, a single EMTG spacecraft file may contain multiple stage blocks. Each stage block defines a spacecraft stage (i.e., configuration).</p>
<p>Each stage block, in addition to containing “unblocked” data, contains a power library block and a propulsion library block. All data in a stage block is independent of all data in all other stage blocks. Thus, a file containing two spacecraft stages is organized as:</p>
<ul class="simple">
<li><p>Spacecraft block data</p></li>
<li><p>Stage 1 block</p>
<ul>
<li><p>Unblocked data</p></li>
<li><p>Power library block data</p></li>
<li><p>Propulsion library block data</p></li>
</ul>
</li>
<li><p>Stage 2 block</p>
<ul>
<li><p>Unblocked data</p></li>
<li><p>Power library block data</p></li>
<li><p>Propulsion library block data</p></li>
</ul>
</li>
</ul>
<p>The data in each block, including format, is described in <a class="reference internal" href="csalt-EMTGSpacecraftFileSpec-smallTables.html#emtgspacecraftfilespec-label"><span class="std std-ref">EMTG Spacecraft File Specification</span></a>. In addition, when creating an EMTG Spacecraft file, it will likely be beneficial to consult the example .emtg_spacecraftopt files provided in gmat/data/emtg/.</p>
</section>
<section id="throttle-tables">
<h3>Throttle Tables<a class="headerlink" href="#throttle-tables" title="Permalink to this heading">¶</a></h3>
<p>One method by which a propulsion model may be set in an EMTG spacecraft file is by referencing an external throttle table file. A throttle table file is a comma-delimited text file of data, typically with extension ThrottleTable. The throttle table file describes discrete settings that the propulsion system may take, as well as best-fit polynomial coefficients of the discrete settings.</p>
<section id="format-of-throttle-table-file">
<h4>Format of Throttle Table File<a class="headerlink" href="#format-of-throttle-table-file" title="Permalink to this heading">¶</a></h4>
<p>A throttle table file is a comma-delimited file in which lines that begin with “#” are comment lines. An example of a populated throttle table file, including instructions of how to populate it, is given in a GMAT installation in gmat/data/emtg/NEXT_TT11_Discovery14_BOL_20190523.ThrottleTable.</p>
</section>
</section>
</section>
<section id="boundary-functions-reference">
<span id="sec-gmatoc-boundaryfunctionsreference"></span><h2>Boundary Functions Reference<a class="headerlink" href="#boundary-functions-reference" title="Permalink to this heading">¶</a></h2>
<p>Boundary functions include algebraic boundary constraints and cost function contributions that are only dependent upon optimization parameters at phase boundaries.</p>
<p>CSALT supports two interfaces to define boundary constraints.  The first interface, the UserPointFunction class, defines the interfaces for algebraic boundary functions, cost functions, and their bounds, but does not support analytic Jacobians.  The OptimalControlFunction utility class is an alternative interface for defining boundary functions that supports analytic Jacobians, and makes it easy to re-use boundary function implementations in different problem configurations.</p>
<p>GMAT supports several interfaces for boundary functions. Those interfaces are discussed below.  Note that GMAT uses the CSALT interfaces to implement boundary functions.  However, that complexity is largely hidden from the user and configuring boundary constraints in GMAT is presented in separate sections below.</p>
<section id="setting-c-boundary-functions-via-the-userpointfunction-class">
<h3>Setting C++ Boundary Functions via the UserPointFunction Class<a class="headerlink" href="#setting-c-boundary-functions-via-the-userpointfunction-class" title="Permalink to this heading">¶</a></h3>
<p>Boundary functions are defined by deriving a new class from the CSALT abstract UserPointFunction base class illustrated in <a class="reference internal" href="#csalt-user-function-diagram"><span class="std std-numref">Fig. 13</span></a>. (Note: Only key functions are illustrated below; see Doxygen output for the full reference.)</p>
<figure class="align-center" id="id82">
<span id="csalt-user-function-diagram"></span><a class="reference internal image-reference" href="../../_images/CSALT_User_Function_Diagram.png"><img alt="../../_images/CSALT_User_Function_Diagram.png" src="../../_images/CSALT_User_Function_Diagram.png" style="width: 513.0px; height: 320.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 13 </span><span class="caption-text">CSALT UserFunction and UserPointFunction classes.</span><a class="headerlink" href="#id82" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>When using those interfaces on the UserPointFunction object, the user must concatenate all boundary functions and bounds and provide all functions in a single vector.  In that case, CSALT uses finite differencing of all Jacobians.  The UserPointFunction class provides Get() and Set() methods that get state data from CSALT to compute algebraic boundary constraints and cost functions.  Functions are set by calling the SetFunctions method and providing the appropriate enumeration for the function type.  To set algebraic functions, use the enumeration ALGEBRAIC.  To set cost functions, use the enumeration COST.  The code snippet below shows how to define the boundary functions for an example problem using Set() and Get() methods provided on UserPointFunction.  For a complete reference, see the Doxygen output for UserFunction and UserPointFunction.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">BrysonDenhamPointObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetInitialStateVector</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetFinalStateVector</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">tInit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetInitialTime</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">tFinal</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetFinalTime</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the cost function passing in COST as the first parameter</span>
<span class="w">    </span><span class="c1">// and the cost function value as the second parameter</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">costFunctions</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">2</span><span class="p">));</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">costFunctions</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Compute the algebraic functions and bounds</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFunctions</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">0</span><span class="p">),</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span><span class="w"> </span><span class="n">stateInit</span><span class="p">(</span><span class="mi">2</span><span class="p">),</span><span class="w"></span>
<span class="w">                            </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">0</span><span class="p">),</span><span class="w"> </span><span class="n">stateFinal</span><span class="p">(</span><span class="mi">1</span><span class="p">),</span><span class="w"> </span><span class="n">tInit</span><span class="p">,</span><span class="w"> </span><span class="n">tFinal</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFuncLower</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFuncUpper</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1.0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the algebraic functions with ALGEBRAIC as the first parameter</span>
<span class="w">    </span><span class="c1">// and the function values as the second parameter</span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">algFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">LOWER</span><span class="p">,</span><span class="w"> </span><span class="n">algFuncLower</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">UPPER</span><span class="p">,</span><span class="w"> </span><span class="n">algFuncUpper</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-c-boundary-functions-via-the-optimalcontrolfunction-class">
<h3>Setting C++ Boundary Functions via the OptimalControlFunction Class<a class="headerlink" href="#setting-c-boundary-functions-via-the-optimalcontrolfunction-class" title="Permalink to this heading">¶</a></h3>
<p>CSALT provides an interface to set boundary functions one at a time by providing an std vector of OptimalControlFunction objects.  The OptimalControlFunction class defines the generic interface for granularly creating and providing to CSALT boundary functions. By using the OptimalControlFunction class, the user does not need to manage the concatenation of large numbers of boundary functions and the careful bookkeeping required to construct the overall NLP Jacobian from the Jacobian of individual functions.  The ability to provide functions at a granular level is particularly important for large problems that often have hundreds of  boundary functions and complicated dependencies. Additionally, using the OptimalControlFunction class allows the user to easily re-use an optimal control function created for one problem for a different problem configuration.</p>
<p>The class diagram for OptimalControlFunction with key data and methods is shown in <a class="reference internal" href="#csalt-optimal-control-function-diagram"><span class="std std-numref">Fig. 14</span></a>. (See doxygen output for complete, up-to-date reference material.)</p>
<figure class="align-center" id="id83">
<span id="csalt-optimal-control-function-diagram"></span><a class="reference internal image-reference" href="../../_images/CSALT_Optimal_Control_Function_Diagram.png"><img alt="../../_images/CSALT_Optimal_Control_Function_Diagram.png" src="../../_images/CSALT_Optimal_Control_Function_Diagram.png" style="width: 368.0px; height: 433.0px;" /></a>
<figcaption>
<p><span class="caption-number">Fig. 14 </span><span class="caption-text">CSALT OptimalControlFunction class.</span><a class="headerlink" href="#id83" title="Permalink to this image">¶</a></p>
</figcaption>
</figure>
<p>To configure a problem to use constraints created using the OptimalControlFunction class, derive a new class from the OptimalControlFunction base class and implement the EvaluateFunctions() method to compute the function values.  The example below shows how to configure the boundary functions for the Brachistichrone optimization problem. The class BranchBoundConstraint is derived from OptimalControlFunction.  When data is accessed to form function values, the first index represents which discretization point in the function the data vector is taken from, while the second index represents which element of that vector to use.  For example, stateData[0][1] is the second element in the state vector at the first discretization point.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">Rvector</span><span class="w"> </span><span class="nf">BrachBoundConstraint::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algF</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">2</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">2</span><span class="p">];</span><span class="w"></span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">algF</span><span class="p">;</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Optimal control functions require information on the phases involved, the point type (initial, final, all), and the state dependencies. The values are typically configured in the problem driver, or, when integrated with GMAT, GMAT sets these values based on the user’s scripted configuration.  The code below illustrates how to configure the Brachistichrone bounds functions using an OptimalControlFunction object; the class BranchBoundConstraint is derived from OptimalControlFunction.  Note that multiple OptimalControlFunction objects can be added to a UserPointFunction object (which is then added to a Trajectory) through the vector sent to the AddFunctions() method seen at the end of this example.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// This function is dependent on the start of phase 0 and end of phase 0.</span>
<span class="n">IntegerArray</span><span class="w"> </span><span class="nf">phaseDependencies</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="c1">// 0 for start, 1 for end, 2 for all points (i.e. path constraint)</span>
<span class="n">IntegerArray</span><span class="w"> </span><span class="nf">pointDependencies</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Define the functional dependencies</span>
<span class="n">Real</span><span class="w"> </span><span class="n">numPhases</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">numFunctions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">8</span><span class="p">;</span><span class="w"></span>
<span class="n">BooleanArray</span><span class="w"> </span><span class="nf">stateDepMap</span><span class="p">();</span><span class="w"> </span><span class="c1">// Dependent upon state at both points</span>
<span class="n">stateDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"></span>
<span class="n">stateDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"></span>
<span class="n">BooleanArray</span><span class="w"> </span><span class="nf">controlDepMap</span><span class="p">();</span><span class="w"> </span><span class="c1">// No control dependency</span>
<span class="n">controlDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span><span class="w"></span>
<span class="n">controlDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span><span class="w"></span>
<span class="n">BooleanArray</span><span class="w"> </span><span class="nf">timeDepMap</span><span class="p">();</span><span class="w"> </span><span class="c1">// Dependent upon time at both points</span>
<span class="n">timeDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"></span>
<span class="n">timeDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">true</span><span class="p">);</span><span class="w"></span>
<span class="n">BooleanArray</span><span class="w"> </span><span class="nf">paramDepMap</span><span class="p">();</span><span class="w"> </span><span class="c1">// No static params dependency</span>
<span class="n">paramDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span><span class="w"></span>
<span class="n">paramDepMap</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="nb">false</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Create the function object and initialize</span>
<span class="n">OptimalControlFunction</span><span class="o">*</span><span class="w"> </span><span class="n">brachFunc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachBoundConstraint</span><span class="p">(</span><span class="s">&quot;BrachBound&quot;</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">Initialize</span><span class="p">();</span><span class="w"></span>

<span class="c1">// Set the various dependencies</span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetNumPhases</span><span class="p">(</span><span class="n">numPhases</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetNumFunctions</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetPhaseDependencies</span><span class="p">(</span><span class="n">phaseDependencies</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetPointDependencies</span><span class="p">(</span><span class="n">pointDependencies</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetStateDepMap</span><span class="p">(</span><span class="n">stateDepMap</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetControlDepMap</span><span class="p">(</span><span class="n">controlDepMap</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetTimeDepMap</span><span class="p">(</span><span class="n">timeDepMap</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetParamDepMap</span><span class="p">(</span><span class="n">paramDepMap</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Create and set the function bounds</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">lowerBounds</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="w">  </span><span class="mf">0.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">,</span><span class="mf">-10.0</span><span class="p">,</span><span class="mf">-10.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">upperBounds</span><span class="p">(</span><span class="mi">8</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">100.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">0.0</span><span class="p">,</span><span class="mf">1.0</span><span class="p">,</span><span class="w"> </span><span class="mf">10.0</span><span class="p">,</span><span class="w">  </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>

<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetUpperBounds</span><span class="p">(</span><span class="n">upperBounds</span><span class="p">);</span><span class="w"></span>
<span class="n">brachFunc</span><span class="o">-&gt;</span><span class="n">SetLowerBounds</span><span class="p">(</span><span class="n">lowerBounds</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Add the new function object to the list of functions</span>
<span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">OptimalControlFunction</span><span class="o">*&gt;</span><span class="w"> </span><span class="n">funcList</span><span class="p">;</span><span class="w"></span>
<span class="n">funcList</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="n">brachFunc</span><span class="p">);</span><span class="w"></span>

<span class="n">pointObject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">BrachistichronePointObject</span><span class="p">();</span><span class="w"> </span><span class="c1">// derived from UserPointFunction</span>
<span class="n">pointObject</span><span class="o">-&gt;</span><span class="n">AddFunctions</span><span class="p">(</span><span class="n">functionList</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>Analytic functions and Jacobians are set up as part of the CSALT initialization process, and then used during optimization to evaluate the supplied functions and derivatives.</p>
<ul class="simple">
<li><p><strong>Custom Function Bounds:</strong>  A user’s OptimalControlFunction object may allow or require custom bounds. Custom bounds are set through the SetLowerBounds(Rvector functionLB) and SetUpperBounds(Rvector functionUB) methods on the OptimalControlFunction object during initialization. If the selected OptimalControlFunction does not allow for custom bounds, an error is reported.</p></li>
<li><p><strong>Custom Scaling:</strong> For some problems, custom scaling may improve the optimization process.  Custom scaling is implemented through the SetScaleFactors(Real referenceEpoch, Real timeUnit, Real distUnit, Real velUnit, Real massUnit) method in the AlgebraicFunction-derived class. <a class="footnote-reference brackets" href="#f-csalt-customscaling" id="id63" role="doc-noteref"><span class="fn-bracket">[</span>1<span class="fn-bracket">]</span></a>  When SetScaleFactors() is called, function values and bounds are scaled using user-defined scale factors during optimization.  Note that when custom scaling is used, the reference epoch must be provided as an A1 modified Julian date.</p></li>
</ul>
<p>An example of the use of these two custom function bounds and custom scaling may look like this in a user’s problem driver (note that the dependency setup was removed for simplicity, please refer to the Brachistichrone example above for use of dependencies):</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">OptimalControlFunction</span><span class="o">*</span><span class="w"> </span><span class="n">stateFunc</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">FullState1</span><span class="p">(</span><span class="s">&quot;StateBound&quot;</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Apply custom state bounds</span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">lowerBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mf">7100.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1500.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">10.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1000.0</span><span class="p">);</span><span class="w"></span>
<span class="n">Rvector</span><span class="w"> </span><span class="nf">upperBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mf">7100.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1500.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">10.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">1000.0</span><span class="p">);</span><span class="w"></span>
<span class="n">stateFunc</span><span class="o">-&gt;</span><span class="n">SetLowerBounds</span><span class="p">(</span><span class="n">lowerBounds</span><span class="p">);</span><span class="w"></span>
<span class="n">stateFunc</span><span class="o">-&gt;</span><span class="n">SetUpperBounds</span><span class="p">(</span><span class="n">upperBounds</span><span class="p">);</span><span class="w"></span>

<span class="c1">// Apply scale factors</span>
<span class="n">Real</span><span class="w"> </span><span class="n">refEpoch</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">28437.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">TU</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">DU</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1000.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">VU</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">10.0</span><span class="p">;</span><span class="w"></span>
<span class="n">Real</span><span class="w"> </span><span class="n">MU</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1000.0</span><span class="p">;</span><span class="w"></span>
<span class="n">stateFunc</span><span class="o">-&gt;</span><span class="n">SetScaleFactors</span><span class="p">(</span><span class="n">refEpoch</span><span class="p">,</span><span class="w"> </span><span class="n">TU</span><span class="p">,</span><span class="w"> </span><span class="n">DU</span><span class="p">,</span><span class="w"> </span><span class="n">VU</span><span class="p">,</span><span class="w"> </span><span class="n">MU</span><span class="p">);</span><span class="w"></span>

<span class="n">std</span><span class="o">::</span><span class="n">vector</span><span class="o">&lt;</span><span class="n">OptimalControlFunction</span><span class="o">*&gt;</span><span class="w"> </span><span class="n">funcList</span><span class="p">;</span><span class="w"></span>
<span class="n">funcList</span><span class="p">.</span><span class="n">push_back</span><span class="p">(</span><span class="n">stateFunc</span><span class="p">);</span><span class="w"></span>
<span class="n">FullStatePointObject</span><span class="o">-&gt;</span><span class="n">AddFunctions</span><span class="p">(</span><span class="n">funcList</span><span class="p">);</span><span class="w"> </span><span class="c1">// FullStatePointObject&#39;s class is derived from UserPointFunction</span>
</pre></div>
</div>
</section>
<section id="optimal-control-function-example-c">
<h3>Optimal Control Function Example (C++)<a class="headerlink" href="#optimal-control-function-example-c" title="Permalink to this heading">¶</a></h3>
<p>Below is an example of the BrachistichronePointObject class being converted to use the OptimalControlFunction interface.  Assume the OptimalControlFunction object for this problem is called BrachistichroneBounds.  The constraint bounds originally found in the BrachistichronePointObject class are moved to the constructor of the new BrachistichroneBounds class, while the point functions are moved to the EvaluateFunctions() method in BrachistichroneBounds.</p>
<p>The first code block is the BrachistchronePointObject class derived from the UserPointFunction class.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">BrachistichronePointObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// This method no longer needs any code</span>
<span class="p">}</span><span class="w"></span>

<span class="kt">void</span><span class="w"> </span><span class="nf">BrachistichronePointObject::EvaluateJacobians</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// This method no longer needs any code</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Here is what the BrachistichroneBounds constructor looks like to set up default bounds and dependencies.  Note that these values can be changed by the user through SetLowerBounds(), SetUpperBounds(), and various set dependancy map methods such as SetStateDepMap().  The required default parameters for a class derived from OptimalControlFunction are the number of points; all other parameters can be set by the user outside of the constructor.</p>
<p>The EvaluateFunctions() method is also shown. The first and second points in this problem are the initial point of the phase and the final point of the phase, respectively. (The trajectory is made up of a single phase.)</p>
<p>This code block shows the constructor and EvaluateFunctions() method of the BrachistichroneBounds class derived from the OptimalControlFunction class.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">BrachistichroneBounds</span><span class="o">::</span><span class="n">BrachistichroneBounds</span><span class="p">(</span><span class="n">std</span><span class="o">::</span><span class="n">string</span><span class="w"> </span><span class="n">funcName</span><span class="p">)</span><span class="w"> </span><span class="o">:</span><span class="w"></span>
<span class="w">    </span><span class="n">OptimalControlFunction</span><span class="p">(</span><span class="n">funcName</span><span class="p">)</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">numPoints</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">numFunctions</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">8</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">stateDepMap</span><span class="p">.</span><span class="n">resize</span><span class="p">(</span><span class="n">numPoints</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">controlDepMap</span><span class="p">.</span><span class="n">resize</span><span class="p">(</span><span class="n">numPoints</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">timeDepMap</span><span class="p">.</span><span class="n">resize</span><span class="p">(</span><span class="n">numPoints</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">paramDepMap</span><span class="p">.</span><span class="n">resize</span><span class="p">(</span><span class="n">numPoints</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="n">Integer</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">0</span><span class="p">;</span><span class="w"> </span><span class="n">i</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="n">numPoints</span><span class="p">;</span><span class="w"> </span><span class="o">++</span><span class="n">i</span><span class="p">)</span><span class="w"></span>
<span class="w">    </span><span class="p">{</span><span class="w"></span>
<span class="w">        </span><span class="n">stateDepMap</span><span class="p">.</span><span class="n">at</span><span class="p">(</span><span class="n">i</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">true</span><span class="p">;</span><span class="w"></span>
<span class="w">        </span><span class="n">controlDepMap</span><span class="p">.</span><span class="n">at</span><span class="p">(</span><span class="n">i</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">false</span><span class="p">;</span><span class="w"></span>
<span class="w">        </span><span class="n">timeDepMap</span><span class="p">.</span><span class="n">at</span><span class="p">(</span><span class="n">i</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">true</span><span class="p">;</span><span class="w"></span>
<span class="w">        </span><span class="n">paramDepMap</span><span class="p">.</span><span class="n">at</span><span class="p">(</span><span class="n">i</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">false</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="p">}</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set default bound values</span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">lowerBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-10.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">upperBounds</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0.0</span><span class="p">;</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>

<span class="n">Rvector</span><span class="w"> </span><span class="n">BrachistichroneBounds</span><span class="o">::</span><span class="n">EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="nf">algF</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">timeData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">1</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">0</span><span class="p">][</span><span class="mi">2</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">0</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">1</span><span class="p">];</span><span class="w"></span>
<span class="w">    </span><span class="n">algF</span><span class="p">(</span><span class="mi">7</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateData</span><span class="p">[</span><span class="mi">1</span><span class="p">][</span><span class="mi">2</span><span class="p">];</span><span class="w"></span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="n">algF</span><span class="p">;</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>To provide an analytic Jacobian when using OptimalControlFunction, the method EvaluateAnalyticJacobian() is used. The prototype of the method is:</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="n">OptimalControlFunction</span><span class="o">::</span><span class="n">EvaluateAnalyticJacobian</span><span class="p">(</span><span class="n">VariableType</span><span class="w"> </span><span class="n">varType</span><span class="p">,</span><span class="w"></span>
<span class="w">   </span><span class="n">Integer</span><span class="w"> </span><span class="n">pointIdx</span><span class="p">,</span><span class="w"> </span><span class="kt">bool</span><span class="w"> </span><span class="o">&amp;</span><span class="n">hasAnalyticJac</span><span class="p">,</span><span class="w"> </span><span class="n">Rmatrix</span><span class="w"> </span><span class="o">&amp;</span><span class="n">jacArray</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<p>The user creates an EvaluateAnalyticJacobian() method on their derived OptimalControlFunction class. Within the method, the user calculates and sets the hasAnalyticJac and jacArray arguments based on the values of varType and pointIdx.</p>
<p>The variable varType is an enumeration that describes the variable with respect to which the function is differentiated. The possible values for for varType are [STATE, CONTROL, TIME, STATIC].</p>
<p>The variable pointIdx represents at which discretization point the Jacobian is being calculated. In other words, if pointIdx == <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>, then EvaluateAnalyticJacobian() must set the derivatives of the optimal control function with respect to variables (time, state, control, static parameters) at discretization point <img class="math" src="../../_images/math/5aa339d4daf45a810dda332e3c80a0698e526e04.png" alt="i"/>. Recall that the discretization point dependencies are set by setting the SetNumPhases, SetPhaseDependencies, and SetPointDependencies OptimalControlFunction methods.</p>
<p>The variable hasAnalyticJac is set by the user to true if the user provides an analytic Jacobian and to false if the user does not provide an analytic Jacobian. If hasAnalyticJac is set to false, then finite differencing is used to approximate the Jacobian. Note that hasAnalyticJac may be set differently for different values of varType, as shown in the example below.</p>
<p>The user sets the variable jacArray to the hold the derivatives of the optimal control function with respect to the variable(s) specified by varType:</p>
<div class="math">
<p><img src="../../_images/math/c93ddf9f04fe53e7dcdcb9b2945f751f731d31dc.png" alt="\texttt{jacArray} (i, j) = \frac{\partial \left[ \texttt{function}_i \right]}{\partial \left[ \texttt{variable}_j \right]}"/></p>
</div><p>Note that the indexing for the variable resets when varType is changed but the indexing for function does NOT change when varType is changed. For example, if the optimal control function has three elements, and there is one time variable and seven state variables, then:</p>
<ul class="simple">
<li><p>if varType == TIME, the dimensions of jacArray are <img class="math" src="../../_images/math/56c606675d481360631e52b6c06dbefdbf932b72.png" alt="3 \times 1"/></p></li>
<li><p>if varType == STATE, the dimensions of jacArray are <img class="math" src="../../_images/math/36720b462af106c32ba4a3bf120c64820f19421e.png" alt="3 \times 7"/></p></li>
</ul>
<p>The code below gives an example of an EvaluateAnalyticJacobian method for a full-state linkage constraint for a seven-element state vector. Note that, while this example does not reference any discretization point data, discretization point data may be obtained via the timeData, stateData, etc. class variables, as was done in the BrachBoundConstraint::EvaluateFunctions() example code snippet.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="kt">void</span><span class="w"> </span><span class="nf">FullStateLinkage::EvaluateAnalyticJacobian</span><span class="p">(</span><span class="n">VariableType</span><span class="w"> </span><span class="n">varType</span><span class="p">,</span><span class="w"></span>
<span class="w">   </span><span class="n">Integer</span><span class="w"> </span><span class="n">pointIdx</span><span class="p">,</span><span class="w"></span>
<span class="w">   </span><span class="kt">bool</span><span class="w"> </span><span class="o">&amp;</span><span class="n">hasAnalyticJac</span><span class="p">,</span><span class="w"></span>
<span class="w">   </span><span class="n">Rmatrix</span><span class="w"> </span><span class="o">&amp;</span><span class="n">jacArray</span><span class="p">)</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">   </span><span class="n">ValidatePointIdx</span><span class="p">(</span><span class="n">pointIdx</span><span class="p">);</span><span class="w"> </span><span class="c1">// OptimalControlFunction method to ensure pointIdx is within bounds</span>
<span class="w">   </span><span class="n">hasAnalyticJac</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">false</span><span class="p">;</span><span class="w"> </span><span class="c1">// assume false until proven otherwise</span>

<span class="w">   </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="n">varType</span><span class="p">)</span><span class="w"> </span><span class="c1">// Jacobian calculation differs based on varType</span>
<span class="w">   </span><span class="p">{</span><span class="w"></span>
<span class="w">   </span><span class="k">case</span><span class="w"> </span><span class="no">TIME</span><span class="p">:</span><span class="w"></span>
<span class="w">      </span><span class="n">hasAnalyticJac</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">true</span><span class="p">;</span><span class="w"> </span><span class="c1">// we are calculating analytic Jacobian</span>
<span class="w">      </span><span class="n">jacArray</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"> </span><span class="c1">// one time variable</span>
<span class="w">      </span><span class="c1">// function 0 is time_1 - time_0</span>
<span class="w">      </span><span class="c1">// pointIdx 0 corresponds to time_0</span>
<span class="w">      </span><span class="c1">// pointIdx 1 corresponds to time_1</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">pointIdx</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">      </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">pointIdx</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">      </span><span class="k">break</span><span class="p">;</span><span class="w"></span>
<span class="w">   </span><span class="k">case</span><span class="w"> </span><span class="no">STATE</span><span class="p">:</span><span class="w"></span>
<span class="w">      </span><span class="n">hasAnalyticJac</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">true</span><span class="p">;</span><span class="w"> </span><span class="c1">// we are calculating analytic Jacobian</span>
<span class="w">      </span><span class="n">jacArray</span><span class="p">.</span><span class="n">SetSize</span><span class="p">(</span><span class="n">numFunctions</span><span class="p">,</span><span class="w"> </span><span class="mi">7</span><span class="p">);</span><span class="w"> </span><span class="c1">// seven state variables</span>
<span class="w">      </span><span class="c1">// function j for j = 1, ..., 7 is state_element_j-1 (time_1) - state_element_j-1 (time_0)</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">pointIdx</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"></span>
<span class="w">      </span><span class="p">{</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>

<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>

<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">-1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">      </span><span class="p">}</span><span class="w"></span>
<span class="w">      </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="n">pointIdx</span><span class="w"> </span><span class="o">==</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"></span>
<span class="w">      </span><span class="p">{</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>

<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="mi">4</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span><span class="w"> </span><span class="mi">5</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>

<span class="w">         </span><span class="n">jacArray</span><span class="p">(</span><span class="mi">7</span><span class="p">,</span><span class="w"> </span><span class="mi">6</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">      </span><span class="p">}</span><span class="w"></span>
<span class="w">      </span><span class="k">break</span><span class="p">;</span><span class="w"></span>
<span class="w">   </span><span class="k">default</span><span class="o">:</span><span class="w"> </span><span class="c1">// no dependency on control or static parameters</span>
<span class="w">      </span><span class="n">OptimalControlFunction</span><span class="o">::</span><span class="n">EvaluateAnalyticJacobian</span><span class="p">(</span><span class="n">varType</span><span class="p">,</span><span class="w"> </span><span class="n">pointIdx</span><span class="p">,</span><span class="w"></span>
<span class="w">         </span><span class="n">hasAnalyticJac</span><span class="p">,</span><span class="w"> </span><span class="n">jacArray</span><span class="p">);</span><span class="w"> </span><span class="c1">// simply error-checks and breaks</span>
<span class="w">   </span><span class="p">}</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-boundary-functions-in-gmat">
<h3>Setting Boundary Functions in GMAT<a class="headerlink" href="#setting-boundary-functions-in-gmat" title="Permalink to this heading">¶</a></h3>
<section id="setting-full-state-linkage-constraints">
<h4>Setting Full State Linkage Constraints<a class="headerlink" href="#setting-full-state-linkage-constraints" title="Permalink to this heading">¶</a></h4>
<p>The GMAT CSALT interface supports a constraint function interface that connects phases via full time and state linkage. The script below configures a simple linkage between the two phases named firstPhase and secondPhase.</p>
<section id="id64">
<h5>GMAT Script Example<a class="headerlink" href="#id64" title="Permalink to this heading">¶</a></h5>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">aTrajectory</span><span class="p">.</span><span class="n">AddSimpleLinkageChain</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">firstPhase</span><span class="p">,</span><span class="n">secondPhase</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>The simple linkage constraint computes the linkages by subtracting values of the first phase from the second phase as shown below.</p>
<div class="math">
<p><img src="../../_images/math/93d5952e37c795d75fd0fe058bb3a0ac7c21d4b4.png" alt="\Delta t = \textrm{secondPhase Start A1MJD} - \textrm{firstPhase End A1MJD}"/></p>
</div><p>Similarly, if <img class="math" src="../../_images/math/9c12c9f2a8349505839c288ba65d68e07392ab98.png" alt="v_y"/> is the velocity difference, it is computed as</p>
<div class="math">
<p><img src="../../_images/math/db51704269be22f32abeb81662492acfaf145fbd.png" alt="\Delta v_y = \textrm{secondPhase Start y component} - \textrm{firstPhase End y component}"/></p>
</div><p>Multiple simple linkages can be added as a complete list if desired.  The script below shows four phases being linked together.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">aTrajectory</span><span class="p">.</span><span class="n">AddSimpleLinkageChain</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">firstPhase</span><span class="p">,</span><span class="w"> </span><span class="n">secondPhase</span><span class="p">,</span><span class="w"> </span><span class="n">thirdPhase</span><span class="p">,</span><span class="w"> </span><span class="n">fourthPhase</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
<p>Before returning the function values to CSALT, the linkage constraints are non-dimensionalized.  Time is non-dimensionalized, and the TU is chosen as the smallest TU between the two phases.</p>
<p>The bounds for simple linkages are all zeros for both the upper and lower bounds and do not require non-dimensionalization before providing to CSALT.</p>
</section>
</section>
<section id="setting-custom-linkage-constraints">
<h4>Setting Custom Linkage Constraints<a class="headerlink" href="#setting-custom-linkage-constraints" title="Permalink to this heading">¶</a></h4>
<p>A CustomLinkageConstraint allows customized constraints on time, position, velocity, and/or mass parameters between phases, or between user-provided state data. To define a custom linkage constraint, the user must define the phases involved, the constraint mode (absolute or difference), which quantities to constrain, and bounds on the constrained quantities.  We begin by discussing the constraint modes and then discuss how to define specific constraint quantities.</p>
<section id="difference-mode">
<h5>Difference Mode<a class="headerlink" href="#difference-mode" title="Permalink to this heading">¶</a></h5>
<p>The CustomLinkageConstraint supports two modes set using the ConstraintMode field: Difference and Absolute.  In Difference mode, the constraint function differences values between specified phases and boundary points of the phases (PhaseBoundaryType = Start or End) according to the following equation:</p>
<div class="math">
<p><img src="../../_images/math/7301e59870e714176b9669c2cd118a07913d3e32.png" alt="g = \textrm{FinalPhase.FinalPhaseBoundaryType.Parameter} - \textrm{InitialPhase.InitialPhaseBoundaryType.Parameter}"/></p>
</div><p>The example below illustrates how to constrain the time between the start of a phase called launchPhase and the start of a phase called thrustPhase to be greater than or equal to 30 days and less than or equal to 40 days.  NIf either an upper or lower bound is not necessary, the user does not have to set it, and the system will use the defaults described in <a class="reference internal" href="#sec-gmatoc-definingconstrainttypes"><span class="std std-ref">Defining Constraint Types</span></a>.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Define a custom linkage constraint that constrains time between phase</span><span class="w"></span>
<span class="c">% boundaries be &gt;= 30 days and &lt;= 40 days</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">CustomLinkageConstraint</span><span class="w"> </span><span class="s">aCustomLinkage</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">ConstraintMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;Difference&#39;</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">InitialPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">launchPhase</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">InitialPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Start</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">FinalPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">thrustPhase</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">FinalPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Start</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeLowerBound&#39;</span><span class="p">,</span><span class="s">&#39;ElapsedDays&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">30</span><span class="p">)</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;TimeUpperBound&#39;</span><span class="p">,</span><span class="s">&#39;ElapsedDays&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">40</span><span class="p">)</span><span class="w"></span>

<span class="c">% Add the constraint to the trajectory</span><span class="w"></span>
<span class="n">traj</span><span class="p">.</span><span class="n">CustomLinkages</span><span class="w"> </span><span class="p">=</span><span class="w">  </span><span class="p">{</span><span class="n">aCustomLinkage</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="absolute-mode">
<h5>Absolute Mode<a class="headerlink" href="#absolute-mode" title="Permalink to this heading">¶</a></h5>
<p>When ConstraintMode is set to Absolute, the linkage constraints are computed as absolute parameter quantities on the initial phase.</p>
<div class="math">
<p><img src="../../_images/math/a821cbb177771b10af19ca886ac624385c63be9c.png" alt="g = \textrm{InitialPhase.InitialPhaseBoundaryType.Parameter}"/></p>
</div><p>To further illustrate how constraints are constructed, the example below enforces an equality constraint on the initial time, position, velocity, and mass at the start of  a phase named launchPhase.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">CustomLinkageConstraint</span><span class="w"> </span><span class="s">aCustomLinkage</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">ConstraintMode</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Absolute</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">InitialPhase</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">launchPhase</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">InitialPhaseBoundaryType</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Start</span><span class="p">;</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">              </span><span class="s">&#39;TimeLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="n">UTCGregorian</span><span class="p">,</span><span class="w"> </span><span class="mi">01</span><span class="w"> </span><span class="n">Jan</span><span class="w"> </span><span class="mi">2034</span><span class="w"> </span><span class="mi">12</span><span class="p">:</span><span class="mi">23</span><span class="p">:</span><span class="mf">45.111</span><span class="p">)</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">             </span><span class="s">&#39;TimeUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="n">UTCGregorian</span><span class="p">,</span><span class="w"> </span><span class="mi">01</span><span class="w"> </span><span class="n">Jan</span><span class="w"> </span><span class="mi">2034</span><span class="w"> </span><span class="mi">12</span><span class="p">:</span><span class="mi">23</span><span class="p">:</span><span class="mf">45.111</span><span class="p">)</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">             </span><span class="s">&#39;PositionLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">125291184.0</span><span class="w"> </span><span class="o">-</span><span class="mf">75613036.0</span><span class="w"> </span><span class="o">-</span><span class="mf">32788527.0</span><span class="p">])</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">             </span><span class="s">&#39;PositionUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">125291184.0</span><span class="w"> </span><span class="o">-</span><span class="mf">75613036.0</span><span class="w"> </span><span class="o">-</span><span class="mf">32788527.0</span><span class="p">])</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">             </span><span class="s">&#39;VelocityLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">13.438</span><span class="w"> </span><span class="mf">25.234</span><span class="w"> </span><span class="mf">10.903</span><span class="p">])</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="w"></span>
<span class="w">             </span><span class="s">&#39;VelocityUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">[</span><span class="mf">13.438</span><span class="w"> </span><span class="mf">25.234</span><span class="w"> </span><span class="mf">10.903</span><span class="p">])</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;MassLowerBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">4000</span><span class="p">)</span><span class="w"></span>
<span class="n">aCustomLinkage</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;MassUpperBound&#39;</span><span class="p">,</span><span class="w"> </span><span class="mi">4000</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="defining-constraint-types">
<span id="sec-gmatoc-definingconstrainttypes"></span><h5>Defining Constraint Types<a class="headerlink" href="#defining-constraint-types" title="Permalink to this heading">¶</a></h5>
<p>Constraint types are defined by setting a bound on the desired parameter by calling the SetModelParameter() method. If a bound is not set for a parameter, then no constraint is applied on that parameter.  The user can optionally set the upper and/or lower bounds.  If only one bound is set, the default is used for the unset bound. See <a class="reference internal" href="#csalt-constrainttypes"><span class="std std-numref">Table 12</span></a> for supported parameter types, descriptions, and default bounds. The general syntax for setting constraint parameters and bounds is</p>
<div class="math">
<p><img src="../../_images/math/f3e6d7729032953e4f5e7fc87428d271b6b4c8bf.png" alt="\textrm{ConstraintName.SetModelParameter(Parameter, arg1, ..., argN)}"/></p>
</div><p>The argument list is overloaded and is dependent upon the parameter type. <a class="reference internal" href="#csalt-constrainttypes"><span class="std std-numref">Table 12</span></a> defines the argument list for each parameter type.</p>
<span id="csalt-constrainttypes"></span><table class="docutils align-default" id="id84">
<caption><span class="caption-number">Table 12 </span><span class="caption-text">Setting GMAT Optimal Control constraints.</span><a class="headerlink" href="#id84" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 75%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Parameter</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>PositionUpperBound</p>
<p>PositionLowerBound</p>
</td>
<td><p>Defines the upper and lower bounds on the position vector (in the selected phase’s coordinate system if in absolute mode).</p>
<p><strong>Example</strong></p>
<p>aCustomLinkage.SetModelParameter(‘PositionLowerBound’, [-1e7 -1e7 -1e7])</p>
<p>aCustomLinkage.SetModelParameter(‘PositionUpperBound’, [1e7 1e7 1e7])</p>
<p><strong>Default Bounds and Units</strong></p>
<p>Unit = km</p>
<p>Lower = 100*[-Max(Real) -Max(Real) -Max(Real) ]</p>
<p>Upper = 100*[Max(Real) Max(Real) Max(Real) ]</p>
</td>
</tr>
<tr class="row-odd"><td><p>VelocityUpperBound</p>
<p>VelocityLowerBound</p>
</td>
<td><p>Defines the upper and lower bounds on the velocity vector (in the selected phase’s coordinate system if in absolute mode).</p>
<p><strong>Example</strong></p>
<p>aCustomLinkage.SetModelParameter(‘VelocityLowerBound’, [-1e2 -1e2 -1e2])</p>
<p>aCustomLinkage.SetModelParameter(‘VelocityUpperBound’, [1e2 1e2 1e2])</p>
<p><strong>Default Bounds and Units</strong></p>
<p>Unit = km/s</p>
<p>Lower = 100*[-Max(Real) -Max(Real) -Max(Real) ]</p>
<p>Upper = 100*[Max(Real) Max(Real) Max(Real) ]</p>
</td>
</tr>
<tr class="row-even"><td><p>MassUpperBound</p>
<p>MassLowerBound</p>
</td>
<td><p>Defines the upper and lower bounds on the total spacecraft mass.</p>
<p><strong>Example</strong></p>
<p>aCustomLinkage.SetModelParameter(‘MassLowerBound’, 1e-7)</p>
<p>aCustomLinkage.SetModelParameter(‘MassUpperBound’, 1e4)</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The lower bound on mass cannot be set less than 1e-7 kg.</p>
</div>
<p><strong>Default Bounds and Units</strong></p>
<p>Unit = kg</p>
<p>Lower = 1e-7</p>
<p>Upper = 100*Max(Real)</p>
</td>
</tr>
<tr class="row-odd"><td><p>TimeUpperBound</p>
<p>TimeUpperBound</p>
</td>
<td><p>Defines the upper and lower bounds on time. Note, this field has a dependency on the ConstraintMode setting. The argument list is different depending upon the selected mode as shown below.</p>
<p>The function prototype is:</p>
<p>SetModelParameter(ParameterName, Epoch Format, Epoch Value)</p>
<p><strong>Example in Absolute Mode</strong></p>
<p>aCustomLinkage.SetModelParameter(‘TimeLowerBound’, TAIModJulian, 32060.0)</p>
<p>aCustomLinkage.SetModelParameter(‘TimeUpperBound’, TAIModJulian, 32560.0)</p>
<p>All GMAT epoch formats are supported.</p>
<p><strong>Default Bounds and Units in Absolute Mode</strong></p>
<p>The default bounds when ConstraintMode is Absolute are</p>
<p>LowerBound = 04 Oct 1957 12:00:00.000 UTC</p>
<p>UpperBound = 28 Feb 2100 00:00:00.000 UTC</p>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>Many SPICE files do not cover the default time span.  In that case you may need to specifically set the time bounds to be consistent with the ephemeris files.</p>
</div>
<p><strong>Example in Difference Mode</strong></p>
<p>aCustomLinkage.SetModelParameter(‘TimeLowerBound’, ‘ElapsedDays’, 32060.0)</p>
<p>aCustomLinkage.SetModelParameter(‘TimeUpperBound’, ‘ElapsedDays’, 32560.0)</p>
<p>Epoch formats are: ElapsedDays, ElapsedSeconds</p>
<p><strong>Default Bounds and Units in Difference Mode</strong></p>
<p>Units are determined by Time Format input.</p>
<p>Lower Bound = -100*Max(Real)</p>
<p>Upper Bound = 100*Max(Real)</p>
</td>
</tr>
</tbody>
</table>
<p>Custom linkage functions and bounds are scaled before being sent to CSALT.  Time is non-dimensionalized using the smallest TU between the two phases.  The smallest TU also determines which of the other scale factors are used between the two phases.</p>
</section>
</section>
<section id="setting-a-celestial-body-rendezvous-constraint">
<h4>Setting a Celestial Body Rendezvous Constraint<a class="headerlink" href="#setting-a-celestial-body-rendezvous-constraint" title="Permalink to this heading">¶</a></h4>
<p>The GMAT CSALT interface supports a rendezvous constraint that enforces time and Cartesian state equality between a user-specified phase and a user-specified celestial body.  The GMAT script to configure a rendezvous between the end of a Phase named ArrivalPhase and a celestial body named RQ36 is shown below.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlFunction</span><span class="w"> </span><span class="s">CometRendezvous</span><span class="w"></span>
<span class="n">CometRendezvous</span><span class="p">.</span><span class="n">Function</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">CelestialBodyRendezvous</span><span class="p">;</span><span class="w"></span>
<span class="n">CometRendezvous</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">AlgebraicConstraint</span><span class="w"></span>
<span class="n">CometRendezvous</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">arrivalPhase</span><span class="p">.</span><span class="n">Final</span><span class="p">}</span><span class="w"></span>
<span class="n">CometRendezvous</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;CelestialBody&#39;</span><span class="p">,</span><span class="w"> </span><span class="n">RQ36</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition warning">
<p class="admonition-title">Warning</p>
<p>The gravitational force of a body with which a spacecraft is to perform a celestial body rendezvous should be turned OFF for the phase(s) in which the rendezvous is to occur to avoid a potential singularity in the dynamics model.</p>
</div>
</section>
<section id="setting-an-integrated-flyby-constraint">
<h4>Setting an Integrated Flyby Constraint<a class="headerlink" href="#setting-an-integrated-flyby-constraint" title="Permalink to this heading">¶</a></h4>
<p>The GMAT CSALT interface provides an integrated flyby constraint given two user-specified phases and a user-specified celestial body.  The flyby is modeled at the end of the first phase and the beginning of the second phase.  The GMAT script to configure an integrated flyby between the end of a Phase named PreEGA1 and the beginning of a Phase named PostEGA1 and a celestial body named Earth is shown below.</p>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Note: To configure an integrated flyby constraint, the user must also apply a simple linkage constraint between the two phases in the flyby.  The simple linkage constraint applies full-state continuity at the linkage point, and the integrated flyby constraint ensures the phase boundary is at periapsis and the magnitude of the position is within the bounds.</p>
</div>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">BoundaryFunction</span><span class="w"> </span><span class="s">EarthGravAssist</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">Function</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">IntegratedFlyBy</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">AlgebraicConstraint</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">.</span><span class="n">Final</span><span class="p">,</span><span class="n">phase2</span><span class="p">.</span><span class="n">Initial</span><span class="p">}</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span>‘<span class="n">CelestialBody</span>’<span class="p">,</span>‘<span class="n">Earth</span>’<span class="p">)</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span>‘<span class="n">PeriapsisRadiusLowerBound</span>’<span class="p">,</span><span class="mi">6000</span><span class="p">)</span><span class="w"></span>
<span class="n">EarthGravAssist</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span>‘<span class="n">PeriapsisRadiusUpperBound</span>’<span class="p">,</span><span class="mi">100000</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-a-patched-conic-launch-constraint">
<h4>Setting a Patched Conic Launch Constraint<a class="headerlink" href="#setting-a-patched-conic-launch-constraint" title="Permalink to this heading">¶</a></h4>
<p>The patched conic launch model uses performance polynomials to compute mass-to-orbit for a launch vehicle and the transfer orbit insertion state.  The launch is modeled at the beginning of the phase.  To configure a patched conic launch, provide the phase, the central body name, the launch vehicle model file, and the name of the launch vehicle as shown in the example below.
Note: To constrain the launch epoch, apply a CustomLinkageConstraint in addition to the Patched Conic Launch Constraint.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlFunction</span><span class="w"> </span><span class="s">pcLaunch</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">AlgebraicConstraint</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">Function</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">PatchedConicLaunch</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase1</span><span class="p">.</span><span class="n">Initial</span><span class="p">}</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="n">VehicleName</span><span class="p">,</span><span class="w"> </span><span class="n">Atlas_V_401</span><span class="p">)</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="n">CentralBody</span><span class="p">,</span><span class="n">Earth</span><span class="p">)</span><span class="w"></span>
<span class="n">pcLaunch</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="n">EMTGLaunchVehicleOptionsFile</span><span class="p">,</span><span class="w"> </span><span class="p">.</span><span class="o">./</span><span class="n">data</span><span class="o">/</span><span class="n">emtg</span><span class="o">/</span><span class="n">filename</span><span class="p">.</span><span class="n">emtg_launchvehicleopt</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<section id="patched-conic-launch-model-file">
<h5>Patched Conic Launch Model File<a class="headerlink" href="#patched-conic-launch-model-file" title="Permalink to this heading">¶</a></h5>
<p>A launch model file contains data for different launch vehicles by vehicle name. The file /gmat/data/emtg/NLSII_August2018_Augmented.emtg_launchvehicleopt is distributed with GMAT. The file can be edited to add additional data and new files may be created and referenced. The data in the launch file is organized as described below.</p>
<p>The first few lines are a header, to be ignored as comments (using the # symbol), which describes the proceeding tokens. Each line contains 8 tokens in order as listed, and all elements are delimited via whitespace.</p>
<ol class="arabic simple">
<li><p>Launch vehicle name (string)</p></li>
<li><p>Model type (integer); always zero, represents polynomial C3 curve</p></li>
<li><p>Declination of launch asymptote (DLA) lower bound, degrees (double)</p></li>
<li><p>DLA upper bound, degrees (double)</p></li>
<li><p>C3 lower bound, km^2/s^2 (double)</p></li>
<li><p>C3 upper bound, km^2/s^2 (double)</p></li>
<li><p>Launch vehicle adapter mass (double), always zero for our purposes</p></li>
<li><p>Coefficients for the polynomial giving mass-to-orbit as a function of C3 (double). The first coefficient in a line is the 0th-order coefficient, to be multiplied by C3^0, followed by the 1st-order coefficient to be multiplied by C3^1 and so on.</p></li>
</ol>
</section>
</section>
<section id="setting-a-custom-in-line-constraint">
<h4>Setting a Custom In-Line Constraint<a class="headerlink" href="#setting-a-custom-in-line-constraint" title="Permalink to this heading">¶</a></h4>
<p>An in-line constraint allows a user to set constraints from within a GMAT script using a scripted equation. Using this capability, a user can quickly set a simple constraint without writing a new CSALT class. In the examples below, the argument to SetModelParameter following ‘Expression’ may be any GMAT Equation.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="n">Create</span><span class="w"> </span><span class="s">OptimalControlFunction</span><span class="w"> </span><span class="s">boundCon</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">Function</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Expression</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">Type</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">AlgebraicConstraint</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">PhaseList</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">phase4</span><span class="p">.</span><span class="n">Final</span><span class="p">}</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;LowerBounds&#39;</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">6000</span><span class="p">)</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;UpperBounds&#39;</span><span class="p">,</span><span class="w"> </span><span class="o">-</span><span class="mi">6000</span><span class="p">)</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span><span class="s">&#39;Expression&#39;</span><span class="p">,</span><span class="w"> </span><span class="s">&#39;mySat.EarthMJ2000eq.BdotT&#39;</span><span class="p">)</span><span class="w"></span>
<span class="n">boundCon</span><span class="p">.</span><span class="n">SetModelParameter</span><span class="p">(</span>‘<span class="n">ScaleFactor</span>’<span class="p">,</span><span class="w"> </span><span class="mi">6000</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-maximum-mass-cost-function">
<h4>Setting Maximum Mass Cost Function<a class="headerlink" href="#setting-maximum-mass-cost-function" title="Permalink to this heading">¶</a></h4>
<p>See <a class="reference internal" href="#sec-csalt-builtincosts"><span class="std std-ref">Built-In Cost and Constraint Functions</span></a>.</p>
</section>
<section id="set-time-cost-function">
<h4>Set Time Cost Function<a class="headerlink" href="#set-time-cost-function" title="Permalink to this heading">¶</a></h4>
<p>See <a class="reference internal" href="#sec-csalt-builtincosts"><span class="std std-ref">Built-In Cost and Constraint Functions</span></a>.</p>
</section>
<section id="set-rmag-cost-function">
<h4>Set RMAG Cost Function<a class="headerlink" href="#set-rmag-cost-function" title="Permalink to this heading">¶</a></h4>
<p>See <a class="reference internal" href="#sec-csalt-builtincosts"><span class="std std-ref">Built-In Cost and Constraint Functions</span></a>.</p>
</section>
<section id="built-in-cost-and-constraint-functions">
<span id="sec-csalt-builtincosts"></span><h4>Built-In Cost and Constraint Functions<a class="headerlink" href="#built-in-cost-and-constraint-functions" title="Permalink to this heading">¶</a></h4>
<p>The GMAT Phase Resource supports a set of “built-in” cost and constraint functions. These are set on the Phase Resource, but are documented here to keep the documentation of cost and constraint functions consolidated to a single chapter.  A script example for setting the cost function to total mass is shown below, followed by a section documenting all supported built-in cost functions.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="o">//</span><span class="w"> </span><span class="n">Define</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">cost</span><span class="w"> </span><span class="k">function</span><span class="w"> </span><span class="n">as</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="n">total</span><span class="w"> </span><span class="n">spacecraft</span><span class="w"> </span><span class="n">mass</span><span class="w"> </span><span class="n">at</span><span class="w"> </span><span class="n">the</span><span class="w"> </span><span class="k">end</span><span class="w"> </span><span class="n">of</span><span class="w"> </span>“<span class="n">thePhase</span>”<span class="p">.</span><span class="w"></span>
<span class="n">thePhase</span><span class="p">.</span><span class="n">BuiltInCost</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="s">&#39;TotalMassFinal&#39;</span><span class="w"></span>
</pre></div>
</div>
<section id="built-in-cost-functions">
<h5>Built-in Cost Functions<a class="headerlink" href="#built-in-cost-functions" title="Permalink to this heading">¶</a></h5>
<p>The GMAT Phase Resource supports several built-in cost functions.  The names of those functions and the descriptions are contained in <a class="reference internal" href="#csalt-builtincosts"><span class="std std-numref">Table 13</span></a>.</p>
<span id="csalt-builtincosts"></span><table class="docutils align-default" id="id85">
<caption><span class="caption-number">Table 13 </span><span class="caption-text">Built-in cost functions.</span><a class="headerlink" href="#id85" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 75%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>Cost Function Name</p></th>
<th class="head"><p>Description</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>RMAGFinal</p></td>
<td><p>The magnitude of the Cartesian state at the end of the phase computed with respect to the central body of the ForceModel used in the Phase’s dynamics configuration.</p></td>
</tr>
<tr class="row-odd"><td><p>TotalMassFinal</p></td>
<td><p>The total spacecraft mass at the end of the phase.</p></td>
</tr>
<tr class="row-even"><td><p>AbsoluteEpochFinal</p></td>
<td><p>The A1 modified Julian epoch at the end of the phase.</p></td>
</tr>
</tbody>
</table>
<p>The built-in cost functions may be minimized or maximized based on the value of Trajectory.OptimizationMode.</p>
</section>
</section>
</section>
</section>
<section id="path-functions-reference">
<span id="sec-gmatoc-pathfunctionsreference"></span><h2>Path Functions Reference<a class="headerlink" href="#path-functions-reference" title="Permalink to this heading">¶</a></h2>
<p>Path function types include dynamics models, algebraic path constraints, and integral cost functions.   CSALT and GMAT interfaces for defining these function types and their Jacobians are described below with detailed examples.</p>
<section id="setting-dynamics-models-in-c">
<h3>Setting Dynamics Models in C++<a class="headerlink" href="#setting-dynamics-models-in-c" title="Permalink to this heading">¶</a></h3>
<p>The example below shows how to set the dynamics and Jacobians for the Brachistichrone test problem. The BrachistichronePathObject class is derived from UserPathFunction.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Setting the dynamics model equations</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">BrachistichronePathObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">yVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">uVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>

<span class="w">    </span><span class="n">Real</span><span class="w">    </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">uVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w">    </span><span class="n">y</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="n">yVec</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w">    </span><span class="n">y2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">yVec</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w">    </span><span class="n">y3</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">gravity</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Evaluate dynamics and compute Jacobians</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">dynFunctions</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="n">y</span><span class="p">,</span><span class="w"> </span><span class="n">y2</span><span class="p">,</span><span class="w"> </span><span class="n">y3</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">dynFunctions</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>

<span class="c1">// Setting the Dynamics Model Jacobians</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">BrachistichronePathObject::EvaluateFuncJacobians</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Get state and control</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">2</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics state Jacobian</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dxdot_dv</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dydot_dv</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">dynState</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="n">dxdot_dv</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="n">dydot_dv</span><span class="p">,</span><span class="w"></span>
<span class="w">        </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics control Jacobian</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dxdot_du</span><span class="w">  </span><span class="o">=</span><span class="w"> </span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">cos</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dydot_du</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">-</span><span class="n">v</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">dvdot_du</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">-</span><span class="n">gravity</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">sin</span><span class="p">(</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">dynControl</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">dxdot_du</span><span class="p">,</span><span class="w"> </span><span class="n">dydot_du</span><span class="p">,</span><span class="w"> </span><span class="n">dvdot_du</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// The dynamics time Jacobian</span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">dynTime</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the Jacobians</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">STATE</span><span class="p">,</span><span class="w"> </span><span class="n">dynState</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of dynamics with respect to state</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">CONTROL</span><span class="p">,</span><span class="w"> </span><span class="n">dynControl</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of dynamics with respect to control</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">DYNAMICS</span><span class="p">,</span><span class="w"> </span><span class="n">TIME</span><span class="p">,</span><span class="w"> </span><span class="n">dynTime</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of dynamics with respect to time</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="setting-dynamics-models-in-gmat">
<h3>Setting Dynamics Models in GMAT<a class="headerlink" href="#setting-dynamics-models-in-gmat" title="Permalink to this heading">¶</a></h3>
<p>The Resource that defines the dynamics for a phase is the DynamicsConfiguration Resource.  To set up a DynamicsConfiguration, attach pre-configured Spacecraft, ForceModel, Fuel Tank, and thrust model Resources.  The example below illustrates how to configure those models.</p>
<section id="configure-the-fuel-tank">
<h4>Configure the Fuel Tank<a class="headerlink" href="#configure-the-fuel-tank" title="Permalink to this heading">¶</a></h4>
<p>A fuel tank is required to handle the fuel mass used by the spacecraft when performing thrust.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a chemical tank</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ChemicalTank</span><span class="w"> </span><span class="s">ChemicalTank1</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">AllowNegativeFuelMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="nb">false</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">FuelMass</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">4150</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Pressure</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1500</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Temperature</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">20</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">RefTemperature</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">20</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">Volume</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">3.5</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">FuelDensity</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mi">1260</span><span class="p">;</span><span class="w"></span>
<span class="n">ChemicalTank1</span><span class="p">.</span><span class="n">PressureModel</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">PressureRegulated</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id65">
<h4>Configure the Spacecraft<a class="headerlink" href="#id65" title="Permalink to this heading">¶</a></h4>
<p>Configuring a spacecraft for this example is relatively simple.  When we configure the guess data later in the tutorial, which includes spacecraft state and mass, those settings on the Spacecraft will be set according to the guess, so setting state and epoch information is not required here. We simply need to create a spacecraft and add a fuel tank.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a spacecraft names aSat. Guess is set later.</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">Spacecraft</span><span class="w"> </span><span class="s">aSat</span><span class="w"></span>
<span class="n">aSat</span><span class="p">.</span><span class="n">Tanks</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemicalTank1</span><span class="p">};</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="id66">
<h4>Configure Orbital Dynamics Models<a class="headerlink" href="#id66" title="Permalink to this heading">¶</a></h4>
<p>Below is the script configuration for a simple Sun-centered dynamics model with Earth, Sun, and Moon point masses included in the model.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create an orbit dynamics model with Earth, Sun, Moon point mass</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">ForceModel</span><span class="w"> </span><span class="s">DeepSpaceForceModel</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.CentralBody</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">Sun</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.PointMasses</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">Sun</span><span class="p">,</span><span class="n">Earth</span><span class="p">,</span><span class="n">Luna</span><span class="p">};</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.Drag</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">None</span><span class="p">;</span><span class="w"></span>
<span class="n">GMAT</span><span class="w"> </span><span class="s">DeepSpaceForceModel.SRP</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">On</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
</section>
<section id="configure-the-thruster-models">
<h4>Configure the Thruster Models<a class="headerlink" href="#configure-the-thruster-models" title="Permalink to this heading">¶</a></h4>
<p>The thrust model is configured by setting up EMTG options files and setting those files on an EMTGSpacecraft Resource. The EMTG options files contain the configuration for the thrust model. See <a class="reference internal" href="#sec-gmatoc-emtgspacecraftreference"><span class="std std-ref">EMTG Spacecraft Reference</span></a> for additional information.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create an emtgThrustModel Resource</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">EMTGSpacecraft</span><span class="w"> </span><span class="s">emtgThrustModel</span><span class="p">;</span><span class="w"></span>
<span class="n">emtgThrustModel</span><span class="p">.</span><span class="n">SpacecraftFile</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="n">FixedThrustAndIsp_Model1</span><span class="p">.</span><span class="n">emtg_spacecraftopt</span><span class="w"></span>
<span class="n">emtgThrustModel</span><span class="p">.</span><span class="n">SpacecraftStage</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="w"></span>
<span class="n">emtgThrustModel</span><span class="p">.</span><span class="n">DutyCycle</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>The EMTGDataPath field specifies the path relative to the GMAT bin directory.</p>
</div>
</section>
<section id="id67">
<h4>Configure the Dynamics Configuration<a class="headerlink" href="#id67" title="Permalink to this heading">¶</a></h4>
<p>Now that the Spacecraft, Fuel Tank, ForceModel, and EMTGSpacecraft Resources are configured, they are added to a DynamicsConfiguration Resource.</p>
<div class="highlight-matlab notranslate"><div class="highlight"><pre><span></span><span class="c">% Create a DynamicsConfiguration object and add spacecraft</span><span class="w"></span>
<span class="c">% ForceModel and Thrust model</span><span class="w"></span>
<span class="n">Create</span><span class="w"> </span><span class="s">DynamicsConfiguration</span><span class="w"> </span><span class="s">SunThrustDynConfig</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">DynamicsModels</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">DeepSpaceForceModel</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">Spacecraft</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">aSat</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">FiniteBurns</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">emtgThrustModel</span><span class="p">}</span><span class="w"></span>
<span class="n">SunThrustDynConfig</span><span class="p">.</span><span class="n">EMTGTankConfig</span><span class="w"> </span><span class="p">=</span><span class="w"> </span><span class="p">{</span><span class="n">ChemicalTank1</span><span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-integral-cost-functions">
<h3>Setting Integral Cost Functions<a class="headerlink" href="#setting-integral-cost-functions" title="Permalink to this heading">¶</a></h3>
<section id="id68">
<h4>C++ Example<a class="headerlink" href="#id68" title="Permalink to this heading">¶</a></h4>
<p>The example below shows how to set the cost function integrand and Jacobians for the Hypersenstive problem.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Setting the Cost Function</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">HyperSensitivePathObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Compute intermediate quantities</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">ySquared</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">y</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">yCubed</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">ySquared</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">y</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">uSquared</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">u</span><span class="p">;</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the cost function integrand</span>
<span class="w">    </span><span class="c1">// J = integral y^2 + u^2</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">costF</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="n">ySquared</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="n">uSquared</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">costF</span><span class="p">);</span><span class="w"></span>
<span class="p">}</span><span class="w"></span>

<span class="c1">// Setting the Cost Function Jacobians</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">HyperSensitivePathObject::EvaluateJacobians</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Computes the user Jacobians</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">u</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Cost Function Partials</span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">costStateJac</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">2.0</span><span class="o">*</span><span class="n">y</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">costControlJac</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">2.0</span><span class="o">*</span><span class="n">u</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">costTimeJac</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mf">0.0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">STATE</span><span class="p">,</span><span class="w"> </span><span class="n">costStateJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of cost with respect to state</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">CONTROL</span><span class="p">,</span><span class="w"> </span><span class="n">costControlJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of cost with respect to control</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">COST</span><span class="p">,</span><span class="w"> </span><span class="n">TIME</span><span class="p">,</span><span class="w"> </span><span class="n">costTimeJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of cost with respect to time</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
<section id="setting-algebraic-path-constraints">
<h3>Setting Algebraic Path Constraints<a class="headerlink" href="#setting-algebraic-path-constraints" title="Permalink to this heading">¶</a></h3>
<section id="id69">
<h4>C++ Example<a class="headerlink" href="#id69" title="Permalink to this heading">¶</a></h4>
<p>The example below shows how to set algebraic path constraints for the ObstacleAvoidance example problem.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Setting the algebraic path functions</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">ObstacleAvoidancePathObject::EvaluateFunctions</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Extract parameter data and define constants</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">V</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">2.138</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">theta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the algebraic path constraints</span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">con1</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.4</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.4</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="p">(</span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.5</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.5</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">con2</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.8</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.8</span><span class="p">)</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="p">(</span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1.5</span><span class="p">)</span><span class="o">*</span><span class="p">(</span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1.5</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFunctions</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="n">con1</span><span class="p">,</span><span class="w"> </span><span class="n">con2</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFuncUpper</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mf">100.0</span><span class="p">,</span><span class="w"> </span><span class="mf">100.0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">algFuncLower</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mf">0.1</span><span class="p">,</span><span class="w"> </span><span class="mf">0.1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctions</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">algFunctions</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">UPPER</span><span class="p">,</span><span class="w"> </span><span class="n">algFuncUpper</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">SetFunctionBounds</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">LOWER</span><span class="p">,</span><span class="w"> </span><span class="n">algFuncLower</span><span class="p">);</span><span class="w"></span>

<span class="p">}</span><span class="w"></span>

<span class="c1">// Setting the algebraic path function Jacobians</span>
<span class="kt">void</span><span class="w"> </span><span class="nf">ObstacleAvoidancePathObject::EvaluateJacobians</span><span class="p">()</span><span class="w"></span>
<span class="p">{</span><span class="w"></span>
<span class="w">    </span><span class="c1">// Extract parameter data and define constants</span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">stateVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetStateVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Rvector</span><span class="w"> </span><span class="n">controlVec</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">GetControlVector</span><span class="p">();</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">stateVec</span><span class="p">(</span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">V</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">2.138</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">Real</span><span class="w"> </span><span class="n">theta</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="n">controlVec</span><span class="p">(</span><span class="mi">0</span><span class="p">);</span><span class="w"></span>

<span class="w">    </span><span class="c1">// Set the algebraic path function state Jacobians</span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">algStateJac</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">2</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">algControlJac</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">Rmatrix</span><span class="w"> </span><span class="n">algTimeJac</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">);</span><span class="w"></span>
<span class="w">    </span><span class="n">algStateJac</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">0.8</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">algStateJac</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">algStateJac</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">0</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">x</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">1.6</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">algStateJac</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="mi">1</span><span class="p">)</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mi">2</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="n">y</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="mf">3.0</span><span class="p">;</span><span class="w"></span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">STATE</span><span class="p">,</span><span class="w"> </span><span class="n">algStateJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of algebraic constraint with respect to state</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">CONTROL</span><span class="p">,</span><span class="w"> </span><span class="n">algControlJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of algebraic constraint with respect to control</span>
<span class="w">    </span><span class="n">SetJacobian</span><span class="p">(</span><span class="n">ALGEBRAIC</span><span class="p">,</span><span class="w"> </span><span class="n">TIME</span><span class="p">,</span><span class="w"> </span><span class="n">algTimeJac</span><span class="p">);</span><span class="w"> </span><span class="c1">// derivatives of algebraic constraint with respect to time</span>
<span class="w">    </span><span class="c1">// Control and state Jacobians are zero</span>
<span class="p">}</span><span class="w"></span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="executioninterface-reference">
<span id="sec-gmatoc-executioninterfacereference"></span><h2>ExecutionInterface Reference<a class="headerlink" href="#executioninterface-reference" title="Permalink to this heading">¶</a></h2>
<p>The ExecutionInterface class provides an interface to request data from CSALT during optimization/execution.  For example, if it is desired to update plots or reports at various stages of optimization, the interface allows the user to obtain information on the optimization process at various stages, such as after initialization, during optimizer iterations, during mesh refinement iterations, and during finalization. Additionally, the ExecutionInterface provides an interrupt interface to stop the optimization process based on user commands.</p>
<p>When implementing an ExecutionInterface for an application, derive an ExecutionInterface class from the CSALT ExecutionInterface base class.  The following pure virtual functions must be overloaded:</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">pure</span><span class="w"> </span><span class="k">virtual</span><span class="w"> </span><span class="n">Publish</span><span class="p">(</span><span class="n">CSALTState</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<p>The CSALT states are [Initializing, Optimizing, EvaluatingMesh, ReInitializingMesh, Finalizing].</p>
<p>To obtain the times, states, and controls for a given phase from inside the Publish() function, the following access methods are provided.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="c1">// Returns the array of states in a requested phase.</span>
<span class="n">Rmatrix</span><span class="w"> </span><span class="n">GetStateArray</span><span class="p">(</span><span class="n">phaseIndex</span><span class="p">)</span><span class="w"></span>
<span class="c1">// Returns the array of controls in a requested phase</span>
<span class="n">Rmatrix</span><span class="w"> </span><span class="n">GetControlArray</span><span class="p">(</span><span class="n">phaseIndex</span><span class="p">)</span><span class="w"></span>
<span class="c1">// Returns the array of times of the mesh points in a phase</span>
<span class="n">Rvector</span><span class="w"> </span><span class="n">GetTimeArray</span><span class="p">(</span><span class="n">phaseIndex</span><span class="p">)</span><span class="w"></span>
</pre></div>
</div>
<p>During problem setup, provide the custom publisher to the Trajectory object.</p>
<div class="highlight-cpp notranslate"><div class="highlight"><pre><span></span><span class="n">MyExecutionInterface</span><span class="w"> </span><span class="o">*</span><span class="n">myPub</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">MyExecutionInterface</span><span class="p">();</span><span class="w"></span>
<span class="n">MyTraj</span><span class="w"> </span><span class="o">*</span><span class="n">traj</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">new</span><span class="w"> </span><span class="n">Trajectory</span><span class="p">();</span><span class="w"></span>
<span class="n">traj</span><span class="o">-&gt;</span><span class="n">SetPublisher</span><span class="p">(</span><span class="n">MyExecutionInterfaceb</span><span class="p">);</span><span class="w"></span>
</pre></div>
</div>
<p>The CSALT application calls the custom MyExecutionInterface’s Publish() method at key execution points, and passes in the CSALT state at each call to allow the user’s custom  publisher object to provide custom publishing functionality depending upon the CSALT state. <a class="reference internal" href="#csalt-states"><span class="std std-numref">Table 14</span></a> defines the states.</p>
<span id="csalt-states"></span><table class="docutils align-default" id="id86">
<caption><span class="caption-number">Table 14 </span><span class="caption-text">CSALT states.</span><a class="headerlink" href="#id86" title="Permalink to this table">¶</a></caption>
<colgroup>
<col style="width: 25%" />
<col style="width: 75%" />
</colgroup>
<thead>
<tr class="row-odd"><th class="head"><p>CSALT State</p></th>
<th class="head"><p>Definition</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Initializing</p></td>
<td><p>The state for all computations and preparations performed to prepare for optimization.  The state changes from Initializing to Optimizing immediately before the call to the optimizer.</p></td>
</tr>
<tr class="row-odd"><td><p>Optimizing</p></td>
<td><p>The state for all computations performed during optimization on a given mesh.</p></td>
</tr>
<tr class="row-even"><td><p>EvaluatingMesh</p></td>
<td><p>The state for all computations performed during the evaluation of mesh for accuracy of the solution compared to the user’s mesh tolerance.</p></td>
</tr>
<tr class="row-odd"><td><p>ReInitializingMesh</p></td>
<td><p>The state when CSALT is re-initializing to optimize on a new mesh grid.</p></td>
</tr>
<tr class="row-even"><td><p>Finalizing</p></td>
<td><p>The state for all computations performed after both the mesh refinement and optimization have converged.</p></td>
</tr>
</tbody>
</table>
<p class="rubric">Footnotes</p>
<aside class="footnote brackets" id="f-csalt-customscaling" role="note">
<span class="label"><span class="fn-bracket">[</span><a role="doc-backlink" href="#id63">1</a><span class="fn-bracket">]</span></span>
<p>The ScaleUtility class also provides capabilities for scaling.</p>
</aside>
</section>
</section>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <div>
    <h3><a href="../../index.html">Table of Contents</a></h3>
    <ul>
<li><a class="reference internal" href="#">User Guide</a><ul>
<li><a class="reference internal" href="#tutorial-setting-up-an-optimal-control-problem-in-csalt">Tutorial: Setting Up an Optimal Control Problem in CSALT</a><ul>
<li><a class="reference internal" href="#csalt-c-user-interface-component-overview">CSALT C++ User Interface Component Overview</a></li>
<li><a class="reference internal" href="#configuring-files-for-tutorial">Configuring Files for Tutorial</a></li>
<li><a class="reference internal" href="#overview-of-csalt-configuration-process">Overview of CSALT Configuration Process</a></li>
<li><a class="reference internal" href="#step-1-configure-the-path-functions">Step 1: Configure the Path Functions</a></li>
<li><a class="reference internal" href="#step-2-configure-the-boundary-functions">Step 2: Configure the Boundary Functions</a></li>
<li><a class="reference internal" href="#step-3-configure-the-phases-and-transcription">Step 3: Configure the Phases and Transcription</a></li>
<li><a class="reference internal" href="#step-4-configure-the-trajectory">Step 4: Configure the Trajectory</a></li>
<li><a class="reference internal" href="#step-5-run-the-optimizer-and-examine-the-solution">Step 5: Run the Optimizer and Examine the Solution</a></li>
<li><a class="reference internal" href="#additional-tutorials">Additional Tutorials</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tutorial-setting-up-an-optimal-control-problem-in-matlab">Tutorial: Setting up an Optimal Control Problem in MATLAB</a><ul>
<li><a class="reference internal" href="#matlab-user-interface-component-overview">MATLAB User Interface Component Overview</a></li>
<li><a class="reference internal" href="#id1">Overview of CSALT Configuration Process</a></li>
<li><a class="reference internal" href="#configuring-the-csalt-path">Configuring the CSALT Path</a></li>
<li><a class="reference internal" href="#id2">Step 1: Configure the Path Functions</a></li>
<li><a class="reference internal" href="#id3">Step 2: Configure the Boundary Functions</a></li>
<li><a class="reference internal" href="#id4">Step 3: Configure the Phases and Transcription</a></li>
<li><a class="reference internal" href="#id5">Step 4: Configure the Trajectory</a></li>
<li><a class="reference internal" href="#id6">Step 5: Run the Optimizer and Examine the Solution</a></li>
<li><a class="reference internal" href="#id7">Additional Tutorials</a></li>
</ul>
</li>
<li><a class="reference internal" href="#tutorial-setting-up-an-optimal-control-problem-in-gmat">Tutorial: Setting up an Optimal Control Problem in GMAT</a><ul>
<li><a class="reference internal" href="#gmat-script-user-interface-component-overview">GMAT Script User Interface Component Overview</a></li>
<li><a class="reference internal" href="#overview-of-gmat-script-configuration-process">Overview of GMAT Script Configuration Process</a></li>
<li><a class="reference internal" href="#step-1-configure-path-functions-and-dynamics">Step 1: Configure Path Functions and Dynamics</a><ul>
<li><a class="reference internal" href="#configure-the-spacecraft">Configure the Spacecraft</a></li>
<li><a class="reference internal" href="#configure-orbital-dynamics-models">Configure Orbital Dynamics Models</a></li>
<li><a class="reference internal" href="#configure-the-thruster-models-emtgspacecraft">Configure the Thruster Models (EMTGSpacecraft)</a></li>
<li><a class="reference internal" href="#configure-the-dynamics-configuration">Configure the Dynamics Configuration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#step-2-configure-the-guess">Step 2: Configure the Guess</a></li>
<li><a class="reference internal" href="#step-3-configure-the-phases">Step 3: Configure the Phases</a></li>
<li><a class="reference internal" href="#step-4-configure-the-boundary-functions">Step 4: Configure the Boundary Functions</a><ul>
<li><a class="reference internal" href="#set-the-initial-conditions">Set the Initial Conditions</a></li>
<li><a class="reference internal" href="#configure-the-phase-duration-constraint">Configure the phase duration constraint</a></li>
<li><a class="reference internal" href="#setting-the-cost-function">Setting the Cost Function</a></li>
</ul>
</li>
<li><a class="reference internal" href="#step-5-configure-the-trajectory">Step 5: Configure the Trajectory</a></li>
<li><a class="reference internal" href="#step-6-run-the-optimizer-and-examine-the-solution">Step 6: Run the Optimizer and Examine the Solution</a></li>
</ul>
</li>
<li><a class="reference internal" href="#trajectory-reference">Trajectory Reference</a><ul>
<li><a class="reference internal" href="#setting-the-phase-objects">Setting the Phase Objects</a><ul>
<li><a class="reference internal" href="#gmat-script-example">GMAT Script Example</a></li>
<li><a class="reference internal" href="#c-example">C++ Example</a></li>
<li><a class="reference internal" href="#matlab-example">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-cost-function-bounds">Setting Cost Function Bounds</a><ul>
<li><a class="reference internal" href="#id8">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id9">C++ Example</a></li>
<li><a class="reference internal" href="#id10">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-optimization-convergence-tolerances">Setting Optimization Convergence Tolerances</a><ul>
<li><a class="reference internal" href="#id11">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id12">C++ Example</a></li>
<li><a class="reference internal" href="#id13">MATLAB Example</a></li>
<li><a class="reference internal" href="#id14">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id15">C++ Example</a></li>
<li><a class="reference internal" href="#id16">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-optimization-iterations-limits">Setting Optimization Iterations Limits</a><ul>
<li><a class="reference internal" href="#id17">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id18">C++ Example</a></li>
<li><a class="reference internal" href="#id19">MATLAB Example</a></li>
<li><a class="reference internal" href="#id20">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id21">C++ Example</a></li>
<li><a class="reference internal" href="#id22">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-mesh-refinement-iterations">Setting Mesh Refinement Iterations</a><ul>
<li><a class="reference internal" href="#id23">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id24">C++ Example</a></li>
<li><a class="reference internal" href="#id25">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-optimization-and-mesh-modes">Setting Optimization and Mesh Modes</a><ul>
<li><a class="reference internal" href="#id26">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id27">C++ Example</a></li>
<li><a class="reference internal" href="#id28">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id29">C++ Example</a></li>
<li><a class="reference internal" href="#id30">MATLAB Example</a></li>
<li><a class="reference internal" href="#id31">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id32">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-snopt-output-data-file">Setting SNOPT Output Data File</a><ul>
<li><a class="reference internal" href="#id33">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id34">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-snopt-print-to-console-verbosity">Setting SNOPT Print-To-Console Verbosity</a><ul>
<li><a class="reference internal" href="#id35">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#scaling-the-optimal-control-problem">Scaling the Optimal Control Problem</a><ul>
<li><a class="reference internal" href="#id36">GMAT Script Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-solution-output-configuration-in-gmat">Setting the Solution Output Configuration in GMAT</a><ul>
<li><a class="reference internal" href="#id37">GMAT Script Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-simple-linkage-constraints-in-gmat">Setting Simple Linkage Constraints in GMAT</a><ul>
<li><a class="reference internal" href="#id38">GMAT Script Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-guess-source-in-gmat">Setting the Guess Source in GMAT</a></li>
<li><a class="reference internal" href="#setting-the-publisher-update-rate-in-gmat">Setting the Publisher Update Rate in GMAT</a></li>
<li><a class="reference internal" href="#setting-path-functions-in-csalt">Setting Path Functions in CSALT</a><ul>
<li><a class="reference internal" href="#id39">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id40">C++ Example</a></li>
<li><a class="reference internal" href="#id41">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-boundary-functions-in-csalt">Setting Boundary Functions in CSALT</a><ul>
<li><a class="reference internal" href="#id42">C++ Example</a></li>
<li><a class="reference internal" href="#id43">C++ Example</a></li>
<li><a class="reference internal" href="#id44">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-executioninterface-object-in-csalt">Setting the ExecutionInterface Object in CSALT</a><ul>
<li><a class="reference internal" href="#id45">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id46">C++ Example</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#phase-reference">Phase Reference</a><ul>
<li><a class="reference internal" href="#state-and-control-representations-in-gmat">State and Control Representations in GMAT</a></li>
<li><a class="reference internal" href="#setting-the-transcription">Setting the Transcription</a><ul>
<li><a class="reference internal" href="#id47">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id48">C++ Example</a></li>
<li><a class="reference internal" href="#id49">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-decision-vector-bounds-and-dimensions">Setting Decision Vector Bounds and Dimensions</a><ul>
<li><a class="reference internal" href="#id50">GMAT Script Example</a></li>
<li><a class="reference internal" href="#id51">C++ Example</a></li>
<li><a class="reference internal" href="#id52">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-initial-guess-in-gmat">Setting the Initial Guess in GMAT</a><ul>
<li><a class="reference internal" href="#id53">GMAT Script Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-initial-guess-in-csalt-c-and-matlab">Setting the Initial Guess in CSALT C++ and MATLAB</a><ul>
<li><a class="reference internal" href="#setting-the-initial-guess-for-time-or-independent-variable">Setting the Initial Guess for Time (or Independent Variable)</a><ul>
<li><a class="reference internal" href="#id54">C++ Example</a></li>
<li><a class="reference internal" href="#id55">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-initial-guess-for-state-and-control-using-linear-guess-model">Setting the Initial Guess for State and Control using Linear Guess Model</a><ul>
<li><a class="reference internal" href="#id56">C++ Example</a></li>
<li><a class="reference internal" href="#id57">MATLAB Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-state-and-control-guesses-in-csalt-c-using-arrays-of-data">Setting State and Control Guesses in CSALT C++ using Arrays of Data</a><ul>
<li><a class="reference internal" href="#id58">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-state-and-control-guesses-in-csalt-c-using-an-och-guess-file">Setting State and Control Guesses in CSALT C++ using an OCH Guess File</a><ul>
<li><a class="reference internal" href="#id59">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-state-and-control-guesses-in-csalt-matlab-using-a-matlab-function">Setting State and Control Guesses in CSALT MATLAB using a MATLAB Function</a><ul>
<li><a class="reference internal" href="#id60">MATLAB Example</a></li>
<li><a class="reference internal" href="#matlab-function-prototype-for-guess-functions">MATLAB Function Prototype for Guess Functions</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-initial-guess-for-static-variables">Setting the Initial Guess for Static Variables</a><ul>
<li><a class="reference internal" href="#id61">C++ Example</a></li>
<li><a class="reference internal" href="#id62">MATLAB Example</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#setting-the-dynamics-configuration-in-gmat">Setting the Dynamics Configuration in GMAT</a></li>
<li><a class="reference internal" href="#setting-phase-graphics-configuration-in-gmat">Setting Phase Graphics Configuration in GMAT</a></li>
<li><a class="reference internal" href="#setting-phase-cost-and-constraint-functions-in-gmat">Setting Phase Cost and Constraint Functions in GMAT</a></li>
</ul>
</li>
<li><a class="reference internal" href="#dynamics-configuration-reference">Dynamics Configuration Reference</a><ul>
<li><a class="reference internal" href="#setting-the-force-model">Setting the Force Model</a></li>
<li><a class="reference internal" href="#setting-the-spacecraft">Setting the Spacecraft</a></li>
<li><a class="reference internal" href="#setting-the-finite-burn">Setting the Finite Burn</a></li>
<li><a class="reference internal" href="#setting-the-emtg-tank-configuration">Setting the EMTG Tank Configuration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#optimal-control-guess-reference">Optimal Control Guess Reference</a></li>
<li><a class="reference internal" href="#emtg-spacecraft-reference">EMTG Spacecraft Reference</a><ul>
<li><a class="reference internal" href="#creating-an-emtg-spacecraft-resource">Creating an EMTG Spacecraft Resource</a></li>
<li><a class="reference internal" href="#setting-the-emtg-spacecraft-file">Setting the EMTG Spacecraft File</a></li>
<li><a class="reference internal" href="#setting-the-emtg-spacecraft-stage">Setting the EMTG Spacecraft Stage</a></li>
<li><a class="reference internal" href="#setting-the-propulsion-system-duty-cycle">Setting the Propulsion System Duty Cycle</a></li>
<li><a class="reference internal" href="#format-of-emtg-spacecraft-file">Format of EMTG Spacecraft File</a></li>
<li><a class="reference internal" href="#throttle-tables">Throttle Tables</a><ul>
<li><a class="reference internal" href="#format-of-throttle-table-file">Format of Throttle Table File</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#boundary-functions-reference">Boundary Functions Reference</a><ul>
<li><a class="reference internal" href="#setting-c-boundary-functions-via-the-userpointfunction-class">Setting C++ Boundary Functions via the UserPointFunction Class</a></li>
<li><a class="reference internal" href="#setting-c-boundary-functions-via-the-optimalcontrolfunction-class">Setting C++ Boundary Functions via the OptimalControlFunction Class</a></li>
<li><a class="reference internal" href="#optimal-control-function-example-c">Optimal Control Function Example (C++)</a></li>
<li><a class="reference internal" href="#setting-boundary-functions-in-gmat">Setting Boundary Functions in GMAT</a><ul>
<li><a class="reference internal" href="#setting-full-state-linkage-constraints">Setting Full State Linkage Constraints</a><ul>
<li><a class="reference internal" href="#id64">GMAT Script Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-custom-linkage-constraints">Setting Custom Linkage Constraints</a><ul>
<li><a class="reference internal" href="#difference-mode">Difference Mode</a></li>
<li><a class="reference internal" href="#absolute-mode">Absolute Mode</a></li>
<li><a class="reference internal" href="#defining-constraint-types">Defining Constraint Types</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-a-celestial-body-rendezvous-constraint">Setting a Celestial Body Rendezvous Constraint</a></li>
<li><a class="reference internal" href="#setting-an-integrated-flyby-constraint">Setting an Integrated Flyby Constraint</a></li>
<li><a class="reference internal" href="#setting-a-patched-conic-launch-constraint">Setting a Patched Conic Launch Constraint</a><ul>
<li><a class="reference internal" href="#patched-conic-launch-model-file">Patched Conic Launch Model File</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-a-custom-in-line-constraint">Setting a Custom In-Line Constraint</a></li>
<li><a class="reference internal" href="#setting-maximum-mass-cost-function">Setting Maximum Mass Cost Function</a></li>
<li><a class="reference internal" href="#set-time-cost-function">Set Time Cost Function</a></li>
<li><a class="reference internal" href="#set-rmag-cost-function">Set RMAG Cost Function</a></li>
<li><a class="reference internal" href="#built-in-cost-and-constraint-functions">Built-In Cost and Constraint Functions</a><ul>
<li><a class="reference internal" href="#built-in-cost-functions">Built-in Cost Functions</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#path-functions-reference">Path Functions Reference</a><ul>
<li><a class="reference internal" href="#setting-dynamics-models-in-c">Setting Dynamics Models in C++</a></li>
<li><a class="reference internal" href="#setting-dynamics-models-in-gmat">Setting Dynamics Models in GMAT</a><ul>
<li><a class="reference internal" href="#configure-the-fuel-tank">Configure the Fuel Tank</a></li>
<li><a class="reference internal" href="#id65">Configure the Spacecraft</a></li>
<li><a class="reference internal" href="#id66">Configure Orbital Dynamics Models</a></li>
<li><a class="reference internal" href="#configure-the-thruster-models">Configure the Thruster Models</a></li>
<li><a class="reference internal" href="#id67">Configure the Dynamics Configuration</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-integral-cost-functions">Setting Integral Cost Functions</a><ul>
<li><a class="reference internal" href="#id68">C++ Example</a></li>
</ul>
</li>
<li><a class="reference internal" href="#setting-algebraic-path-constraints">Setting Algebraic Path Constraints</a><ul>
<li><a class="reference internal" href="#id69">C++ Example</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#executioninterface-reference">ExecutionInterface Reference</a></li>
</ul>
</li>
</ul>

  </div>
  <div>
    <h4>Previous topic</h4>
    <p class="topless"><a href="csalt-ConceptsAndAlgorithms.html"
                          title="previous chapter">Concepts and Algorithms</a></p>
  </div>
  <div>
    <h4>Next topic</h4>
    <p class="topless"><a href="csalt-UserInterfaceSpec-smallTables.html"
                          title="next chapter">User Interface Specification</a></p>
  </div>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="../../_sources/CSALT/source/csalt-UserGuide.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="../../search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"/>
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>document.getElementById('searchbox').style.display = "block"</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="../../genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="csalt-UserInterfaceSpec-smallTables.html" title="User Interface Specification"
             >next</a> |</li>
        <li class="right" >
          <a href="csalt-ConceptsAndAlgorithms.html" title="Concepts and Algorithms"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="../../index.html">GMAT Tools and Extensions R2025a documentation</a> &#187;</li>
          <li class="nav-item nav-item-1"><a href="csaltIndex.html" >GMAT Optimal Control</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Guide</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; Copyright 2018-2025, United States Government as represented by the Administrator of the National Aeronautics and Space Administration..
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 5.0.2.
    </div>
  </body>
</html>