#!/usr/bin/env python3
"""
Test script for the contact visualization
"""

import sys
from pathlib import Path

# Add current directory to path
sys.path.append('.')
from process_contact import ContactReport

# Import visualization libraries
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import networkx as nx
import numpy as np

def test_contact_parsing():
    """Test if we can parse the contact files"""
    output_dir = Path('./output')
    
    # Check if contact files exist
    eva1_file = output_dir / 'EVA1Contacts.txt'
    eva2_file = output_dir / 'EVA2Contacts.txt'
    
    if not eva1_file.exists():
        print(f"Warning: {eva1_file} not found")
        return None, None
    
    if not eva2_file.exists():
        print(f"Warning: {eva2_file} not found")
        return None, None
    
    # Parse contact reports
    print("Parsing EVA1 contacts...")
    eva1_contacts = ContactReport(eva1_file)
    eva1_contacts.scale_down(600)  # 10 mins -> 1 sec
    
    print("Parsing EVA2 contacts...")
    eva2_contacts = ContactReport(eva2_file)
    eva2_contacts.scale_down(600)  # 10 mins -> 1 sec
    
    print("\nEVA1 Summary:")
    eva1_contacts.print_summary()
    
    print("\nEVA2 Summary:")
    eva2_contacts.print_summary()
    
    return eva1_contacts, eva2_contacts

def create_simple_static_graph(eva1_contacts, eva2_contacts):
    """Create a simple static graph to test the visualization"""
    
    if not eva1_contacts or not eva2_contacts:
        print("No contact data available for visualization")
        return
    
    # Create the graph
    G = nx.Graph()
    
    # Add nodes
    spacecraft_nodes = {eva1_contacts.target, eva2_contacts.target}
    ground_station_nodes = set()
    
    for contacts in [eva1_contacts, eva2_contacts]:
        for observer_name in contacts.get_observer_names():
            ground_station_nodes.add(observer_name)
    
    G.add_nodes_from(spacecraft_nodes, node_type='spacecraft')
    G.add_nodes_from(ground_station_nodes, node_type='ground_station')
    
    # Create layout
    pos = {}
    
    # Position spacecraft in center
    spacecraft_list = list(spacecraft_nodes)
    for i, sc in enumerate(spacecraft_list):
        if len(spacecraft_list) == 1:
            pos[sc] = (0, 0)
        else:
            angle = 2 * np.pi * i / len(spacecraft_list)
            pos[sc] = (0.3 * np.cos(angle), 0.3 * np.sin(angle))
    
    # Position ground stations in circle
    gs_list = list(ground_station_nodes)
    for i, gs in enumerate(gs_list):
        angle = 2 * np.pi * i / len(gs_list)
        pos[gs] = (2 * np.cos(angle), 2 * np.sin(angle))
    
    # Create plot
    plt.figure(figsize=(10, 8))
    
    # Draw nodes
    spacecraft_positions = [pos[node] for node in spacecraft_nodes]
    gs_positions = [pos[node] for node in ground_station_nodes]
    
    if spacecraft_positions:
        sc_x, sc_y = zip(*spacecraft_positions)
        plt.scatter(sc_x, sc_y, c='red', s=300, marker='s', label='Spacecraft')
        for node in spacecraft_nodes:
            plt.annotate(node, pos[node], xytext=(5, 5), textcoords='offset points', 
                        fontsize=10, fontweight='bold')
    
    if gs_positions:
        gs_x, gs_y = zip(*gs_positions)
        plt.scatter(gs_x, gs_y, c='blue', s=200, marker='^', label='Ground Stations')
        for node in ground_station_nodes:
            plt.annotate(node, pos[node], xytext=(5, 5), textcoords='offset points', 
                        fontsize=9)
    
    # Draw some sample connections
    for contacts, color, mission in [(eva1_contacts, 'green', 'EVA1'), (eva2_contacts, 'orange', 'EVA2')]:
        for observer_name in contacts.get_observer_names():
            events = contacts.get_events_for_observer(observer_name)
            if events:  # If there are any events, draw a connection
                sc_pos = pos[contacts.target]
                gs_pos = pos[observer_name]
                plt.plot([sc_pos[0], gs_pos[0]], [sc_pos[1], gs_pos[1]], 
                        color=color, linewidth=2, alpha=0.5, label=f'{mission} Contact' if observer_name == list(contacts.get_observer_names())[0] else "")
    
    plt.xlim(-3, 3)
    plt.ylim(-3, 3)
    plt.gca().set_aspect('equal')
    plt.grid(True, alpha=0.3)
    plt.title('Contact Network (Static View)', fontsize=14, fontweight='bold')
    plt.legend()
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("Testing contact visualization...")
    
    # Test parsing
    eva1_contacts, eva2_contacts = test_contact_parsing()
    
    # Test static visualization
    if eva1_contacts and eva2_contacts:
        print("\nCreating static visualization test...")
        create_simple_static_graph(eva1_contacts, eva2_contacts)
    else:
        print("Cannot create visualization without contact data")
