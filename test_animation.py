#!/usr/bin/env python3
"""
Test the animated contact visualization with sample data
"""

import sys
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
from pathlib import Path

# Add current directory to path
sys.path.append('.')

def create_sample_contact_data():
    """Create sample contact data for testing"""
    
    class MockContactEvent:
        def __init__(self, start_time, stop_time, duration):
            self.start_time = start_time
            self.stop_time = stop_time
            self.duration = duration
    
    class MockContactReport:
        def __init__(self, target, observers_data):
            self.target = target
            self.observers_data = observers_data
        
        def get_observer_names(self):
            return list(self.observers_data.keys())
        
        def get_events_for_observer(self, observer_name):
            return self.observers_data.get(observer_name, [])
    
    # Create sample EVA1 contacts
    eva1_events = {
        'Houston': [
            MockContactEvent(0, 100, 100),
            MockContactEvent(200, 250, 50),
        ],
        'Sydney': [
            MockContactEvent(50, 150, 100),
            Mock<PERSON>ontact<PERSON>vent(300, 400, 100),
        ],
        'Ireland': [
            Mock<PERSON>ontactEvent(120, 180, 60),
        ]
    }
    
    # Create sample EVA2 contacts
    eva2_events = {
        'Houston': [
            MockContactEvent(80, 160, 80),
            MockContactEvent(280, 350, 70),
        ],
        'HLS': [
            MockContactEvent(20, 90, 70),
            MockContactEvent(180, 220, 40),
            MockContactEvent(320, 380, 60),
        ],
        'Sydney': [
            MockContactEvent(150, 200, 50),
        ]
    }
    
    eva1_contacts = MockContactReport('EVA1', eva1_events)
    eva2_contacts = MockContactReport('EVA2', eva2_events)
    
    return eva1_contacts, eva2_contacts

def create_animated_contact_graph(eva1_contacts, eva2_contacts):
    """Create an animated graph showing contact links appearing and disappearing"""
    
    # Collect all nodes
    spacecraft_nodes = {eva1_contacts.target, eva2_contacts.target}
    ground_station_nodes = set()
    
    for contacts in [eva1_contacts, eva2_contacts]:
        for observer_name in contacts.get_observer_names():
            ground_station_nodes.add(observer_name)
    
    # Create time-based contact events
    contact_events = []
    
    # Process EVA1 contacts
    for observer_name in eva1_contacts.get_observer_names():
        events = eva1_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva1_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA1'
            })
    
    # Process EVA2 contacts
    for observer_name in eva2_contacts.get_observer_names():
        events = eva2_contacts.get_events_for_observer(observer_name)
        for event in events:
            contact_events.append({
                'start_time': event.start_time,
                'stop_time': event.stop_time,
                'spacecraft': eva2_contacts.target,
                'ground_station': observer_name,
                'duration': event.duration,
                'mission': 'EVA2'
            })
    
    # Sort events by start time
    contact_events.sort(key=lambda x: x['start_time'])
    
    # Determine animation time range
    if contact_events:
        min_time = min(event['start_time'] for event in contact_events)
        max_time = max(event['stop_time'] for event in contact_events)
        time_steps = np.linspace(min_time, max_time, 200)
    else:
        time_steps = np.linspace(0, 400, 200)
    
    # Set up the plot
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # Create layout for nodes - matching the diagram
    pos = {}
    
    # Position spacecraft in the center area
    spacecraft_list = list(spacecraft_nodes)
    if len(spacecraft_list) == 2:
        pos[spacecraft_list[0]] = (0, 0.5)   # EVA1 upper center
        pos[spacecraft_list[1]] = (0, -0.5)  # EVA2 lower center
    elif len(spacecraft_list) == 1:
        pos[spacecraft_list[0]] = (0, 0)
    
    # Position ground stations in specific locations
    gs_positions = {
        'Houston': (-2.5, 1.5),
        'Sydney': (-2.5, -1.5), 
        'Ireland': (-2.5, -3),
        'HLS': (2.5, 0),
    }
    
    # Assign positions
    for gs in ground_station_nodes:
        if gs in gs_positions:
            pos[gs] = gs_positions[gs]
        else:
            # Fallback position
            pos[gs] = (3, 0)
    
    def animate(frame):
        ax.clear()
        current_time = time_steps[frame]
        
        # Find active contacts at current time
        active_contacts = []
        for event in contact_events:
            if event['start_time'] <= current_time <= event['stop_time']:
                active_contacts.append(event)
        
        # Draw spacecraft nodes as circles
        for node in spacecraft_nodes:
            circle = plt.Circle(pos[node], 0.4, color='lightblue', ec='black', linewidth=2, zorder=3)
            ax.add_patch(circle)
            ax.text(pos[node][0], pos[node][1], node, ha='center', va='center', 
                   fontsize=12, fontweight='bold', zorder=4)
        
        # Draw ground station nodes as circles
        for node in ground_station_nodes:
            circle = plt.Circle(pos[node], 0.4, color='lightgray', ec='black', linewidth=2, zorder=3)
            ax.add_patch(circle)
            ax.text(pos[node][0], pos[node][1], node, ha='center', va='center', 
                   fontsize=10, fontweight='bold', zorder=4)
        
        # Draw active contact links with arrows
        for contact in active_contacts:
            sc_pos = pos[contact['spacecraft']]
            gs_pos = pos[contact['ground_station']]
            
            color = 'black'
            linewidth = 2
            
            # Calculate arrow positions
            dx = gs_pos[0] - sc_pos[0]
            dy = gs_pos[1] - sc_pos[1]
            length = np.sqrt(dx**2 + dy**2)
            
            if length > 0:
                dx_norm = dx / length
                dy_norm = dy / length
                
                start_x = sc_pos[0] + 0.4 * dx_norm
                start_y = sc_pos[1] + 0.4 * dy_norm
                end_x = gs_pos[0] - 0.4 * dx_norm
                end_y = gs_pos[1] - 0.4 * dy_norm
                
                ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                           arrowprops=dict(arrowstyle='->', color=color, lw=linewidth),
                           zorder=2)
        
        # Set plot properties
        ax.set_xlim(-4, 4)
        ax.set_ylim(-4, 4)
        ax.set_aspect('equal')
        ax.axis('off')
        
        # Add title
        ax.text(0, 3.5, f'Contact Network Animation', ha='center', va='center',
               fontsize=16, fontweight='bold')
        ax.text(0, 3.1, f'Time: {current_time:.1f}s | Active Contacts: {len(active_contacts)}', 
               ha='center', va='center', fontsize=12)
        
        # Add mission legend
        ax.text(-3.5, 3.5, f'EVA1: {eva1_contacts.target}', fontsize=10, fontweight='bold')
        ax.text(-3.5, 3.2, f'EVA2: {eva2_contacts.target}', fontsize=10, fontweight='bold')
    
    # Create animation
    anim = animation.FuncAnimation(fig, animate, frames=len(time_steps), 
                                 interval=100, repeat=True, blit=False)
    
    plt.tight_layout()
    return fig, anim

if __name__ == "__main__":
    print("Creating test animation with sample data...")
    
    # Create sample data
    eva1_contacts, eva2_contacts = create_sample_contact_data()
    
    # Create animation
    fig, anim = create_animated_contact_graph(eva1_contacts, eva2_contacts)
    
    print("Showing animation... Close the window to exit.")
    plt.show()
