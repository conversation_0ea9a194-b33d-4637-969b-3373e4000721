%----------------------------------------
% GMAT Mission Script
%
% Scenario: Lunar Relay Satellite Constellation
% Author: AI Assistant
% Date: 2024-07-30
%
% This script simulates a two-satellite constellation in polar orbit
% around the Moon to provide communication coverage for a ground station
% at the Shackleton Crater (lunar south pole).
%
% It performs a line-of-sight contact analysis between:
% 1. The lunar satellites and three ground stations on Earth.
% 2. The lunar ground station and the two lunar satellites.
%
% The visualization is split into two views for clarity:
% - A Moon-centered view for observing the satellite orbits.
% - An Earth-Moon barycenter view for observing the entire system.
%----------------------------------------

%----------------------------------------
%---------- Celestial Bodies & Calculated Points
%----------------------------------------

Create Barycenter EarthMoonBarycenter;
GMAT EarthMoonBarycenter.BodyNames = {Earth, Luna};
GMAT EarthMoonBarycenter.OrbitColor = 'Yellow';

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem MoonMJ2000Eq;
GMAT MoonMJ2000Eq.Origin = Luna;
GMAT MoonMJ2000Eq.Axes = MJ2000Eq;

Create CoordinateSystem BarycenterMJ2000Eq;
GMAT BarycenterMJ2000Eq.Origin = EarthMoonBarycenter;
GMAT BarycenterMJ2000Eq.Axes = MJ2000Eq;

%----------------------------------------
%---------- Spacecraft
%----------------------------------------

% Two spacecraft in polar lunar orbits, phased 180 degrees apart.
Create Spacecraft Sat1, Sat2;

% Sat1 Configuration (Stable 2000km altitude circular polar orbit)
GMAT Sat1.DateFormat = UTCGregorian;
GMAT Sat1.Epoch = '01 Jan 2000 12:00:00.000';
GMAT Sat1.CoordinateSystem = MoonMJ2000Eq;
GMAT Sat1.DisplayStateType = Keplerian;
GMAT Sat1.SMA = 3737.4; % 1737.4km (Moon radius) + 2000km (altitude)
GMAT Sat1.ECC = 0.0;
GMAT Sat1.INC = 90.0;
GMAT Sat1.RAAN = 0.0;
GMAT Sat1.AOP = 0.0;
GMAT Sat1.TA = 0.0;
GMAT Sat1.OrbitColor = 'Red';

% Sat2 Configuration (Phased 180 deg from Sat1)
GMAT Sat2.DateFormat = UTCGregorian;
GMAT Sat2.Epoch = '01 Jan 2000 12:00:00.000';
GMAT Sat2.CoordinateSystem = MoonMJ2000Eq;
GMAT Sat2.DisplayStateType = Keplerian;
GMAT Sat2.SMA = 3737.4;
GMAT Sat2.ECC = 0.0;
GMAT Sat2.INC = 90.0;
GMAT Sat2.RAAN = 0.0;
GMAT Sat2.AOP = 0.0;
GMAT Sat2.TA = 180.0;
GMAT Sat2.OrbitColor = 'Green';

%----------------------------------------
%---------- GroundStations
%----------------------------------------

% Earth Ground Stations
Create GroundStation Houston, Sydney, Ireland;

GMAT Houston.CentralBody = Earth;
GMAT Houston.StateType = Spherical;
GMAT Houston.Location1 = 29.76;
GMAT Houston.Location2 = -95.36;
GMAT Houston.Location3 = 0.0;

GMAT Sydney.CentralBody = Earth;
GMAT Sydney.StateType = Spherical;
GMAT Sydney.Location1 = -33.87;
GMAT Sydney.Location2 = 151.21;
GMAT Sydney.Location3 = 0.0;

GMAT Ireland.CentralBody = Earth;
GMAT Ireland.StateType = Spherical;
GMAT Ireland.Location1 = 53.0;
GMAT Ireland.Location2 = -8.0;
GMAT Ireland.Location3 = 0.0;

% Lunar South Pole Ground Station
Create GroundStation ShackletonCrater;
GMAT ShackletonCrater.CentralBody = Luna;
GMAT ShackletonCrater.StateType = Spherical;
GMAT ShackletonCrater.Location1 = -90.0; % South Pole
GMAT ShackletonCrater.Location2 = 0.0;
GMAT ShackletonCrater.Location3 = 0.0;
GMAT ShackletonCrater.OrbitColor = 'Orange';

%----------------------------------------
%---------- ForceModels & Propagators
%----------------------------------------

Create ForceModel LunarProp_ForceModel;
GMAT LunarProp_ForceModel.CentralBody = Luna;
GMAT LunarProp_ForceModel.PrimaryBodies = {Luna};
GMAT LunarProp_ForceModel.PointMasses = {Earth, Sun};
GMAT LunarProp_ForceModel.GravityField.Luna.Degree = 8;
GMAT LunarProp_ForceModel.GravityField.Luna.Order = 8;
GMAT LunarProp_ForceModel.GravityField.Luna.PotentialFile = 'LP165P.cof';

Create Propagator LunarProp;
GMAT LunarProp.FM = LunarProp_ForceModel;
GMAT LunarProp.Type = RungeKutta89;

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

Create ContactLocator Sat1_To_Earth, Sat2_To_Earth, Shackleton_To_Sat1, Shackleton_To_Sat2;

% Contacts from Sat1 to Earth Stations
GMAT Sat1_To_Earth.Target = Sat1;
GMAT Sat1_To_Earth.Observers = {Houston, Sydney, Ireland};
GMAT Sat1_To_Earth.OccultingBodies = {Luna};
GMAT Sat1_To_Earth.Filename = '../output/Sat1_To_Earth_Contacts.txt';
GMAT Sat1_To_Earth.StepSize = 10.0;

% Contacts from Sat2 to Earth Stations
GMAT Sat2_To_Earth.Target = Sat2;
GMAT Sat2_To_Earth.Observers = {Houston, Sydney, Ireland};
GMAT Sat2_To_Earth.OccultingBodies = {Luna};
GMAT Sat2_To_Earth.Filename = '../output/Sat2_To_Earth_Contacts.txt';
GMAT Sat2_To_Earth.StepSize = 10.0;

% Contacts from Shackleton Crater to Sat1
GMAT Shackleton_To_Sat1.Target = Sat1;
GMAT Shackleton_To_Sat1.Observers = {ShackletonCrater};
GMAT Shackleton_To_Sat1.Filename = '../output/Shackleton_To_Sat1_Contacts.txt';
GMAT Shackleton_To_Sat1.StepSize = 10.0;

% Contacts from Shackleton Crater to Sat2
GMAT Shackleton_To_Sat2.Target = Sat2;
GMAT Shackleton_To_Sat2.Observers = {ShackletonCrater};
GMAT Shackleton_To_Sat2.Filename = '../output/Shackleton_To_Sat2_Contacts.txt';
GMAT Shackleton_To_Sat2.StepSize = 10.0;

%----------------------------------------
%---------- Subscribers (Visualizers)
%----------------------------------------

% A Moon-centered view to see the satellite orbits clearly
Create OrbitView MoonView;
GMAT MoonView.SolverIterations = Current;
GMAT MoonView.UpperLeft = [0.0, 0.0];
GMAT MoonView.Size = [0.5, 1.0];
GMAT MoonView.Add = {Sat1, Sat2, ShackletonCrater, Luna, Earth};
GMAT MoonView.CoordinateSystem = MoonMJ2000Eq;
GMAT MoonView.ViewPointReference = Luna;
GMAT MoonView.ViewPointVector = [0, 40000, 0]; % View from the side
GMAT MoonView.ViewDirection = Luna;
GMAT MoonView.ViewUpAxis = Z;
GMAT MoonView.Axes = On;
GMAT MoonView.XYPlane = On;

% A view centered on the Earth-Moon barycenter to see the whole system
Create OrbitView BarycenterView;
GMAT BarycenterView.SolverIterations = Current;
GMAT BarycenterView.UpperLeft = [0.5, 0.0];
GMAT BarycenterView.Size = [0.5, 1.0];
GMAT BarycenterView.Add = {Sat1, Sat2, Earth, Luna, EarthMoonBarycenter};
GMAT BarycenterView.CoordinateSystem = BarycenterMJ2000Eq;
GMAT BarycenterView.ViewPointReference = EarthMoonBarycenter;
GMAT BarycenterView.ViewPointVector = [0, 0, 600000]; % Top-down view
GMAT BarycenterView.ViewDirection = EarthMoonBarycenter;
GMAT BarycenterView.ViewUpAxis = Y;
GMAT BarycenterView.Axes = On;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;
Propagate Synchronized LunarProp(Sat1, Sat2) {Sat1.ElapsedDays = 1.0}; 