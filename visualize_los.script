%----------------------------------------
% GMAT Mission Script - Line-of-Sight (LOS) Visualization
%
% Author: AI Assistant
% Date: 2024-07-30
%
% This script is for VISUALIZATION ONLY and does not perform contact analysis.
% It uses "dummy" spacecraft to draw lines representing the communication
% links between real objects at a specific moment in time.
%
% INSTRUCTIONS:
% 1. Run `final_contact_analysis.script` to generate the contact reports
%    in the 'output' directory.
% 2. Open a contact report (e.g., 'output/HLS_to_SV1_Contacts.txt').
% 3. Select a specific time from within a contact event.
% 4. In THIS script, set the 'Epoch' for SV1 and SV2 to your chosen time.
%    The script will automatically handle the epochs for all other objects.
% 5. Run this script to see the line-of-sight links at that specific moment.
%----------------------------------------

%----------------------------------------
%---------- Spacecraft Configuration
%----------------------------------------

% Create the real spacecraft
Create Spacecraft SV1, SV2;

% SV1 Configuration
GMAT SV1.DateFormat            = UTCGregorian;
GMAT SV1.Epoch                 = '01 Jan 2000 12:00:00.000'; % <-- SET THIS TIME
GMAT SV1.CoordinateSystem      = LunarMJ2000Eq;
GMAT SV1.DisplayStateType      = Keplerian;
GMAT SV1.SMA                   = 2037.4;
GMAT SV1.ECC                   = 0.0;
GMAT SV1.INC                   = 90.0;
GMAT SV1.RAAN                  = 0.0;
GMAT SV1.AOP                   = 0.0;
GMAT SV1.TA                    = 0.0;
GMAT SV1.OrbitColor            = 'Blue';

% SV2 Configuration
GMAT SV2.DateFormat            = UTCGregorian;
GMAT SV2.Epoch                 = '01 Jan 2000 12:00:00.000'; % <-- AND THIS TIME
GMAT SV2.CoordinateSystem      = LunarMJ2000Eq;
GMAT SV2.DisplayStateType      = Keplerian;
GMAT SV2.SMA                   = 2037.4;
GMAT SV2.ECC                   = 0.0;
GMAT SV2.INC                   = 90.0;
GMAT SV2.RAAN                  = 180.0;
GMAT SV2.AOP                   = 0.0;
GMAT SV2.TA                    = 180.0;
GMAT SV2.OrbitColor            = 'Magenta';

% Create "dummy" spacecraft to draw LOS lines
Create Spacecraft LOS_HLS_SV1, LOS_HLS_SV2, LOS_SV1_LEGS, LOS_SV2_LEGS;

% Dummy spacecraft will be configured in the mission sequence.
% Set their orbit color here for clarity.
GMAT LOS_HLS_SV1.OrbitColor = 'Yellow';
GMAT LOS_HLS_SV2.OrbitColor = 'Yellow';
GMAT LOS_SV1_LEGS.OrbitColor = 'Cyan';
GMAT LOS_SV2_LEGS.OrbitColor = 'Cyan';

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------
Create CoordinateSystem LunarMJ2000Eq;
GMAT LunarMJ2000Eq.Origin      = Luna;
GMAT LunarMJ2000Eq.Axes        = MJ2000Eq;

%----------------------------------------
%---------- Ground Stations
%----------------------------------------
Create GroundStation LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland, HLS_Station;

% LEGS1 - Ohio, USA
GMAT LEGS1_Ohio.CentralBody        = Earth;
GMAT LEGS1_Ohio.StateType          = Spherical;
GMAT LEGS1_Ohio.Location1          = 40.4173;
GMAT LEGS1_Ohio.Location2          = 277.0147;
GMAT LEGS1_Ohio.Location3          = 0.260;

% LEGS2 - Sydney, Australia
GMAT LEGS2_Sydney.CentralBody      = Earth;
GMAT LEGS2_Sydney.StateType        = Spherical;
GMAT LEGS2_Sydney.Location1        = -33.8688;
GMAT LEGS2_Sydney.Location2        = 151.2093;
GMAT LEGS2_Sydney.Location3        = 0.058;

% LEGS3 - Dublin, Ireland
GMAT LEGS3_Ireland.CentralBody     = Earth;
GMAT LEGS3_Ireland.StateType       = Spherical;
GMAT LEGS3_Ireland.Location1       = 53.3498;
GMAT LEGS3_Ireland.Location2       = 353.7603;
GMAT LEGS3_Ireland.Location3       = 0.047;

% Lunar Ground Station
GMAT HLS_Station.CentralBody       = Luna;
GMAT HLS_Station.StateType         = Spherical;
GMAT HLS_Station.Location1         = -89.0;
GMAT HLS_Station.Location2         = 0.0;
GMAT HLS_Station.Location3         = 0.0;

%----------------------------------------
%---------- Propagator for Visualization
%----------------------------------------
Create Propagator VizProp;
Create ForceModel VizFM;
GMAT VizProp.FM = VizFM;

%----------------------------------------
%---------- Visualizers
%----------------------------------------
Create OrbitView LOS_View;
GMAT LOS_View.SolverIterations      = Current;
GMAT LOS_View.UpperLeft             = [0.0, 0.0];
GMAT LOS_View.Size                  = [1.0, 1.0];
GMAT LOS_View.Add = {SV1, SV2, HLS_Station, LEGS1_Ohio, LEGS2_Sydney, LEGS3_Ireland, Earth, Luna, LOS_HLS_SV1, LOS_HLS_SV2, LOS_SV1_LEGS, LOS_SV2_LEGS};
GMAT LOS_View.CoordinateSystem      = EarthMJ2000Eq;
GMAT LOS_View.ViewPointReference    = Earth;
GMAT LOS_View.ViewPointVector       = [0, -600000, 300000];
GMAT LOS_View.ViewUpAxis            = Z;
GMAT LOS_View.ShowPlot              = true;

%========================================
%---------- Mission Sequence
%========================================
BeginMissionSequence;

% --- Configure Dummy Spacecraft for HLS to SV1/SV2 links ---
% Set the dummy spacecraft to the position of the HLS station
GMAT LOS_HLS_SV1.Epoch = SV1.Epoch;
GMAT LOS_HLS_SV1.CoordinateSystem = HLS_Station.CoordinateSystem;
GMAT LOS_HLS_SV1.X = HLS_Station.X;
GMAT LOS_HLS_SV1.Y = HLS_Station.Y;
GMAT LOS_HLS_SV1.Z = HLS_Station.Z;
GMAT LOS_HLS_SV2.Epoch = SV2.Epoch;
GMAT LOS_HLS_SV2.CoordinateSystem = HLS_Station.CoordinateSystem;
GMAT LOS_HLS_SV2.X = HLS_Station.X;
GMAT LOS_HLS_SV2.Y = HLS_Station.Y;
GMAT LOS_HLS_SV2.Z = HLS_Station.Z;

% Set velocity vector to point from HLS to the target spacecraft
% The large multiplier creates a long line for visualization.
GMAT LOS_HLS_SV1.VX = (SV1.X - HLS_Station.X) * 1000;
GMAT LOS_HLS_SV1.VY = (SV1.Y - HLS_Station.Y) * 1000;
GMAT LOS_HLS_SV1.VZ = (SV1.Z - HLS_Station.Z) * 1000;
GMAT LOS_HLS_SV2.VX = (SV2.X - HLS_Station.X) * 1000;
GMAT LOS_HLS_SV2.VY = (SV2.Y - HLS_Station.Y) * 1000;
GMAT LOS_HLS_SV2.VZ = (SV2.Z - HLS_Station.Z) * 1000;


% --- Configure Dummy Spacecraft for SV1/SV2 to LEGS links ---
% For simplicity, we use one LEGS station as the anchor.
% Set the dummy spacecraft to the position of the real spacecraft
GMAT LOS_SV1_LEGS.Epoch = SV1.Epoch;
GMAT LOS_SV1_LEGS.CoordinateSystem = SV1.CoordinateSystem;
GMAT LOS_SV1_LEGS.X = SV1.X;
GMAT LOS_SV1_LEGS.Y = SV1.Y;
GMAT LOS_SV1_LEGS.Z = SV1.Z;
GMAT LOS_SV2_LEGS.Epoch = SV2.Epoch;
GMAT LOS_SV2_LEGS.CoordinateSystem = SV2.CoordinateSystem;
GMAT LOS_SV2_LEGS.X = SV2.X;
GMAT LOS_SV2_LEGS.Y = SV2.Y;
GMAT LOS_SV2_LEGS.Z = SV2.Z;

% Set velocity vector to point from the spacecraft to the Earth station
GMAT LOS_SV1_LEGS.VX = (LEGS1_Ohio.X - SV1.X) * 1000;
GMAT LOS_SV1_LEGS.VY = (LEGS1_Ohio.Y - SV1.Y) * 1000;
GMAT LOS_SV1_LEGS.VZ = (LEGS1_Ohio.Z - SV1.Z) * 1000;
GMAT LOS_SV2_LEGS.VX = (LEGS1_Ohio.X - SV2.X) * 1000;
GMAT LOS_SV2_LEGS.VY = (LEGS1_Ohio.Y - SV2.Y) * 1000;
GMAT LOS_SV2_LEGS.VZ = (LEGS1_Ohio.Z - SV2.Z) * 1000;

% Propagate the dummy spacecraft for a very short duration to draw the lines
Propagate Synchronized VizProp(LOS_HLS_SV1, LOS_HLS_SV2, LOS_SV1_LEGS, LOS_SV2_LEGS) {LOS_HLS_SV1.ElapsedSecs = 1.0}; 