# This file was automatically generated by SWIG (http://www.swig.org).
# Version 4.0.2
#
# Do not make changes to this file unless you know what you are doing--modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info
if _swig_python_version_info < (2, 7, 0):
    raise RuntimeError("Python 2.7 or later required")

# Import the low-level C/C++ module
if __package__ or "." in __name__:
    from . import _station_py
else:
    import _station_py

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__

def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "thisown":
            self.this.own(value)
        elif name == "this":
            set(self, name, value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)
    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)
    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""
    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())
    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""
    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


import gmat_py
class GroundStation(gmat_py.GroundstationInterface):
    r"""Defines the Groundstation class used to model ground based tracking stations."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _station_py.delete_GroundStation

    def __init__(self, *args):
        _station_py.GroundStation_swiginit(self, _station_py.new_GroundStation(*args))

    def Clone(self):
        return _station_py.GroundStation_Clone(self)

    def Copy(self, orig):
        return _station_py.GroundStation_Copy(self, orig)

    def GetParameterText(self, id):
        return _station_py.GroundStation_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _station_py.GroundStation_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _station_py.GroundStation_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _station_py.GroundStation_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _station_py.GroundStation_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _station_py.GroundStation_IsParameterReadOnly(self, *args)

    def GetStringParameter(self, *args):
        return _station_py.GroundStation_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _station_py.GroundStation_SetStringParameter(self, *args)

    def GetStringArrayParameter(self, *args):
        return _station_py.GroundStation_GetStringArrayParameter(self, *args)

    def GetRealParameter(self, *args):
        return _station_py.GroundStation_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _station_py.GroundStation_SetRealParameter(self, *args)

    def RenameRefObject(self, type, oldName, newName):
        return _station_py.GroundStation_RenameRefObject(self, type, oldName, newName)

    def GetRefObjectNameArray(self, type):
        return _station_py.GroundStation_GetRefObjectNameArray(self, type)

    def GetRefObject(self, type, name):
        return _station_py.GroundStation_GetRefObject(self, type, name)

    def SetRefObject(self, *args):
        return _station_py.GroundStation_SetRefObject(self, *args)

    def GetRefObjectArray(self, *args):
        return _station_py.GroundStation_GetRefObjectArray(self, *args)

    def HasRefObjectTypeArray(self):
        return _station_py.GroundStation_HasRefObjectTypeArray(self)

    def GetRefObjectTypeArray(self):
        return _station_py.GroundStation_GetRefObjectTypeArray(self)

    def Initialize(self):
        return _station_py.GroundStation_Initialize(self)

    def IsEstimationParameterValid(self, id):
        return _station_py.GroundStation_IsEstimationParameterValid(self, id)

    def GetEstimationParameterSize(self, id):
        return _station_py.GroundStation_GetEstimationParameterSize(self, id)

    def GetEstimationParameterValue(self, id):
        return _station_py.GroundStation_GetEstimationParameterValue(self, id)

    def IsValidID(self, id):
        return _station_py.GroundStation_IsValidID(self, id)

    def IsValidElevationAngle(self, state_sez):
        return _station_py.GroundStation_IsValidElevationAngle(self, state_sez)

    def CreateErrorModelForSignalPath(self, spacecraftName, spacecraftId):
        return _station_py.GroundStation_CreateErrorModelForSignalPath(self, spacecraftName, spacecraftId)

    def GetErrorModelMap(self):
        return _station_py.GroundStation_GetErrorModelMap(self)

    def HasLocalClones(self):
        return _station_py.GroundStation_HasLocalClones(self)
    STATION_ID = _station_py.GroundStation_STATION_ID
    ADD_HARDWARE = _station_py.GroundStation_ADD_HARDWARE
    IONOSPHERE_MODEL = _station_py.GroundStation_IONOSPHERE_MODEL
    TROPOSPHERE_MODEL = _station_py.GroundStation_TROPOSPHERE_MODEL
    DATA_SOURCE = _station_py.GroundStation_DATA_SOURCE
    TEMPERATURE = _station_py.GroundStation_TEMPERATURE
    PRESSURE = _station_py.GroundStation_PRESSURE
    HUMIDITY = _station_py.GroundStation_HUMIDITY
    MINIMUM_ELEVATION_ANGLE = _station_py.GroundStation_MINIMUM_ELEVATION_ANGLE
    ERROR_MODELS = _station_py.GroundStation_ERROR_MODELS
    MASK_FILENAME = _station_py.GroundStation_MASK_FILENAME
    GroundStationParamCount = _station_py.GroundStation_GroundStationParamCount

    @staticmethod
    def SetClass(base):
        return _station_py.GroundStation_SetClass(base)

# Register GroundStation in _station_py:
_station_py.GroundStation_swigregister(GroundStation)

def GroundStation_SetClass(base):
    return _station_py.GroundStation_SetClass(base)



