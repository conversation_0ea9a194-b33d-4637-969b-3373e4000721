# This file was automatically generated by SWIG (http://www.swig.org).
# Version 4.0.2
#
# Do not make changes to this file unless you know what you are doing--modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info
if _swig_python_version_info < (2, 7, 0):
    raise RuntimeError("Python 2.7 or later required")

# Import the low-level C/C++ module
if __package__ or "." in __name__:
    from . import _navigation_py
else:
    import _navigation_py

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__

def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "thisown":
            self.this.own(value)
        elif name == "this":
            set(self, name, value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)
    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)
    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""
    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())
    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""
    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


import gmat_py
class RampTableDataVector(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return _navigation_py.RampTableDataVector_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return _navigation_py.RampTableDataVector___nonzero__(self)

    def __bool__(self):
        return _navigation_py.RampTableDataVector___bool__(self)

    def __len__(self):
        return _navigation_py.RampTableDataVector___len__(self)

    def __getslice__(self, i, j):
        return _navigation_py.RampTableDataVector___getslice__(self, i, j)

    def __setslice__(self, *args):
        return _navigation_py.RampTableDataVector___setslice__(self, *args)

    def __delslice__(self, i, j):
        return _navigation_py.RampTableDataVector___delslice__(self, i, j)

    def __delitem__(self, *args):
        return _navigation_py.RampTableDataVector___delitem__(self, *args)

    def __getitem__(self, *args):
        return _navigation_py.RampTableDataVector___getitem__(self, *args)

    def __setitem__(self, *args):
        return _navigation_py.RampTableDataVector___setitem__(self, *args)

    def pop(self):
        return _navigation_py.RampTableDataVector_pop(self)

    def append(self, x):
        return _navigation_py.RampTableDataVector_append(self, x)

    def empty(self):
        return _navigation_py.RampTableDataVector_empty(self)

    def size(self):
        return _navigation_py.RampTableDataVector_size(self)

    def swap(self, v):
        return _navigation_py.RampTableDataVector_swap(self, v)

    def begin(self):
        return _navigation_py.RampTableDataVector_begin(self)

    def end(self):
        return _navigation_py.RampTableDataVector_end(self)

    def rbegin(self):
        return _navigation_py.RampTableDataVector_rbegin(self)

    def rend(self):
        return _navigation_py.RampTableDataVector_rend(self)

    def clear(self):
        return _navigation_py.RampTableDataVector_clear(self)

    def get_allocator(self):
        return _navigation_py.RampTableDataVector_get_allocator(self)

    def pop_back(self):
        return _navigation_py.RampTableDataVector_pop_back(self)

    def erase(self, *args):
        return _navigation_py.RampTableDataVector_erase(self, *args)

    def __init__(self, *args):
        _navigation_py.RampTableDataVector_swiginit(self, _navigation_py.new_RampTableDataVector(*args))

    def push_back(self, x):
        return _navigation_py.RampTableDataVector_push_back(self, x)

    def front(self):
        return _navigation_py.RampTableDataVector_front(self)

    def back(self):
        return _navigation_py.RampTableDataVector_back(self)

    def assign(self, n, x):
        return _navigation_py.RampTableDataVector_assign(self, n, x)

    def resize(self, *args):
        return _navigation_py.RampTableDataVector_resize(self, *args)

    def insert(self, *args):
        return _navigation_py.RampTableDataVector_insert(self, *args)

    def reserve(self, n):
        return _navigation_py.RampTableDataVector_reserve(self, n)

    def capacity(self):
        return _navigation_py.RampTableDataVector_capacity(self)
    __swig_destroy__ = _navigation_py.delete_RampTableDataVector

# Register RampTableDataVector in _navigation_py:
_navigation_py.RampTableDataVector_swigregister(RampTableDataVector)

class TrackingDataAdapterVector(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return _navigation_py.TrackingDataAdapterVector_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return _navigation_py.TrackingDataAdapterVector___nonzero__(self)

    def __bool__(self):
        return _navigation_py.TrackingDataAdapterVector___bool__(self)

    def __len__(self):
        return _navigation_py.TrackingDataAdapterVector___len__(self)

    def __getslice__(self, i, j):
        return _navigation_py.TrackingDataAdapterVector___getslice__(self, i, j)

    def __setslice__(self, *args):
        return _navigation_py.TrackingDataAdapterVector___setslice__(self, *args)

    def __delslice__(self, i, j):
        return _navigation_py.TrackingDataAdapterVector___delslice__(self, i, j)

    def __delitem__(self, *args):
        return _navigation_py.TrackingDataAdapterVector___delitem__(self, *args)

    def __getitem__(self, *args):
        return _navigation_py.TrackingDataAdapterVector___getitem__(self, *args)

    def __setitem__(self, *args):
        return _navigation_py.TrackingDataAdapterVector___setitem__(self, *args)

    def pop(self):
        return _navigation_py.TrackingDataAdapterVector_pop(self)

    def append(self, x):
        return _navigation_py.TrackingDataAdapterVector_append(self, x)

    def empty(self):
        return _navigation_py.TrackingDataAdapterVector_empty(self)

    def size(self):
        return _navigation_py.TrackingDataAdapterVector_size(self)

    def swap(self, v):
        return _navigation_py.TrackingDataAdapterVector_swap(self, v)

    def begin(self):
        return _navigation_py.TrackingDataAdapterVector_begin(self)

    def end(self):
        return _navigation_py.TrackingDataAdapterVector_end(self)

    def rbegin(self):
        return _navigation_py.TrackingDataAdapterVector_rbegin(self)

    def rend(self):
        return _navigation_py.TrackingDataAdapterVector_rend(self)

    def clear(self):
        return _navigation_py.TrackingDataAdapterVector_clear(self)

    def get_allocator(self):
        return _navigation_py.TrackingDataAdapterVector_get_allocator(self)

    def pop_back(self):
        return _navigation_py.TrackingDataAdapterVector_pop_back(self)

    def erase(self, *args):
        return _navigation_py.TrackingDataAdapterVector_erase(self, *args)

    def __init__(self, *args):
        _navigation_py.TrackingDataAdapterVector_swiginit(self, _navigation_py.new_TrackingDataAdapterVector(*args))

    def push_back(self, x):
        return _navigation_py.TrackingDataAdapterVector_push_back(self, x)

    def front(self):
        return _navigation_py.TrackingDataAdapterVector_front(self)

    def back(self):
        return _navigation_py.TrackingDataAdapterVector_back(self)

    def assign(self, n, x):
        return _navigation_py.TrackingDataAdapterVector_assign(self, n, x)

    def resize(self, *args):
        return _navigation_py.TrackingDataAdapterVector_resize(self, *args)

    def insert(self, *args):
        return _navigation_py.TrackingDataAdapterVector_insert(self, *args)

    def reserve(self, n):
        return _navigation_py.TrackingDataAdapterVector_reserve(self, n)

    def capacity(self):
        return _navigation_py.TrackingDataAdapterVector_capacity(self)
    __swig_destroy__ = _navigation_py.delete_TrackingDataAdapterVector

# Register TrackingDataAdapterVector in _navigation_py:
_navigation_py.TrackingDataAdapterVector_swigregister(TrackingDataAdapterVector)

class SignalDataVector(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return _navigation_py.SignalDataVector_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return _navigation_py.SignalDataVector___nonzero__(self)

    def __bool__(self):
        return _navigation_py.SignalDataVector___bool__(self)

    def __len__(self):
        return _navigation_py.SignalDataVector___len__(self)

    def __getslice__(self, i, j):
        return _navigation_py.SignalDataVector___getslice__(self, i, j)

    def __setslice__(self, *args):
        return _navigation_py.SignalDataVector___setslice__(self, *args)

    def __delslice__(self, i, j):
        return _navigation_py.SignalDataVector___delslice__(self, i, j)

    def __delitem__(self, *args):
        return _navigation_py.SignalDataVector___delitem__(self, *args)

    def __getitem__(self, *args):
        return _navigation_py.SignalDataVector___getitem__(self, *args)

    def __setitem__(self, *args):
        return _navigation_py.SignalDataVector___setitem__(self, *args)

    def pop(self):
        return _navigation_py.SignalDataVector_pop(self)

    def append(self, x):
        return _navigation_py.SignalDataVector_append(self, x)

    def empty(self):
        return _navigation_py.SignalDataVector_empty(self)

    def size(self):
        return _navigation_py.SignalDataVector_size(self)

    def swap(self, v):
        return _navigation_py.SignalDataVector_swap(self, v)

    def begin(self):
        return _navigation_py.SignalDataVector_begin(self)

    def end(self):
        return _navigation_py.SignalDataVector_end(self)

    def rbegin(self):
        return _navigation_py.SignalDataVector_rbegin(self)

    def rend(self):
        return _navigation_py.SignalDataVector_rend(self)

    def clear(self):
        return _navigation_py.SignalDataVector_clear(self)

    def get_allocator(self):
        return _navigation_py.SignalDataVector_get_allocator(self)

    def pop_back(self):
        return _navigation_py.SignalDataVector_pop_back(self)

    def erase(self, *args):
        return _navigation_py.SignalDataVector_erase(self, *args)

    def __init__(self, *args):
        _navigation_py.SignalDataVector_swiginit(self, _navigation_py.new_SignalDataVector(*args))

    def push_back(self, x):
        return _navigation_py.SignalDataVector_push_back(self, x)

    def front(self):
        return _navigation_py.SignalDataVector_front(self)

    def back(self):
        return _navigation_py.SignalDataVector_back(self)

    def assign(self, n, x):
        return _navigation_py.SignalDataVector_assign(self, n, x)

    def resize(self, *args):
        return _navigation_py.SignalDataVector_resize(self, *args)

    def insert(self, *args):
        return _navigation_py.SignalDataVector_insert(self, *args)

    def reserve(self, n):
        return _navigation_py.SignalDataVector_reserve(self, n)

    def capacity(self):
        return _navigation_py.SignalDataVector_capacity(self)
    __swig_destroy__ = _navigation_py.delete_SignalDataVector

# Register SignalDataVector in _navigation_py:
_navigation_py.SignalDataVector_swigregister(SignalDataVector)

class SignalBaseVector(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def iterator(self):
        return _navigation_py.SignalBaseVector_iterator(self)
    def __iter__(self):
        return self.iterator()

    def __nonzero__(self):
        return _navigation_py.SignalBaseVector___nonzero__(self)

    def __bool__(self):
        return _navigation_py.SignalBaseVector___bool__(self)

    def __len__(self):
        return _navigation_py.SignalBaseVector___len__(self)

    def __getslice__(self, i, j):
        return _navigation_py.SignalBaseVector___getslice__(self, i, j)

    def __setslice__(self, *args):
        return _navigation_py.SignalBaseVector___setslice__(self, *args)

    def __delslice__(self, i, j):
        return _navigation_py.SignalBaseVector___delslice__(self, i, j)

    def __delitem__(self, *args):
        return _navigation_py.SignalBaseVector___delitem__(self, *args)

    def __getitem__(self, *args):
        return _navigation_py.SignalBaseVector___getitem__(self, *args)

    def __setitem__(self, *args):
        return _navigation_py.SignalBaseVector___setitem__(self, *args)

    def pop(self):
        return _navigation_py.SignalBaseVector_pop(self)

    def append(self, x):
        return _navigation_py.SignalBaseVector_append(self, x)

    def empty(self):
        return _navigation_py.SignalBaseVector_empty(self)

    def size(self):
        return _navigation_py.SignalBaseVector_size(self)

    def swap(self, v):
        return _navigation_py.SignalBaseVector_swap(self, v)

    def begin(self):
        return _navigation_py.SignalBaseVector_begin(self)

    def end(self):
        return _navigation_py.SignalBaseVector_end(self)

    def rbegin(self):
        return _navigation_py.SignalBaseVector_rbegin(self)

    def rend(self):
        return _navigation_py.SignalBaseVector_rend(self)

    def clear(self):
        return _navigation_py.SignalBaseVector_clear(self)

    def get_allocator(self):
        return _navigation_py.SignalBaseVector_get_allocator(self)

    def pop_back(self):
        return _navigation_py.SignalBaseVector_pop_back(self)

    def erase(self, *args):
        return _navigation_py.SignalBaseVector_erase(self, *args)

    def __init__(self, *args):
        _navigation_py.SignalBaseVector_swiginit(self, _navigation_py.new_SignalBaseVector(*args))

    def push_back(self, x):
        return _navigation_py.SignalBaseVector_push_back(self, x)

    def front(self):
        return _navigation_py.SignalBaseVector_front(self)

    def back(self):
        return _navigation_py.SignalBaseVector_back(self)

    def assign(self, n, x):
        return _navigation_py.SignalBaseVector_assign(self, n, x)

    def resize(self, *args):
        return _navigation_py.SignalBaseVector_resize(self, *args)

    def insert(self, *args):
        return _navigation_py.SignalBaseVector_insert(self, *args)

    def reserve(self, n):
        return _navigation_py.SignalBaseVector_reserve(self, n)

    def capacity(self):
        return _navigation_py.SignalBaseVector_capacity(self)
    __swig_destroy__ = _navigation_py.delete_SignalBaseVector

# Register SignalBaseVector in _navigation_py:
_navigation_py.SignalBaseVector_swigregister(SignalBaseVector)

class MeasurementData(object):
    r"""
    Definition of the MeasurementData class, used to manage calculated
    measurements

     The measurement data structure

     This class acts mostly as a structure which provides the core set of
     information used for calculated measurement data.  Each measurement can be
     captured in this structure.

     This struct presents as a class because it includes code for the assignment
     operator and copy constructor.  To me, it's just bad form to call something
     a struct and include those pieces.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_MeasurementData

    def CleanUp(self):
        return _navigation_py.MeasurementData_CleanUp(self)

    def __init__(self, *args):
        _navigation_py.MeasurementData_swiginit(self, _navigation_py.new_MeasurementData(*args))
    type = property(_navigation_py.MeasurementData_type_get, _navigation_py.MeasurementData_type_set, doc=r""" The type of measurement in this record""")
    typeName = property(_navigation_py.MeasurementData_typeName_get, _navigation_py.MeasurementData_typeName_set, doc=r""" String value for type of measurement in this record""")
    uniqueID = property(_navigation_py.MeasurementData_uniqueID_get, _navigation_py.MeasurementData_uniqueID_set, doc=r""" Unique ID for associated model.  This number can change from run to run.""")
    isPeriodic = property(_navigation_py.MeasurementData_isPeriodic_get, _navigation_py.MeasurementData_isPeriodic_set, doc=r""" Is measurement periodic, such as 0 to 360, requiring care to compute O - C""")
    minValue = property(_navigation_py.MeasurementData_minValue_get, _navigation_py.MeasurementData_minValue_set, doc=r""" For a periodic measurement, the minimum value (for Azimuth, this is 0, for XEast -180)""")
    period = property(_navigation_py.MeasurementData_period_get, _navigation_py.MeasurementData_period_set, doc=r""" For a periodic measurement, the value wraps at minValue + period (for Azimuth and XEast this is 360)""")
    epochSystem = property(_navigation_py.MeasurementData_epochSystem_get, _navigation_py.MeasurementData_epochSystem_set, doc=r""" Enumerated ID for the epoch time system""")
    epoch = property(_navigation_py.MeasurementData_epoch_get, _navigation_py.MeasurementData_epoch_set, doc=r""" The epoch of the measurement""")
    epochGT = property(_navigation_py.MeasurementData_epochGT_get, _navigation_py.MeasurementData_epochGT_set)
    participantIDs = property(_navigation_py.MeasurementData_participantIDs_get, _navigation_py.MeasurementData_participantIDs_set, doc=r""" Who is involved in the measurement.  First one is the "anchor" node""")
    sensorIDs = property(_navigation_py.MeasurementData_sensorIDs_get, _navigation_py.MeasurementData_sensorIDs_set, doc=r""" The id of sensor attached to participant in order to send or receive signal (use for GPS Point Solution)""")
    value = property(_navigation_py.MeasurementData_value_get, _navigation_py.MeasurementData_value_set, doc=r""" The measured value.  Array to handle more than one value, like AZ_EL""")
    correction = property(_navigation_py.MeasurementData_correction_get, _navigation_py.MeasurementData_correction_set, doc=r""" Corrections included in the measured value.  Array to handle more than one value, like AZ_EL""")
    rangeVecs = property(_navigation_py.MeasurementData_rangeVecs_get, _navigation_py.MeasurementData_rangeVecs_set, doc=r""" The range vectors for each signal segment""")
    rangeRateVecs = property(_navigation_py.MeasurementData_rangeRateVecs_get, _navigation_py.MeasurementData_rangeRateVecs_set, doc=r""" The range rate vector for each signal segment""")
    rangeRates = property(_navigation_py.MeasurementData_rangeRates_get, _navigation_py.MeasurementData_rangeRates_set, doc=r""" The range rate for each signal segment""")
    tBodies = property(_navigation_py.MeasurementData_tBodies_get, _navigation_py.MeasurementData_tBodies_set, doc=r""" The central body for each transmit signal segment""")
    rBodies = property(_navigation_py.MeasurementData_rBodies_get, _navigation_py.MeasurementData_rBodies_set, doc=r""" The central body for each receive signal segment""")
    tCSOrigins = property(_navigation_py.MeasurementData_tCSOrigins_get, _navigation_py.MeasurementData_tCSOrigins_set, doc=r""" The origin of coordinate system for each transmit signal segment""")
    rCSOrigins = property(_navigation_py.MeasurementData_rCSOrigins_get, _navigation_py.MeasurementData_rCSOrigins_set, doc=r""" The origin of coordinate system for each receive signal segment""")
    tPrecTimes = property(_navigation_py.MeasurementData_tPrecTimes_get, _navigation_py.MeasurementData_tPrecTimes_set, doc=r""" The transmit time for each signal segment""")
    rPrecTimes = property(_navigation_py.MeasurementData_rPrecTimes_get, _navigation_py.MeasurementData_rPrecTimes_set, doc=r""" The receive time for each signal segment""")
    tLocs = property(_navigation_py.MeasurementData_tLocs_get, _navigation_py.MeasurementData_tLocs_set, doc=r""" The transmit location for each signal segment""")
    rLocs = property(_navigation_py.MeasurementData_rLocs_get, _navigation_py.MeasurementData_rLocs_set, doc=r""" The receive location for each signal segment""")
    valueInTime = property(_navigation_py.MeasurementData_valueInTime_get, _navigation_py.MeasurementData_valueInTime_set, doc=r""" The measured value present in travel time without noise and bias""")
    isFeasible = property(_navigation_py.MeasurementData_isFeasible_get, _navigation_py.MeasurementData_isFeasible_set, doc=r""" Flag indicating if the measurement could be made when it was attempted""")
    unfeasibleReason = property(_navigation_py.MeasurementData_unfeasibleReason_get, _navigation_py.MeasurementData_unfeasibleReason_set, doc=r""" Flag to indicate unfeasible reason""")
    feasibilityValue = property(_navigation_py.MeasurementData_feasibilityValue_get, _navigation_py.MeasurementData_feasibilityValue_set, doc=r""" Value used for root finding""")
    covariance = property(_navigation_py.MeasurementData_covariance_get, _navigation_py.MeasurementData_covariance_set, doc=r""" Measurement error covariance matrix""")
    eventCount = property(_navigation_py.MeasurementData_eventCount_get, _navigation_py.MeasurementData_eventCount_set, doc=r""" Number of events associated with this measurement""")
    uplinkBand = property(_navigation_py.MeasurementData_uplinkBand_get, _navigation_py.MeasurementData_uplinkBand_set, doc=r""" Uplink band""")
    downlinkBand = property(_navigation_py.MeasurementData_downlinkBand_get, _navigation_py.MeasurementData_downlinkBand_set, doc=r""" Downlink band of the frequency at receiver""")
    uplinkFreq = property(_navigation_py.MeasurementData_uplinkFreq_get, _navigation_py.MeasurementData_uplinkFreq_set, doc=r""" Uplink frequency""")
    uplinkFreqAtRecei = property(_navigation_py.MeasurementData_uplinkFreqAtRecei_get, _navigation_py.MeasurementData_uplinkFreqAtRecei_set, doc=r""" Uplink frequency at received epoch""")
    rangeModulo = property(_navigation_py.MeasurementData_rangeModulo_get, _navigation_py.MeasurementData_rangeModulo_set, doc=r""" Range modulo""")
    transmitDelay = property(_navigation_py.MeasurementData_transmitDelay_get, _navigation_py.MeasurementData_transmitDelay_set, doc=r"""Transmit delay, only used for DSN_PNRange for now""")
    receiveDelay = property(_navigation_py.MeasurementData_receiveDelay_get, _navigation_py.MeasurementData_receiveDelay_set, doc=r"""Receive delay, only used for DSN_PNRange for now""")
    dopplerCountInterval = property(_navigation_py.MeasurementData_dopplerCountInterval_get, _navigation_py.MeasurementData_dopplerCountInterval_set)
    tdrsNode4Freq = property(_navigation_py.MeasurementData_tdrsNode4Freq_get, _navigation_py.MeasurementData_tdrsNode4Freq_set)
    tdrsNode4Band = property(_navigation_py.MeasurementData_tdrsNode4Band_get, _navigation_py.MeasurementData_tdrsNode4Band_set)
    tdrsServiceID = property(_navigation_py.MeasurementData_tdrsServiceID_get, _navigation_py.MeasurementData_tdrsServiceID_set)
    tdrsSMARID = property(_navigation_py.MeasurementData_tdrsSMARID_get, _navigation_py.MeasurementData_tdrsSMARID_set)
    tdrsDataFlag = property(_navigation_py.MeasurementData_tdrsDataFlag_get, _navigation_py.MeasurementData_tdrsDataFlag_set)
    dopplerCountIntervalTDRSRef = property(_navigation_py.MeasurementData_dopplerCountIntervalTDRSRef_get, _navigation_py.MeasurementData_dopplerCountIntervalTDRSRef_set)
    tdrsNode4FreqTDRSRef = property(_navigation_py.MeasurementData_tdrsNode4FreqTDRSRef_get, _navigation_py.MeasurementData_tdrsNode4FreqTDRSRef_set)
    tdrsNode4BandTDRSRef = property(_navigation_py.MeasurementData_tdrsNode4BandTDRSRef_get, _navigation_py.MeasurementData_tdrsNode4BandTDRSRef_set)
    tdrsServiceIDTDRSRef = property(_navigation_py.MeasurementData_tdrsServiceIDTDRSRef_get, _navigation_py.MeasurementData_tdrsServiceIDTDRSRef_set)
    tdrsSMARIDTDRSRef = property(_navigation_py.MeasurementData_tdrsSMARIDTDRSRef_get, _navigation_py.MeasurementData_tdrsSMARIDTDRSRef_set)
    tdrsDataFlagTDRSRef = property(_navigation_py.MeasurementData_tdrsDataFlagTDRSRef_get, _navigation_py.MeasurementData_tdrsDataFlagTDRSRef_set)
    uplinkBandTDRSRef = property(_navigation_py.MeasurementData_uplinkBandTDRSRef_get, _navigation_py.MeasurementData_uplinkBandTDRSRef_set)
    uplinkFreqTDRSRef = property(_navigation_py.MeasurementData_uplinkFreqTDRSRef_get, _navigation_py.MeasurementData_uplinkFreqTDRSRef_set)
    uplinkFreqAtReceiTDRSRef = property(_navigation_py.MeasurementData_uplinkFreqAtReceiTDRSRef_get, _navigation_py.MeasurementData_uplinkFreqAtReceiTDRSRef_set)
    horpHeight = property(_navigation_py.MeasurementData_horpHeight_get, _navigation_py.MeasurementData_horpHeight_set)
    horpAngle = property(_navigation_py.MeasurementData_horpAngle_get, _navigation_py.MeasurementData_horpAngle_set)
    isTropoCorrectWarning = property(_navigation_py.MeasurementData_isTropoCorrectWarning_get, _navigation_py.MeasurementData_isTropoCorrectWarning_set)
    isIonoCorrectWarning = property(_navigation_py.MeasurementData_isIonoCorrectWarning_get, _navigation_py.MeasurementData_isIonoCorrectWarning_set)
    tropoCorrectRawValue = property(_navigation_py.MeasurementData_tropoCorrectRawValue_get, _navigation_py.MeasurementData_tropoCorrectRawValue_set)
    ionoCorrectRawValue = property(_navigation_py.MeasurementData_ionoCorrectRawValue_get, _navigation_py.MeasurementData_ionoCorrectRawValue_set)
    tropoCorrectValue = property(_navigation_py.MeasurementData_tropoCorrectValue_get, _navigation_py.MeasurementData_tropoCorrectValue_set)
    ionoCorrectValue = property(_navigation_py.MeasurementData_ionoCorrectValue_get, _navigation_py.MeasurementData_ionoCorrectValue_set)

# Register MeasurementData in _navigation_py:
_navigation_py.MeasurementData_swigregister(MeasurementData)

class MeasurementModelBase(gmat_py.GmatBase):
    r"""
    MeasurementModelBase declaration used in GMAT's estimator and simulator
    factory code

    Base class for measurement models and tracking file sets so the Factories
    can manage them as the same core type
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_MeasurementModelBase

    @staticmethod
    def SetClass(base):
        return _navigation_py.MeasurementModelBase_SetClass(base)

# Register MeasurementModelBase in _navigation_py:
_navigation_py.MeasurementModelBase_swigregister(MeasurementModelBase)

def MeasurementModelBase_SetClass(base):
    return _navigation_py.MeasurementModelBase_SetClass(base)

class MeasureModel(gmat_py.GmatBase):
    r"""
    The estimation measurement model

    This class is the reworked measurement model for GMAT's estimation subsystem.
    It uses the signal classes to model the path of a measurement.  The output
    resulting from the modeling is built by an Adapter that uses the raw data to
    generate measurement information.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_MeasureModel

    def CleanUp(self):
        return _navigation_py.MeasureModel_CleanUp(self)

    def __init__(self, *args):
        _navigation_py.MeasureModel_swiginit(self, _navigation_py.new_MeasureModel(*args))

    def Clone(self):
        return _navigation_py.MeasureModel_Clone(self)

    def SetSolarSystem(self, ss):
        return _navigation_py.MeasureModel_SetSolarSystem(self, ss)

    def GetParameterText(self, id):
        return _navigation_py.MeasureModel_GetParameterText(self, id)

    def GetParameterID(self, str):
        return _navigation_py.MeasureModel_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.MeasureModel_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.MeasureModel_GetParameterTypeString(self, id)

    def GetStringParameter(self, *args):
        return _navigation_py.MeasureModel_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.MeasureModel_SetStringParameter(self, *args)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.MeasureModel_GetStringArrayParameter(self, *args)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.MeasureModel_GetRefObjectNameArray(self, type)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.MeasureModel_RenameRefObject(self, type, oldName, newName)

    def SetRefObject(self, *args):
        return _navigation_py.MeasureModel_SetRefObject(self, *args)

    def HasLocalClones(self):
        return _navigation_py.MeasureModel_HasLocalClones(self)

    def SetPropagators(self, ps, spMap):
        return _navigation_py.MeasureModel_SetPropagators(self, ps, spMap)

    def SetTransientForces(self, tf):
        return _navigation_py.MeasureModel_SetTransientForces(self, tf)

    def Initialize(self):
        return _navigation_py.MeasureModel_Initialize(self)

    def CalculateMeasurement(self, withEvents=False, withMediaCorrection=True, forObservation=None, rampTB=None, forSimulation=False, atTimeOffset=0.0, forStrand=-1):
        return _navigation_py.MeasureModel_CalculateMeasurement(self, withEvents, withMediaCorrection, forObservation, rampTB, forSimulation, atTimeOffset, forStrand)

    def ReCalculateFrequencyAndMediaCorrection(self, pathIndex, uplinkFrequency, rampTB):
        return _navigation_py.MeasureModel_ReCalculateFrequencyAndMediaCorrection(self, pathIndex, uplinkFrequency, rampTB)

    def CalculateMeasurementDerivatives(self, obj, id, measTime=None, forRangeRate=False, forStrand=-1):
        return _navigation_py.MeasureModel_CalculateMeasurementDerivatives(self, obj, id, measTime, forRangeRate, forStrand)

    def GetSignalData(self):
        return _navigation_py.MeasureModel_GetSignalData(self)

    def GetSignalPaths(self):
        return _navigation_py.MeasureModel_GetSignalPaths(self)

    def CheckEpochValidity(self, theEpoch):
        return _navigation_py.MeasureModel_CheckEpochValidity(self, theEpoch)

    def IsMeasurementFeasible(self):
        return _navigation_py.MeasureModel_IsMeasurementFeasible(self)

    def UsesLightTime(self, tf):
        return _navigation_py.MeasureModel_UsesLightTime(self, tf)

    def SetCorrection(self, correctionName, correctionType):
        r""" Set measurement (relativity, ET-TAI, ionosphere, or troposphere) correction to measurement model"""
        return _navigation_py.MeasureModel_SetCorrection(self, correctionName, correctionType)

    def AddCorrection(self, correctionName, correctionType):
        return _navigation_py.MeasureModel_AddCorrection(self, correctionName, correctionType)

    def SetTimeTagFlag(self, flag):
        r""" Set flag to indicate measurement time tag is at the end of signal path"""
        return _navigation_py.MeasureModel_SetTimeTagFlag(self, flag)

    def GetTimeTagFlag(self):
        r""" Get flag to indicate measurement time tag"""
        return _navigation_py.MeasureModel_GetTimeTagFlag(self)

    def GetUplinkFrequency(self, *args):
        r"""
        *Overload 1:*
        Get uplink frequency and uplink band for a given signal path

        |

        *Overload 2:*
        Get uplink frequency and uplink band for a given signal path, these are used if GMD data is required
        """
        return _navigation_py.MeasureModel_GetUplinkFrequency(self, *args)

    def GetUplinkFrequencyAtReceivedEpoch(self, *args):
        return _navigation_py.MeasureModel_GetUplinkFrequencyAtReceivedEpoch(self, *args)

    def GetUplinkFrequencyBand(self, *args):
        return _navigation_py.MeasureModel_GetUplinkFrequencyBand(self, *args)

    def GetTransmitFrequency(self, atNode, pathIndex):
        r""" Get arrival frequency and frequency band at a given node. It is used in TDRS measurement to specify node 4 frequency and band"""
        return _navigation_py.MeasureModel_GetTransmitFrequency(self, atNode, pathIndex)

    def GetTransmitFrequencyBand(self, atNode, pathIndex):
        return _navigation_py.MeasureModel_GetTransmitFrequencyBand(self, atNode, pathIndex)

    def GetTransmitFrequencyOscillatorBias(self, atNode, pathIndex):
        return _navigation_py.MeasureModel_GetTransmitFrequencyOscillatorBias(self, atNode, pathIndex)

    def GetFrequencyMixerProperty(self, atNode, pathIndex):
        r""" Get frequency mixer's properties a and b from a node 4 TDRS spacecraft"""
        return _navigation_py.MeasureModel_GetFrequencyMixerProperty(self, atNode, pathIndex)

    def AddTransientForce(self, spacePoint, odeModel, propMan):
        return _navigation_py.MeasureModel_AddTransientForce(self, spacePoint, odeModel, propMan)

    def SetProgressReporter(self, reporter):
        r""" Measurement Model Settings"""
        return _navigation_py.MeasureModel_SetProgressReporter(self, reporter)

    def SetCountInterval(self, timeInterval):
        r""" Set value for Doppler count interval. It is used to calculate measurement for Start path"""
        return _navigation_py.MeasureModel_SetCountInterval(self, timeInterval)

    def GetParticipantObjectLists(self):
        r""" Get paticipant objects lists"""
        return _navigation_py.MeasureModel_GetParticipantObjectLists(self)

    def SaveState(self, precTimeVec, epochVec, epochGTVec, valsVec):
        r""" Save and restore states of objects being propagated"""
        return _navigation_py.MeasureModel_SaveState(self, precTimeVec, epochVec, epochGTVec, valsVec)

    def RestoreState(self, precTimeVec, epochVec, epochGTVec, valsVec):
        return _navigation_py.MeasureModel_RestoreState(self, precTimeVec, epochVec, epochGTVec, valsVec)

    def UseIonosphereCache(self, cache):
        r""" Uses ionosphere cache"""
        return _navigation_py.MeasureModel_UseIonosphereCache(self, cache)

    def IsCrossLink(self):
        return _navigation_py.MeasureModel_IsCrossLink(self)
    measParticipantIndex = property(_navigation_py.MeasureModel_measParticipantIndex_get, _navigation_py.MeasureModel_measParticipantIndex_set)

    @staticmethod
    def SetClass(base):
        return _navigation_py.MeasureModel_SetClass(base)

# Register MeasureModel in _navigation_py:
_navigation_py.MeasureModel_swigregister(MeasureModel)

def MeasureModel_SetClass(base):
    return _navigation_py.MeasureModel_SetClass(base)

class GmatData(object):
    r"""
    Class that contains GMAT data

    This class is essentially a struct designed to contain GMAT data
    retrieved from a data stream.

    Class used to set and retrieve GMAT data record.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_GmatData

    def Clear(self):
        r""" declare abstract functions:"""
        return _navigation_py.GmatData_Clear(self)
    dataFormat = property(_navigation_py.GmatData_dataFormat_get, _navigation_py.GmatData_dataFormat_set, doc=r''' Data format: one of "GMATInternal", "GMAT_OD", "GMAT_ODDoppler" or "GMAT_RampTable"''')

# Register GmatData in _navigation_py:
_navigation_py.GmatData_swigregister(GmatData)

class RampTableData(GmatData):
    r"""
    Class that contains ramp table record

    This class is essentially a struct designed to contain a record
    retrieved from a RampTableType object.

    Class used to set and retrieve data from ramp table
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_RampTableData

    def __init__(self, *args):
        _navigation_py.RampTableData_swiginit(self, _navigation_py.new_RampTableData(*args))

    def Clear(self):
        return _navigation_py.RampTableData_Clear(self)
    typeName = property(_navigation_py.RampTableData_typeName_get, _navigation_py.RampTableData_typeName_set, doc=r""" The text name of the data type, if available""")
    type = property(_navigation_py.RampTableData_type_get, _navigation_py.RampTableData_type_set, doc=r""" The type of measurement in this record""")
    epochSystem = property(_navigation_py.RampTableData_epochSystem_get, _navigation_py.RampTableData_epochSystem_set, doc=r""" Enumerated ID for the epoch time system""")
    epoch = property(_navigation_py.RampTableData_epoch_get, _navigation_py.RampTableData_epoch_set, doc=r""" The start epoch in a ramp""")
    epochGT = property(_navigation_py.RampTableData_epochGT_get, _navigation_py.RampTableData_epochGT_set)
    participantIDs = property(_navigation_py.RampTableData_participantIDs_get, _navigation_py.RampTableData_participantIDs_set, doc=r""" Who is involved in the measurement.  First one is the "anchor" node""")
    uplinkBand = property(_navigation_py.RampTableData_uplinkBand_get, _navigation_py.RampTableData_uplinkBand_set, doc=r""" Uplink band""")
    rampType = property(_navigation_py.RampTableData_rampType_get, _navigation_py.RampTableData_rampType_set, doc=r""" Ramp type""")
    rampFrequency = property(_navigation_py.RampTableData_rampFrequency_get, _navigation_py.RampTableData_rampFrequency_set, doc=r""" Ramp frequency""")
    rampRate = property(_navigation_py.RampTableData_rampRate_get, _navigation_py.RampTableData_rampRate_set, doc=r""" Ramp rate""")
    indexkey = property(_navigation_py.RampTableData_indexkey_get, _navigation_py.RampTableData_indexkey_set, doc=r""" Index key used for sorting records contains participantIDs and epoch""")

    @staticmethod
    def SetClass(base):
        return _navigation_py.RampTableData_SetClass(base)

# Register RampTableData in _navigation_py:
_navigation_py.RampTableData_swigregister(RampTableData)

def RampTableData_SetClass(base):
    return _navigation_py.RampTableData_SetClass(base)

BIASTYPE_IS_UNDEFINED = _navigation_py.BIASTYPE_IS_UNDEFINED
r"""
    Base class for the tracking data adapters

    The tracking data adapters convert the raw data computed in measurement
    models into the formatted data needed for the estimation and simulation
    processes.  For many measurement types, this conversion is a very lightweight
    process.

    The adapters also assemble the derivative data needed by these processes.
    """
BIASTYPE_IS_NONE = _navigation_py.BIASTYPE_IS_NONE
BIASTYPE_IS_BIAS = _navigation_py.BIASTYPE_IS_BIAS
BIASTYPE_IS_PASSBIAS = _navigation_py.BIASTYPE_IS_PASSBIAS
class TrackingDataAdapter(MeasurementModelBase):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_TrackingDataAdapter

    def SetSolarSystem(self, ss):
        return _navigation_py.TrackingDataAdapter_SetSolarSystem(self, ss)

    def GetParameterText(self, id):
        return _navigation_py.TrackingDataAdapter_GetParameterText(self, id)

    def GetParameterID(self, str):
        return _navigation_py.TrackingDataAdapter_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.TrackingDataAdapter_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.TrackingDataAdapter_GetParameterTypeString(self, id)

    def GetIntegerParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_GetIntegerParameter(self, *args)

    def SetIntegerParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_SetIntegerParameter(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_SetRealParameter(self, *args)

    def GetBooleanParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_GetBooleanParameter(self, *args)

    def SetBooleanParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_SetBooleanParameter(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_SetStringParameter(self, *args)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.TrackingDataAdapter_GetStringArrayParameter(self, *args)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.TrackingDataAdapter_GetRefObjectNameArray(self, type)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.TrackingDataAdapter_RenameRefObject(self, type, oldName, newName)

    def SetRefObject(self, *args):
        return _navigation_py.TrackingDataAdapter_SetRefObject(self, *args)

    def SetPropagators(self, ps, spMap):
        return _navigation_py.TrackingDataAdapter_SetPropagators(self, ps, spMap)

    def Initialize(self):
        return _navigation_py.TrackingDataAdapter_Initialize(self)

    def SetTransientForces(self, tf):
        return _navigation_py.TrackingDataAdapter_SetTransientForces(self, tf)

    def HasLocalClones(self):
        return _navigation_py.TrackingDataAdapter_HasLocalClones(self)

    def CalculateMeasurement(self, withEvents=False, forObservation=None, rampTB=None, forSimulation=False):
        return _navigation_py.TrackingDataAdapter_CalculateMeasurement(self, withEvents, forObservation, rampTB, forSimulation)

    def CalculateMeasurementDerivatives(self, obj, id):
        return _navigation_py.TrackingDataAdapter_CalculateMeasurementDerivatives(self, obj, id)

    def GetTrackingConfig(self):
        return _navigation_py.TrackingDataAdapter_GetTrackingConfig(self)

    def SetMeasurement(self, meas):
        return _navigation_py.TrackingDataAdapter_SetMeasurement(self, meas)

    def SetModelID(self, newID):
        return _navigation_py.TrackingDataAdapter_SetModelID(self, newID)

    def GetModelID(self):
        return _navigation_py.TrackingDataAdapter_GetModelID(self)

    def SetModelTypeID(self, theID, type, mult=1.0):
        return _navigation_py.TrackingDataAdapter_SetModelTypeID(self, theID, type, mult)

    def GetModelTypeID(self):
        return _navigation_py.TrackingDataAdapter_GetModelTypeID(self)

    def GetModelType(self):
        return _navigation_py.TrackingDataAdapter_GetModelType(self)

    def GetMeasurement(self):
        return _navigation_py.TrackingDataAdapter_GetMeasurement(self)

    def WriteMeasurements(self):
        return _navigation_py.TrackingDataAdapter_WriteMeasurements(self)

    def WriteMeasurement(self, id):
        return _navigation_py.TrackingDataAdapter_WriteMeasurement(self, id)

    def HasParameterCovariances(self, parameterId):
        return _navigation_py.TrackingDataAdapter_HasParameterCovariances(self, parameterId)

    def GetEventCount(self):
        return _navigation_py.TrackingDataAdapter_GetEventCount(self)

    def SetCorrection(self, correctionName, correctionType):
        return _navigation_py.TrackingDataAdapter_SetCorrection(self, correctionName, correctionType)

    def SetProgressReporter(self, reporter):
        return _navigation_py.TrackingDataAdapter_SetProgressReporter(self, reporter)

    def UsesLightTime(self, tf):
        return _navigation_py.TrackingDataAdapter_UsesLightTime(self, tf)

    def SetMultiplierFactor(self, mult):
        return _navigation_py.TrackingDataAdapter_SetMultiplierFactor(self, mult)

    def GetMultiplierFactor(self):
        return _navigation_py.TrackingDataAdapter_GetMultiplierFactor(self)

    def GetMeasurementModel(self):
        return _navigation_py.TrackingDataAdapter_GetMeasurementModel(self)

    def AddMediaCorrection(self, isAdd):
        return _navigation_py.TrackingDataAdapter_AddMediaCorrection(self, isAdd)

    def AddBias(self, isAdd):
        return _navigation_py.TrackingDataAdapter_AddBias(self, isAdd)

    def AddNoise(self, isAdd):
        return _navigation_py.TrackingDataAdapter_AddNoise(self, isAdd)

    def SetRangeOnly(self, isRangeOnly):
        return _navigation_py.TrackingDataAdapter_SetRangeOnly(self, isRangeOnly)

    def SetUsedForObjects(self, objArray):
        return _navigation_py.TrackingDataAdapter_SetUsedForObjects(self, objArray)

    def GetParticipants(self, forPathIndex):
        return _navigation_py.TrackingDataAdapter_GetParticipants(self, forPathIndex)

    def GetErrorMessage(self):
        return _navigation_py.TrackingDataAdapter_GetErrorMessage(self)

    def ApiGetDerivativeValue(self, row, column):
        return _navigation_py.TrackingDataAdapter_ApiGetDerivativeValue(self, row, column)

    def GetMeasurementDimension(self):
        return _navigation_py.TrackingDataAdapter_GetMeasurementDimension(self)

    def SetIonosphereCache(self, cache):
        return _navigation_py.TrackingDataAdapter_SetIonosphereCache(self, cache)

    def SetupHorp(self, height, angle, useOblateness=False, force=False):
        return _navigation_py.TrackingDataAdapter_SetupHorp(self, height, angle, useOblateness, force)

    def GetMeasurementErrorModel(self, measIndex, measType, numTrip):
        return _navigation_py.TrackingDataAdapter_GetMeasurementErrorModel(self, measIndex, measType, numTrip)

    def IsCrossLink(self):
        return _navigation_py.TrackingDataAdapter_IsCrossLink(self)

    @staticmethod
    def SetClass(base):
        return _navigation_py.TrackingDataAdapter_SetClass(base)

# Register TrackingDataAdapter in _navigation_py:
_navigation_py.TrackingDataAdapter_swigregister(TrackingDataAdapter)

def TrackingDataAdapter_SetClass(base):
    return _navigation_py.TrackingDataAdapter_SetClass(base)

class RangeAdapterKm(TrackingDataAdapter):
    r"""
    A measurement adapter for ranges in Km

    A measurement adapter for ranges in Km
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_RangeAdapterKm

    def __init__(self, *args):
        _navigation_py.RangeAdapterKm_swiginit(self, _navigation_py.new_RangeAdapterKm(*args))

    def Clone(self):
        return _navigation_py.RangeAdapterKm_Clone(self)

    def GetParameterText(self, id):
        return _navigation_py.RangeAdapterKm_GetParameterText(self, id)

    def GetParameterID(self, str):
        return _navigation_py.RangeAdapterKm_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.RangeAdapterKm_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.RangeAdapterKm_GetParameterTypeString(self, id)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.RangeAdapterKm_RenameRefObject(self, type, oldName, newName)

    def SetMeasurement(self, meas):
        return _navigation_py.RangeAdapterKm_SetMeasurement(self, meas)

    def Initialize(self):
        return _navigation_py.RangeAdapterKm_Initialize(self)

    def CalculateMeasurement(self, withEvents=False, forObservation=None, rampTB=None, forSimulation=False):
        return _navigation_py.RangeAdapterKm_CalculateMeasurement(self, withEvents, forObservation, rampTB, forSimulation)

    def ReCalculateFrequencyAndMediaCorrection(self, pathIndex, uplinkFrequency, rampTB):
        return _navigation_py.RangeAdapterKm_ReCalculateFrequencyAndMediaCorrection(self, pathIndex, uplinkFrequency, rampTB)

    def CalculateMeasurementAtOffset(self, withEvents=False, dt=0.0, forObservation=None, rampTB=None, forSimulation=False):
        return _navigation_py.RangeAdapterKm_CalculateMeasurementAtOffset(self, withEvents, dt, forObservation, rampTB, forSimulation)

    def CalculateMeasurementDerivatives(self, obj, id):
        return _navigation_py.RangeAdapterKm_CalculateMeasurementDerivatives(self, obj, id)

    def WriteMeasurements(self):
        return _navigation_py.RangeAdapterKm_WriteMeasurements(self)

    def WriteMeasurement(self, id):
        return _navigation_py.RangeAdapterKm_WriteMeasurement(self, id)

    def HasParameterCovariances(self, parameterId):
        return _navigation_py.RangeAdapterKm_HasParameterCovariances(self, parameterId)

    def GetEventCount(self):
        return _navigation_py.RangeAdapterKm_GetEventCount(self)

    def SetCorrection(self, correctionName, correctionType):
        return _navigation_py.RangeAdapterKm_SetCorrection(self, correctionName, correctionType)

    def ApplyMultiplier(self, useMeasType, factor, obj):
        return _navigation_py.RangeAdapterKm_ApplyMultiplier(self, useMeasType, factor, obj)

    def GetIonoCorrection(self):
        return _navigation_py.RangeAdapterKm_GetIonoCorrection(self)

    def GetTropoCorrection(self):
        return _navigation_py.RangeAdapterKm_GetTropoCorrection(self)

    def SetForTdrs(self):
        return _navigation_py.RangeAdapterKm_SetForTdrs(self)

    def HasLocalClones(self):
        return _navigation_py.RangeAdapterKm_HasLocalClones(self)

    @staticmethod
    def SetClass(base):
        return _navigation_py.RangeAdapterKm_SetClass(base)

# Register RangeAdapterKm in _navigation_py:
_navigation_py.RangeAdapterKm_swigregister(RangeAdapterKm)

def RangeAdapterKm_SetClass(base):
    return _navigation_py.RangeAdapterKm_SetClass(base)

class TrackingFileSet(MeasurementModelBase):
    r"""
    User access to measurement modes and tracking data file interfaces

    The TrackingFile set defines the measurements and tracking data that GMAT
    uses in the estimation process.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_TrackingFileSet

    def __init__(self, *args):
        _navigation_py.TrackingFileSet_swiginit(self, _navigation_py.new_TrackingFileSet(*args))

    def Clone(self):
        return _navigation_py.TrackingFileSet_Clone(self)

    def GetParameterText(self, id):
        return _navigation_py.TrackingFileSet_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.TrackingFileSet_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.TrackingFileSet_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.TrackingFileSet_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.TrackingFileSet_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.TrackingFileSet_IsParameterReadOnly(self, *args)

    def GetIntegerParameter(self, *args):
        return _navigation_py.TrackingFileSet_GetIntegerParameter(self, *args)

    def SetIntegerParameter(self, *args):
        return _navigation_py.TrackingFileSet_SetIntegerParameter(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.TrackingFileSet_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.TrackingFileSet_SetRealParameter(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.TrackingFileSet_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.TrackingFileSet_SetStringParameter(self, *args)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.TrackingFileSet_GetStringArrayParameter(self, *args)

    def GetBooleanParameter(self, *args):
        return _navigation_py.TrackingFileSet_GetBooleanParameter(self, *args)

    def SetBooleanParameter(self, *args):
        return _navigation_py.TrackingFileSet_SetBooleanParameter(self, *args)

    def GetPropertyObjectType(self, id):
        return _navigation_py.TrackingFileSet_GetPropertyObjectType(self, id)

    def HasLocalClones(self):
        return _navigation_py.TrackingFileSet_HasLocalClones(self)

    def GetRefObjectName(self, type):
        return _navigation_py.TrackingFileSet_GetRefObjectName(self, type)

    def GetRefObjectTypeArray(self):
        return _navigation_py.TrackingFileSet_GetRefObjectTypeArray(self)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.TrackingFileSet_GetRefObjectNameArray(self, type)

    def SetRefObjectName(self, type, name):
        return _navigation_py.TrackingFileSet_SetRefObjectName(self, type, name)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.TrackingFileSet_RenameRefObject(self, type, oldName, newName)

    def GetRefObject(self, *args):
        return _navigation_py.TrackingFileSet_GetRefObject(self, *args)

    def SetRefObject(self, *args):
        return _navigation_py.TrackingFileSet_SetRefObject(self, *args)

    def GetRefObjectArray(self, *args):
        return _navigation_py.TrackingFileSet_GetRefObjectArray(self, *args)

    def SetSolarSystem(self, ss):
        return _navigation_py.TrackingFileSet_SetSolarSystem(self, ss)

    def SetPropagators(self, ps, spMap):
        return _navigation_py.TrackingFileSet_SetPropagators(self, ps, spMap)

    def SetPropagator(self, *args):
        return _navigation_py.TrackingFileSet_SetPropagator(self, *args)

    def Initialize(self):
        return _navigation_py.TrackingFileSet_Initialize(self)

    def GetParticipants(self):
        return _navigation_py.TrackingFileSet_GetParticipants(self)

    def GetAdapter(self, index):
        return _navigation_py.TrackingFileSet_GetAdapter(self, index)

    def GetAdapters(self):
        return _navigation_py.TrackingFileSet_GetAdapters(self)

    def GenerateTrackingConfigs(self, strandsList, sensorsList, typesList):
        return _navigation_py.TrackingFileSet_GenerateTrackingConfigs(self, strandsList, sensorsList, typesList)

    def ClearIonosphereCache(self):
        return _navigation_py.TrackingFileSet_ClearIonosphereCache(self)

    def UsesHorp(self):
        return _navigation_py.TrackingFileSet_UsesHorp(self)

    @staticmethod
    def SetClass(base):
        return _navigation_py.TrackingFileSet_SetClass(base)

# Register TrackingFileSet in _navigation_py:
_navigation_py.TrackingFileSet_swigregister(TrackingFileSet)

def TrackingFileSet_SetClass(base):
    return _navigation_py.TrackingFileSet_SetClass(base)

class Sensor(gmat_py.Imager):
    r"""
    Implementation for the Sensor class

    Sensor is the base class for all sensor hardware used in the estimation
    subsystem.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_Sensor

    def __init__(self, *args):
        _navigation_py.Sensor_swiginit(self, _navigation_py.new_Sensor(*args))

    def GetParameterText(self, id):
        return _navigation_py.Sensor_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.Sensor_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.Sensor_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.Sensor_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.Sensor_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.Sensor_IsParameterReadOnly(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.Sensor_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.Sensor_SetStringParameter(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.Sensor_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.Sensor_SetRealParameter(self, *args)

    def Initialize(self):
        return _navigation_py.Sensor_Initialize(self)

    def GetDelay(self, whichOne=0):
        return _navigation_py.Sensor_GetDelay(self, whichOne)

    def SetDelay(self, delay, whichOne=0):
        return _navigation_py.Sensor_SetDelay(self, delay, whichOne)

    def IsFeasible(self, whichOne=0):
        return _navigation_py.Sensor_IsFeasible(self, whichOne)

    def GetSignalCount(self):
        return _navigation_py.Sensor_GetSignalCount(self)

    def IsTransmitted(self, whichOne=0):
        return _navigation_py.Sensor_IsTransmitted(self, whichOne)

    def GetSignal(self, whichOne=0):
        return _navigation_py.Sensor_GetSignal(self, whichOne)

    def SetSignal(self, s, whichOne=0):
        return _navigation_py.Sensor_SetSignal(self, s, whichOne)

    @staticmethod
    def SetClass(base):
        return _navigation_py.Sensor_SetClass(base)

# Register Sensor in _navigation_py:
_navigation_py.Sensor_swigregister(Sensor)

def Sensor_SetClass(base):
    return _navigation_py.Sensor_SetClass(base)

class RFHardware(Sensor):
    r"""
    Implementation for the RFHardware class

    RFHardware is the base class for all RF based hardware used in the estimation
    subsystem.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_RFHardware

    def __init__(self, *args):
        _navigation_py.RFHardware_swiginit(self, _navigation_py.new_RFHardware(*args))

    def GetParameterText(self, id):
        return _navigation_py.RFHardware_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.RFHardware_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.RFHardware_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.RFHardware_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.RFHardware_GetParameterTypeString(self, id)

    def GetPropertyObjectType(self, id):
        return _navigation_py.RFHardware_GetPropertyObjectType(self, id)

    def GetStringParameter(self, *args):
        return _navigation_py.RFHardware_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.RFHardware_SetStringParameter(self, *args)

    def GetRefObject(self, *args):
        return _navigation_py.RFHardware_GetRefObject(self, *args)

    def SetRefObject(self, *args):
        return _navigation_py.RFHardware_SetRefObject(self, *args)

    def GetRefObjectName(self, type):
        return _navigation_py.RFHardware_GetRefObjectName(self, type)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.RFHardware_GetRefObjectNameArray(self, type)

    def GetRefObjectTypeArray(self):
        return _navigation_py.RFHardware_GetRefObjectTypeArray(self)

    def HasRefObjectTypeArray(self):
        return _navigation_py.RFHardware_HasRefObjectTypeArray(self)

    def HasLocalClone(self):
        return _navigation_py.RFHardware_HasLocalClone(self)

    def Initialize(self):
        return _navigation_py.RFHardware_Initialize(self)

    @staticmethod
    def SetClass(base):
        return _navigation_py.RFHardware_SetClass(base)

# Register RFHardware in _navigation_py:
_navigation_py.RFHardware_swigregister(RFHardware)

def RFHardware_SetClass(base):
    return _navigation_py.RFHardware_SetClass(base)

class Antenna(gmat_py.Imager):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_Antenna

    def __init__(self, *args):
        _navigation_py.Antenna_swiginit(self, _navigation_py.new_Antenna(*args))

    def Clone(self):
        return _navigation_py.Antenna_Clone(self)

    def Copy(self, ant):
        return _navigation_py.Antenna_Copy(self, ant)

    def GetParameterText(self, id):
        return _navigation_py.Antenna_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.Antenna_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.Antenna_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.Antenna_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.Antenna_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.Antenna_IsParameterReadOnly(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.Antenna_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.Antenna_SetRealParameter(self, *args)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.Antenna_RenameRefObject(self, type, oldName, newName)

    @staticmethod
    def SetClass(base):
        return _navigation_py.Antenna_SetClass(base)

# Register Antenna in _navigation_py:
_navigation_py.Antenna_swigregister(Antenna)

def Antenna_SetClass(base):
    return _navigation_py.Antenna_SetClass(base)

class Receiver(RFHardware):
    r"""Implementation for the Receiver class"""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_Receiver

    def __init__(self, *args):
        _navigation_py.Receiver_swiginit(self, _navigation_py.new_Receiver(*args))

    def Clone(self):
        return _navigation_py.Receiver_Clone(self)

    def Copy(self, recei):
        return _navigation_py.Receiver_Copy(self, recei)

    def GetParameterText(self, id):
        return _navigation_py.Receiver_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.Receiver_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.Receiver_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.Receiver_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.Receiver_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.Receiver_IsParameterReadOnly(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.Receiver_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.Receiver_SetRealParameter(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.Receiver_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.Receiver_SetStringParameter(self, *args)

    def GetRefObject(self, type, name):
        return _navigation_py.Receiver_GetRefObject(self, type, name)

    def SetRefObject(self, *args):
        return _navigation_py.Receiver_SetRefObject(self, *args)

    def GetRefObjectArray(self, *args):
        return _navigation_py.Receiver_GetRefObjectArray(self, *args)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.Receiver_GetRefObjectNameArray(self, type)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.Receiver_GetStringArrayParameter(self, *args)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.Receiver_RenameRefObject(self, type, oldName, newName)

    def GetRefObjectTypeArray(self):
        return _navigation_py.Receiver_GetRefObjectTypeArray(self)

    def HasRefObjectTypeArray(self):
        return _navigation_py.Receiver_HasRefObjectTypeArray(self)

    def Initialize(self):
        return _navigation_py.Receiver_Initialize(self)

    def GetDelay(self, whichOne=0):
        return _navigation_py.Receiver_GetDelay(self, whichOne)

    def SetDelay(self, delay, whichOne=0):
        return _navigation_py.Receiver_SetDelay(self, delay, whichOne)

    def IsFeasible(self, whichOne=0):
        return _navigation_py.Receiver_IsFeasible(self, whichOne)

    def GetSignalCount(self):
        return _navigation_py.Receiver_GetSignalCount(self)

    def IsTransmitted(self, whichOne=0):
        return _navigation_py.Receiver_IsTransmitted(self, whichOne)

    def GetSignal(self, whichOne=0):
        return _navigation_py.Receiver_GetSignal(self, whichOne)

    def SetSignal(self, s, whichOne=0):
        return _navigation_py.Receiver_SetSignal(self, s, whichOne)

    def HasLocalClones(self):
        return _navigation_py.Receiver_HasLocalClones(self)

    @staticmethod
    def SetClass(base):
        return _navigation_py.Receiver_SetClass(base)

# Register Receiver in _navigation_py:
_navigation_py.Receiver_swigregister(Receiver)

def Receiver_SetClass(base):
    return _navigation_py.Receiver_SetClass(base)

class Transmitter(RFHardware):
    r"""
    Implementation for the Transmitter class

    Transmitters used in the Estimation processes
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_Transmitter

    def __init__(self, *args):
        _navigation_py.Transmitter_swiginit(self, _navigation_py.new_Transmitter(*args))

    def Clone(self):
        return _navigation_py.Transmitter_Clone(self)

    def Copy(self, trans):
        return _navigation_py.Transmitter_Copy(self, trans)

    def GetParameterText(self, id):
        return _navigation_py.Transmitter_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.Transmitter_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.Transmitter_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.Transmitter_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.Transmitter_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.Transmitter_IsParameterReadOnly(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.Transmitter_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.Transmitter_SetRealParameter(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.Transmitter_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.Transmitter_SetStringParameter(self, *args)

    def GetRvectorParameter(self, *args):
        return _navigation_py.Transmitter_GetRvectorParameter(self, *args)

    def SetRvectorParameter(self, id, value):
        return _navigation_py.Transmitter_SetRvectorParameter(self, id, value)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.Transmitter_GetStringArrayParameter(self, *args)

    def Initialize(self):
        return _navigation_py.Transmitter_Initialize(self)

    def GetDelay(self, whichOne=0):
        return _navigation_py.Transmitter_GetDelay(self, whichOne)

    def SetDelay(self, delay, whichOne=0):
        return _navigation_py.Transmitter_SetDelay(self, delay, whichOne)

    def GetSignalCount(self):
        return _navigation_py.Transmitter_GetSignalCount(self)

    def IsTransmitted(self, whichOne=0):
        return _navigation_py.Transmitter_IsTransmitted(self, whichOne)

    def GetSignal(self, whichOne=0):
        return _navigation_py.Transmitter_GetSignal(self, whichOne)

    def SetSignal(self, s, whichOne=0):
        return _navigation_py.Transmitter_SetSignal(self, s, whichOne)

    def GetOutPutFrequency(self):
        return _navigation_py.Transmitter_GetOutPutFrequency(self)

    def GetFrequencyBiasDerivative(self, *args):
        return _navigation_py.Transmitter_GetFrequencyBiasDerivative(self, *args)

    def HasParameterCovariances(self, parameterId):
        return _navigation_py.Transmitter_HasParameterCovariances(self, parameterId)

    def GetParameterCovariances(self, parameterId):
        return _navigation_py.Transmitter_GetParameterCovariances(self, parameterId)

    def GetEstimationParameterSize(self, item):
        return _navigation_py.Transmitter_GetEstimationParameterSize(self, item)

    def IsEstimationParameterValid(self, id):
        return _navigation_py.Transmitter_IsEstimationParameterValid(self, id)

    def GetFrequency(self, *args):
        return _navigation_py.Transmitter_GetFrequency(self, *args)

    def GetFrequencyOscillatorBias(self, *args):
        return _navigation_py.Transmitter_GetFrequencyOscillatorBias(self, *args)

    def SetEpoch(self, epoch):
        return _navigation_py.Transmitter_SetEpoch(self, epoch)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.Transmitter_RenameRefObject(self, type, oldName, newName)

    @staticmethod
    def SetClass(base):
        return _navigation_py.Transmitter_SetClass(base)

# Register Transmitter in _navigation_py:
_navigation_py.Transmitter_swigregister(Transmitter)

def Transmitter_SetClass(base):
    return _navigation_py.Transmitter_SetClass(base)

class Transponder(RFHardware):
    r"""Implementation for the Transponder class"""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_Transponder

    def __init__(self, *args):
        _navigation_py.Transponder_swiginit(self, _navigation_py.new_Transponder(*args))

    def Clone(self):
        return _navigation_py.Transponder_Clone(self)

    def Copy(self, trans):
        return _navigation_py.Transponder_Copy(self, trans)

    def GetParameterText(self, id):
        return _navigation_py.Transponder_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.Transponder_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.Transponder_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.Transponder_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.Transponder_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.Transponder_IsParameterReadOnly(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.Transponder_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.Transponder_SetRealParameter(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.Transponder_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.Transponder_SetStringParameter(self, *args)

    def Initialize(self):
        return _navigation_py.Transponder_Initialize(self)

    def GetDelay(self, whichOne=0):
        return _navigation_py.Transponder_GetDelay(self, whichOne)

    def SetDelay(self, delay, whichOne=0):
        return _navigation_py.Transponder_SetDelay(self, delay, whichOne)

    def IsFeasible(self, whichOne=0):
        return _navigation_py.Transponder_IsFeasible(self, whichOne)

    def GetSignalCount(self):
        return _navigation_py.Transponder_GetSignalCount(self)

    def IsTransmitted(self, whichOne=0):
        return _navigation_py.Transponder_IsTransmitted(self, whichOne)

    def GetSignal(self, whichOne=0):
        return _navigation_py.Transponder_GetSignal(self, whichOne)

    def SetSignal(self, s, whichOne=0):
        return _navigation_py.Transponder_SetSignal(self, s, whichOne)

    def GetTurnAroundRatio(self):
        return _navigation_py.Transponder_GetTurnAroundRatio(self)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.Transponder_RenameRefObject(self, type, oldName, newName)

    @staticmethod
    def SetClass(base):
        return _navigation_py.Transponder_SetClass(base)

# Register Transponder in _navigation_py:
_navigation_py.Transponder_swigregister(Transponder)

def Transponder_SetClass(base):
    return _navigation_py.Transponder_SetClass(base)

class ErrorModel(gmat_py.GmatBase):
    r"""Definition for the ErrorModel class used to define an error model for a measurement model"""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_ErrorModel

    def __init__(self, *args):
        _navigation_py.ErrorModel_swiginit(self, _navigation_py.new_ErrorModel(*args))

    def __eq__(self, em):
        return _navigation_py.ErrorModel___eq__(self, em)

    def Clone(self):
        return _navigation_py.ErrorModel_Clone(self)

    def Initialize(self):
        return _navigation_py.ErrorModel_Initialize(self)

    def Finalize(self):
        return _navigation_py.ErrorModel_Finalize(self)

    def GetParameterText(self, id):
        return _navigation_py.ErrorModel_GetParameterText(self, id)

    def GetParameterUnit(self, id):
        return _navigation_py.ErrorModel_GetParameterUnit(self, id)

    def GetParameterID(self, str):
        return _navigation_py.ErrorModel_GetParameterID(self, str)

    def GetParameterType(self, id):
        return _navigation_py.ErrorModel_GetParameterType(self, id)

    def GetParameterTypeString(self, id):
        return _navigation_py.ErrorModel_GetParameterTypeString(self, id)

    def IsParameterReadOnly(self, *args):
        return _navigation_py.ErrorModel_IsParameterReadOnly(self, *args)

    def IsParameterEnabled(self, *args):
        return _navigation_py.ErrorModel_IsParameterEnabled(self, *args)

    def GetStringParameter(self, *args):
        return _navigation_py.ErrorModel_GetStringParameter(self, *args)

    def SetStringParameter(self, *args):
        return _navigation_py.ErrorModel_SetStringParameter(self, *args)

    def GetStringArrayParameter(self, *args):
        return _navigation_py.ErrorModel_GetStringArrayParameter(self, *args)

    def GetRealParameter(self, *args):
        return _navigation_py.ErrorModel_GetRealParameter(self, *args)

    def SetRealParameter(self, *args):
        return _navigation_py.ErrorModel_SetRealParameter(self, *args)

    def GetIntegerParameter(self, *args):
        return _navigation_py.ErrorModel_GetIntegerParameter(self, *args)

    def SetIntegerParameter(self, *args):
        return _navigation_py.ErrorModel_SetIntegerParameter(self, *args)

    def GetRvectorParameter(self, *args):
        return _navigation_py.ErrorModel_GetRvectorParameter(self, *args)

    def SetRvectorParameter(self, *args):
        return _navigation_py.ErrorModel_SetRvectorParameter(self, *args)

    def IsEstimationParameterValid(self, id):
        return _navigation_py.ErrorModel_IsEstimationParameterValid(self, id)

    def GetEstimationParameterSize(self, id):
        return _navigation_py.ErrorModel_GetEstimationParameterSize(self, id)

    def GetEstimationParameterValue(self, id):
        return _navigation_py.ErrorModel_GetEstimationParameterValue(self, id)

    def HasParameterCovariances(self, parameterId):
        return _navigation_py.ErrorModel_HasParameterCovariances(self, parameterId)

    def GetParameterCovariances(self, parameterId):
        return _navigation_py.ErrorModel_GetParameterCovariances(self, parameterId)

    def GetPassBiasStartEpoches(self):
        return _navigation_py.ErrorModel_GetPassBiasStartEpoches(self)

    def GetTimeRangeInUTCGregorian(self, passNum):
        return _navigation_py.ErrorModel_GetTimeRangeInUTCGregorian(self, passNum)

    def GetTimeRangeStartInUTCGregorian(self, passNum):
        return _navigation_py.ErrorModel_GetTimeRangeStartInUTCGregorian(self, passNum)

    def GetTimeRangeEndInUTCGregorian(self, passNum):
        return _navigation_py.ErrorModel_GetTimeRangeEndInUTCGregorian(self, passNum)

    def SetPassBiasStartEpoches(self, startEpoches):
        return _navigation_py.ErrorModel_SetPassBiasStartEpoches(self, startEpoches)

    def GetBiasPassNumber(self, currentTime):
        return _navigation_py.ErrorModel_GetBiasPassNumber(self, currentTime)

    def GetPassBias(self, *args):
        return _navigation_py.ErrorModel_GetPassBias(self, *args)

    def HasLocalClones(self):
        r""" TODO: : Check this"""
        return _navigation_py.ErrorModel_HasLocalClones(self)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.ErrorModel_RenameRefObject(self, type, oldName, newName)

    @staticmethod
    def SetClass(base):
        return _navigation_py.ErrorModel_SetClass(base)

# Register ErrorModel in _navigation_py:
_navigation_py.ErrorModel_swigregister(ErrorModel)

def ErrorModel_SetClass(base):
    return _navigation_py.ErrorModel_SetClass(base)

class MeasurementManager(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_MeasurementManager

    def CleanUp(self):
        return _navigation_py.MeasurementManager_CleanUp(self)

    def __init__(self, *args):
        _navigation_py.MeasurementManager_swiginit(self, _navigation_py.new_MeasurementManager(*args))

    def SetPropagators(self, props, satPropMap):
        return _navigation_py.MeasurementManager_SetPropagators(self, props, satPropMap)

    def Initialize(self):
        return _navigation_py.MeasurementManager_Initialize(self)

    def PrepareForProcessing(self, simulating=False):
        return _navigation_py.MeasurementManager_PrepareForProcessing(self, simulating)

    def ProcessingComplete(self):
        return _navigation_py.MeasurementManager_ProcessingComplete(self)

    def Finalize(self):
        return _navigation_py.MeasurementManager_Finalize(self)

    def ValidateDuplicationOfGroundStationID(self, errorMsg):
        return _navigation_py.MeasurementManager_ValidateDuplicationOfGroundStationID(self, errorMsg)

    def CalculateMeasurements(self, forSimulation=False, withEvents=False, addNoise=False):
        return _navigation_py.MeasurementManager_CalculateMeasurements(self, forSimulation, withEvents, addNoise)

    def CalculateDerivatives(self, obj, wrt, forMeasurement):
        return _navigation_py.MeasurementManager_CalculateDerivatives(self, obj, wrt, forMeasurement)

    def MeasurementHasEvents(self):
        return _navigation_py.MeasurementManager_MeasurementHasEvents(self)

    def GetActiveEvents(self):
        return _navigation_py.MeasurementManager_GetActiveEvents(self)

    def ProcessEvent(self, locatedEvent):
        return _navigation_py.MeasurementManager_ProcessEvent(self, locatedEvent)

    def WriteMeasurements(self):
        return _navigation_py.MeasurementManager_WriteMeasurements(self)

    def AddMeasurement(self, *args):
        return _navigation_py.MeasurementManager_AddMeasurement(self, *args)

    def AddMeasurementName(self, measName):
        return _navigation_py.MeasurementManager_AddMeasurementName(self, measName)

    def GetClone(self, obj):
        return _navigation_py.MeasurementManager_GetClone(self, obj)

    def GetMeasurementNames(self):
        return _navigation_py.MeasurementManager_GetMeasurementNames(self)

    def GetMeasurementId(self, modelName):
        return _navigation_py.MeasurementManager_GetMeasurementId(self, modelName)

    def GetParticipantList(self):
        return _navigation_py.MeasurementManager_GetParticipantList(self)

    def GetSignalPathList(self):
        return _navigation_py.MeasurementManager_GetSignalPathList(self)

    def Calculate(self, measurementToCalc, withEvents=False):
        return _navigation_py.MeasurementManager_Calculate(self, measurementToCalc, withEvents)

    def CountFeasibleMeasurements(self, measurementToCalc):
        return _navigation_py.MeasurementManager_CountFeasibleMeasurements(self, measurementToCalc)

    def GetMeasurement(self, measurementToGet):
        return _navigation_py.MeasurementManager_GetMeasurement(self, measurementToGet)

    def GetMeasurementObject(self, measurementToGet):
        return _navigation_py.MeasurementManager_GetMeasurementObject(self, measurementToGet)

    def GetEventCount(self, forMeasurement=-1):
        return _navigation_py.MeasurementManager_GetEventCount(self, forMeasurement)

    def GetStreamList(self):
        return _navigation_py.MeasurementManager_GetStreamList(self)

    def SetStreamObject(self, newStream):
        return _navigation_py.MeasurementManager_SetStreamObject(self, newStream)

    def WriteMeasurement(self, measurementToWrite):
        return _navigation_py.MeasurementManager_WriteMeasurement(self, measurementToWrite)

    def GetRampTableDataStreamList(self):
        return _navigation_py.MeasurementManager_GetRampTableDataStreamList(self)

    def SetRampTableDataStreamObject(self, newStream):
        return _navigation_py.MeasurementManager_SetRampTableDataStreamObject(self, newStream)

    def GetValidMeasurementList(self):
        return _navigation_py.MeasurementManager_GetValidMeasurementList(self)

    def LoadObservations(self):
        return _navigation_py.MeasurementManager_LoadObservations(self)

    def LoadRampTables(self):
        return _navigation_py.MeasurementManager_LoadRampTables(self)

    def GetAllTrackingFileSets(self):
        return _navigation_py.MeasurementManager_GetAllTrackingFileSets(self)

    def GetAllTrackingDataAdapters(self):
        return _navigation_py.MeasurementManager_GetAllTrackingDataAdapters(self)

    def SetTransientForces(self, tf):
        return _navigation_py.MeasurementManager_SetTransientForces(self, tf)

    def GetMeasurementSize(self):
        return _navigation_py.MeasurementManager_GetMeasurementSize(self)

    def GetCurrentRecordNumber(self):
        return _navigation_py.MeasurementManager_GetCurrentRecordNumber(self)

    def GetEpochGT(self):
        return _navigation_py.MeasurementManager_GetEpochGT(self)

    def GetNextEpochGT(self):
        return _navigation_py.MeasurementManager_GetNextEpochGT(self)

    def GetObsData(self, observationToGet=-1):
        return _navigation_py.MeasurementManager_GetObsData(self, observationToGet)

    def GetObsDataObject(self, observationToGet=-1):
        return _navigation_py.MeasurementManager_GetObsDataObject(self, observationToGet)

    def AdvanceObservation(self):
        return _navigation_py.MeasurementManager_AdvanceObservation(self)

    def RemoveObservation(self, observationToRemove=-1):
        return _navigation_py.MeasurementManager_RemoveObservation(self, observationToRemove)

    def Reset(self):
        return _navigation_py.MeasurementManager_Reset(self)

    def SetDirection(self, forwards):
        return _navigation_py.MeasurementManager_SetDirection(self, forwards)

    def IsForward(self):
        return _navigation_py.MeasurementManager_IsForward(self)

    def GetObservationDataList(self):
        return _navigation_py.MeasurementManager_GetObservationDataList(self)

    def AutoGenerateTrackingDataAdapters(self):
        r""" This function is used to generate tracking data adapters for tracking file set objects having no tracking configs"""
        return _navigation_py.MeasurementManager_AutoGenerateTrackingDataAdapters(self)

    def SetStatisticsDataFiltersToDataFiles(self, index):
        return _navigation_py.MeasurementManager_SetStatisticsDataFiltersToDataFiles(self, index)

    def GetStatisticsDataFilters(self, tfs=None):
        return _navigation_py.MeasurementManager_GetStatisticsDataFilters(self, tfs)

    def ClearIonosphereCache(self):
        return _navigation_py.MeasurementManager_ClearIonosphereCache(self)

    def SetPassBiasesToTrackingDataAdapters(self):
        return _navigation_py.MeasurementManager_SetPassBiasesToTrackingDataAdapters(self)

# Register MeasurementManager in _navigation_py:
_navigation_py.MeasurementManager_swigregister(MeasurementManager)

class SignalData(object):
    r"""The SignalData class is a structure for communicating signal data information"""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_SignalData

    def __init__(self, *args):
        _navigation_py.SignalData_swiginit(self, _navigation_py.new_SignalData(*args))

    def CleanUp(self):
        return _navigation_py.SignalData_CleanUp(self)
    transmitParticipant = property(_navigation_py.SignalData_transmitParticipant_get, _navigation_py.SignalData_transmitParticipant_set, doc=r""" Name of the starting participant""")
    receiveParticipant = property(_navigation_py.SignalData_receiveParticipant_get, _navigation_py.SignalData_receiveParticipant_set, doc=r""" Name of the end point participant""")
    tNode = property(_navigation_py.SignalData_tNode_get, _navigation_py.SignalData_tNode_set, doc=r""" The starting participant""")
    tMovable = property(_navigation_py.SignalData_tMovable_get, _navigation_py.SignalData_tMovable_set, doc=r""" Flag indicating if the transmitter moved during light time iteration""")
    rNode = property(_navigation_py.SignalData_rNode_get, _navigation_py.SignalData_rNode_set, doc=r""" The end point participant""")
    rMovable = property(_navigation_py.SignalData_rMovable_get, _navigation_py.SignalData_rMovable_set, doc=r""" Flag indicating if the transmitter moved during light time iteration""")
    tPropagator = property(_navigation_py.SignalData_tPropagator_get, _navigation_py.SignalData_tPropagator_set, doc=r""" The propagator used for the transmitter, if used""")
    rPropagator = property(_navigation_py.SignalData_rPropagator_get, _navigation_py.SignalData_rPropagator_set, doc=r""" The propagator used for the receiver, if used""")
    stationParticipant = property(_navigation_py.SignalData_stationParticipant_get, _navigation_py.SignalData_stationParticipant_set, doc=r""" Flag indicating if one of the participants is a ground station""")
    tPrecTime = property(_navigation_py.SignalData_tPrecTime_get, _navigation_py.SignalData_tPrecTime_set, doc=r""" Transmitter epoch""")
    rPrecTime = property(_navigation_py.SignalData_rPrecTime_get, _navigation_py.SignalData_rPrecTime_set, doc=r""" Receiver epoch""")
    tLoc = property(_navigation_py.SignalData_tLoc_get, _navigation_py.SignalData_tLoc_set, doc=r""" MJ2000Eq location of the transmit node""")
    tOStateSSB = property(_navigation_py.SignalData_tOStateSSB_get, _navigation_py.SignalData_tOStateSSB_set, doc=r""" SSBMJ2000 state of the transmit node's origin""")
    tLocTcs = property(_navigation_py.SignalData_tLocTcs_get, _navigation_py.SignalData_tLocTcs_set, doc=r""" Location of the transmit node in its coordinate system""")
    rLoc = property(_navigation_py.SignalData_rLoc_get, _navigation_py.SignalData_rLoc_set, doc=r""" MJ2000Eq location of the receive node""")
    rOStateSSB = property(_navigation_py.SignalData_rOStateSSB_get, _navigation_py.SignalData_rOStateSSB_set, doc=r""" SSBMJ2000 state of the receive node's origin""")
    rLocRcs = property(_navigation_py.SignalData_rLocRcs_get, _navigation_py.SignalData_rLocRcs_set, doc=r""" Location of the receive node in its coordinate system""")
    tVel = property(_navigation_py.SignalData_tVel_get, _navigation_py.SignalData_tVel_set, doc=r""" MJ2000Eq transmitter velocity""")
    rVel = property(_navigation_py.SignalData_rVel_get, _navigation_py.SignalData_rVel_set, doc=r""" MJ2000Eq receiver velocity""")
    j2kOriginSep = property(_navigation_py.SignalData_j2kOriginSep_get, _navigation_py.SignalData_j2kOriginSep_set, doc=r""" Displacement of origins from the transmit node at time tTime and receive node at time rTime""")
    j2kOriginVel = property(_navigation_py.SignalData_j2kOriginVel_get, _navigation_py.SignalData_j2kOriginVel_set, doc=r""" Relative velocity of the origin of the receive node at time rTime w.r.t. the origin of the transmit node at time tTime""")
    rangeVecInertial = property(_navigation_py.SignalData_rangeVecInertial_get, _navigation_py.SignalData_rangeVecInertial_set, doc=r""" The SSBMj2000 equatorial range vector from transmit at time tTime to receive node at time rTime""")
    rangeVecI = property(_navigation_py.SignalData_rangeVecI_get, _navigation_py.SignalData_rangeVecI_set)
    rangeRateVecInertial = property(_navigation_py.SignalData_rangeRateVecInertial_get, _navigation_py.SignalData_rangeRateVecInertial_set, doc=r""" Relative velocity of the receive node at time rTime w.r.t. the transmit node at time tTime""")
    rangeVecObs = property(_navigation_py.SignalData_rangeVecObs_get, _navigation_py.SignalData_rangeVecObs_set, doc=r""" The range vector from transmit to receive node in obs coordinates""")
    rangeRateVecObs = property(_navigation_py.SignalData_rangeRateVecObs_get, _navigation_py.SignalData_rangeRateVecObs_set, doc=r""" The range rate vector from transmit to receive node in obs coordinates""")
    feasibility = property(_navigation_py.SignalData_feasibility_get, _navigation_py.SignalData_feasibility_set, doc=r""" feasibility""")
    feasibilityReason = property(_navigation_py.SignalData_feasibilityReason_get, _navigation_py.SignalData_feasibilityReason_set)
    feasibilityValue = property(_navigation_py.SignalData_feasibilityValue_get, _navigation_py.SignalData_feasibilityValue_set)
    tSTM = property(_navigation_py.SignalData_tSTM_get, _navigation_py.SignalData_tSTM_set, doc=r""" The STM of transmit participant at transmit time t1""")
    rSTM = property(_navigation_py.SignalData_rSTM_get, _navigation_py.SignalData_rSTM_set, doc=r""" The STM of receive participant at receive time t2""")
    tSTMtm = property(_navigation_py.SignalData_tSTMtm_get, _navigation_py.SignalData_tSTMtm_set, doc=r"""
     The STM of transmit participant at measurement time tm
    (note that : measurement time tm is different from transmit time t1 and receive time t2 due to hardware delay)
    """)
    rSTMtm = property(_navigation_py.SignalData_rSTMtm_get, _navigation_py.SignalData_rSTMtm_set, doc=r"""
     The STM of receive participant at  measurement time tm
    (note that : measurement time tm is different from transmit time t1 and receive time t2 due to hardware delay)
    """)
    tJ2kRotation = property(_navigation_py.SignalData_tJ2kRotation_get, _navigation_py.SignalData_tJ2kRotation_set, doc=r""" Rotation matrix from J2K to transmitter coordinate system""")
    rJ2kRotation = property(_navigation_py.SignalData_rJ2kRotation_get, _navigation_py.SignalData_rJ2kRotation_set, doc=r""" Rotation matrix from J2K to receiver coordinate system""")
    tJ2kRotationDot = property(_navigation_py.SignalData_tJ2kRotationDot_get, _navigation_py.SignalData_tJ2kRotationDot_set, doc=r""" Rotation dot matrix from J2K to transmitter coordinate system""")
    rJ2kRotationDot = property(_navigation_py.SignalData_rJ2kRotationDot_get, _navigation_py.SignalData_rJ2kRotationDot_set, doc=r""" Rotation dot matrix from J2K to receiver coordinate system""")
    correctionIDs = property(_navigation_py.SignalData_correctionIDs_get, _navigation_py.SignalData_correctionIDs_set, doc=r""" Correction identifiers""")
    correctionTypes = property(_navigation_py.SignalData_correctionTypes_get, _navigation_py.SignalData_correctionTypes_set, doc=r""" Correction types""")
    corrections = property(_navigation_py.SignalData_corrections_get, _navigation_py.SignalData_corrections_set, doc=r""" Correction data""")
    useCorrection = property(_navigation_py.SignalData_useCorrection_get, _navigation_py.SignalData_useCorrection_set, doc=r""" Flags for the corrections to use""")
    solveLightTime = property(_navigation_py.SignalData_solveLightTime_get, _navigation_py.SignalData_solveLightTime_set, doc=r""" Flag for light time solution""")
    tDelay = property(_navigation_py.SignalData_tDelay_get, _navigation_py.SignalData_tDelay_set, doc=r""" Hardware delay associated with transmit participant""")
    rDelay = property(_navigation_py.SignalData_rDelay_get, _navigation_py.SignalData_rDelay_set, doc=r""" Hardware delay associated with receive participant""")
    arriveFreq = property(_navigation_py.SignalData_arriveFreq_get, _navigation_py.SignalData_arriveFreq_set, doc=r"""
    signal frequencies
    Frequency (MHz) of the received signal at tNode's transponder. For the first signal leg, arrivedFreq is not used due to tNode is a transmiter only.
    """)
    transmitFreq = property(_navigation_py.SignalData_transmitFreq_get, _navigation_py.SignalData_transmitFreq_set)
    receiveFreq = property(_navigation_py.SignalData_receiveFreq_get, _navigation_py.SignalData_receiveFreq_set)
    mediaCorrectionFrequency = property(_navigation_py.SignalData_mediaCorrectionFrequency_get, _navigation_py.SignalData_mediaCorrectionFrequency_set)
    transmitFreqOscillatorBias = property(_navigation_py.SignalData_transmitFreqOscillatorBias_get, _navigation_py.SignalData_transmitFreqOscillatorBias_set)
    horpHeight = property(_navigation_py.SignalData_horpHeight_get, _navigation_py.SignalData_horpHeight_set)
    horpAngle = property(_navigation_py.SignalData_horpAngle_get, _navigation_py.SignalData_horpAngle_set)
    forTDRS = property(_navigation_py.SignalData_forTDRS_get, _navigation_py.SignalData_forTDRS_set)
    next = property(_navigation_py.SignalData_next_get, _navigation_py.SignalData_next_set, doc=r""" Linked list so separate signal paths are clear""")

# Register SignalData in _navigation_py:
_navigation_py.SignalData_swigregister(SignalData)

class SignalBase(gmat_py.GmatBase):
    r"""
    Base class for signals between two measurement participants

    This class sets the nodes for measurement signals, and defines the interfaces
    used by measurements when calculating signal data.

    SignalBase objects are arranged in a linked list, so that a full signal path
    can be defined through the list.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_SignalBase

    def HasLocalClones(self):
        return _navigation_py.SignalBase_HasLocalClones(self)

    def SetProgressReporter(self, reporter):
        return _navigation_py.SignalBase_SetProgressReporter(self, reporter)

    def SetTransmitParticipantName(self, name):
        return _navigation_py.SignalBase_SetTransmitParticipantName(self, name)

    def SetReceiveParticipantName(self, name):
        return _navigation_py.SignalBase_SetReceiveParticipantName(self, name)

    def SetSolarSystem(self, ss):
        return _navigation_py.SignalBase_SetSolarSystem(self, ss)

    def GetRefObjectNameArray(self, type):
        return _navigation_py.SignalBase_GetRefObjectNameArray(self, type)

    def SetRefObject(self, *args):
        return _navigation_py.SignalBase_SetRefObject(self, *args)

    def RenameRefObject(self, type, oldName, newName):
        return _navigation_py.SignalBase_RenameRefObject(self, type, oldName, newName)

    def GetStart(self, epochIsAtEnd):
        return _navigation_py.SignalBase_GetStart(self, epochIsAtEnd)

    def GetNext(self):
        return _navigation_py.SignalBase_GetNext(self)

    def Add(self, signalToAdd):
        return _navigation_py.SignalBase_Add(self, signalToAdd)

    def SetPropagator(self, propagator, forObj=None):
        return _navigation_py.SignalBase_SetPropagator(self, propagator, forObj)

    def Initialize(self):
        return _navigation_py.SignalBase_Initialize(self)

    def InitializeSignal(self, chainForwards=False):
        return _navigation_py.SignalBase_InitializeSignal(self, chainForwards)

    def ModelSignal(self, atEpoch, forSimulation, EpochAtReceive=True):
        return _navigation_py.SignalBase_ModelSignal(self, atEpoch, forSimulation, EpochAtReceive)

    def ModelSignalDerivative(self, obj, forId, measTime, forRangeRate=False):
        return _navigation_py.SignalBase_ModelSignalDerivative(self, obj, forId, measTime, forRangeRate)

    def SignalFrequencyCalculation(self, rampTB, uplinkFrequency=0.0, measTime=0.0):
        r""" This fucntion is used to compute signal frequency on each signal leg"""
        return _navigation_py.SignalBase_SignalFrequencyCalculation(self, rampTB, uplinkFrequency, measTime)

    def AddCorrection(self, modelName, mediaCorrectionType):
        r""" This function is used to add media correction to measurement model"""
        return _navigation_py.SignalBase_AddCorrection(self, modelName, mediaCorrectionType)

    def MediaCorrectionCalculation(self, rampTB=None):
        return _navigation_py.SignalBase_MediaCorrectionCalculation(self, rampTB)

    def HardwareDelayCalculation(self):
        r""" This function is used to calculate total hardware delay"""
        return _navigation_py.SignalBase_HardwareDelayCalculation(self)

    def GetPathDescription(self, fullList=True):
        return _navigation_py.SignalBase_GetPathDescription(self, fullList)

    def GetSignalDataObject(self):
        return _navigation_py.SignalBase_GetSignalDataObject(self)

    def SetSignalData(self, newData):
        return _navigation_py.SignalBase_SetSignalData(self, newData)

    def IsSignalFeasible(self):
        return _navigation_py.SignalBase_IsSignalFeasible(self)

    def UsesLighttime(self, tf):
        return _navigation_py.SignalBase_UsesLighttime(self, tf)

    def StepParticipant(self, stepToTake, forTransmitter):
        return _navigation_py.SignalBase_StepParticipant(self, stepToTake, forTransmitter)

    def MoveToEpoch(self, theEpoch, epochAtReceive, moveAll=True):
        return _navigation_py.SignalBase_MoveToEpoch(self, theEpoch, epochAtReceive, moveAll)

    def SetIonosphereCache(self, cache):
        return _navigation_py.SignalBase_SetIonosphereCache(self, cache)

    def SetStrandId(self, id):
        return _navigation_py.SignalBase_SetStrandId(self, id)

    def SetupHorp(self, height, angle, useOblateness, force):
        return _navigation_py.SignalBase_SetupHorp(self, height, angle, useOblateness, force)

    @staticmethod
    def SetClass(base):
        return _navigation_py.SignalBase_SetClass(base)

# Register SignalBase in _navigation_py:
_navigation_py.SignalBase_swigregister(SignalBase)

def SignalBase_SetClass(base):
    return _navigation_py.SignalBase_SetClass(base)

SELECT_CENTRAL_BODY = _navigation_py.SELECT_CENTRAL_BODY
SELECT_PRIMARY_BODY = _navigation_py.SELECT_PRIMARY_BODY
SELECT_POINT_MASSES = _navigation_py.SELECT_POINT_MASSES
SELECT_ALL_BODIES = _navigation_py.SELECT_ALL_BODIES
class PhysicalSignal(SignalBase):
    r"""
    Signal class used for instantaneous measurements

    This class might be going away based on the evolving measurement design
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    __swig_destroy__ = _navigation_py.delete_PhysicalSignal

    def __init__(self, *args):
        _navigation_py.PhysicalSignal_swiginit(self, _navigation_py.new_PhysicalSignal(*args))

    def Clone(self):
        return _navigation_py.PhysicalSignal_Clone(self)

    def InitializeSignal(self, chainForwards=False):
        return _navigation_py.PhysicalSignal_InitializeSignal(self, chainForwards)

    def ModelSignal(self, atEpoch, forSimulation, EpochAtReceive=True):
        return _navigation_py.PhysicalSignal_ModelSignal(self, atEpoch, forSimulation, EpochAtReceive)

    def ModelSignalDerivative(self, obj, forId, measTime, forRangeRate=False):
        return _navigation_py.PhysicalSignal_ModelSignalDerivative(self, obj, forId, measTime, forRangeRate)

    def AddCorrection(self, modelName, mediaCorrectionType):
        r""" This function is used to add media correction to measurement model"""
        return _navigation_py.PhysicalSignal_AddCorrection(self, modelName, mediaCorrectionType)

    def GetFrequencyFromRampTable(self, t, rampTB):
        r""" This function is used to get frequency at a given time from ramped frequency table"""
        return _navigation_py.PhysicalSignal_GetFrequencyFromRampTable(self, t, rampTB)

    def GetFrequencyBandFromRampTable(self, t, rampTB):
        r""" This function is used to get frequency band at a given time from ramped frequency table"""
        return _navigation_py.PhysicalSignal_GetFrequencyBandFromRampTable(self, t, rampTB)

    def FrequencyBand(self, frequency):
        r""" This function is used to specify frequency band based range of each band"""
        return _navigation_py.PhysicalSignal_FrequencyBand(self, frequency)

    def GetTDRSTransmitFrequency(self, rampTB):
        return _navigation_py.PhysicalSignal_GetTDRSTransmitFrequency(self, rampTB)

    @staticmethod
    def SetClass(base):
        return _navigation_py.PhysicalSignal_SetClass(base)

# Register PhysicalSignal in _navigation_py:
_navigation_py.PhysicalSignal_swigregister(PhysicalSignal)

def PhysicalSignal_SetClass(base):
    return _navigation_py.PhysicalSignal_SetClass(base)



