[Help] 
BaseHelpLink=file://../docs/help/html/%s.html
PropSetup=file://../docs/help/html/Propagator.html
PropSetupKeyword=Propagator.html
ChemicalTank=file://../docs/help/html/FuelTank.html
ChemicalTankKeyword=FuelTank.html
ChemicalThruster=file://../docs/help/html/Thruster.html
ChemicalThrusterKeyword=Thruster.html
Command Summary for Propagate=file://../docs/help/html/CommandSummary.html
Command Summary for PropagateKeyword=CommandSummary.html
Planet=file://../docs/help/html/CelestialBody.html
PlanetKeyword=CelestialBody.html
Star=file://../docs/help/html/CelestialBody.html
StarKeyword=CelestialBody.html
Asteroid=file://../docs/help/html/CelestialBody.html
AsteroidKeyword=CelestialBody.html
Comet=file://../docs/help/html/CelestialBody.html
CometKeyword=CelestialBody.html
Moon=file://../docs/help/html/CelestialBody.html
MoonKeyword=CelestialBody.html
New Coordinate System=file://../docs/help/html/CoordinateSystem.html
New Coordinate SystemKeyword=CoordinateSystem.html
ParameterCreateDialog=file://../docs/help/html/Variable.html
ParameterCreateDialogKeyword=Variable.html
Gmat=file://../docs/help/html/Assignment.html
GmatKeyword=Assignment.html
ParameterSelectDialog=file://../docs/help/html/CalculationParameters.html
ParameterSelectDialogKeyword=CalculationParameters.html
PenDown=file://../docs/help/html/PenUpPenDown.html
PenDownKeyword=PenUpPenDown.html
PenUp=file://../docs/help/html/PenUpPenDown.html
PenUpKeyword=PenUpPenDown.html
VariableArrayString=file://../docs/help/html/Variable.html
VariableArrayStringKeyword=Variable.html

[Fuel Tank]
VolumeHint=
PressureModelHint=
MassHint=
DensityHint=
TemperatureHint=
ReferenceTemperatureHint=
PressureHint=

[Ground Station]
IDHint=
HardwareHint=
CentralBodyHint=
StateTypeHint=
HorizonReferenceHint=
XHint=
YHint=
ZHint=
LatitudeHint=
LongitudeHint=
AltitudeHint=

[Formation]
AvailableSpacecraftListHint=
SelectedSpacecraftListHint=
AddSpacecraftHint=
RemoveSpacecraftHint=
ClearSpacecraftHint=

[Parameter]
VariableNameHint=
VariableValueHint=
ArrayNameHint=
ArrayRowValueHint=
ArrayColumnValueHint=
StringNameHint=
StringValueHint=
CreateVariableHint=
SelectHint=
CreateArrayHint=
EditArrayHint=
CreateStringHint=
VariableListHint=
ArrayListHint=
StringListHint=
ClearVariableHint=Clear
ClearArrayHint=Clear
ClearStringHint=Clear

[Propagator]
IntegratorTypeHint=
IntegratorInitialStepSizeHint=
IntegratorAccuracyHint=
IntegratorMinStepSizeHint=
IntegratorMaxStepSizeHint=
IntegratorMaxStepAttemptsHint=
IntegratorMinIntegrationErrorHint=
IntegratorNominalIntegrationErrorHint=
ForceModelErrorControlHint=
ForceModelCentralBodyHint=
ForceModelPrimaryBodiesComboHint=
ForceModelPrimaryBodiesEditHint=
ForceModelPrimaryBodiesSelectHint=Body to use for gravity and drag model
ForceModelGravityModelHint=Name of gravity model
ForceModelGravityDegreeHint=Maximum degree to use
ForceModelGravityOrderHint=Maximum order to use
ForceModelGravityMaxDegreeHint=Maximum degree allowed by file
ForceModelGravityMaxOrderHint=Maximum order allowed by file
ForceModelGravityStmLimitHint=Cutoff for degree and order used to calculate state transition matrix
ForceModelTideModelHint=Tide model to use
ForceModelTideHint=Tide models contained in current potential or tide file
ForceModelGravityPotentialFileHint=Gravity potential file path
ForceModelGravitySearchHint=Browse for potential file
ForceModelGravitySaveHint=Write current model to temporary.cof file
ForceModelTidePotentialFileHint=Tide model file path
ForceModelTideSearchHint=Browse for tide file
ForceModelTideClearHint=Clear loaded tide file
ForceModelDragAtmosphereModelHint=
ForceModelDragSetupHint=
ForceModelMagneticFieldModelHint=
ForceModelMagneticDegreeHint=
ForceModelMagneticOrderHint=
ForceModelMagneticSearchHint=
ForceModelPointMassesHint=
ForceModelSelectPointMassesHint=
ForceModelUseSolarRadiationPressureHint=
ForceModelUseRelativisticCorrectionHint=

[Celestial Body]
AvailableBodiesHint=
SelectedBodiesHint=
AddBodyHint=
RemoveBodyHint=
ClearBodiesHint=

[Celestial Body Properties]
MuHint=
EquatorialRadiusHint=
FlatteningHint=
TextureMapFileHint=
BrowseTextureMapFileHint=Browse for texture map file

[Celestial Body Orbit]
EphemerisSourceHint=
EphemerisFileHint=
BrowseEphemerisFileHint=Browse for ephemeris file
NAIFIDHint=
SPKFileListHint=
AddSPKFileHint=
RemoveSPKFileHint=
CentralBodyHint=
InitialA1EpochHint=
SMAHint=
ECCHint=
INCHint=
RAANHint=
AOPHint=
TAHint=

[Celestial Body Orientation]
SpinAxisRAConstantHint=
SpinAxisRARateHint=
SpinAxisDECConstantHint=
SpinAxisDECRateHint=
RotationConstantHint=
RotationRateHint=
NutationUpdateIntervalHint=
RotationDataSourceHint=

[Spacecraft Ballistic Mass]
DryMassHint=
DragCoefficientHint=
ReflectivityCoefficientHint=
DragAreaHint=
SRPAreaHint=

[Spacecraft Orbit]
EpochFormatHint=
EpochHint=
CoordinateSystemHint=
StateTypeHint=
AnomalyTypeHint=
ElementsXHint=
ElementsYHint=
ElementsZHint=
ElementsVXHint=
ElementsVYHint=
ElementsVZHint=
ElementsSMAHint=
ElementsECCHint=
ElementsINCHint=
ElementsRAANHint=
ElementsAOPHint=
ElementsTAHint=
ElementsRadPerHint=
ElementsRadApoHint=
ElementsRMAGHint=
ElementsRAHint=
ElementsDECHint=
ElementsVMAGHint=
ElementsAZIHint=
ElementsFPAHint=
ElementsRAVHint=
ElementsDECVHint=
ElementsHHint=
ElementsKHint=
ElementsPHint=
ElementsQHint=
ElementsMLONGHint=

[Spacecraft Tanks]
AvailableTanksHint=
SelectedTanksHint=
AddTankHint=
AddAllTanksHint=
RemoveTankHint=
ClearTanksHint=

[Spacecraft Thrusters]
AvailableThrustersHint=
SelectedThrustersHint=
AddThrusterHint=
AddAllThrustersHint=
RemoveThrusterHint=
ClearThrustersHint=

[Spacecraft Attitude]
AttitudeModelHint=
CoordinateSystemHint=
EulerAngleSequenceHint=
StateTypeHint=
RateStateTypeHint=
EulerAngle1Hint=
EulerAngle2Hint=
EulerAngle3Hint=
EulerAngleRate1Hint=
EulerAngleRate2Hint=
EulerAngleRate3Hint=
Quaternion1Hint=
Quaternion2Hint=
Quaternion3Hint=
Quaternion4Hint=
AngularVelocity1Hint=
AngularVelocity2Hint=
AngularVelocity3Hint=
DCM1Hint=
DCM2Hint=
DCM3Hint=
DCM4Hint=
DCM5Hint=
DCM6Hint=
DCM7Hint=
DCM8Hint=
DCM9Hint=
DCM10Hint=

[Spacecraft Spice]
NAIFIDHint=
NAIFIDRefFrameHint=
SPKFileListHint=
AddSPKFileHint=
RemoveSPKFileHint=
CKFileListHint=
AddCKFileHint=
RemoveCKFileHint=
SCLKFileListHint=
AddSCLKFileHint=
RemoveSCLKFileHint=
FKFileListHint=
AddFKFileHint=
RemoveFKFileHint=

[Solar System]
EphemerisUpdateIntervalHint=
EphemerisSourceHint=
EphemerisFilenameHint=
LeapSecondFilenameHint=
BrowseEphemerisFilenameHint=Browse for ephemeris file
BrowseLSKFilenameHint=Browse for leap second kernel
UseTTForEphemerisHint=

[Coordinate System]
NameHint=
OriginHint=
PrimaryHint=
EpochFormatHint=
EpochHint=
SecondaryHint=
XHint=
YHint=
ZHint=
UpdateIntervalHint=

[Finite Burn Setup]
ThrusterHint=

[Burn Thruster]
CoordinateSystemHint=
OriginHint=
AxesHint=
ThrustDirection1Hint=
ThrustDirection2Hint=
ThrustDirection3Hint=
DutyCycleHint=
ThrustScaleFactorHint=
DecrementMassHint=
TankHint=
IspHint=
GravitationalAccelHint=
EditThrusterCoefficientHint=
EditImpulseCoefficientHint=

[Impulsive Burn]
Element1Hint=
Element2Hint=
Element3Hint=

[Begin Finite Burn]
BurnHint=
SpacecraftHint=
SelectSpacecraftHint=

[End Finite Burn]
BurnHint=
SpacecraftHint=
SelectSpacecraftHint=

[Equation]
LeftHandSideHint=
RightHandSideHint=

[Ephemeris File]
SpacecraftHint=
StateTypeHint=
CoordinateSystemHint=
WriteEphemerisHint=
FileFormatHint=
FilenameHint=
BrowseEphemerisFilenameHint=Browse for ephemeris file
InterpolatorHint=
InterpolationOrderHint=
StepSizeHint=
EpochFormatHint=
InitialEpochHint=
FinalEpochHint=

[XY Plot]
ShowPlotHint=
ShowGridHint=
SelectedXHint=
SelectXHint=
SelectedYHint=
SelectYHint=
SolverIterationsHint=

[Parameter Select]
SelectEntireObjectHint=
ObjectTypeListHint=
SpacecraftListHint=
SpacePointListHint=
ImpulsiveBurnListHint=
VariableListHint=
ArrayRowHint=
ArrayColHint=
ObjectPropertiesHint=
CoordinateSystemHint=
OriginHint=
SelectedListHint=
MoveUpHint=
MoveDownHint=
AddSelectedHint=
RemoveSelectedHint=
AddAllHint=
RemoveAllHint=

[Advanced Mode]
NonSavableGUIModeHint=Currently GMAT disallows saving from the GUI if a main script contains #Include statements before the BeginMissionSequence

[Welcome/Links]
; Welcome Links can be either web urls or help topics (windows only)
; For web links, include the http:// to ensure GMAT correctly tries to show
; the web page.  Use Welcome/Links/Online to include web links to replace
; help topics on non-windows machines
; For example, "Reference Guide=RefGuide.html" uses the topic from the
; gmat chm file.  In Welcome/Links/Online, 
; put "Reference Guide=http://http://gmat.sourceforge.net/docs/R2022a/html/RefGuide.html" to 
; have a link that works for non-windows machines
Reference Guide=RefGuide.html
Wiki=https://gmat.atlassian.net/wiki/spaces/GW/overview
Source Code at SourceForge=http://sourceforge.net/p/gmat/git/
Report an Issue=https://gmat.atlassian.net/browse/GMT/issues/?filter=allissues

[Welcome/Links/Online]
; the inclusion of all of these entries here is necessary because of the
; way the WelcomePanel works - for non-windows platforms, the WelcomePanel
; must be set up to point to Welcome/Links/Online for all selections
Reference Guide=http://gmat.sourceforge.net/docs/R2022a/html/RefGuide.html
Wiki=https://gmat.atlassian.net/wiki/spaces/GW/overview
Source Code at SourceForge=http://sourceforge.net/p/gmat/git/
Report an Issue=https://gmat.atlassian.net/browse/GMT/issues/?filter=allissues

[Welcome/Samples]
Sample Missions=../samples

[GettingStarted/Tutorials]
; GettingStarted/Tutorials can be either web urls or help topics (windows only)
; For web links, include the http:// to ensure GMAT correctly tries to show
; the web page.  Use GettingStarted/Tutorials/Online to include web links to replace
; help topics on non-windows machines15
; For example, "Reference Guide=RefGuide.html" uses the topic from the
; gmat chm file.  In GettingStarted/Tutorials/Online, 
; put "Reference Guide=http://http://gmat.sourceforge.net/docs/R2022a/html/RefGuide.html" to 
; have a link that works for non-windows machines
Step By Step Text Tutorials=Tutorials.html
Video Tutorials=https://www.youtube.com/channel/UCt-REODJNr2mB3t-xH6kbjg

[GettingStarted/Tutorials/Online]
Step By Step Text Tutorials=http://gmat.sourceforge.net/docs/R2022a/html/Tutorials.html
Video Tutorials=https://www.youtube.com/channel/UCt-REODJNr2mB3t-xH6kbjg

[GettingStarted/Tutorials/Icons]
Step By Step Text Tutorials=StepByStep.png
Video Tutorials=VideoTutorial.png

