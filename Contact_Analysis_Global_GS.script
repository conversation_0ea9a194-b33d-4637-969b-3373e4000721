%----------------------------------------
% Multi-Body Contact Analysis - Global Ground Stations
% 
% Updated Configuration:
% - LEGS1, LEGS2, LEGS3 as ground stations (Ohio, Sydney, Ireland)
% - SV1, SV2 as lunar orbiters
% - <PERSON><PERSON> as lunar ground station
%
% Contact Analysis:
% 1. HLS <-> SV1/SV2 (lunar ground station to lunar orbiters)
% 2. SV1/SV2 <-> LEGS1/LEGS2/LEGS3 (lunar orbiters to Earth ground stations)
%
% Simulation: 1 day, 10-second resolution
%----------------------------------------

%----------------------------------------
%---------- Spacecraft Configuration
%----------------------------------------

% Lunar orbiters - SV1 and SV2 (polar orbits)
Create Spacecraft SV1;
GMAT SV1.DateFormat = UTCGregorian;
GMAT SV1.Epoch = '01 Jan 2000 00:00:00.000';
GMAT SV1.CoordinateSystem = LunarMJ2000Eq;
GMAT SV1.DisplayStateType = Keplerian;
GMAT SV1.SMA = 2037.4;
GMAT SV1.ECC = 0.0;
GMAT SV1.INC = 90.0;
GMAT SV1.RAAN = 0.0;
GMAT SV1.AOP = 0.0;
GMAT SV1.TA = 0.0;

Create Spacecraft SV2;
GMAT SV2.DateFormat = UTCGregorian;
GMAT SV2.Epoch = '01 Jan 2000 00:00:00.000';
GMAT SV2.CoordinateSystem = LunarMJ2000Eq;
GMAT SV2.DisplayStateType = Keplerian;
GMAT SV2.SMA = 2037.4;
GMAT SV2.ECC = 0.0;
GMAT SV2.INC = 90.0;
GMAT SV2.RAAN = 180.0;
GMAT SV2.AOP = 0.0;
GMAT SV2.TA = 180.0;

%----------------------------------------
%---------- Coordinate Systems
%----------------------------------------

Create CoordinateSystem LunarMJ2000Eq;
GMAT LunarMJ2000Eq.Origin = Luna;
GMAT LunarMJ2000Eq.Axes = MJ2000Eq;

%----------------------------------------
%---------- Ground Stations
%----------------------------------------

% LEGS1 - Ohio Ground Station (USA)
Create GroundStation LEGS1_Ohio;
GMAT LEGS1_Ohio.CentralBody = Earth;
GMAT LEGS1_Ohio.StateType = Spherical;
GMAT LEGS1_Ohio.HorizonReference = Ellipsoid;
GMAT LEGS1_Ohio.Location1 = 40.4173;     % Columbus, Ohio latitude
GMAT LEGS1_Ohio.Location2 = 277.0147;    % Columbus, Ohio longitude (360 - 82.9851)
GMAT LEGS1_Ohio.Location3 = 0.260;       % Altitude (km)
GMAT LEGS1_Ohio.Id = 'LEGS1_Ohio';
GMAT LEGS1_Ohio.MinimumElevationAngle = 5.0;

% LEGS2 - Sydney Ground Station (Australia)
Create GroundStation LEGS2_Sydney;
GMAT LEGS2_Sydney.CentralBody = Earth;
GMAT LEGS2_Sydney.StateType = Spherical;
GMAT LEGS2_Sydney.HorizonReference = Ellipsoid;
GMAT LEGS2_Sydney.Location1 = -33.8688;   % Sydney latitude (negative for South)
GMAT LEGS2_Sydney.Location2 = 151.2093;   % Sydney longitude 
GMAT LEGS2_Sydney.Location3 = 0.058;      % Altitude (km)
GMAT LEGS2_Sydney.Id = 'LEGS2_Sydney';
GMAT LEGS2_Sydney.MinimumElevationAngle = 5.0;

% LEGS3 - Ireland Ground Station (Dublin)
Create GroundStation LEGS3_Ireland;
GMAT LEGS3_Ireland.CentralBody = Earth;
GMAT LEGS3_Ireland.StateType = Spherical;
GMAT LEGS3_Ireland.HorizonReference = Ellipsoid;
GMAT LEGS3_Ireland.Location1 = 53.3498;   % Dublin latitude
GMAT LEGS3_Ireland.Location2 = 353.7603;  % Dublin longitude (360 - 6.2603)
GMAT LEGS3_Ireland.Location3 = 0.047;     % Altitude (km)
GMAT LEGS3_Ireland.Id = 'LEGS3_Ireland';
GMAT LEGS3_Ireland.MinimumElevationAngle = 5.0;

% HLS (Human Landing System) - Lunar South Pole
Create GroundStation HLS_Station;
GMAT HLS_Station.CentralBody = Luna;
GMAT HLS_Station.StateType = Spherical;
GMAT HLS_Station.HorizonReference = Ellipsoid;
GMAT HLS_Station.Location1 = -89.0;       % South Pole latitude
GMAT HLS_Station.Location2 = 0.0;         % Longitude
GMAT HLS_Station.Location3 = 0.0;         % Altitude
GMAT HLS_Station.Id = 'HLS_SouthPole';
GMAT HLS_Station.MinimumElevationAngle = 0.0;

%----------------------------------------
%---------- Force Models (Simplified)
%----------------------------------------

Create ForceModel LunarProp_fm;
GMAT LunarProp_fm.CentralBody = Luna;
GMAT LunarProp_fm.PrimaryBodies = {Luna};
GMAT LunarProp_fm.PointMasses = {Earth, Sun};
GMAT LunarProp_fm.Drag = None;
GMAT LunarProp_fm.SRP = Off;
GMAT LunarProp_fm.RelativisticCorrection = Off;
GMAT LunarProp_fm.ErrorControl = RSSStep;

%----------------------------------------
%---------- Propagators
%----------------------------------------

Create Propagator LunarProp;
GMAT LunarProp.FM = LunarProp_fm;
GMAT LunarProp.Type = RungeKutta89;
GMAT LunarProp.InitialStepSize = 60;
GMAT LunarProp.Accuracy = 9.999999999999999e-12;
GMAT LunarProp.MinStep = 0.001;
GMAT LunarProp.MaxStep = 2700;
GMAT LunarProp.MaxStepAttempts = 50;
GMAT LunarProp.StopIfAccuracyIsViolated = true;

%----------------------------------------
%---------- Contact Locators
%----------------------------------------

% Lunar orbiters to Earth ground stations
Create ContactLocator SV1_LEGS1_Contacts;
GMAT SV1_LEGS1_Contacts.Target = SV1;
GMAT SV1_LEGS1_Contacts.Observers = {LEGS1_Ohio};
GMAT SV1_LEGS1_Contacts.Filename = 'SV1_LEGS1_Ohio.txt';
GMAT SV1_LEGS1_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV1_LEGS1_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV1_LEGS1_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV1_LEGS1_Contacts.StepSize = 10.0;
GMAT SV1_LEGS1_Contacts.UseLightTimeDelay = false;
GMAT SV1_LEGS1_Contacts.UseStellarAberration = false;
GMAT SV1_LEGS1_Contacts.WriteReport = true;
GMAT SV1_LEGS1_Contacts.RunMode = Automatic;

Create ContactLocator SV1_LEGS2_Contacts;
GMAT SV1_LEGS2_Contacts.Target = SV1;
GMAT SV1_LEGS2_Contacts.Observers = {LEGS2_Sydney};
GMAT SV1_LEGS2_Contacts.Filename = 'SV1_LEGS2_Sydney.txt';
GMAT SV1_LEGS2_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV1_LEGS2_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV1_LEGS2_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV1_LEGS2_Contacts.StepSize = 10.0;
GMAT SV1_LEGS2_Contacts.UseLightTimeDelay = false;
GMAT SV1_LEGS2_Contacts.UseStellarAberration = false;
GMAT SV1_LEGS2_Contacts.WriteReport = true;
GMAT SV1_LEGS2_Contacts.RunMode = Automatic;

Create ContactLocator SV1_LEGS3_Contacts;
GMAT SV1_LEGS3_Contacts.Target = SV1;
GMAT SV1_LEGS3_Contacts.Observers = {LEGS3_Ireland};
GMAT SV1_LEGS3_Contacts.Filename = 'SV1_LEGS3_Ireland.txt';
GMAT SV1_LEGS3_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV1_LEGS3_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV1_LEGS3_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV1_LEGS3_Contacts.StepSize = 10.0;
GMAT SV1_LEGS3_Contacts.UseLightTimeDelay = false;
GMAT SV1_LEGS3_Contacts.UseStellarAberration = false;
GMAT SV1_LEGS3_Contacts.WriteReport = true;
GMAT SV1_LEGS3_Contacts.RunMode = Automatic;

Create ContactLocator SV2_LEGS1_Contacts;
GMAT SV2_LEGS1_Contacts.Target = SV2;
GMAT SV2_LEGS1_Contacts.Observers = {LEGS1_Ohio};
GMAT SV2_LEGS1_Contacts.Filename = 'SV2_LEGS1_Ohio.txt';
GMAT SV2_LEGS1_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV2_LEGS1_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV2_LEGS1_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV2_LEGS1_Contacts.StepSize = 10.0;
GMAT SV2_LEGS1_Contacts.UseLightTimeDelay = false;
GMAT SV2_LEGS1_Contacts.UseStellarAberration = false;
GMAT SV2_LEGS1_Contacts.WriteReport = true;
GMAT SV2_LEGS1_Contacts.RunMode = Automatic;

Create ContactLocator SV2_LEGS2_Contacts;
GMAT SV2_LEGS2_Contacts.Target = SV2;
GMAT SV2_LEGS2_Contacts.Observers = {LEGS2_Sydney};
GMAT SV2_LEGS2_Contacts.Filename = 'SV2_LEGS2_Sydney.txt';
GMAT SV2_LEGS2_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV2_LEGS2_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV2_LEGS2_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV2_LEGS2_Contacts.StepSize = 10.0;
GMAT SV2_LEGS2_Contacts.UseLightTimeDelay = false;
GMAT SV2_LEGS2_Contacts.UseStellarAberration = false;
GMAT SV2_LEGS2_Contacts.WriteReport = true;
GMAT SV2_LEGS2_Contacts.RunMode = Automatic;

Create ContactLocator SV2_LEGS3_Contacts;
GMAT SV2_LEGS3_Contacts.Target = SV2;
GMAT SV2_LEGS3_Contacts.Observers = {LEGS3_Ireland};
GMAT SV2_LEGS3_Contacts.Filename = 'SV2_LEGS3_Ireland.txt';
GMAT SV2_LEGS3_Contacts.InputEpochFormat = UTCGregorian;
GMAT SV2_LEGS3_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT SV2_LEGS3_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT SV2_LEGS3_Contacts.StepSize = 10.0;
GMAT SV2_LEGS3_Contacts.UseLightTimeDelay = false;
GMAT SV2_LEGS3_Contacts.UseStellarAberration = false;
GMAT SV2_LEGS3_Contacts.WriteReport = true;
GMAT SV2_LEGS3_Contacts.RunMode = Automatic;

% HLS to lunar spacecraft
Create ContactLocator HLS_SV1_Contacts;
GMAT HLS_SV1_Contacts.Target = SV1;
GMAT HLS_SV1_Contacts.Observers = {HLS_Station};
GMAT HLS_SV1_Contacts.Filename = 'HLS_SV1_Global.txt';
GMAT HLS_SV1_Contacts.InputEpochFormat = UTCGregorian;
GMAT HLS_SV1_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT HLS_SV1_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT HLS_SV1_Contacts.StepSize = 10.0;
GMAT HLS_SV1_Contacts.UseLightTimeDelay = false;
GMAT HLS_SV1_Contacts.UseStellarAberration = false;
GMAT HLS_SV1_Contacts.WriteReport = true;
GMAT HLS_SV1_Contacts.RunMode = Automatic;

Create ContactLocator HLS_SV2_Contacts;
GMAT HLS_SV2_Contacts.Target = SV2;
GMAT HLS_SV2_Contacts.Observers = {HLS_Station};
GMAT HLS_SV2_Contacts.Filename = 'HLS_SV2_Global.txt';
GMAT HLS_SV2_Contacts.InputEpochFormat = UTCGregorian;
GMAT HLS_SV2_Contacts.InitialEpoch = '01 Jan 2000 00:00:00.000';
GMAT HLS_SV2_Contacts.FinalEpoch = '02 Jan 2000 00:00:00.000';
GMAT HLS_SV2_Contacts.StepSize = 10.0;
GMAT HLS_SV2_Contacts.UseLightTimeDelay = false;
GMAT HLS_SV2_Contacts.UseStellarAberration = false;
GMAT HLS_SV2_Contacts.WriteReport = true;
GMAT HLS_SV2_Contacts.RunMode = Automatic;

%----------------------------------------
%---------- Mission Sequence
%----------------------------------------

BeginMissionSequence;

% Simple 1-day propagation with contact analysis every 10 seconds
Propagate Synchronized LunarProp(SV1, SV2) {SV1.ElapsedDays = 1.0}; 