from os import PathLike
from typing import Text<PERSON>, List, Dict
from datetime import datetime
import re


class ContactEvent:
    """Represents a single contact event between target and observer"""

    def __init__(self, start_time: float, stop_time: float, duration: float):
        self.start_time = start_time  # seconds since epoch (01 Jan 2000 00:00:00.000)
        self.stop_time = stop_time    # seconds since epoch (01 Jan 2000 00:00:00.000)
        self.duration = duration      # duration in seconds

    def __repr__(self):
        return f"ContactEvent(start={self.start_time:.3f}s, stop={self.stop_time:.3f}s, duration={self.duration:.3f}s)"


class ObserverData:
    """Represents all contact data for a single observer"""

    def __init__(self, name: str):
        self.name = name
        self.events: List[ContactEvent] = []
        self.event_count = 0

    def add_event(self, start_time: float, stop_time: float, duration: float):
        event = ContactEvent(start_time, stop_time, duration)
        self.events.append(event)

    def set_event_count(self, count: int):
        self.event_count = count

    def __repr__(self):
        return f"ObserverData(name='{self.name}', events={len(self.events)}, count={self.event_count})"


class ContactReport:
    """Parses and stores contact locator report data"""

    def __init__(self, file_path: PathLike) -> None:
        # Initialize class variables
        self.target: str = ""
        self.observers: Dict[str, ObserverData] = {}
        self.total_observers = 0
        self.total_events = 0

        # Set epoch time as 01 Jan 2000 00:00:00.000
        self.epoch_time = datetime(2000, 1, 1, 0, 0, 0, 0)

        # Parse the file
        with open(file_path, 'r') as f:
            self.process_contact_report(f)

        # Calculate totals
        self._calculate_totals()

    def process_contact_report(self, file: TextIO) -> None:
        """Parse the contact report file and extract all data"""
        lines = file.readlines()
        current_observer = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Parse target
            if line.startswith('Target: '):
                self.target = line.split('Target: ')[1].strip()

            # Parse observer
            elif line.startswith('Observer: '):
                observer_name = line.split('Observer: ')[1].strip()
                current_observer = ObserverData(observer_name)
                self.observers[observer_name] = current_observer

            # Parse contact events (look for time format)
            elif self._is_contact_event_line(line) and current_observer:
                start_time, stop_time, duration = self._parse_contact_event(line)
                if start_time is not None and stop_time is not None and duration is not None:
                    current_observer.add_event(start_time, stop_time, duration)

            # Parse event count
            elif line.startswith('Number of events : ') and current_observer:
                count = int(line.split('Number of events : ')[1].strip())
                current_observer.set_event_count(count)

            i += 1

    def _is_contact_event_line(self, line: str) -> bool:
        """Check if line contains contact event data (has date/time format)"""
        # Look for pattern like "01 Jan 2000 08:55:59.829"
        date_pattern = r'\d{2} \w{3} \d{4} \d{2}:\d{2}:\d{2}\.\d{3}'
        return bool(re.search(date_pattern, line))

    def _parse_contact_event(self, line: str) -> tuple:
        """Parse a contact event line and extract start time, stop time, and duration"""
        try:
            # Split by multiple spaces to separate the three columns
            parts = re.split(r'\s{2,}', line.strip())
            if len(parts) >= 3:
                start_time_str = parts[0].strip()
                stop_time_str = parts[1].strip()
                duration = float(parts[2].strip())

                # Convert time strings to seconds since epoch
                start_time_seconds = self._time_string_to_seconds(start_time_str)
                stop_time_seconds = self._time_string_to_seconds(stop_time_str)

                return start_time_seconds, stop_time_seconds, duration
        except (ValueError, IndexError):
            pass
        return None, None, None

    def _time_string_to_seconds(self, time_str: str) -> float:
        """Convert time string like '01 Jan 2000 08:55:59.829' to seconds since epoch"""
        try:
            # Parse the datetime string
            dt = datetime.strptime(time_str, '%d %b %Y %H:%M:%S.%f')

            # Calculate seconds since epoch (01 Jan 2000 00:00:00.000)
            time_delta = dt - self.epoch_time
            return time_delta.total_seconds()
        except ValueError:
            return 0.0

    def _calculate_totals(self):
        """Calculate total observers and events"""
        self.total_observers = len(self.observers)
        self.total_events = sum(len(obs.events) for obs in self.observers.values())

    def get_observer_names(self) -> List[str]:
        """Get list of all observer names"""
        return list(self.observers.keys())

    def get_events_for_observer(self, observer_name: str) -> List[ContactEvent]:
        """Get all events for a specific observer"""
        if observer_name in self.observers:
            return self.observers[observer_name].events
        return []

    def get_total_duration_for_observer(self, observer_name: str) -> float:
        """Get total contact duration for a specific observer"""
        if observer_name in self.observers:
            return sum(event.duration for event in self.observers[observer_name].events)
        return 0.0

    def print_summary(self):
        """Print a summary of the parsed data"""
        print(f"Target: {self.target}")
        print(f"Total Observers: {self.total_observers}")
        print(f"Total Events: {self.total_events}")
        print(f"Epoch Time: {self.epoch_time} (normalized as 0.0 seconds)")
        print("\nObserver Summary:")
        for name, observer in self.observers.items():
            total_duration = self.get_total_duration_for_observer(name)
            print(f"  {name}: {len(observer.events)} events, {total_duration:.2f}s total duration")

        for name, observer in self.observers.items():
            print(f"\n{name}:")
            for event in observer.events:
                print(f"    {event}")


    def scale_down(self, scale_factor: float):
        """Scale down all times by a factor"""
        for observer in self.observers.values():
            for event in observer.events:
                event.start_time /= scale_factor
                event.stop_time /= scale_factor
                event.duration /= scale_factor

    def __repr__(self):
        return f"ContactReport(target='{self.target}', observers={self.total_observers}, events={self.total_events})"


# Example usage
if __name__ == "__main__":
    report = ContactReport('./output/EVA2Contacts.txt')
    report.print_summary()
    report.scale_down(600)  # Scale down by 10 minutes. 10 real minutes -> 1 simulated second
    report.print_summary()
