import sys
from os import path
from pathlib import Path
import pandas as pd
import numpy as np

# Set matplotlib to use non-interactive backend before importing pyplot
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for headless environment
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.animation import FuncAnimation

apistartup = "api_startup_file.txt"
GmatInstall = "/home/<USER>/Downloads/gmat-ubuntu-x64-R2025a/GMAT/R2025a"
GmatBinPath = GmatInstall + "/bin"
Startup = GmatBinPath + "/" + apistartup
# --- Configuration ---
# Point this to the bin directory of your GMAT installation
GMAT_BIN_PATH = Path('./bin')
# The GMAT script to run for the simulation
GMAT_SCRIPT_FILE = GmatInstall + '/lunar_contact_scenario.script'
# --- End Configuration ---


if path.exists(Startup):


   sys.path.insert(1, GmatBinPath)

   import gmatpy as gmat
   gmat.Setup(Startup)

else:
   print("Cannot find ", Startup)
   print()
   print("Please set up a GMAT startup file named ", apistartup, " in the ", 
      GmatBinPath, " folder.")

def parse_contact_report(file_path):
    """Parses a GMAT contact report into a list of start/end times."""
    if not file_path.exists():
        print(f"Warning: Contact report not found at {file_path}")
        return []
    
    intervals = []
    with open(file_path, 'r') as f:
        lines = f.readlines()
        
    for line in lines:
        # Look for lines containing contact data (start and end times)
        if 'Jan 2000' in line:
            parts = line.split('   ')
            parts = [p.strip() for p in parts if p.strip()]
            if len(parts) >= 2:
                try:
                    start_time = pd.to_datetime(parts[0].replace('Jan', '01'), format='%d %m %Y %H:%M:%S.%f')
                    end_time = pd.to_datetime(parts[1].replace('Jan', '01'), format='%d %m %Y %H:%M:%S.%f')
                    intervals.append((start_time, end_time))
                except ValueError as e:
                    print(f"Warning: Could not parse date from line: '{line.strip()}'\nError: {e}")

    return intervals

def is_contact_active(contact_intervals, current_time):
    """Checks if the current simulation time falls within a contact interval."""
    for start, end in contact_intervals:
        if start <= current_time <= end:
            return True
    return False

def main():
    """
    Runs the GMAT simulation and generates a 3D animated plot of the
    lunar contact scenario with line-of-sight visualizations.
    """
    # Initialize GMAT
    gmat.Clear()
    gmat.LoadScript(GMAT_SCRIPT_FILE)

    # --- FIX: Ensure the output directory exists using an absolute path ---
    output_dir = Path(GmatInstall) / 'output'
    output_dir.mkdir(exist_ok=True)

    # Run the GMAT script to generate ephemeris and contact reports
    print("Running GMAT simulation to generate data...")
    if gmat.RunScript():
        gmat.SaveScript(GMAT_SCRIPT_FILE + ".edited")
        print("GMAT run completed successfully.")
    else:
        print("GMAT run failed. Please check the GMAT script for errors.")
        return

    # --- FIX: Get celestial body objects correctly from the SolarSystem ---
    ss = gmat.GetSolarSystem()
    earth = ss.GetBody("Earth")
    luna = ss.GetBody("Luna")

    # Load the results of the GMAT run - FIX: Use correct object names from script
    sat1 = gmat.GetObject("Sat1")
    sat2 = gmat.GetObject("Sat2")
    houston = gmat.GetObject("Houston")
    sydney = gmat.GetObject("Sydney")
    ireland = gmat.GetObject("Ireland")
    shackleton = gmat.GetObject("ShackletonCrater")

    # --- Parse all contact reports ---
    output_dir = Path('./output')
    if not output_dir.exists():
        output_dir = Path('../output') # Handle running from bin dir
        
    contacts = {
        'sat1_earth': parse_contact_report(output_dir / 'Sat1_To_Earth_Contacts.txt'),
        'sat2_earth': parse_contact_report(output_dir / 'Sat2_To_Earth_Contacts.txt'),
        'shackleton_sat1': parse_contact_report(output_dir / 'Shackleton_To_Sat1_Contacts.txt'),
        'shackleton_sat2': parse_contact_report(output_dir / 'Shackleton_To_Sat2_Contacts.txt'),
    }
    
    # Set up the matplotlib figure and 3D axis
    print("Setting up 3D plot...")
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Create a simple static plot instead of animation for testing
    print("Creating static visualization...")
    
    # For celestial bodies, use fixed approximate positions (simplified for visualization)
    # Earth at origin, Moon at approximate distance
    earth_pos = [0, 0, 0, 0, 0, 0]  # Earth at origin
    luna_pos = [384400, 0, 0, 0, 0, 0]  # Moon at ~384,400 km distance (simplified)
    
    # For spacecraft, use GetRvectorParameter("CartesianState")
    try:
        sat1_pos = sat1.GetRvectorParameter("CartesianState")
        sat2_pos = sat2.GetRvectorParameter("CartesianState")
        print(f"Sat1 position: {sat1_pos[:3]}")
        print(f"Sat2 position: {sat2_pos[:3]}")
    except Exception as e:
        print(f"Error getting spacecraft positions: {e}")
        # Use default positions if API call fails
        sat1_pos = [200000, 0, 0, 0, 0, 0]
        sat2_pos = [200000, 100000, 0, 0, 0, 0]
    
    # For ground stations, get their configured coordinates and convert to Cartesian
    # FIX: Use correct API methods to get ground station parameters
    import math
    earth_radius = 6378.137  # km
    luna_radius = 1737.4     # km
    
    # Houston coordinates (spherical to Cartesian conversion)
    houston_lat = houston.GetRealParameter("Location1")  # 29.76 deg
    houston_lon = houston.GetRealParameter("Location2")  # -95.36 deg
    houston_alt = houston.GetRealParameter("Location3")  # 0.0 km
    
    houston_r = earth_radius + houston_alt
    houston_lat_rad = math.radians(houston_lat)
    houston_lon_rad = math.radians(houston_lon)
    
    houston_pos = [
        houston_r * math.cos(houston_lat_rad) * math.cos(houston_lon_rad),
        houston_r * math.cos(houston_lat_rad) * math.sin(houston_lon_rad),
        houston_r * math.sin(houston_lat_rad),
        0, 0, 0  # velocity components
    ]
    
    # Sydney coordinates 
    sydney_lat = sydney.GetRealParameter("Location1")  # -33.87 deg
    sydney_lon = sydney.GetRealParameter("Location2")  # 151.21 deg
    sydney_alt = sydney.GetRealParameter("Location3")  # 0.0 km
    sydney_r = earth_radius + sydney_alt
    sydney_lat_rad = math.radians(sydney_lat)
    sydney_lon_rad = math.radians(sydney_lon)
    
    sydney_pos = [
        sydney_r * math.cos(sydney_lat_rad) * math.cos(sydney_lon_rad),
        sydney_r * math.cos(sydney_lat_rad) * math.sin(sydney_lon_rad),
        sydney_r * math.sin(sydney_lat_rad),
        0, 0, 0
    ]
    
    # Ireland coordinates
    ireland_lat = ireland.GetRealParameter("Location1")  # 53.0 deg
    ireland_lon = ireland.GetRealParameter("Location2")  # -8.0 deg
    ireland_alt = ireland.GetRealParameter("Location3")  # 0.0 km
    ireland_r = earth_radius + ireland_alt
    ireland_lat_rad = math.radians(ireland_lat)
    ireland_lon_rad = math.radians(ireland_lon)
    
    ireland_pos = [
        ireland_r * math.cos(ireland_lat_rad) * math.cos(ireland_lon_rad),
        ireland_r * math.cos(ireland_lat_rad) * math.sin(ireland_lon_rad),
        ireland_r * math.sin(ireland_lat_rad),
        0, 0, 0
    ]
    
    # Shackleton Crater coordinates (on Moon)
    shackleton_lat = shackleton.GetRealParameter("Location1")  # -90.0 deg (South Pole)
    shackleton_lon = shackleton.GetRealParameter("Location2")  # 0.0 deg  
    shackleton_alt = shackleton.GetRealParameter("Location3")  # 0.0 km
    shackleton_r = luna_radius + shackleton_alt
    shackleton_lat_rad = math.radians(shackleton_lat)
    shackleton_lon_rad = math.radians(shackleton_lon)
    
    # Position relative to Moon center, then add Moon position
    shackleton_rel = [
        shackleton_r * math.cos(shackleton_lat_rad) * math.cos(shackleton_lon_rad),
        shackleton_r * math.cos(shackleton_lat_rad) * math.sin(shackleton_lon_rad),
        shackleton_r * math.sin(shackleton_lat_rad)
    ]
    
    shackleton_pos = [
        luna_pos[0] + shackleton_rel[0],
        luna_pos[1] + shackleton_rel[1], 
        luna_pos[2] + shackleton_rel[2],
        0, 0, 0
    ]

    # Plot celestial bodies
    ax.scatter(*earth_pos[:3], color='blue', s=200, label='Earth')
    ax.scatter(*luna_pos[:3], color='gray', s=100, label='Moon')
    
    # Plot spacecraft
    ax.scatter(*sat1_pos[:3], color='red', s=50, label='Sat1')
    ax.scatter(*sat2_pos[:3], color='orange', s=50, label='Sat2')
    
    # Plot ground stations
    ax.scatter(*houston_pos[:3], color='green', s=30, label='Houston')
    ax.scatter(*sydney_pos[:3], color='lightgreen', s=30, label='Sydney')
    ax.scatter(*ireland_pos[:3], color='darkgreen', s=30, label='Ireland')
    ax.scatter(*shackleton_pos[:3], color='purple', s=30, label='Shackleton Crater')
    
    # Draw some example contact lines (simplified for demonstration)
    print("Drawing example contact lines...")
    try:
        # Draw line from Sat1 to Houston (example)
        ax.plot([sat1_pos[0], houston_pos[0]], [sat1_pos[1], houston_pos[1]], 
               [sat1_pos[2], houston_pos[2]], 'r--', alpha=0.7, linewidth=1, label='Sat1-Houston')
        
        # Draw line from Shackleton to Sat2 (example)
        ax.plot([shackleton_pos[0], sat2_pos[0]], [shackleton_pos[1], sat2_pos[1]], 
               [shackleton_pos[2], sat2_pos[2]], 'purple', linestyle='--', alpha=0.7, linewidth=1, label='Shackleton-Sat2')
    except Exception as e:
        print(f"Warning: Could not draw contact lines: {e}")
    
    # Set axis properties
    ax.set_xlabel('X (km)')
    ax.set_ylabel('Y (km)')
    ax.set_zlabel('Z (km)')
    ax.set_title('Lunar Contact Scenario - Static View')
    ax.legend()
    
    # Save the static plot
    print("Saving static plot...")
    plt.savefig('lunar_contact_static.png', dpi=150, bbox_inches='tight')
    print("Static plot saved as lunar_contact_static.png")
    
    # Close the plot
    plt.close()


if __name__ == '__main__':
    main() 